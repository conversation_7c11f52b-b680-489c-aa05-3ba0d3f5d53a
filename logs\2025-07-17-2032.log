2025-07-17 20:32:20 INFO - try to run core in service mode
2025-07-17 20:32:20 INFO - start service: {"log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-17-2032.log", "core_type": "verge-mihomo-alpha", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev"}
2025-07-17 20:32:20 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-17 20:32:20 INFO - No hotkeys configured
2025-07-17 20:32:20 INFO - Starting to create window
2025-07-17 20:32:20 INFO - Creating new window
2025-07-17 20:32:20 INFO - Window created successfully, attempting to show
2025-07-17 20:32:21 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-17 20:32:21 INFO - Successfully registered hotkey Control+Q for quit
2025-07-17 20:32:21 INFO - running timer task `R3SfPp0qApId`
2025-07-17 21:03:32 INFO - Starting to create window
2025-07-17 21:03:32 INFO - Found existing window, trying to show it
2025-07-17 21:03:32 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-17 21:03:32 INFO - Successfully registered hotkey Control+Q for quit
2025-07-17 21:09:40 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-17 21:09:40 INFO - Successfully registered hotkey Control+Q for quit
2025-07-17 21:09:40 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-17 21:09:40 INFO - Successfully registered hotkey Control+Q for quit
2025-07-17 21:32:12 INFO - Starting to create window
2025-07-17 21:32:12 INFO - Found existing window, trying to show it
2025-07-17 21:32:12 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-17 21:32:12 INFO - Successfully registered hotkey Control+Q for quit
2025-07-17 21:32:20 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-17 21:32:20 INFO - Successfully registered hotkey Control+Q for quit
2025-07-17 21:32:50 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-17 21:32:50 INFO - Successfully registered hotkey Control+Q for quit
2025-07-17 21:32:50 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-17 21:32:50 INFO - Successfully registered hotkey Control+Q for quit
