Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-27T20:26:17.557900500+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-27T20:26:17.588328700+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-27T20:26:17.588328700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-27T20:26:17.589398700+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-27T20:26:17.610817000+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-27T20:26:17.611328300+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-27T20:26:17.880602400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-27T20:26:17.880602400+08:00" level=info msg="Load GeoSite rule: private"
time="2025-06-27T20:26:17.897886700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-27T20:26:17.900645900+08:00" level=info msg="Initial configuration complete, total time: 314ms"
time="2025-06-27T20:26:17.900815900+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-06-27T20:26:17.902315700+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-27T20:26:17.902826800+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-06-27T20:26:17.902826800+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-06-27T20:26:17.902826800+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider TVB"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider NowE"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider google"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Discord"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider apple"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Disney"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider BBC"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider telegram"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Lan"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider Line"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-27T20:26:17.911449900+08:00" level=info msg="Start initial provider TVer"
time="2025-06-27T20:26:19.868975300+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-27T20:26:19.869662900+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-27T20:26:19.869662900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-27T20:26:19.870173400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-27T20:26:19.870173400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-27T20:26:19.870173400+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-27T20:26:19.871189900+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-27T20:26:22.914006800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-27T20:26:22.914620100+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-27T20:26:22.915136800+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-27T20:26:22.915136800+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-27T20:26:22.914625800+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-27T20:26:22.915136800+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.914117200+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-27T20:26:22.914117200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:22.915136800+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-27T20:26:22.915136800+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-27T20:26:22.917715200+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-27T20:26:22.917715200+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-27T20:26:22.917715200+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-27T20:26:22.917715200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-27T20:26:22.919589900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider BBC"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Line"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Lan"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider telegram"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider TVB"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider apple"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider NowE"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-27T20:26:22.919578600+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-27T20:26:22.919578600+08:00" level=info msg="Start initial provider google"
time="2025-06-27T20:26:22.919578600+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-27T20:26:22.919578600+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-27T20:26:22.919578600+08:00" level=info msg="Start initial provider Discord"
time="2025-06-27T20:26:22.919578600+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-27T20:26:22.919578600+08:00" level=info msg="Start initial provider TVer"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Disney"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-27T20:26:22.919578600+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-27T20:26:22.919076100+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920695000+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:26:27.920185800+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-27T20:26:27.920695000+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-27T20:26:27.919680500+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-27T20:26:27.920695000+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-27T20:26:27.920695000+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-27T20:26:27.925578500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-27T20:26:27.925578500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-27T20:26:27.925578500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-27T20:26:27.925578500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-27T20:26:28.177285400+08:00" level=info msg="[TCP] 127.0.0.1:51510 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:26:28.240596100+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-27T20:26:28.241107700+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-27T20:26:28.241107700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-27T20:26:28.242035300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-27T20:26:28.242035300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-27T20:26:28.242035300+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-27T20:26:28.243567700+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-27T20:26:28.244581200+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Line"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider TVer"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider telegram"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider apple"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider TVB"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider google"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider BBC"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Lan"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Disney"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider NowE"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Discord"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-27T20:26:28.245091800+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-27T20:26:28.399076400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.399076400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.399076400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.400114000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.400114000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.400837600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.401774300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.401774300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.401774300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.401774300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.403489600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.403489600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.403489600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.403489600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.403489600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.404005400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.404005400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.404005400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.404005400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.406679900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.406679900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.406679900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.406679900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407187300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407187300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407187300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407187300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407187300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407187300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407187300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407187300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407187300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407699000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407699000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407699000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407699000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.407699000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.408087500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.408087500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.408087500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.413998800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.413998800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.413998800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.413998800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.413998800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.413998800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.414512600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.414512600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.414512600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-27T20:26:28.945089100+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-06-27T20:26:28.951378100+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-06-27T20:26:28.963168900+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-06-27T20:26:28.985628000+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-06-27T20:26:28.992806600+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-06-27T20:26:28.997754500+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-06-27T20:26:29.008333700+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-06-27T20:26:29.013751500+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-06-27T20:26:29.019119700+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-06-27T20:26:29.031585000+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-06-27T20:26:29.033084500+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-06-27T20:26:29.034373100+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-06-27T20:26:29.036658600+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-06-27T20:26:29.040822500+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-06-27T20:26:29.042125500+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-06-27T20:26:29.044825500+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-06-27T20:26:29.046839500+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-06-27T20:26:29.051377700+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-06-27T20:26:29.052625900+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-06-27T20:26:29.061979400+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-06-27T20:26:29.063633100+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-06-27T20:26:29.069811900+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-06-27T20:26:29.075407500+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-06-27T20:26:29.081657700+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-06-27T20:26:29.082515600+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-06-27T20:26:29.093217100+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-06-27T20:26:29.096192800+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-06-27T20:26:29.096581900+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-06-27T20:26:29.110443600+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-06-27T20:26:29.120377000+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-06-27T20:26:29.139684700+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-06-27T20:26:29.139684700+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-06-27T20:26:29.143729100+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-06-27T20:26:29.146324000+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-06-27T20:26:29.147696100+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-06-27T20:26:29.566233200+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-06-27T20:26:29.693809900+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-06-27T20:26:32.342444400+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-06-27T20:26:34.481840200+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-27T20:26:34.487429200+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-27T20:26:34.491745000+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-27T20:26:34.514153200+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-27T20:26:34.515951300+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-27T20:26:34.515951300+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-27T20:26:34.518043100+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-27T20:26:34.519144200+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-27T20:26:34.520907500+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-27T20:26:34.522739500+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-27T20:26:34.698668000+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-27T20:26:34.703312500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-27T20:26:34.703312500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-27T20:26:34.703312500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-27T20:26:34.703312500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-27T20:26:35.177640500+08:00" level=info msg="[TCP] 127.0.0.1:51657 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:26:35.340576600+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-27T20:26:35.340576600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T20:26:35.340576600+08:00" level=error msg="🇸🇬新加坡01 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-27T20:26:35.340576600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-27T20:26:35.618871600+08:00" level=info msg="[TCP] 127.0.0.1:51670 --> www.bing.com:443 using GLOBAL"
time="2025-06-27T20:26:36.294183600+08:00" level=info msg="[TCP] 127.0.0.1:51688 --> cn.bing.com:443 using GLOBAL"
time="2025-06-27T20:26:36.910725100+08:00" level=info msg="[TCP] 127.0.0.1:51695 --> github.com:443 using GLOBAL"
time="2025-06-27T20:26:36.910725100+08:00" level=info msg="[TCP] 127.0.0.1:51698 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:26:36.914313100+08:00" level=info msg="[TCP] 127.0.0.1:51696 --> github.githubassets.com:443 using GLOBAL"
time="2025-06-27T20:26:37.983829900+08:00" level=info msg="[TCP] 127.0.0.1:51705 --> github.githubassets.com:443 using GLOBAL"
time="2025-06-27T20:26:37.985353600+08:00" level=info msg="[TCP] 127.0.0.1:51704 --> avatars.githubusercontent.com:443 using GLOBAL"
time="2025-06-27T20:26:38.592811100+08:00" level=info msg="[TCP] 127.0.0.1:51713 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:26:39.174348300+08:00" level=info msg="[TCP] 127.0.0.1:51715 --> alive.github.com:443 using GLOBAL"
time="2025-06-27T20:26:39.253767500+08:00" level=info msg="[TCP] 127.0.0.1:51717 --> collector.github.com:443 using GLOBAL"
time="2025-06-27T20:26:39.276004100+08:00" level=info msg="[TCP] 127.0.0.1:51719 --> api.github.com:443 using GLOBAL"
time="2025-06-27T20:26:39.575114000+08:00" level=info msg="[TCP] 127.0.0.1:51721 --> collector.github.com:443 using GLOBAL"
time="2025-06-27T20:26:41.800586200+08:00" level=info msg="[TCP] 127.0.0.1:51726 --> www.bing.com:443 using GLOBAL"
time="2025-06-27T20:26:42.327876800+08:00" level=info msg="[TCP] 127.0.0.1:51728 --> objects.githubusercontent.com:443 using GLOBAL"
time="2025-06-27T20:26:42.473408200+08:00" level=info msg="[TCP] 127.0.0.1:51730 --> cn.bing.com:443 using GLOBAL"
time="2025-06-27T20:26:48.498099300+08:00" level=info msg="[TCP] 127.0.0.1:51737 --> objects.githubusercontent.com:443 using GLOBAL"
time="2025-06-27T20:26:50.199941700+08:00" level=info msg="[TCP] 127.0.0.1:51741 --> dl-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:26:55.568582700+08:00" level=info msg="[TCP] 127.0.0.1:51748 --> download.clashverge.dev:443 using GLOBAL"
time="2025-06-27T20:27:15.931759000+08:00" level=info msg="[TCP] 127.0.0.1:51766 --> alive.github.com:443 using GLOBAL"
time="2025-06-27T20:27:16.229218300+08:00" level=info msg="[TCP] 127.0.0.1:51769 --> objects.githubusercontent.com:443 using GLOBAL"
time="2025-06-27T20:27:17.777258500+08:00" level=info msg="[TCP] 127.0.0.1:51773 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:27:18.183286300+08:00" level=info msg="[TCP] 127.0.0.1:51775 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:27:18.972324300+08:00" level=info msg="[TCP] 127.0.0.1:51777 --> app-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:27:21.170838100+08:00" level=info msg="[TCP] 127.0.0.1:51782 --> www.bing.com:443 using GLOBAL"
time="2025-06-27T20:50:34.832251200+08:00" level=info msg="[TCP] 127.0.0.1:54924 --> px.ads.linkedin.com:443 using GLOBAL"
time="2025-06-27T20:50:34.932833800+08:00" level=info msg="[TCP] 127.0.0.1:54930 --> pro.fontawesome.com:443 using GLOBAL"
time="2025-06-27T20:50:34.933935900+08:00" level=info msg="[TCP] 127.0.0.1:54927 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:50:34.940691000+08:00" level=info msg="[TCP] 127.0.0.1:54933 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:50:34.941736700+08:00" level=info msg="[TCP] 127.0.0.1:54935 --> cdn.cookielaw.org:443 using GLOBAL"
time="2025-06-27T20:50:34.941736700+08:00" level=info msg="[TCP] 127.0.0.1:54928 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:50:36.074627800+08:00" level=info msg="[TCP] 127.0.0.1:54943 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:50:36.351137600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54912 --> maliva-mcs.byteoversea.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:50:36.917788400+08:00" level=info msg="[TCP] 127.0.0.1:54949 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:50:36.926301000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54920 --> mon-va.byteoversea.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-27T20:50:36.980461900+08:00" level=info msg="[TCP] 127.0.0.1:54951 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:50:37.171071900+08:00" level=info msg="[TCP] 127.0.0.1:54955 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-27T20:50:37.176581200+08:00" level=info msg="[TCP] 127.0.0.1:54954 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-27T20:50:39.914262500+08:00" level=info msg="[TCP] 127.0.0.1:54967 --> www.google-analytics.com:443 using GLOBAL"
time="2025-06-27T20:50:40.996779400+08:00" level=info msg="[TCP] 127.0.0.1:54970 --> www.facebook.com:443 using GLOBAL"
time="2025-06-27T20:50:41.091371100+08:00" level=info msg="[TCP] 127.0.0.1:54973 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:50:41.094544900+08:00" level=info msg="[TCP] 127.0.0.1:54975 --> cdn.cookielaw.org:443 using GLOBAL"
time="2025-06-27T20:50:41.108136600+08:00" level=info msg="[TCP] 127.0.0.1:54977 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:50:41.122411900+08:00" level=info msg="[TCP] 127.0.0.1:54979 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:50:41.378237700+08:00" level=info msg="[TCP] 127.0.0.1:54981 --> pro.fontawesome.com:443 using GLOBAL"
time="2025-06-27T20:50:42.383515300+08:00" level=info msg="[TCP] 127.0.0.1:54984 --> federation.identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:50:43.226162500+08:00" level=info msg="[TCP] 127.0.0.1:54987 --> a.quora.com:443 using GLOBAL"
time="2025-06-27T20:50:43.325484900+08:00" level=info msg="[TCP] 127.0.0.1:54989 --> pixel-config.reddit.com:443 using GLOBAL"
time="2025-06-27T20:50:43.429486700+08:00" level=info msg="[TCP] 127.0.0.1:54991 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:50:43.756258200+08:00" level=info msg="[TCP] 127.0.0.1:54993 --> www.facebook.com:443 using GLOBAL"
time="2025-06-27T20:50:44.128896800+08:00" level=info msg="[TCP] 127.0.0.1:54996 --> bat.bing.com:443 using GLOBAL"
time="2025-06-27T20:50:44.769248300+08:00" level=info msg="[TCP] 127.0.0.1:54998 --> conversions-config.reddit.com:443 using GLOBAL"
time="2025-06-27T20:50:47.750128600+08:00" level=info msg="[TCP] 127.0.0.1:55003 --> img.en25.com:443 using GLOBAL"
time="2025-06-27T20:50:47.866695800+08:00" level=info msg="[TCP] 127.0.0.1:55005 --> s1325.t.eloqua.com:443 using GLOBAL"
time="2025-06-27T20:50:47.972469600+08:00" level=info msg="[TCP] 127.0.0.1:55007 --> s1325.t.eloqua.com:443 using GLOBAL"
time="2025-06-27T20:50:48.545118200+08:00" level=info msg="[TCP] 127.0.0.1:55009 --> federation.identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:50:49.244280900+08:00" level=info msg="[TCP] 127.0.0.1:55012 --> static.hotjar.com:443 using GLOBAL"
time="2025-06-27T20:50:49.372870200+08:00" level=info msg="[TCP] 127.0.0.1:55014 --> www.google.com:443 using GLOBAL"
time="2025-06-27T20:50:49.373993800+08:00" level=info msg="[TCP] 127.0.0.1:55016 --> a.quora.com:443 using GLOBAL"
time="2025-06-27T20:50:49.659213900+08:00" level=info msg="[TCP] 127.0.0.1:55018 --> pixel-config.reddit.com:443 using GLOBAL"
time="2025-06-27T20:50:50.211855500+08:00" level=info msg="[TCP] 127.0.0.1:55021 --> q.quora.com:443 using GLOBAL"
time="2025-06-27T20:50:50.218074400+08:00" level=info msg="[TCP] 127.0.0.1:55022 --> static.ads-twitter.com:443 using GLOBAL"
time="2025-06-27T20:50:50.225562500+08:00" level=info msg="[TCP] 127.0.0.1:55025 --> px.ads.linkedin.com:443 using GLOBAL"
time="2025-06-27T20:50:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:55027 --> alb.reddit.com:443 using GLOBAL"
time="2025-06-27T20:50:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:55029 --> rum-collector-2.pingdom.net:443 using GLOBAL"
time="2025-06-27T20:50:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:55031 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:50:52.*********+08:00" level=info msg="[TCP] 127.0.0.1:55034 --> accounts.google.com:443 using GLOBAL"
time="2025-06-27T20:50:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:55040 --> static.ads-twitter.com:443 using GLOBAL"
time="2025-06-27T20:50:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:55042 --> alb.reddit.com:443 using GLOBAL"
time="2025-06-27T20:50:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:55044 --> px.ads.linkedin.com:443 using GLOBAL"
time="2025-06-27T20:50:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:55046 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:50:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:55048 --> rum-collector-2.pingdom.net:443 using GLOBAL"
time="2025-06-27T20:51:05.*********+08:00" level=info msg="[TCP] 127.0.0.1:55057 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-27T20:51:05.*********+08:00" level=info msg="[TCP] 127.0.0.1:55059 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-27T20:51:05.*********+08:00" level=info msg="[TCP] 127.0.0.1:55061 --> ssl.gstatic.com:443 using GLOBAL"
time="2025-06-27T20:51:07.411079900+08:00" level=info msg="[TCP] 127.0.0.1:55066 --> www.google.com:443 using GLOBAL"
time="2025-06-27T20:51:08.016118700+08:00" level=info msg="[TCP] 127.0.0.1:55068 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:51:10.044670900+08:00" level=info msg="[TCP] 127.0.0.1:55072 --> federation.identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:51:10.045748400+08:00" level=info msg="[TCP] 127.0.0.1:55071 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:51:11.181379100+08:00" level=info msg="[TCP] 127.0.0.1:55076 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:51:11.438263800+08:00" level=info msg="[TCP] 127.0.0.1:55078 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:51:11.748333600+08:00" level=info msg="[TCP] 127.0.0.1:55082 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:51:13.474454300+08:00" level=info msg="[TCP] 127.0.0.1:55087 --> play.google.com:443 using GLOBAL"
time="2025-06-27T20:51:13.499297500+08:00" level=info msg="[TCP] 127.0.0.1:55089 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-27T20:51:14.333342900+08:00" level=info msg="[TCP] 127.0.0.1:55091 --> play.google.com:443 using GLOBAL"
time="2025-06-27T20:51:14.474306400+08:00" level=info msg="[TCP] 127.0.0.1:55085 --> play.google.com:443 using GLOBAL"
time="2025-06-27T20:51:16.203185700+08:00" level=info msg="[TCP] 127.0.0.1:55094 --> federation.identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:51:19.666397000+08:00" level=info msg="[TCP] 127.0.0.1:55102 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:51:20.432028600+08:00" level=info msg="[TCP] 127.0.0.1:55099 --> lf3-static.bytednsdoc.com:443 using GLOBAL"
time="2025-06-27T20:51:21.540924700+08:00" level=info msg="[TCP] 127.0.0.1:55105 --> w3-reporting-nel.reddit.com:443 using GLOBAL"
time="2025-06-27T20:51:22.514449600+08:00" level=info msg="[TCP] 127.0.0.1:55108 --> www.bing.com:443 using GLOBAL"
time="2025-06-27T20:51:23.304596400+08:00" level=info msg="[TCP] 127.0.0.1:55111 --> unpkg.com:443 using GLOBAL"
time="2025-06-27T20:51:23.322443300+08:00" level=info msg="[TCP] 127.0.0.1:55113 --> img.en25.com:443 using GLOBAL"
time="2025-06-27T20:51:23.417621500+08:00" level=info msg="[TCP] 127.0.0.1:55115 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:51:23.719181100+08:00" level=info msg="[TCP] 127.0.0.1:55121 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:51:23.719181100+08:00" level=info msg="[TCP] 127.0.0.1:55120 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:51:23.720996100+08:00" level=info msg="[TCP] 127.0.0.1:55119 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:51:23.720996100+08:00" level=info msg="[TCP] 127.0.0.1:55118 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:51:23.723586400+08:00" level=info msg="[TCP] 127.0.0.1:55122 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-27T20:51:24.045805200+08:00" level=info msg="[TCP] 127.0.0.1:55128 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:51:24.061946400+08:00" level=info msg="[TCP] 127.0.0.1:55130 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:51:24.062977000+08:00" level=info msg="[TCP] 127.0.0.1:55131 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:51:24.065336500+08:00" level=info msg="[TCP] 127.0.0.1:55134 --> pro.fontawesome.com:443 using GLOBAL"
time="2025-06-27T20:51:24.157417600+08:00" level=info msg="[TCP] 127.0.0.1:55136 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:51:24.465402200+08:00" level=info msg="[TCP] 127.0.0.1:55138 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:51:24.939814500+08:00" level=info msg="[TCP] 127.0.0.1:55140 --> identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:51:27.678092600+08:00" level=info msg="[TCP] 127.0.0.1:55146 --> w3-reporting-nel.reddit.com:443 using GLOBAL"
time="2025-06-27T20:51:28.674268200+08:00" level=info msg="[TCP] 127.0.0.1:55149 --> www.bing.com:443 using GLOBAL"
time="2025-06-27T20:51:29.088345000+08:00" level=info msg="[TCP] 127.0.0.1:55151 --> www.google-analytics.com:443 using GLOBAL"
time="2025-06-27T20:51:29.454717100+08:00" level=info msg="[TCP] 127.0.0.1:55153 --> unpkg.com:443 using GLOBAL"
time="2025-06-27T20:51:29.477904900+08:00" level=info msg="[TCP] 127.0.0.1:55155 --> img.en25.com:443 using GLOBAL"
time="2025-06-27T20:51:29.936254700+08:00" level=info msg="[TCP] 127.0.0.1:55158 --> d6vtbcy3ong79.cloudfront.net:443 using GLOBAL"
time="2025-06-27T20:51:30.096702400+08:00" level=info msg="[TCP] 127.0.0.1:55160 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:51:30.098494300+08:00" level=info msg="[TCP] 127.0.0.1:55162 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:51:31.820634400+08:00" level=info msg="[TCP] 127.0.0.1:55167 --> www.facebook.com:443 using GLOBAL"
time="2025-06-27T20:51:33.266463100+08:00" level=info msg="[TCP] 127.0.0.1:55170 --> d6vtbcy3ong79.cloudfront.net:443 using GLOBAL"
time="2025-06-27T20:51:34.000510400+08:00" level=info msg="[TCP] 127.0.0.1:55173 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-27T20:51:34.552841500+08:00" level=info msg="[TCP] 127.0.0.1:55176 --> tos-sg16-share.vodupload.com:443 using GLOBAL"
time="2025-06-27T20:51:36.006582600+08:00" level=info msg="[TCP] 127.0.0.1:55181 --> static.ads-twitter.com:443 using GLOBAL"
time="2025-06-27T20:51:36.006582600+08:00" level=info msg="[TCP] 127.0.0.1:55180 --> q.quora.com:443 using GLOBAL"
time="2025-06-27T20:51:36.009929100+08:00" level=info msg="[TCP] 127.0.0.1:55182 --> a.quora.com:443 using GLOBAL"
time="2025-06-27T20:51:36.009929100+08:00" level=info msg="[TCP] 127.0.0.1:55183 --> script.crazyegg.com:443 using GLOBAL"
time="2025-06-27T20:51:36.009929100+08:00" level=info msg="[TCP] 127.0.0.1:55184 --> www.redditstatic.com:443 using GLOBAL"
time="2025-06-27T20:51:36.014831200+08:00" level=info msg="[TCP] 127.0.0.1:55191 --> px.ads.linkedin.com:443 using GLOBAL"
time="2025-06-27T20:51:36.018346900+08:00" level=info msg="[TCP] 127.0.0.1:55190 --> px.ads.linkedin.com:443 using GLOBAL"
time="2025-06-27T20:51:36.041002000+08:00" level=info msg="[TCP] 127.0.0.1:55196 --> rum-collector-2.pingdom.net:443 using GLOBAL"
time="2025-06-27T20:51:36.242145100+08:00" level=info msg="[TCP] 127.0.0.1:55198 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:51:36.259129200+08:00" level=info msg="[TCP] 127.0.0.1:55200 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:51:36.630709600+08:00" level=info msg="[TCP] 127.0.0.1:55204 --> t.co:443 using GLOBAL"
time="2025-06-27T20:51:36.630709600+08:00" level=info msg="[TCP] 127.0.0.1:55203 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-27T20:51:36.632091400+08:00" level=info msg="[TCP] 127.0.0.1:55207 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-27T20:51:36.632091400+08:00" level=info msg="[TCP] 127.0.0.1:55202 --> t.co:443 using GLOBAL"
time="2025-06-27T20:51:37.016714800+08:00" level=info msg="[TCP] 127.0.0.1:55210 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:51:37.030782600+08:00" level=info msg="[TCP] 127.0.0.1:55194 --> bat.bing.com:443 using GLOBAL"
time="2025-06-27T20:51:40.298143600+08:00" level=info msg="[TCP] 127.0.0.1:55215 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-06-27T20:51:41.825978200+08:00" level=info msg="[TCP] 127.0.0.1:55220 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-27T20:51:42.788580300+08:00" level=info msg="[TCP] 127.0.0.1:55225 --> www.redditstatic.com:443 using GLOBAL"
time="2025-06-27T20:51:43.017486500+08:00" level=info msg="[TCP] 127.0.0.1:55227 --> alb.reddit.com:443 using GLOBAL"
time="2025-06-27T20:51:43.772294100+08:00" level=info msg="[TCP] 127.0.0.1:55223 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-27T20:51:46.172750800+08:00" level=info msg="[TCP] 127.0.0.1:55231 --> metrics.progress.com:443 using GLOBAL"
time="2025-06-27T20:51:47.055866300+08:00" level=info msg="[TCP] 127.0.0.1:55234 --> federation.identity.telerik.com:443 using GLOBAL"
time="2025-06-27T20:51:49.951711800+08:00" level=info msg="[TCP] 127.0.0.1:55239 --> identity.getfiddler.com:443 using GLOBAL"
time="2025-06-27T20:51:51.933130200+08:00" level=info msg="[TCP] 127.0.0.1:55244 --> getfiddler.com:443 using GLOBAL"
time="2025-06-27T20:51:54.313850800+08:00" level=info msg="[TCP] 127.0.0.1:55248 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-27T20:51:54.637621200+08:00" level=info msg="[TCP] 127.0.0.1:55250 --> bat.bing.com:443 using GLOBAL"
time="2025-06-27T20:51:55.219343300+08:00" level=info msg="[TCP] 127.0.0.1:55252 --> cdn.cookielaw.org:443 using GLOBAL"
time="2025-06-27T20:52:00.770551900+08:00" level=info msg="[TCP] 127.0.0.1:55266 --> bat.bing.com:443 using GLOBAL"
time="2025-06-27T20:52:01.383029200+08:00" level=info msg="[TCP] 127.0.0.1:55274 --> cdn.cookielaw.org:443 using GLOBAL"
time="2025-06-27T20:52:02.220911800+08:00" level=info msg="[TCP] 127.0.0.1:55278 --> cdn.cookielaw.org:443 using GLOBAL"
time="2025-06-27T20:52:02.946566000+08:00" level=info msg="[TCP] 127.0.0.1:55280 --> geolocation.onetrust.com:443 using GLOBAL"
time="2025-06-27T20:52:04.755966400+08:00" level=info msg="[TCP] 127.0.0.1:55286 --> d6vtbcy3ong79.cloudfront.net:443 using GLOBAL"
time="2025-06-27T20:52:04.755966400+08:00" level=info msg="[TCP] 127.0.0.1:55283 --> d2r1yp2w7bby2u.cloudfront.net:443 using GLOBAL"
time="2025-06-27T20:52:04.758930500+08:00" level=info msg="[TCP] 127.0.0.1:55285 --> d6vtbcy3ong79.cloudfront.net:443 using GLOBAL"
time="2025-06-27T20:52:04.835172100+08:00" level=info msg="[TCP] 127.0.0.1:55289 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-27T20:52:05.133871300+08:00" level=info msg="[TCP] 127.0.0.1:55292 --> wzrkt.com:443 using GLOBAL"
time="2025-06-27T20:52:05.923099100+08:00" level=info msg="[TCP] 127.0.0.1:55294 --> privacyportal.onetrust.com:443 using GLOBAL"
time="2025-06-27T20:52:11.266958700+08:00" level=info msg="[TCP] 127.0.0.1:55301 --> wzrkt.com:443 using GLOBAL"
time="2025-06-27T20:52:12.579517000+08:00" level=info msg="[TCP] 127.0.0.1:55309 --> wzrkt.com:443 using GLOBAL"
time="2025-06-27T20:52:12.579517000+08:00" level=info msg="[TCP] 127.0.0.1:55311 --> wzrkt.com:443 using GLOBAL"
time="2025-06-27T20:52:12.580662300+08:00" level=info msg="[TCP] 127.0.0.1:55305 --> wzrkt.com:443 using GLOBAL"
time="2025-06-27T20:52:12.580662300+08:00" level=info msg="[TCP] 127.0.0.1:55307 --> wzrkt.com:443 using GLOBAL"
time="2025-06-27T20:52:22.568238400+08:00" level=info msg="[TCP] 127.0.0.1:55323 --> www.bing.com:443 using GLOBAL"
time="2025-06-27T20:52:31.445410200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55333 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-28T00:31:57.295185800+08:00" level=warning msg="Mihomo shutting down"
