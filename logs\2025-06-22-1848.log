2025-06-22 18:48:36 INFO - try to run core in service mode
2025-06-22 18:48:36 INFO - start service: {"config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "core_type": "verge-mihomo-alpha", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-06-22-1848.log", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe"}
2025-06-22 18:48:36 INFO - Initializing hotkeys, global hotkey enabled: true
2025-06-22 18:48:36 INFO - No hotkeys configured
2025-06-22 18:48:36 INFO - Starting to create window
2025-06-22 18:48:36 INFO - Creating new window
2025-06-22 18:48:36 INFO - Window created successfully, attempting to show
2025-06-22 18:48:36 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-22 18:48:36 INFO - Successfully registered hotkey Control+Q for quit
2025-06-22 21:30:16 INFO - Starting to create window
2025-06-22 21:30:16 INFO - Found existing window, trying to show it
2025-06-22 21:30:16 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-22 21:30:16 INFO - Successfully registered hotkey Control+Q for quit
2025-06-22 21:35:41 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-22 21:35:41 INFO - Successfully registered hotkey Control+Q for quit
2025-06-22 22:22:41 INFO - Starting to create window
2025-06-22 22:22:41 INFO - Found existing window, trying to show it
2025-06-22 22:22:41 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-22 22:22:41 INFO - Successfully registered hotkey Control+Q for quit
2025-06-22 23:04:22 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-22 23:04:22 INFO - Successfully registered hotkey Control+Q for quit
2025-06-22 23:04:22 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-22 23:04:22 INFO - Successfully registered hotkey Control+Q for quit
