2025-06-06 20:50:39 INFO - try to run core in service mode
2025-06-06 20:50:39 INFO - start service: {"bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "core_type": "verge-mihomo-alpha", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-06-06-2050.log"}
2025-06-06 20:50:39 INFO - Initializing hotkeys, global hotkey enabled: true
2025-06-06 20:50:39 INFO - No hotkeys configured
2025-06-06 20:50:39 INFO - Starting to create window
2025-06-06 20:50:39 INFO - Creating new window
2025-06-06 20:50:39 INFO - Window created successfully, attempting to show
2025-06-06 20:50:39 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-06 20:50:39 INFO - Successfully registered hotkey Control+Q for quit
2025-06-06 20:50:39 INFO - running timer task `R3SfPp0qApId`
2025-06-06 20:50:49 INFO - stop the core by service
2025-06-06 20:50:49 ERROR - Failed to unregister hotkey: HotKey { mods: Modifiers(CONTROL), key: KeyQ, id: 524323 }
2025-06-06 20:50:49 INFO - error sending request for url (http://127.0.0.1:9097/configs?force=true)
