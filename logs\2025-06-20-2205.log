2025-06-20 22:05:36 INFO - try to run core in service mode
2025-06-20 22:05:36 INFO - start service: {"core_type": "verge-mihomo-alpha", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-06-20-2205.log"}
2025-06-20 22:05:36 INFO - Initializing hotkeys, global hotkey enabled: true
2025-06-20 22:05:36 INFO - No hotkeys configured
2025-06-20 22:05:36 INFO - Starting to create window
2025-06-20 22:05:36 INFO - Creating new window
2025-06-20 22:05:36 INFO - Window created successfully, attempting to show
2025-06-20 22:05:36 INFO - running timer task `R3SfPp0qApId`
2025-06-20 22:05:36 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-20 22:05:36 INFO - Successfully registered hotkey Control+Q for quit
