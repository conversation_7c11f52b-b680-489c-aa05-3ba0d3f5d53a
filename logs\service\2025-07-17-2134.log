Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-17T21:34:27.476412200+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-17T21:34:27.487205400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-17T21:34:27.487205400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-17T21:34:27.488231100+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-17T21:34:27.511759000+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-17T21:34:27.511759000+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-17T21:34:27.876459700+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-17T21:34:27.876459700+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-17T21:34:27.895562400+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-17T21:34:27.898565500+08:00" level=info msg="Initial configuration complete, total time: 414ms"
time="2025-07-17T21:34:27.898565500+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-17T21:34:27.900567400+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-17T21:34:27.900567400+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-17T21:34:27.900567400+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-17T21:34:27.900567400+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Line"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider TVer"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider NowE"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider google"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider BBC"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Discord"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider TVB"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider telegram"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider apple"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Lan"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Disney"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-17T21:34:27.909575500+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-17T21:34:28.089357600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.089357600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.089873200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.090381400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.090381400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.090979100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.092038100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.092038100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.093081000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.093594000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.093594000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.094641300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.094641300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.094641300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.094641300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.095186600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.095186600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.098776200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.098776200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.099005700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.099005700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.099005700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.099005700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.099516100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.099516100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.099516100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.099516100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.100029700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.100029700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.100531700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.100575300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.106593500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.106593500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.106593500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.107106900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.107106900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.107106900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.107106900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.107106900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.107620600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.107620600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.107620600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.108133300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.108133300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.108646200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.109167800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.109167800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.109167800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.109692600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:28.168464200+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-17T21:34:28.191454200+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-17T21:34:28.215161700+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-17T21:34:29.150157300+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-17T21:34:29.151769200+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-17T21:34:29.155478800+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-17T21:34:29.166708200+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-17T21:34:29.167215400+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-17T21:34:29.191607000+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-07-17T21:34:29.192472400+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-17T21:34:29.192472400+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-17T21:34:29.193743800+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-17T21:34:29.193743800+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-17T21:34:29.196471500+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-17T21:34:29.198407500+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-17T21:34:29.207273500+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-17T21:34:29.214178400+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-17T21:34:29.216193100+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-17T21:34:29.217194000+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-17T21:34:29.217194000+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-17T21:34:29.219859800+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-17T21:34:29.241034700+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-17T21:34:29.247527500+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-17T21:34:29.259666800+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-17T21:34:29.265034800+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-17T21:34:29.288853400+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-17T21:34:29.297179800+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-17T21:34:29.299645700+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-17T21:34:29.300156200+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-07-17T21:34:29.314747600+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-17T21:34:29.314747600+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-17T21:34:29.440099200+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-17T21:34:29.444376900+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-17T21:34:29.449836300+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-17T21:34:29.451907100+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-17T21:34:29.455439700+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-17T21:34:29.481655000+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-17T21:34:29.482203300+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-17T21:34:29.509773000+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-17T21:34:29.594881400+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-17T21:34:29.612263200+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-17T21:34:29.677311800+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-17T21:34:29.711947200+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-17T21:34:29.715321800+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-17T21:34:29.808056400+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-17T21:34:29.986752600+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-07-17T21:34:29.994445500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:31.212190800+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-17T21:34:31.484280900+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-17T21:34:32.008840600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:34:32.727658700+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-17T21:34:32.732863400+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-17T21:34:32.732863400+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-17T21:34:32.732863400+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-17T21:34:32.732863400+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-17T21:34:33.409192100+08:00" level=info msg="[TCP] 127.0.0.1:50166 --> download.clashverge.dev:443 using GLOBAL"
time="2025-07-17T21:34:33.477294800+08:00" level=info msg="[TCP] 127.0.0.1:50176 --> my.microsoftpersonalcontent.com:443 using GLOBAL"
time="2025-07-17T21:34:33.666201400+08:00" level=info msg="[TCP] 127.0.0.1:50195 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T21:34:33.698002000+08:00" level=info msg="[TCP] 127.0.0.1:50200 --> ipv6check-http.steamserver.net:80 using GLOBAL"
time="2025-07-17T21:34:33.749235500+08:00" level=info msg="[TCP] 127.0.0.1:50208 --> api.steampowered.com:443 using GLOBAL"
time="2025-07-17T21:34:34.018937200+08:00" level=info msg="[TCP] 127.0.0.1:50224 --> cmp2-hkg1.steamserver.net:27024 using GLOBAL"
time="2025-07-17T21:34:34.020945800+08:00" level=info msg="[TCP] 127.0.0.1:50225 --> ext2-maa2.steamserver.net:27032 using GLOBAL"
time="2025-07-17T21:34:34.021448200+08:00" level=info msg="[TCP] 127.0.0.1:50223 --> ext2-maa2.steamserver.net:27037 using GLOBAL"
time="2025-07-17T21:34:34.027165900+08:00" level=info msg="[TCP] 127.0.0.1:50232 --> ext2-syd1.steamserver.net:27019 using GLOBAL"
time="2025-07-17T21:34:34.335929500+08:00" level=info msg="[TCP] 127.0.0.1:50247 --> cmp1-sgp1.steamserver.net:27019 using GLOBAL"
time="2025-07-17T21:34:34.742716500+08:00" level=info msg="[TCP] 127.0.0.1:50258 --> login.live.com:443 using GLOBAL"
time="2025-07-17T21:34:35.011386600+08:00" level=info msg="[TCP] 127.0.0.1:50268 --> e5.c.lencr.org:80 using GLOBAL"
time="2025-07-17T21:34:35.533103900+08:00" level=info msg="[TCP] 127.0.0.1:50278 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:35.811763800+08:00" level=info msg="[TCP] 127.0.0.1:50283 --> clients2.google.com:443 using GLOBAL"
time="2025-07-17T21:34:35.843746800+08:00" level=info msg="[TCP] 127.0.0.1:50268 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-07-17T21:34:35.928244600+08:00" level=info msg="[TCP] 127.0.0.1:50288 --> ecs.office.com:443 using GLOBAL"
time="2025-07-17T21:34:35.938957100+08:00" level=info msg="[TCP] 127.0.0.1:50291 --> api.onedrive.com:443 using GLOBAL"
time="2025-07-17T21:34:35.940823900+08:00" level=info msg="[TCP] 127.0.0.1:50292 --> roaming.officeapps.live.com:443 using GLOBAL"
time="2025-07-17T21:34:36.391567400+08:00" level=info msg="[TCP] 127.0.0.1:50307 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:36.491017800+08:00" level=info msg="[TCP] 127.0.0.1:50310 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:36.491017800+08:00" level=info msg="[TCP] 127.0.0.1:50313 --> cmp2-hkg1.steamserver.net:27022 using GLOBAL"
time="2025-07-17T21:34:36.493681400+08:00" level=info msg="[TCP] 127.0.0.1:50315 --> cmp2-tyo3.steamserver.net:27019 using GLOBAL"
time="2025-07-17T21:34:36.493681400+08:00" level=info msg="[TCP] 127.0.0.1:50314 --> cmp1-sgp1.steamserver.net:443 using GLOBAL"
time="2025-07-17T21:34:36.756290000+08:00" level=info msg="[TCP] 127.0.0.1:50322 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:36.869458300+08:00" level=info msg="[TCP] 127.0.0.1:50325 --> cmp1-tyo3.steamserver.net:443 using GLOBAL"
time="2025-07-17T21:34:37.355109400+08:00" level=info msg="[TCP] 127.0.0.1:50268 --> e6.c.lencr.org:80 using GLOBAL"
time="2025-07-17T21:34:37.603653900+08:00" level=info msg="[TCP] 127.0.0.1:50335 --> e5.c.lencr.org:80 using GLOBAL"
time="2025-07-17T21:34:37.604313600+08:00" level=info msg="[TCP] 127.0.0.1:50336 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-17T21:34:37.674218300+08:00" level=info msg="[TCP] 127.0.0.1:50341 --> storage.live.com:443 using GLOBAL"
time="2025-07-17T21:34:37.829568300+08:00" level=info msg="[TCP] 127.0.0.1:50344 --> api.onedrive.com:443 using GLOBAL"
time="2025-07-17T21:34:37.855411100+08:00" level=info msg="[TCP] 127.0.0.1:50347 --> skyapi.live.net:443 using GLOBAL"
time="2025-07-17T21:34:38.168190900+08:00" level=info msg="[TCP] 127.0.0.1:50351 --> ext2-syd1.steamserver.net:27033 using GLOBAL"
time="2025-07-17T21:34:38.363901700+08:00" level=info msg="[TCP] 127.0.0.1:50356 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:38.365456300+08:00" level=info msg="[TCP] 127.0.0.1:50361 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:38.367513400+08:00" level=info msg="[TCP] 127.0.0.1:50359 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:38.368414600+08:00" level=info msg="[TCP] 127.0.0.1:50360 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:38.589655600+08:00" level=info msg="[TCP] 127.0.0.1:50373 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:38.596225300+08:00" level=info msg="[TCP] 127.0.0.1:50376 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:38.606393600+08:00" level=info msg="[TCP] 127.0.0.1:50379 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:38.798025800+08:00" level=info msg="[TCP] 127.0.0.1:50384 --> g.ceipmsn.com:80 using GLOBAL"
time="2025-07-17T21:34:39.194162400+08:00" level=info msg="[TCP] 127.0.0.1:50388 --> cmp3-hkg1.steamserver.net:443 using GLOBAL"
time="2025-07-17T21:34:39.254416100+08:00" level=info msg="[TCP] 127.0.0.1:50391 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-17T21:34:39.400973700+08:00" level=info msg="[TCP] 127.0.0.1:50395 --> arc.msn.com:443 using GLOBAL"
time="2025-07-17T21:34:39.484342200+08:00" level=info msg="[TCP] 127.0.0.1:50398 --> onedriveclucprodbn20010.blob.core.windows.net:443 using GLOBAL"
time="2025-07-17T21:34:39.541616600+08:00" level=info msg="[TCP] 127.0.0.1:50401 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:39.542987300+08:00" level=info msg="[TCP] 127.0.0.1:50404 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:39.551537300+08:00" level=info msg="[TCP] 127.0.0.1:50407 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:39.651351700+08:00" level=error msg="🇲🇾马来西亚01 [2x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-17T21:34:39.651351700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-17T21:34:40.390814700+08:00" level=info msg="[TCP] 127.0.0.1:50410 --> g.live.com:443 using GLOBAL"
time="2025-07-17T21:34:40.604077300+08:00" level=info msg="[TCP] 127.0.0.1:50413 --> bingwallpaper.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:40.637601800+08:00" level=info msg="[TCP] 127.0.0.1:50416 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:40.645281300+08:00" level=info msg="[TCP] 127.0.0.1:50419 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:41.114737400+08:00" level=info msg="[TCP] 127.0.0.1:50422 --> download.clashverge.dev:443 using GLOBAL"
time="2025-07-17T21:34:41.617625400+08:00" level=info msg="[TCP] 127.0.0.1:50425 --> oneclient.sfx.ms:443 using GLOBAL"
time="2025-07-17T21:34:41.666119900+08:00" level=info msg="[TCP] 127.0.0.1:50428 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:41.686030900+08:00" level=info msg="[TCP] 127.0.0.1:50431 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:42.458526200+08:00" level=info msg="[TCP] 127.0.0.1:50440 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-17T21:34:42.755088400+08:00" level=info msg="[TCP] 127.0.0.1:50443 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-17T21:34:43.026964000+08:00" level=info msg="[TCP] 127.0.0.1:50447 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:43.033208200+08:00" level=info msg="[TCP] 127.0.0.1:50450 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:43.054145800+08:00" level=info msg="[TCP] 127.0.0.1:50453 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:43.095175900+08:00" level=info msg="[TCP] 127.0.0.1:50456 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-17T21:34:43.356833500+08:00" level=info msg="[TCP] 127.0.0.1:50459 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:43.706262500+08:00" level=info msg="[TCP] 127.0.0.1:50462 --> settings-win.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:43.838090900+08:00" level=info msg="[TCP] 127.0.0.1:50465 --> my.microsoftpersonalcontent.com:443 using GLOBAL"
time="2025-07-17T21:34:44.061975800+08:00" level=info msg="[TCP] 127.0.0.1:50471 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:44.090088900+08:00" level=info msg="[TCP] 127.0.0.1:50475 --> steamcommunity.com:443 using GLOBAL"
time="2025-07-17T21:34:44.206119400+08:00" level=info msg="[TCP] 127.0.0.1:50478 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:44.289209600+08:00" level=info msg="[TCP] 127.0.0.1:50481 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-17T21:34:44.440388400+08:00" level=info msg="[TCP] 127.0.0.1:50484 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-17T21:34:44.572002300+08:00" level=info msg="[TCP] 127.0.0.1:50487 --> store.steampowered.com:443 using GLOBAL"
time="2025-07-17T21:34:44.574527200+08:00" level=info msg="[TCP] 127.0.0.1:50490 --> store.steampowered.com:443 using GLOBAL"
time="2025-07-17T21:34:44.710360500+08:00" level=info msg="[TCP] 127.0.0.1:50493 --> steamcommunity.com:443 using GLOBAL"
time="2025-07-17T21:34:45.142069100+08:00" level=info msg="[TCP] 127.0.0.1:50496 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:45.167580700+08:00" level=info msg="[TCP] 127.0.0.1:50502 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:45.168083700+08:00" level=info msg="[TCP] 127.0.0.1:50503 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:45.169216600+08:00" level=info msg="[TCP] 127.0.0.1:50508 --> steamcommunity-a.akamaihd.net:443 using GLOBAL"
time="2025-07-17T21:34:46.137949800+08:00" level=info msg="[TCP] 127.0.0.1:50513 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-17T21:34:46.189101700+08:00" level=info msg="[TCP] 127.0.0.1:50516 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:46.198874300+08:00" level=info msg="[TCP] 127.0.0.1:50522 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:46.200951300+08:00" level=info msg="[TCP] 127.0.0.1:50519 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:47.256574300+08:00" level=info msg="[TCP] 127.0.0.1:50525 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:47.269667600+08:00" level=info msg="[TCP] 127.0.0.1:50528 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:47.786925800+08:00" level=info msg="[TCP] 127.0.0.1:50534 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-07-17T21:34:48.209637200+08:00" level=info msg="[TCP] 127.0.0.1:50537 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-07-17T21:34:48.308264500+08:00" level=info msg="[TCP] 127.0.0.1:50541 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:48.313722200+08:00" level=info msg="[TCP] 127.0.0.1:50544 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:48.585578700+08:00" level=info msg="[TCP] 127.0.0.1:50548 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-07-17T21:34:49.252187200+08:00" level=info msg="[TCP] 127.0.0.1:50551 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:49.271450500+08:00" level=info msg="[TCP] 127.0.0.1:50554 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:49.344941900+08:00" level=info msg="[TCP] 127.0.0.1:50557 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:50.242094200+08:00" level=info msg="[TCP] 127.0.0.1:50561 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:50.244487300+08:00" level=info msg="[TCP] 127.0.0.1:50564 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.275102000+08:00" level=info msg="[TCP] 127.0.0.1:50569 --> disc501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.293691600+08:00" level=info msg="[TCP] 127.0.0.1:50574 --> array502.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.297359400+08:00" level=info msg="[TCP] 127.0.0.1:50577 --> array513.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.303901300+08:00" level=info msg="[TCP] 127.0.0.1:50581 --> array515.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.307949400+08:00" level=info msg="[TCP] 127.0.0.1:50584 --> array515.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.315906700+08:00" level=info msg="[TCP] 127.0.0.1:50587 --> array519.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.317612100+08:00" level=info msg="[TCP] 127.0.0.1:50590 --> array517.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.321404900+08:00" level=info msg="[TCP] 127.0.0.1:50593 --> array513.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.326815500+08:00" level=info msg="[TCP] 127.0.0.1:50596 --> array513.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.333194900+08:00" level=info msg="[TCP] 127.0.0.1:50599 --> array502.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.336144500+08:00" level=info msg="[TCP] 127.0.0.1:50602 --> array505.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.341548200+08:00" level=info msg="[TCP] 127.0.0.1:50605 --> array513.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:51.599726800+08:00" level=info msg="[TCP] 127.0.0.1:50613 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:34:51.599726800+08:00" level=info msg="[TCP] 127.0.0.1:50612 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:34:51.600979200+08:00" level=info msg="[TCP] 127.0.0.1:50614 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:51.600979200+08:00" level=info msg="[TCP] 127.0.0.1:50615 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:51.602999200+08:00" level=info msg="[TCP] 127.0.0.1:50616 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:34:51.770644900+08:00" level=info msg="[TCP] 127.0.0.1:50629 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.770644900+08:00" level=info msg="[TCP] 127.0.0.1:50630 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.771479500+08:00" level=info msg="[TCP] 127.0.0.1:50631 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.771479500+08:00" level=info msg="[TCP] 127.0.0.1:50627 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.772455500+08:00" level=info msg="[TCP] 127.0.0.1:50633 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.772455500+08:00" level=info msg="[TCP] 127.0.0.1:50636 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.773802000+08:00" level=info msg="[TCP] 127.0.0.1:50634 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.773802000+08:00" level=info msg="[TCP] 127.0.0.1:50635 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.773802000+08:00" level=info msg="[TCP] 127.0.0.1:50628 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.773802000+08:00" level=info msg="[TCP] 127.0.0.1:50639 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.776067700+08:00" level=info msg="[TCP] 127.0.0.1:50642 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.776067700+08:00" level=info msg="[TCP] 127.0.0.1:50641 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.776067700+08:00" level=info msg="[TCP] 127.0.0.1:50632 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.776581500+08:00" level=info msg="[TCP] 127.0.0.1:50640 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.776581500+08:00" level=info msg="[TCP] 127.0.0.1:50643 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.776581500+08:00" level=info msg="[TCP] 127.0.0.1:50645 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.776581500+08:00" level=info msg="[TCP] 127.0.0.1:50637 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.777210400+08:00" level=info msg="[TCP] 127.0.0.1:50647 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.782285000+08:00" level=info msg="[TCP] 127.0.0.1:50649 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.782285000+08:00" level=info msg="[TCP] 127.0.0.1:50648 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.782285000+08:00" level=info msg="[TCP] 127.0.0.1:50646 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.782798600+08:00" level=info msg="[TCP] 127.0.0.1:50638 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:51.783304700+08:00" level=info msg="[TCP] 127.0.0.1:50644 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:52.085285200+08:00" level=info msg="[TCP] 127.0.0.1:50698 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:52.085285200+08:00" level=info msg="[TCP] 127.0.0.1:50696 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:52.087006200+08:00" level=info msg="[TCP] 127.0.0.1:50699 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:52.087006200+08:00" level=info msg="[TCP] 127.0.0.1:50697 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:52.097957000+08:00" level=info msg="[TCP] 127.0.0.1:50709 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:52.098470900+08:00" level=info msg="[TCP] 127.0.0.1:50708 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:52.262708900+08:00" level=info msg="[TCP] 127.0.0.1:50714 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:52.521822600+08:00" level=info msg="[TCP] 127.0.0.1:50717 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:52.542249100+08:00" level=info msg="[TCP] 127.0.0.1:50720 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:52.551277900+08:00" level=info msg="[TCP] 127.0.0.1:50723 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.614362100+08:00" level=info msg="[TCP] 127.0.0.1:50726 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.621582700+08:00" level=info msg="[TCP] 127.0.0.1:50729 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.627261300+08:00" level=info msg="[TCP] 127.0.0.1:50732 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.627261300+08:00" level=info msg="[TCP] 127.0.0.1:50733 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.637599700+08:00" level=info msg="[TCP] 127.0.0.1:50738 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.643795400+08:00" level=info msg="[TCP] 127.0.0.1:50741 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.650294600+08:00" level=info msg="[TCP] 127.0.0.1:50744 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.655915100+08:00" level=info msg="[TCP] 127.0.0.1:50748 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.656947400+08:00" level=info msg="[TCP] 127.0.0.1:50747 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.667879400+08:00" level=info msg="[TCP] 127.0.0.1:50753 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.670895400+08:00" level=info msg="[TCP] 127.0.0.1:50756 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.681753000+08:00" level=info msg="[TCP] 127.0.0.1:50759 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.688831000+08:00" level=info msg="[TCP] 127.0.0.1:50762 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.698101500+08:00" level=info msg="[TCP] 127.0.0.1:50765 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.706478400+08:00" level=info msg="[TCP] 127.0.0.1:50768 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.708673000+08:00" level=info msg="[TCP] 127.0.0.1:50771 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.710956700+08:00" level=info msg="[TCP] 127.0.0.1:50774 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.714511300+08:00" level=info msg="[TCP] 127.0.0.1:50778 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:52.866488500+08:00" level=info msg="[TCP] 127.0.0.1:50781 --> array506.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:53.121708000+08:00" level=info msg="[TCP] 127.0.0.1:50785 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:34:53.139925600+08:00" level=info msg="[TCP] 127.0.0.1:50788 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:34:53.186060500+08:00" level=info msg="[TCP] 127.0.0.1:50792 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:53.188904900+08:00" level=info msg="[TCP] 127.0.0.1:50795 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:53.188904900+08:00" level=info msg="[TCP] 127.0.0.1:50791 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:53.190139300+08:00" level=info msg="[TCP] 127.0.0.1:50796 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:53.191154800+08:00" level=info msg="[TCP] 127.0.0.1:50794 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:53.194698100+08:00" level=info msg="[TCP] 127.0.0.1:50793 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:53.194698100+08:00" level=info msg="[TCP] 127.0.0.1:50801 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:53.219900100+08:00" level=info msg="[TCP] 127.0.0.1:50813 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:53.236329200+08:00" level=info msg="[TCP] 127.0.0.1:50817 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:53.237584800+08:00" level=info msg="[TCP] 127.0.0.1:50816 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:54.325240100+08:00" level=info msg="[TCP] 127.0.0.1:50822 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:54.500407100+08:00" level=info msg="[TCP] 127.0.0.1:50828 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:54.500407100+08:00" level=info msg="[TCP] 127.0.0.1:50829 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:54.562463500+08:00" level=info msg="[TCP] 127.0.0.1:50839 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:54.562463500+08:00" level=info msg="[TCP] 127.0.0.1:50835 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:34:54.562463500+08:00" level=info msg="[TCP] 127.0.0.1:50840 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:54.562463500+08:00" level=info msg="[TCP] 127.0.0.1:50838 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:54.565466000+08:00" level=info msg="[TCP] 127.0.0.1:50836 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:34:54.565466000+08:00" level=info msg="[TCP] 127.0.0.1:50841 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:54.568468800+08:00" level=info msg="[TCP] 127.0.0.1:50837 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:54.642412900+08:00" level=info msg="[TCP] 127.0.0.1:50854 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:54.665433500+08:00" level=info msg="[TCP] 127.0.0.1:50857 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:54.670438600+08:00" level=info msg="[TCP] 127.0.0.1:50862 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:54.775579400+08:00" level=info msg="[TCP] 127.0.0.1:50867 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:54.777581100+08:00" level=info msg="[TCP] 127.0.0.1:50868 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:54.871845700+08:00" level=info msg="[TCP] 127.0.0.1:50873 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:34:55.585316900+08:00" level=info msg="[TCP] 127.0.0.1:50876 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:55.766280400+08:00" level=info msg="[TCP] 127.0.0.1:50879 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:55.784264000+08:00" level=info msg="[TCP] 127.0.0.1:50882 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:34:56.147883700+08:00" level=info msg="[TCP] 127.0.0.1:50885 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:34:56.151199900+08:00" level=info msg="[TCP] 127.0.0.1:50889 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:56.154179400+08:00" level=info msg="[TCP] 127.0.0.1:50888 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:56.612360400+08:00" level=info msg="[TCP] 127.0.0.1:50895 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:56.789127100+08:00" level=info msg="[TCP] 127.0.0.1:50898 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:34:56.918222100+08:00" level=info msg="[TCP] 127.0.0.1:50901 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:34:57.142830500+08:00" level=info msg="[TCP] 127.0.0.1:50904 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:57.643494400+08:00" level=info msg="[TCP] 127.0.0.1:50908 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:58.069351400+08:00" level=info msg="[TCP] 127.0.0.1:50912 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:58.682262300+08:00" level=info msg="[TCP] 127.0.0.1:50918 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:58.805461300+08:00" level=info msg="[TCP] 127.0.0.1:50921 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:34:58.985986900+08:00" level=info msg="[TCP] 127.0.0.1:50925 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:34:59.004120800+08:00" level=info msg="[TCP] 127.0.0.1:50928 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:59.086468500+08:00" level=info msg="[TCP] 127.0.0.1:50931 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:59.147894000+08:00" level=info msg="[TCP] 127.0.0.1:50935 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:34:59.622696800+08:00" level=info msg="[TCP] 127.0.0.1:50938 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:34:59.971360100+08:00" level=info msg="[TCP] 127.0.0.1:50944 --> edge-consumer-static.azureedge.net:443 using GLOBAL"
time="2025-07-17T21:35:00.104818000+08:00" level=info msg="[TCP] 127.0.0.1:50947 --> substrate.office.com:443 using GLOBAL"
time="2025-07-17T21:35:00.114331300+08:00" level=info msg="[TCP] 127.0.0.1:50950 --> substrate.office.com:443 using GLOBAL"
time="2025-07-17T21:35:00.604388500+08:00" level=info msg="[TCP] 127.0.0.1:50954 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:00.614068600+08:00" level=info msg="[TCP] 127.0.0.1:50957 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:00.656810000+08:00" level=info msg="[TCP] 127.0.0.1:50962 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:00.656810000+08:00" level=info msg="[TCP] 127.0.0.1:50963 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:00.657835100+08:00" level=info msg="[TCP] 127.0.0.1:50960 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:00.659941000+08:00" level=info msg="[TCP] 127.0.0.1:50961 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:01.136837200+08:00" level=info msg="[TCP] 127.0.0.1:50973 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:01.644621300+08:00" level=info msg="[TCP] 127.0.0.1:50976 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:01.891196700+08:00" level=info msg="[TCP] 127.0.0.1:50979 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:02.186544800+08:00" level=info msg="[TCP] 127.0.0.1:50982 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:02.762835100+08:00" level=info msg="[TCP] 127.0.0.1:50987 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:02.762835100+08:00" level=info msg="[TCP] 127.0.0.1:50986 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:02.764499000+08:00" level=info msg="[TCP] 127.0.0.1:50985 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:02.766696800+08:00" level=info msg="[TCP] 127.0.0.1:50988 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:03.077950600+08:00" level=info msg="[TCP] 127.0.0.1:50997 --> api.segment.io:443 using GLOBAL"
time="2025-07-17T21:35:03.111441700+08:00" level=info msg="[TCP] 127.0.0.1:51000 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:03.133368800+08:00" level=info msg="[TCP] 127.0.0.1:51003 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:03.211743700+08:00" level=info msg="[TCP] 127.0.0.1:51006 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:03.284866700+08:00" level=info msg="[TCP] 127.0.0.1:51009 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:03.522722300+08:00" level=info msg="[TCP] 127.0.0.1:51013 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:03.587836100+08:00" level=info msg="[TCP] 127.0.0.1:51016 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:03.605165500+08:00" level=info msg="[TCP] 127.0.0.1:51019 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:04.121719200+08:00" level=info msg="[TCP] 127.0.0.1:51023 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:04.122230600+08:00" level=info msg="[TCP] 127.0.0.1:51024 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:04.122795200+08:00" level=info msg="[TCP] 127.0.0.1:51025 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:04.127445400+08:00" level=info msg="[TCP] 127.0.0.1:51026 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:04.168533600+08:00" level=info msg="[TCP] 127.0.0.1:51035 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:04.219937300+08:00" level=info msg="[TCP] 127.0.0.1:51038 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:04.633094800+08:00" level=info msg="[TCP] 127.0.0.1:51041 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:05.024294200+08:00" level=info msg="[TCP] 127.0.0.1:51044 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:05.242184000+08:00" level=info msg="[TCP] 127.0.0.1:51047 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:06.237743400+08:00" level=info msg="[TCP] 127.0.0.1:51050 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:07.573380000+08:00" level=info msg="[TCP] 127.0.0.1:51055 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:08.125043100+08:00" level=info msg="[TCP] 127.0.0.1:51058 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:08.434524700+08:00" level=info msg="[TCP] 127.0.0.1:51061 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:08.803347900+08:00" level=info msg="[TCP] 127.0.0.1:51064 --> westus-0.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-17T21:35:08.945932900+08:00" level=info msg="[TCP] 127.0.0.1:51067 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:11.060937000+08:00" level=info msg="[TCP] 127.0.0.1:51072 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:12.089647400+08:00" level=info msg="[TCP] 127.0.0.1:51076 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:12.095521600+08:00" level=info msg="[TCP] 127.0.0.1:51079 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:12.756227500+08:00" level=info msg="[TCP] 127.0.0.1:51083 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:12.976401400+08:00" level=info msg="[TCP] 127.0.0.1:51086 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:13.765699300+08:00" level=info msg="[TCP] 127.0.0.1:51090 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:13.831803000+08:00" level=info msg="[TCP] 127.0.0.1:51093 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:14.113135000+08:00" level=info msg="[TCP] 127.0.0.1:51099 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:14.114398800+08:00" level=info msg="[TCP] 127.0.0.1:51097 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:14.114398800+08:00" level=info msg="[TCP] 127.0.0.1:51098 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:14.114909100+08:00" level=info msg="[TCP] 127.0.0.1:51096 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:14.272424900+08:00" level=info msg="[TCP] 127.0.0.1:51108 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:14.930943800+08:00" level=info msg="[TCP] 127.0.0.1:51111 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:15.024156400+08:00" level=info msg="[TCP] 127.0.0.1:51114 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:15.505135800+08:00" level=info msg="[TCP] 127.0.0.1:51117 --> api-prod.nvidia.cn:443 using GLOBAL"
time="2025-07-17T21:35:15.520243400+08:00" level=info msg="[TCP] 127.0.0.1:51120 --> gfwsl.geforce.cn:443 using GLOBAL"
time="2025-07-17T21:35:15.774604900+08:00" level=info msg="[TCP] 127.0.0.1:51124 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:16.177346100+08:00" level=info msg="[TCP] 127.0.0.1:51131 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:22.763488800+08:00" level=info msg="[TCP] 127.0.0.1:51140 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:23.768010800+08:00" level=info msg="[TCP] 127.0.0.1:51144 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:24.273541100+08:00" level=info msg="[TCP] 127.0.0.1:51148 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:25.853084900+08:00" level=info msg="[TCP] 127.0.0.1:51153 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:26.284248300+08:00" level=info msg="[TCP] 127.0.0.1:51156 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:26.704183000+08:00" level=info msg="[TCP] 127.0.0.1:51162 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:27.175383600+08:00" level=info msg="[TCP] 127.0.0.1:51165 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:27.267931200+08:00" level=info msg="[TCP] 127.0.0.1:51168 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:27.713618900+08:00" level=info msg="[TCP] 127.0.0.1:51171 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:28.180748400+08:00" level=info msg="[TCP] 127.0.0.1:51175 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:28.555066700+08:00" level=info msg="[TCP] 127.0.0.1:51179 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:28.556261300+08:00" level=info msg="[TCP] 127.0.0.1:51180 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:28.556261300+08:00" level=info msg="[TCP] 127.0.0.1:51181 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:29.582381600+08:00" level=info msg="[TCP] 127.0.0.1:51188 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:29.702657800+08:00" level=info msg="[TCP] 127.0.0.1:51191 --> prod.rewardsplatform.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:29.995068100+08:00" level=info msg="[TCP] 127.0.0.1:51194 --> static.edge.microsoftapp.net:443 using GLOBAL"
time="2025-07-17T21:35:30.114110000+08:00" level=info msg="[TCP] 127.0.0.1:51197 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:30.238054400+08:00" level=info msg="[TCP] 127.0.0.1:51200 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:30.646895300+08:00" level=info msg="[TCP] 127.0.0.1:51203 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:30.728788600+08:00" level=info msg="[TCP] 127.0.0.1:51206 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:31.169324800+08:00" level=info msg="[TCP] 127.0.0.1:51210 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:35:31.296524600+08:00" level=info msg="[TCP] 127.0.0.1:51213 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:32.281328700+08:00" level=info msg="[TCP] 127.0.0.1:51217 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:32.303117500+08:00" level=info msg="[TCP] 127.0.0.1:51220 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:32.718439100+08:00" level=info msg="[TCP] 127.0.0.1:51227 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:32.721483400+08:00" level=info msg="[TCP] 127.0.0.1:51226 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:32.721483400+08:00" level=info msg="[TCP] 127.0.0.1:51228 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:32.779090600+08:00" level=info msg="[TCP] 127.0.0.1:51235 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:33.638905300+08:00" level=info msg="[TCP] 127.0.0.1:51238 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:33.725862000+08:00" level=info msg="[TCP] 127.0.0.1:51241 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:34.709512300+08:00" level=info msg="[TCP] 127.0.0.1:51246 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:35.137917600+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-17T21:35:35.138446200+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-17T21:35:35.138446200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-17T21:35:35.138953300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-17T21:35:35.139467700+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-17T21:35:35.139467700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-17T21:35:35.141004100+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-17T21:35:35.142535100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-17T21:35:35.149747600+08:00" level=warning msg="[TUN] default interface changed by monitor,  => 以太网"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="[TUN] Tun adapter listening at: Meta([********/30],[fdfe:dcba:9876::1/126]), mtu: 9000, auto route: true, auto redir: false, ip stack: Mixed"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Discord"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-17T21:35:35.452402400+08:00" level=info msg="Start initial provider BBC"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider Disney"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-17T21:35:35.452402400+08:00" level=info msg="Start initial provider NowE"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider Line"
time="2025-07-17T21:35:35.453051400+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-17T21:35:35.453051400+08:00" level=info msg="Start initial provider apple"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider telegram"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Lan"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider google"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider TVB"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider TVer"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-17T21:35:35.451899700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-17T21:35:35.452439000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-17T21:35:35.764386900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.764386900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.765387800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.765387800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.765387800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.765387800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.767389400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.767389400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.767389400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.767389400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.767389400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.767389400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.770379700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.770379700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.770379700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.770379700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.770379700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.770891100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.770891100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.770891100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.770891100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.771402000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.771402000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.771402000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.771402000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.776639700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.776639700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.776639700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.776639700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.776639700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.777153800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.777153800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.777153800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.777153800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.777682500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.778196400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.778196400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.778709000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.778709000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.778709000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.779233400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.779233400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.779233400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.779739800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.779739800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.789424600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.789424600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.789424600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:35.790047800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:35:40.030051600+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-17T21:35:40.038967900+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-17T21:35:40.040545000+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-17T21:35:40.041360300+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-17T21:35:40.057983200+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-17T21:35:40.069227700+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-17T21:35:40.071026300+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-17T21:35:40.075898600+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-17T21:35:40.077118400+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-17T21:35:40.079205800+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-17T21:35:40.080135900+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-17T21:35:40.082944900+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-17T21:35:40.082944900+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-17T21:35:40.086520500+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-17T21:35:40.087441100+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-17T21:35:40.087951600+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-17T21:35:40.087951600+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-17T21:35:40.092151500+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-17T21:35:40.096092300+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-17T21:35:40.098460700+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-17T21:35:40.107737000+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-17T21:35:40.107737000+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-17T21:35:40.113892300+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-17T21:35:40.114929300+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-17T21:35:40.116108600+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-17T21:35:40.118827600+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-17T21:35:40.122266500+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-17T21:35:40.134202400+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-17T21:35:40.140320500+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-07-17T21:35:40.144846300+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-17T21:35:40.149045400+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-17T21:35:40.168206100+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-17T21:35:40.233281700+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-17T21:35:40.249532600+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-17T21:35:40.259452400+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-17T21:35:40.267498200+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-17T21:35:40.283926700+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-17T21:35:40.287189400+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-17T21:35:40.312319300+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-17T21:35:40.320713000+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-17T21:35:40.337996400+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-17T21:35:40.339521000+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-17T21:35:40.352887700+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-17T21:35:40.352887700+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-17T21:35:40.353888700+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-07-17T21:35:40.361683800+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-17T21:35:41.381064600+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-07-17T21:35:43.136543700+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-17T21:35:45.455176300+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": net/http: TLS handshake timeout"
time="2025-07-17T21:35:45.460514300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-17T21:35:45.460514300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-17T21:35:45.460514300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-17T21:35:45.461027700+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-17T21:35:45.604053800+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:49156 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-17T21:35:45.983491300+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:62607 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-17T21:35:46.040503000+08:00" level=info msg="[TCP] ********:51485 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:47.029143400+08:00" level=info msg="[TCP] ********:51505 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:47.382956800+08:00" level=info msg="[TCP] ********:51508 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:48.201122800+08:00" level=info msg="[TCP] ********:51517 --> api.msn.cn:443 using GLOBAL"
time="2025-07-17T21:35:48.320398100+08:00" level=info msg="[TCP] ********:51522 --> gx-target-experiments-frontend-api.gx.nvidia.cn:443 using GLOBAL"
time="2025-07-17T21:35:48.730525000+08:00" level=info msg="[TCP] ********:51527 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:35:48.731543000+08:00" level=info msg="[TCP] ********:51528 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:35:49.900255500+08:00" level=info msg="[TCP] ********:51543 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:35:49.900255500+08:00" level=info msg="[TCP] ********:51541 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:49.902302000+08:00" level=info msg="[TCP] ********:51542 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:49.903824800+08:00" level=info msg="[TCP] ********:51550 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:49.993848200+08:00" level=info msg="[TCP] ********:51553 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:49.996412400+08:00" level=info msg="[TCP] ********:51554 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.052999000+08:00" level=info msg="[TCP] ********:51561 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.052999000+08:00" level=info msg="[TCP] ********:51564 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.054618100+08:00" level=info msg="[TCP] ********:51563 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.054618100+08:00" level=info msg="[TCP] ********:51562 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.055925800+08:00" level=info msg="[TCP] ********:51578 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.055925800+08:00" level=info msg="[TCP] ********:51565 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.055925800+08:00" level=info msg="[TCP] ********:51568 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.055925800+08:00" level=info msg="[TCP] ********:51575 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.056437700+08:00" level=info msg="[TCP] ********:51574 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.058731000+08:00" level=info msg="[TCP] ********:51571 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.058731000+08:00" level=info msg="[TCP] ********:51581 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.058731000+08:00" level=info msg="[TCP] ********:51594 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.061699300+08:00" level=info msg="[TCP] ********:51597 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.069305300+08:00" level=info msg="[TCP] ********:51600 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.093057900+08:00" level=info msg="[TCP] ********:51603 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.094505400+08:00" level=info msg="[TCP] ********:51606 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.096183100+08:00" level=info msg="[TCP] ********:51607 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.158606500+08:00" level=info msg="[TCP] ********:51613 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.158606500+08:00" level=info msg="[TCP] ********:51614 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.159121300+08:00" level=info msg="[TCP] ********:51612 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.160656400+08:00" level=info msg="[TCP] ********:51621 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.231920500+08:00" level=info msg="[TCP] ********:51624 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.557616300+08:00" level=info msg="[TCP] ********:51635 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:50.766627900+08:00" level=info msg="[TCP] ********:51642 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:35:51.431795400+08:00" level=info msg="[TCP] ********:51664 --> repo42.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.554031100+08:00" level=info msg="[TCP] ********:51672 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.633061500+08:00" level=info msg="[TCP] ********:51682 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.646036500+08:00" level=info msg="[TCP] ********:51685 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.652660800+08:00" level=info msg="[TCP] ********:51688 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.661329400+08:00" level=info msg="[TCP] ********:51694 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.661329400+08:00" level=info msg="[TCP] ********:51691 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.670078200+08:00" level=info msg="[TCP] ********:51697 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.678750900+08:00" level=info msg="[TCP] ********:51700 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.686315600+08:00" level=info msg="[TCP] ********:51703 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.723017600+08:00" level=info msg="[TCP] ********:51708 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.737581300+08:00" level=info msg="[TCP] ********:51711 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.765432800+08:00" level=info msg="[TCP] ********:51714 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.769528500+08:00" level=info msg="[TCP] ********:51717 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.777280500+08:00" level=info msg="[TCP] ********:51720 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.797528300+08:00" level=info msg="[TCP] ********:51723 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.811830100+08:00" level=info msg="[TCP] ********:51726 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.853493400+08:00" level=info msg="[TCP] ********:51729 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-17T21:35:51.855376500+08:00" level=info msg="[TCP] ********:51736 --> api3.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.875615600+08:00" level=info msg="[TCP] ********:51741 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.893354200+08:00" level=info msg="[TCP] ********:51745 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:35:51.950685800+08:00" level=info msg="[TCP] ********:51748 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:51.998934900+08:00" level=info msg="[TCP] ********:51753 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:52.014436700+08:00" level=info msg="[TCP] ********:51758 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:52.089612800+08:00" level=info msg="[TCP] ********:51765 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:52.696355500+08:00" level=info msg="[TCP] ********:51774 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:52.696886100+08:00" level=info msg="[TCP] ********:51775 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:53.777555100+08:00" level=info msg="[TCP] ********:51781 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:53.781553200+08:00" level=info msg="[TCP] ********:51782 --> api4.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:53.827865600+08:00" level=info msg="[TCP] ********:51785 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:53.827865600+08:00" level=info msg="[TCP] ********:51783 --> api4.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:53.827865600+08:00" level=info msg="[TCP] ********:51784 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:53.827865600+08:00" level=info msg="[TCP] ********:51787 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:53.828866100+08:00" level=info msg="[TCP] ********:51786 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:53.862131700+08:00" level=info msg="[TCP] ********:51792 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:54.077028000+08:00" level=info msg="[TCP] ********:51805 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:54.093810000+08:00" level=info msg="[TCP] ********:51808 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:35:54.122341100+08:00" level=info msg="[TCP] ********:51811 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:54.428028700+08:00" level=info msg="[TCP] ********:51814 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:35:54.429026700+08:00" level=info msg="[TCP] ********:51815 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:35:54.456997600+08:00" level=info msg="[TCP] ********:51816 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:35:54.966986400+08:00" level=info msg="[TCP] ********:51823 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:54.973658600+08:00" level=info msg="[TCP] ********:51824 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:35:55.001051300+08:00" level=info msg="[TCP] ********:51829 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:55.023269100+08:00" level=info msg="[TCP] ********:51832 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:55.454703900+08:00" level=info msg="[TCP] ********:51837 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:55.454703900+08:00" level=info msg="[TCP] ********:51836 --> us-only.gcpp.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:55.600162600+08:00" level=error msg="🇭🇰香港03 [1.5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp [2409:8a15:2052:15f0:31b9:6105:fb02:5f6f]:51667->[2409:8754:5630:210:0:1:6666:3]:19811: use of closed network connection"
time="2025-07-17T21:35:55.600162600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-17T21:35:55.879527700+08:00" level=info msg="[TCP] ********:51843 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:35:55.889916200+08:00" level=info msg="[TCP] ********:51846 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:56.568055700+08:00" level=info msg="[TCP] ********:51849 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:56.893239500+08:00" level=info msg="[TCP] ********:51852 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:56.932586000+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:51082 --> [2409:8a15:2442:d681:8c83:9e83:c724:bb51]:62207 using GLOBAL"
time="2025-07-17T21:35:57.037631500+08:00" level=info msg="[TCP] ********:51855 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:57.292500200+08:00" level=info msg="[TCP] ********:51858 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:57.860339700+08:00" level=info msg="[TCP] ********:51861 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:58.813108500+08:00" level=info msg="[TCP] ********:51866 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:58.862773100+08:00" level=info msg="[TCP] ********:51869 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:35:58.929452700+08:00" level=info msg="[TCP] ********:51872 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:35:59.317240800+08:00" level=info msg="[TCP] ********:51875 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:00.585976200+08:00" level=info msg="[TCP] ********:51878 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:00.826702200+08:00" level=info msg="[TCP] ********:51881 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:01.442090200+08:00" level=info msg="[TCP] ********:51885 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:02.227050900+08:00" level=info msg="[TCP] ********:51889 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:02.228576500+08:00" level=info msg="[TCP] ********:51890 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:02.228576500+08:00" level=info msg="[TCP] ********:51892 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:02.229604000+08:00" level=info msg="[TCP] ********:51891 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:02.309409900+08:00" level=info msg="[TCP] ********:51901 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:02.511525300+08:00" level=info msg="[TCP] ********:51904 --> api.segment.io:443 using GLOBAL"
time="2025-07-17T21:36:02.801716100+08:00" level=info msg="[TCP] ********:51907 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:03.025392900+08:00" level=info msg="[UDP] ********:51081 --> 111.31.205.120:3478 using GLOBAL"
time="2025-07-17T21:36:03.056026000+08:00" level=info msg="[UDP] ********:65072 --> 111.31.205.120:3478 using GLOBAL"
time="2025-07-17T21:36:03.354783700+08:00" level=info msg="[TCP] ********:51910 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:03.355297500+08:00" level=info msg="[TCP] ********:51911 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:03.355297500+08:00" level=info msg="[TCP] ********:51919 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:03.356824500+08:00" level=info msg="[TCP] ********:51912 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:03.368994500+08:00" level=info msg="[TCP] ********:51922 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:03.371006800+08:00" level=info msg="[TCP] ********:51923 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:03.834640400+08:00" level=info msg="[TCP] ********:51928 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:03.860465500+08:00" level=info msg="[TCP] ********:51931 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:04.606139100+08:00" level=info msg="[TCP] ********:51935 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:04.806061300+08:00" level=info msg="[TCP] ********:51938 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:06.090161500+08:00" level=info msg="[TCP] ********:51942 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:06.277328000+08:00" level=info msg="[TCP] ********:51945 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:06.810048700+08:00" level=info msg="[TCP] ********:51948 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:07.539881800+08:00" level=info msg="[TCP] ********:51952 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:07.687144200+08:00" level=info msg="[TCP] ********:51955 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:08.046361700+08:00" level=info msg="[TCP] ********:51959 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:10.329010500+08:00" level=info msg="[TCP] ********:51963 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:12.211755600+08:00" level=info msg="[TCP] ********:51968 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:13.047489000+08:00" level=info msg="[TCP] ********:51972 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:13.047489000+08:00" level=info msg="[TCP] ********:51971 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:15.004995200+08:00" level=info msg="[TCP] ********:51979 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:16.235345600+08:00" level=info msg="[TCP] ********:51982 --> api4.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:16.655560700+08:00" level=info msg="[TCP] ********:51986 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:17.235512800+08:00" level=info msg="[TCP] ********:51992 --> 156.238.233.201:60381 using GLOBAL"
time="2025-07-17T21:36:17.392886900+08:00" level=info msg="[TCP] ********:51995 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:17.404193100+08:00" level=info msg="[TCP] ********:51998 --> static.nvidiagrid.net:443 using GLOBAL"
time="2025-07-17T21:36:19.848153300+08:00" level=info msg="[TCP] ********:52002 --> events.gx.nvidia.cn:443 using GLOBAL"
time="2025-07-17T21:36:22.215605000+08:00" level=info msg="[TCP] ********:52006 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:23.369484000+08:00" level=info msg="[TCP] ********:52011 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:23.662867900+08:00" level=info msg="[TCP] ********:52014 --> prod.otel.kaizen.nvidia.com:443 using GLOBAL"
time="2025-07-17T21:36:23.882072800+08:00" level=info msg="[TCP] ********:52017 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:24.449793600+08:00" level=info msg="[TCP] ********:52020 --> ocsp.r2m03.amazontrust.com:80 using GLOBAL"
time="2025-07-17T21:36:25.028192300+08:00" level=info msg="[TCP] ********:52023 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:29.681552100+08:00" level=info msg="[TCP] ********:52030 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-17T21:36:31.096485600+08:00" level=info msg="[TCP] ********:52035 --> 156.238.233.201:60381 using GLOBAL"
time="2025-07-17T21:36:32.223227600+08:00" level=info msg="[TCP] ********:52039 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:32.379937900+08:00" level=info msg="[TCP] ********:52043 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:33.093075300+08:00" level=info msg="[TCP] ********:52046 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:33.501980400+08:00" level=info msg="[TCP] ********:52049 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:33.689646000+08:00" level=info msg="[TCP] 127.0.0.1:52052 --> client-update.queniuqe.com:443 using GLOBAL"
time="2025-07-17T21:36:33.885385900+08:00" level=info msg="[TCP] ********:52055 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:34.387087700+08:00" level=info msg="[TCP] ********:52058 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:34.624663200+08:00" level=info msg="[TCP] ********:52061 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:35.622472900+08:00" level=info msg="[TCP] ********:52066 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:36.616959100+08:00" level=info msg="[TCP] ********:52069 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:37.172437000+08:00" level=info msg="[TCP] ********:52072 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:37.614454300+08:00" level=info msg="[TCP] ********:52075 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:38.629908000+08:00" level=info msg="[TCP] ********:52080 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:39.718468500+08:00" level=info msg="[TCP] ********:52083 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:40.737104800+08:00" level=info msg="[TCP] ********:52087 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:42.102861300+08:00" level=info msg="[TCP] ********:52091 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:42.228908000+08:00" level=info msg="[TCP] ********:52094 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:43.082709700+08:00" level=info msg="[TCP] ********:52097 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:43.165473900+08:00" level=info msg="[TCP] ********:52100 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:44.408431000+08:00" level=info msg="[TCP] ********:52104 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:36:44.874538700+08:00" level=info msg="[TCP] ********:52108 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:45.006375300+08:00" level=info msg="[TCP] ********:52111 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.074631900+08:00" level=info msg="[TCP] ********:52114 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.105651700+08:00" level=info msg="[TCP] ********:52117 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.105651700+08:00" level=info msg="[TCP] ********:52119 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.105651700+08:00" level=info msg="[TCP] ********:52118 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.107235200+08:00" level=info msg="[TCP] ********:52126 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.197821800+08:00" level=info msg="[TCP] ********:52129 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.198852300+08:00" level=info msg="[TCP] ********:52133 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.198852300+08:00" level=info msg="[TCP] ********:52130 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.199364200+08:00" level=info msg="[TCP] ********:52142 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.199364200+08:00" level=info msg="[TCP] ********:52137 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.199364200+08:00" level=info msg="[TCP] ********:52144 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.200388400+08:00" level=info msg="[TCP] ********:52140 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.201412000+08:00" level=info msg="[TCP] ********:52162 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.202948300+08:00" level=info msg="[TCP] ********:52132 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.202948300+08:00" level=info msg="[TCP] ********:52148 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.202948300+08:00" level=info msg="[TCP] ********:52146 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.204484700+08:00" level=info msg="[TCP] ********:52166 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.204484700+08:00" level=info msg="[TCP] ********:52165 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.204484700+08:00" level=info msg="[TCP] ********:52131 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.252359600+08:00" level=info msg="[TCP] ********:52171 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.255362500+08:00" level=info msg="[TCP] ********:52172 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:45.337997300+08:00" level=info msg="[TCP] ********:52177 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.338513200+08:00" level=info msg="[TCP] ********:52178 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.344129100+08:00" level=info msg="[TCP] ********:52183 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.438706500+08:00" level=info msg="[TCP] ********:52186 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.660112700+08:00" level=info msg="[TCP] ********:52189 --> api3.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.702569100+08:00" level=info msg="[TCP] ********:52192 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.706657700+08:00" level=info msg="[TCP] ********:52195 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.845994100+08:00" level=info msg="[TCP] ********:52198 --> api4.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.899049300+08:00" level=info msg="[TCP] ********:52201 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.899568300+08:00" level=info msg="[TCP] ********:52204 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.908616700+08:00" level=info msg="[TCP] ********:52207 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.951610000+08:00" level=info msg="[TCP] ********:52210 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.967111500+08:00" level=info msg="[TCP] ********:52213 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:45.991257900+08:00" level=info msg="[TCP] ********:52216 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.015085700+08:00" level=info msg="[TCP] ********:52219 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.020924400+08:00" level=info msg="[TCP] ********:52222 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.026054700+08:00" level=info msg="[TCP] ********:52225 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.103363100+08:00" level=info msg="[TCP] ********:52228 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.108997600+08:00" level=info msg="[TCP] ********:52231 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.125760900+08:00" level=info msg="[TCP] ********:52234 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.240057400+08:00" level=info msg="[TCP] ********:52238 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.242105000+08:00" level=info msg="[TCP] ********:52237 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.250822300+08:00" level=info msg="[TCP] ********:52243 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.288852400+08:00" level=info msg="[TCP] ********:52246 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.332577500+08:00" level=info msg="[TCP] ********:52249 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.376985300+08:00" level=info msg="[TCP] ********:52252 --> api4.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:46.752335100+08:00" level=info msg="[TCP] ********:52255 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:47.003839400+08:00" level=info msg="[TCP] ********:52260 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:47.004861300+08:00" level=info msg="[TCP] ********:52261 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:47.005887000+08:00" level=info msg="[TCP] ********:52259 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:47.033515000+08:00" level=info msg="[TCP] ********:52268 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:47.116559600+08:00" level=info msg="[TCP] ********:52271 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:47.648482300+08:00" level=info msg="[TCP] ********:52275 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:47.707691500+08:00" level=info msg="[TCP] ********:52278 --> us-only.gcpp.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:48.174122100+08:00" level=info msg="[TCP] ********:52281 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:48.690692500+08:00" level=info msg="[TCP] ********:52284 --> us-only.gcpp.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:36:49.355223700+08:00" level=info msg="[TCP] ********:52287 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:50.996540500+08:00" level=info msg="[TCP] ********:52292 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:52.232096800+08:00" level=info msg="[TCP] ********:52295 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:53.175399700+08:00" level=info msg="[TCP] ********:52299 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:36:53.808073600+08:00" level=info msg="[TCP] ********:52303 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:36:54.998984000+08:00" level=info msg="[TCP] ********:52306 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:56.871807900+08:00" level=info msg="[TCP] ********:52311 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:36:57.833042200+08:00" level=info msg="[TCP] ********:52316 --> gx-target-experiments-frontend-api.gx.nvidia.cn:443 using GLOBAL"
time="2025-07-17T21:37:02.242226500+08:00" level=info msg="[TCP] ********:52322 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:02.819701500+08:00" level=info msg="[TCP] ********:52326 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:03.152445300+08:00" level=info msg="[TCP] ********:52329 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:03.658204600+08:00" level=info msg="[TCP] ********:52332 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:07.089068900+08:00" level=info msg="[TCP] ********:52337 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:37:07.247906400+08:00" level=info msg="[TCP] ********:52340 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:08.175268100+08:00" level=info msg="[TCP] ********:52344 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:09.621874400+08:00" level=info msg="[TCP] ********:52348 --> pti.store.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:37:09.978193300+08:00" level=info msg="[TCP] ********:52351 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:10.786517000+08:00" level=info msg="[TCP] ********:52354 --> activity.windows.com:443 using GLOBAL"
time="2025-07-17T21:37:10.880551800+08:00" level=info msg="[TCP] ********:52357 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:11.201196500+08:00" level=info msg="[TCP] ********:52361 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:12.199791700+08:00" level=info msg="[TCP] ********:52365 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:12.249182000+08:00" level=info msg="[TCP] ********:52368 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:13.581610300+08:00" level=info msg="[TCP] ********:52371 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:14.084110500+08:00" level=info msg="[TCP] ********:52374 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:14.619209100+08:00" level=info msg="[TCP] ********:52378 --> adl.windows.com:80 using GLOBAL"
time="2025-07-17T21:37:22.254976000+08:00" level=info msg="[TCP] ********:52386 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:23.133253900+08:00" level=info msg="[TCP] ********:52389 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:23.645040100+08:00" level=info msg="[TCP] ********:52393 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:29.638551900+08:00" level=info msg="[TCP] ********:52400 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:37:29.763133700+08:00" level=info msg="[TCP] ********:52403 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-17T21:37:30.483577500+08:00" level=info msg="[TCP] ********:52408 --> fd.api.iris.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:37:30.483577500+08:00" level=info msg="[TCP] ********:52407 --> arc.msn.com:443 using GLOBAL"
time="2025-07-17T21:37:30.982780000+08:00" level=info msg="[TCP] ********:52413 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:32.274560000+08:00" level=info msg="[TCP] ********:52416 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:33.216027600+08:00" level=info msg="[TCP] ********:52421 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:33.722242900+08:00" level=info msg="[TCP] ********:52424 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:38.078204300+08:00" level=info msg="[TCP] ********:52429 --> client.wns.windows.com:443 using GLOBAL"
time="2025-07-17T21:37:42.282203200+08:00" level=info msg="[TCP] ********:52437 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:43.136159600+08:00" level=info msg="[TCP] ********:52440 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:37:43.194399500+08:00" level=info msg="[TCP] ********:52443 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:43.707622500+08:00" level=info msg="[TCP] ********:52446 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:45.242423400+08:00" level=info msg="[TCP] ********:52451 --> client.wns.windows.com:443 using GLOBAL"
time="2025-07-17T21:37:46.981291400+08:00" level=info msg="[TCP] ********:52454 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:47.190733100+08:00" level=info msg="[TCP] ********:52457 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:48.164981900+08:00" level=info msg="[TCP] ********:52461 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:37:52.292919000+08:00" level=info msg="[TCP] ********:52467 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:53.225769500+08:00" level=info msg="[TCP] ********:52470 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:53.729571200+08:00" level=info msg="[TCP] ********:52474 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:37:54.872932100+08:00" level=info msg="[TCP] ********:52478 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:37:57.998174700+08:00" level=info msg="[TCP] ********:52483 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:02.299163500+08:00" level=info msg="[TCP] ********:52488 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:02.640306300+08:00" level=info msg="[TCP] ********:52491 --> v20.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:03.715723200+08:00" level=info msg="[TCP] ********:52496 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:03.809019800+08:00" level=info msg="[TCP] ********:52499 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:04.220482800+08:00" level=info msg="[TCP] ********:52502 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:05.235676200+08:00" level=info msg="[TCP] ********:52505 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:08.115514400+08:00" level=info msg="[TCP] ********:52510 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:10.034886900+08:00" level=info msg="[TCP] ********:52515 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:38:10.561825300+08:00" level=info msg="[TCP] ********:52518 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:11.004134800+08:00" level=info msg="[TCP] ********:52525 --> updates.ghub.logitechg.com:443 using GLOBAL"
time="2025-07-17T21:38:11.004134800+08:00" level=info msg="[TCP] ********:52526 --> updates.ghub.logitechg.com:443 using GLOBAL"
time="2025-07-17T21:38:11.095088400+08:00" level=info msg="[TCP] ********:52532 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:11.114189000+08:00" level=info msg="[TCP] ********:52535 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:11.121796800+08:00" level=info msg="[TCP] ********:52538 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:11.615849500+08:00" level=info msg="[TCP] ********:52541 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:12.040771300+08:00" level=info msg="[TCP] ********:52545 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:12.320258000+08:00" level=info msg="[TCP] ********:52548 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:12.445123300+08:00" level=info msg="[TCP] ********:52552 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:12.491441600+08:00" level=info msg="[TCP] ********:52555 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:12.494001700+08:00" level=info msg="[TCP] ********:52556 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:12.494001700+08:00" level=info msg="[TCP] ********:52557 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:12.654907900+08:00" level=info msg="[TCP] ********:52564 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:13.023662600+08:00" level=info msg="[TCP] ********:52567 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:13.090885400+08:00" level=info msg="[TCP] ********:52570 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:13.167298000+08:00" level=info msg="[TCP] ********:52573 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:13.455795700+08:00" level=info msg="[TCP] ********:52576 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:13.471506500+08:00" level=info msg="[TCP] ********:52579 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:13.761056400+08:00" level=info msg="[TCP] ********:52582 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:13.855740000+08:00" level=info msg="[TCP] ********:52585 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:14.886286900+08:00" level=info msg="[TCP] ********:52588 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:15.908308300+08:00" level=info msg="[TCP] ********:52593 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:16.461582300+08:00" level=info msg="[TCP] ********:52596 --> updates.ghub.logitechg.com:443 using GLOBAL"
time="2025-07-17T21:38:16.926486400+08:00" level=info msg="[TCP] ********:52599 --> dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-17T21:38:17.451993600+08:00" level=info msg="[TCP] ********:52602 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:18.466271700+08:00" level=info msg="[TCP] ********:52606 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:18.751546800+08:00" level=info msg="[TCP] ********:52610 --> events.gx.nvidia.cn:443 using GLOBAL"
time="2025-07-17T21:38:19.498329700+08:00" level=info msg="[TCP] ********:52613 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:20.578094800+08:00" level=info msg="[TCP] ********:52616 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:21.662306500+08:00" level=info msg="[TCP] ********:52621 --> dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-17T21:38:22.184013600+08:00" level=info msg="[TCP] ********:52624 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:22.326474700+08:00" level=info msg="[TCP] ********:52627 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:23.139000200+08:00" level=info msg="[TCP] ********:52630 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:23.221977600+08:00" level=info msg="[TCP] ********:52633 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:23.654478800+08:00" level=info msg="[TCP] ********:52636 --> prod.otel.kaizen.nvidia.com:443 using GLOBAL"
time="2025-07-17T21:38:23.785820700+08:00" level=info msg="[TCP] ********:52639 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:24.282384900+08:00" level=info msg="[TCP] ********:52643 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:25.295072400+08:00" level=info msg="[TCP] ********:52647 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:26.373868800+08:00" level=info msg="[TCP] ********:52650 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:28.829722500+08:00" level=info msg="[TCP] ********:52655 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:29.149690400+08:00" level=info msg="[TCP] ********:52658 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:30.204881500+08:00" level=info msg="[TCP] ********:52662 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:31.166448500+08:00" level=info msg="[TCP] ********:52666 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:31.184969200+08:00" level=info msg="[TCP] ********:52669 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:32.274584100+08:00" level=info msg="[TCP] ********:52672 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:32.341090200+08:00" level=info msg="[TCP] ********:52675 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:33.426798300+08:00" level=info msg="[TCP] ********:52679 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:33.432877400+08:00" level=info msg="[TCP] ********:52682 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:33.937120400+08:00" level=info msg="[TCP] ********:52686 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:34.536426200+08:00" level=info msg="[TCP] ********:52689 --> dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-17T21:38:35.059751600+08:00" level=info msg="[TCP] ********:52692 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:42.338485200+08:00" level=info msg="[TCP] ********:52699 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:42.632384600+08:00" level=info msg="[TCP] ********:52703 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:43.170846600+08:00" level=info msg="[TCP] ********:52707 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:43.663523400+08:00" level=info msg="[TCP] ********:52710 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:44.716933700+08:00" level=info msg="[TCP] ********:52713 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:45.679485700+08:00" level=info msg="[TCP] ********:52717 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:46.175709900+08:00" level=info msg="[TCP] ********:52721 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:46.303626300+08:00" level=info msg="[TCP] ********:52724 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:46.770826200+08:00" level=info msg="[TCP] ********:52727 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:47.792622000+08:00" level=info msg="[TCP] ********:52730 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:48.177205900+08:00" level=info msg="[TCP] ********:52733 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:38:48.857546500+08:00" level=info msg="[TCP] ********:52737 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:49.922684200+08:00" level=info msg="[TCP] ********:52741 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:52.350972000+08:00" level=info msg="[TCP] ********:52746 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:53.275231300+08:00" level=info msg="[TCP] ********:52749 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:53.442406000+08:00" level=info msg="[TCP] ********:52752 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:53.953680700+08:00" level=info msg="[TCP] ********:52755 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:38:54.351305600+08:00" level=info msg="[TCP] ********:52758 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:55.443779400+08:00" level=info msg="[TCP] ********:52763 --> dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-17T21:38:55.964091100+08:00" level=info msg="[TCP] ********:52766 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:57.001761700+08:00" level=info msg="[TCP] ********:52769 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:58.027224700+08:00" level=info msg="[TCP] ********:52773 --> dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-17T21:38:58.516054100+08:00" level=info msg="[TCP] ********:52777 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:38:59.504726600+08:00" level=info msg="[TCP] ********:52780 --> dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-17T21:39:02.351493900+08:00" level=info msg="[TCP] ********:52785 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:03.127197800+08:00" level=info msg="[TCP] ********:52788 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:03.637704000+08:00" level=info msg="[TCP] ********:52791 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:08.678248000+08:00" level=info msg="[TCP] ********:52800 --> gx-target-experiments-frontend-api.gx.nvidia.cn:443 using GLOBAL"
time="2025-07-17T21:39:12.402676000+08:00" level=info msg="[TCP] ********:52805 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:13.232046800+08:00" level=info msg="[TCP] ********:52809 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:13.745041600+08:00" level=info msg="[TCP] ********:52813 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:14.228125300+08:00" level=info msg="[TCP] ********:52816 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:14.444870600+08:00" level=info msg="[TCP] ********:52819 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:16.826157300+08:00" level=info msg="[TCP] ********:52824 --> psuite.vivo.com.cn:443 using GLOBAL"
time="2025-07-17T21:39:17.065053600+08:00" level=info msg="[TCP] ********:52827 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:17.888891600+08:00" level=info msg="[TCP] ********:52830 --> dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-17T21:39:18.407538500+08:00" level=info msg="[TCP] ********:52833 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:19.235357200+08:00" level=info msg="[TCP] ********:52837 --> activity.windows.com:443 using GLOBAL"
time="2025-07-17T21:39:21.547803900+08:00" level=info msg="[TCP] ********:52841 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:22.357894700+08:00" level=info msg="[TCP] ********:52845 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:22.522480100+08:00" level=info msg="[TCP] ********:52848 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:23.180155500+08:00" level=info msg="[TCP] ********:52852 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:23.560809900+08:00" level=info msg="[TCP] ********:52855 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:23.694546500+08:00" level=info msg="[TCP] ********:52858 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:24.599372900+08:00" level=info msg="[TCP] ********:52861 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:25.572224800+08:00" level=info msg="[TCP] ********:52865 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:26.325118100+08:00" level=info msg="[TCP] ********:52869 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:26.649686100+08:00" level=info msg="[TCP] ********:52872 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:27.219657100+08:00" level=info msg="[TCP] ********:52875 --> api4.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:27.607704400+08:00" level=info msg="[TCP] ********:52878 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:27.920033800+08:00" level=info msg="[TCP] ********:52881 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-17T21:39:28.577379100+08:00" level=info msg="[TCP] ********:52886 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:39:28.577379100+08:00" level=info msg="[TCP] ********:52885 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:39:28.577889400+08:00" level=info msg="[TCP] ********:52894 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:28.577889400+08:00" level=info msg="[TCP] ********:52887 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:39:28.580953100+08:00" level=info msg="[TCP] ********:52895 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:28.694968500+08:00" level=info msg="[TCP] ********:52901 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.817493800+08:00" level=info msg="[TCP] ********:52904 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.817493800+08:00" level=info msg="[TCP] ********:52907 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.818010500+08:00" level=info msg="[TCP] ********:52905 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.818947000+08:00" level=info msg="[TCP] ********:52906 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.819459300+08:00" level=info msg="[TCP] ********:52913 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.820480700+08:00" level=info msg="[TCP] ********:52908 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.820480700+08:00" level=info msg="[TCP] ********:52918 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.821528000+08:00" level=info msg="[TCP] ********:52931 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.822039000+08:00" level=info msg="[TCP] ********:52916 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.823062600+08:00" level=info msg="[TCP] ********:52932 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.824588300+08:00" level=info msg="[TCP] ********:52933 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.825617100+08:00" level=info msg="[TCP] ********:52917 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.826639700+08:00" level=info msg="[TCP] ********:52940 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.833235500+08:00" level=info msg="[TCP] ********:52943 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.889071100+08:00" level=info msg="[TCP] ********:52946 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.889582500+08:00" level=info msg="[TCP] ********:52947 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.904332100+08:00" level=info msg="[TCP] ********:52952 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.904844700+08:00" level=info msg="[TCP] ********:52953 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:28.951472800+08:00" level=info msg="[TCP] ********:52958 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:29.308090400+08:00" level=info msg="[TCP] ********:52961 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:29.542500500+08:00" level=info msg="[TCP] ********:52964 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:39:29.543047900+08:00" level=info msg="[TCP] ********:52967 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:29.554633000+08:00" level=info msg="[TCP] ********:52970 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:29.558603900+08:00" level=info msg="[TCP] ********:52973 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:29.565229500+08:00" level=info msg="[TCP] ********:52976 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:29.587228800+08:00" level=info msg="[TCP] ********:52979 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:29.604788700+08:00" level=info msg="[TCP] ********:52982 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:30.894579300+08:00" level=info msg="[TCP] ********:52989 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.894579300+08:00" level=info msg="[TCP] ********:52988 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.894579300+08:00" level=info msg="[TCP] ********:52985 --> repo42.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.894579300+08:00" level=info msg="[TCP] ********:52990 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.894579300+08:00" level=info msg="[TCP] ********:52991 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.895580000+08:00" level=info msg="[TCP] ********:52987 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:30.895580000+08:00" level=info msg="[TCP] ********:52986 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:30.895580000+08:00" level=info msg="[TCP] ********:52992 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.895580000+08:00" level=info msg="[TCP] ********:52993 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.975269000+08:00" level=info msg="[TCP] ********:53018 --> api3.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.975269000+08:00" level=info msg="[TCP] ********:53009 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.975269000+08:00" level=info msg="[TCP] ********:53012 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.975269000+08:00" level=info msg="[TCP] ********:53007 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.975269000+08:00" level=info msg="[TCP] ********:53010 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.976269700+08:00" level=info msg="[TCP] ********:53019 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.976269700+08:00" level=info msg="[TCP] ********:53020 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.976269700+08:00" level=info msg="[TCP] ********:53021 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.976269700+08:00" level=info msg="[TCP] ********:53022 --> api4.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.976269700+08:00" level=info msg="[TCP] ********:53015 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:30.977270700+08:00" level=info msg="[TCP] ********:53014 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:31.110956800+08:00" level=info msg="[TCP] ********:53045 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:31.170262700+08:00" level=info msg="[TCP] ********:53049 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:39:31.212241600+08:00" level=info msg="[TCP] ********:53051 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:39:31.213242600+08:00" level=info msg="[TCP] ********:53050 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:39:31.263131600+08:00" level=info msg="[TCP] ********:53054 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-17T21:39:31.325585300+08:00" level=info msg="[TCP] ********:53059 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:31.346906100+08:00" level=info msg="[TCP] ********:53062 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:32.028432600+08:00" level=info msg="[TCP] ********:53070 --> 117.144.241.215:443 using GLOBAL"
time="2025-07-17T21:39:32.028432600+08:00" level=info msg="[TCP] ********:53069 --> 117.144.241.215:443 using GLOBAL"
time="2025-07-17T21:39:32.028432600+08:00" level=info msg="[TCP] ********:53068 --> 117.144.241.215:443 using GLOBAL"
time="2025-07-17T21:39:32.034428100+08:00" level=info msg="[TCP] ********:53071 --> 117.144.241.215:443 using GLOBAL"
time="2025-07-17T21:39:32.373106300+08:00" level=info msg="[TCP] ********:53080 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:32.545324100+08:00" level=info msg="[TCP] ********:53084 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:32.545961000+08:00" level=info msg="[TCP] ********:53083 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:32.548467400+08:00" level=info msg="[TCP] ********:53085 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:39:32.549192200+08:00" level=info msg="[TCP] ********:53087 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:32.550697400+08:00" level=info msg="[TCP] ********:53086 --> dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-17T21:39:32.571307000+08:00" level=info msg="[TCP] ********:53099 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:32.572323400+08:00" level=info msg="[TCP] ********:53098 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:32.574843300+08:00" level=info msg="[TCP] ********:53100 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:32.777906700+08:00" level=info msg="[TCP] ********:53107 --> api4.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:33.073474800+08:00" level=info msg="[TCP] ********:53110 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:33.181177700+08:00" level=info msg="[TCP] ********:53113 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:33.533129300+08:00" level=info msg="[TCP] ********:53119 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:39:33.534295400+08:00" level=info msg="[TCP] ********:53116 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:33.690184400+08:00" level=info msg="[TCP] ********:53122 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:33.817472600+08:00" level=info msg="[TCP] ********:53127 --> 156.238.233.201:60381 using GLOBAL"
time="2025-07-17T21:39:33.909930100+08:00" level=info msg="[TCP] ********:53130 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:34.217385500+08:00" level=info msg="[TCP] ********:53133 --> us-only.gcpp.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:34.228565300+08:00" level=info msg="[TCP] ********:53136 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:39:34.243827900+08:00" level=info msg="[TCP] ********:53139 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:34.687070200+08:00" level=info msg="[TCP] ********:53143 --> 221.181.98.242:443 using GLOBAL"
time="2025-07-17T21:39:35.065396200+08:00" level=info msg="[TCP] ********:53147 --> us-only.gcpp.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:35.522173100+08:00" level=info msg="[TCP] ********:53150 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:35.522721300+08:00" level=info msg="[TCP] ********:53153 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:39:35.766829900+08:00" level=info msg="[TCP] ********:53156 --> api3.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:36.202248000+08:00" level=info msg="[TCP] ********:53159 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:39:39.397258800+08:00" level=info msg="[TCP] ********:53164 --> fp.msedge.net:443 using GLOBAL"
time="2025-07-17T21:39:39.722311000+08:00" level=info msg="[TCP] ********:53168 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:39.722311000+08:00" level=info msg="[TCP] ********:53167 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:39.724494200+08:00" level=info msg="[TCP] ********:53170 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:39.725566500+08:00" level=info msg="[TCP] ********:53169 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:39.863225200+08:00" level=info msg="[TCP] ********:53179 --> 183.194.198.84:443 using GLOBAL"
time="2025-07-17T21:39:40.678237900+08:00" level=info msg="[TCP] ********:53183 --> api.segment.io:443 using GLOBAL"
time="2025-07-17T21:39:40.695065400+08:00" level=info msg="[TCP] ********:53186 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:40.767845800+08:00" level=info msg="[TCP] ********:53189 --> api3.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:40.894145000+08:00" level=info msg="[TCP] ********:53192 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-07-17T21:39:40.988297900+08:00" level=info msg="[TCP] ********:53196 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:39:41.533766200+08:00" level=info msg="[TCP] ********:53199 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:41.694006600+08:00" level=info msg="[TCP] ********:53202 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:41.959435800+08:00" level=info msg="[TCP] ********:53205 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:42.037201600+08:00" level=info msg="[TCP] ********:53208 --> 183.194.198.84:443 using GLOBAL"
time="2025-07-17T21:39:42.227583100+08:00" level=info msg="[TCP] ********:53211 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:42.370585100+08:00" level=info msg="[TCP] ********:53214 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:42.645308400+08:00" level=info msg="[TCP] ********:53217 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:43.153461900+08:00" level=info msg="[TCP] ********:53220 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:39:43.628292700+08:00" level=info msg="[TCP] ********:53224 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:39:43.705735800+08:00" level=info msg="[TCP] ********:53227 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:43.823541800+08:00" level=info msg="[TCP] ********:53230 --> fp-afd-nocache-ccp.azureedge.net:443 using GLOBAL"
time="2025-07-17T21:39:44.005788700+08:00" level=info msg="[TCP] ********:53234 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:44.256492000+08:00" level=info msg="[TCP] ********:53237 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:44.308764300+08:00" level=info msg="[TCP] ********:53240 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:44.731750300+08:00" level=info msg="[TCP] ********:53243 --> cp501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:39:44.735334900+08:00" level=info msg="[TCP] ********:53246 --> shc.y.qq.com:443 using GLOBAL"
time="2025-07-17T21:39:44.769060100+08:00" level=info msg="[TCP] ********:53249 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:39:44.798520400+08:00" level=info msg="[TCP] ********:53252 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:39:44.899506900+08:00" level=info msg="[TCP] ********:53255 --> ln-ring.msedge.net:443 using GLOBAL"
time="2025-07-17T21:39:44.965484700+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-17T21:39:44.965994700+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-17T21:39:44.965994700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-17T21:39:44.966503100+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-17T21:39:44.966503100+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-17T21:39:44.966503100+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-17T21:39:44.968050500+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-17T21:39:44.969585500+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Discord"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider apple"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider TVer"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider BBC"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Disney"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider TVB"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider google"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Lan"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider telegram"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider NowE"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Line"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-17T21:39:45.072410300+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-17T21:39:45.265772200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.265772200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.266286300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.266799900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.266799900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.266799900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.267721600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.267721600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.268434500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.270145200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.270145200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.270145200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.270656800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.270656800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.273033300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.273033300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.273033300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.273033300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.273545400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.273545400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.273545400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.273545400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.273545400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.274062100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.274062100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.274062100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.274062100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.274062100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.280318800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.280318800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.280855700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.280855700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.280855700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.281372300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.281372300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.281893800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.281893800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.281893800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.281893800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.281893800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.282405000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.282405000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.282405000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.282915900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.282915900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.282915900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.293151200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.293151200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:45.293658900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:39:46.160773800+08:00" level=warning msg="Hop loop fetch serverAddress error: '%!s(MISSING)', ignoredlookup invalid IP: no such host"
time="2025-07-17T21:39:46.331580100+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-17T21:39:46.335611100+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-17T21:39:46.337943800+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-17T21:39:46.354820800+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-17T21:39:46.363903600+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-17T21:39:46.364088400+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-17T21:39:46.374459600+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-17T21:39:46.385383900+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-17T21:39:46.392027300+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-17T21:39:46.393580000+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-17T21:39:46.399972500+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-17T21:39:46.405204200+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-17T21:39:46.405204200+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-17T21:39:46.406026500+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-17T21:39:46.406921500+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-17T21:39:46.407677100+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-17T21:39:46.411093000+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-17T21:39:46.413279100+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-17T21:39:46.417033600+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-17T21:39:46.419481900+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-17T21:39:46.421029600+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-07-17T21:39:46.425032500+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-17T21:39:46.439373300+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-17T21:39:46.442598900+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-17T21:39:46.443620200+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-17T21:39:46.455000900+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-17T21:39:46.456604300+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-17T21:39:46.476286100+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-17T21:39:46.478564100+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-17T21:39:46.478564100+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-17T21:39:46.487192900+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-17T21:39:46.516897500+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-17T21:39:46.520583400+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-17T21:39:46.521348100+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-17T21:39:46.593608900+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-17T21:39:46.598374100+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-17T21:39:46.599045300+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-17T21:39:46.599045300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-17T21:39:46.600512600+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-17T21:39:46.601247700+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-17T21:39:46.601247700+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-17T21:39:46.602263000+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-17T21:39:46.602783400+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-17T21:39:46.602783400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-17T21:39:46.603298700+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-17T21:39:46.603818300+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-17T21:39:46.603823500+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-17T21:39:46.604335800+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-17T21:39:51.605113300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:39:51.605113300+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/users/sign_in\": EOF"
time="2025-07-17T21:39:51.606936100+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-17T21:39:51.606936100+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-17T21:39:51.606936100+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-17T21:39:51.606936100+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-17T21:39:59.523774000+08:00" level=error msg="🇻🇳越南01 [1.5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-17T21:39:59.523774000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-17T21:40:02.924826600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53576 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:08.107312900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53616 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:08.107312900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53618 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:08.107312900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53617 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:08.107312900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53749 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:08.107924800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53620 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:09.168377700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53750 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:09.168426600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53751 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:09.168426600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53748 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:10.230209500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53781 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:10.230281100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53782 --> cmp2-sgp1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:10.230281100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53786 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:10.233215000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53783 --> ext1-maa2.steamserver.net:27025 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:11.030636300+08:00" level=info msg="[TCP] 127.0.0.1:53804 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T21:40:15.386393200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53798 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:26.413326800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53970 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:31.546762200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53986 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:31.546762200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53985 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:31.546762200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53984 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:31.546762200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53983 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:31.546762200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53994 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:32.551067500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53995 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:32.551143400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53993 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:32.551143400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53996 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:33.552008400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53999 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:33.552008400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54000 --> ext1-maa2.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:33.552008400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53998 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:33.552123100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54003 --> cmp2-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:40:34.306904300+08:00" level=info msg="[TCP] 127.0.0.1:54020 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T21:40:38.734473200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54019 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:17.812199400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54098 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:22.960468500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54111 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:22.960468500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54112 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:22.960468500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54110 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:22.960468500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54113 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:22.960589500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54129 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:22.960589500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54121 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:23.726229500+08:00" level=info msg="[TCP] 127.0.0.1:54138 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T21:41:23.967225900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54123 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:23.967314000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54122 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:23.967314000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54120 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:24.972743100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54124 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:24.972851400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54125 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:24.972851400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54127 --> cmp2-sgp1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:24.976358700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54126 --> ext2-bom2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:41:28.177642300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54137 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:16.201848500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54201 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:21.338245300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54212 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:21.338245300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54213 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:21.338245300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54214 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:21.338245300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54232 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:21.338245300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54215 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:21.338245300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54221 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:22.127815700+08:00" level=info msg="[TCP] 127.0.0.1:54248 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T21:42:22.349246400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54220 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:22.349246400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54222 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:22.349334700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54223 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:23.353201100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54226 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:23.353201100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54227 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:23.353201100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54228 --> cmp1-tyo3.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:23.357945600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54229 --> cmp1-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:42:26.567434000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54242 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:25.547933000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54318 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:30.697654900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54328 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:30.697744600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54334 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:30.697744600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54349 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:30.700252200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54333 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:30.700252200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54335 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:30.700252200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54337 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:31.476805400+08:00" level=info msg="[TCP] 127.0.0.1:54364 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T21:43:31.711204800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54338 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:31.711204800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54336 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:31.711204800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54339 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:32.723087100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54342 --> cmp1-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:32.723087100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54340 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:32.723087100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54341 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:32.726619500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54343 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:43:35.876362200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54362 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:15.886115900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54850 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:20.908027600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54861 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:26.068069100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54872 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:26.068069100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54871 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:26.068069100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54870 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:26.068069100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54879 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:26.071687600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54877 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:27.081400100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54882 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:27.081485100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54881 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:27.081485100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54880 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:28.091678100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54883 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:28.091678100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54884 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:28.091792300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54887 --> cmp2-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:28.092810100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54885 --> cmp3-hkg1.steamserver.net:27023 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:50:28.889403400+08:00" level=info msg="[TCP] 127.0.0.1:54908 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T21:50:33.299706900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54903 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:03.286470700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55249 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:08.468683500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55259 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:08.468683500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55258 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:08.468683500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55260 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:08.468683500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55261 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:08.468683500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55276 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:08.468683500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55266 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:09.212667400+08:00" level=info msg="[TCP] 127.0.0.1:55285 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T21:57:09.469111700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55268 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:09.469111700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55267 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:09.469764400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55269 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:10.470970400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55274 --> ext1-maa2.steamserver.net:27030 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:10.471071900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55273 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:10.471071900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55275 --> cmp1-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:10.471071900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55272 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:57:13.668602800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55284 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:00:49.676874100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55444 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:00:54.654959400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55452 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:00:59.809175600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55461 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:00:59.809175600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55460 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:00:59.809175600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55463 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:00:59.809175600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55462 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:00:59.809175600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55469 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:00:59.809175600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55478 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:01:00.603321500+08:00" level=info msg="[TCP] 127.0.0.1:55487 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:01:00.817902600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55471 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:01:00.817902600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55470 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:01:00.817902600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55472 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:01:01.829224100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55477 --> ext1-maa2.steamserver.net:27025 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:01:01.829224100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55476 --> cmp1-tyo3.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:01:01.829224100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55475 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:01:01.829224100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55474 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:47.012133400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56152 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:52.142231900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56162 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:52.142357500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56161 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:52.142357500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56180 --> cmp2-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:52.145864200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56168 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:52.145864200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56167 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:52.145935000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56169 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:52.933240100+08:00" level=info msg="[TCP] 127.0.0.1:56197 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:14:53.199872300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56170 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:53.199872300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56171 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:53.199999600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56172 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:54.254516500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56175 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:54.254516500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56176 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:54.257572900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56177 --> ext1-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:54.257572900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56178 --> ext1-maa2.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:14:57.351669700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56195 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:24:59.313948400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56638 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:04.346168800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56646 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:09.499072100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56654 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:09.499072100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56655 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:09.499072100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56665 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:09.499072100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56671 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:09.502101400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56660 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:09.502101400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56661 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:09.502101400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56669 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:09.502101400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56663 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:10.360899100+08:00" level=info msg="[TCP] 127.0.0.1:56686 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:25:10.547517800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56664 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:10.547517800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56666 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:11.594501400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56668 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:25:11.598432100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56670 --> cmp1-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:34.688049600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57186 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:39.857811200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57197 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:39.857811200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57196 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:39.857811200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57195 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:39.857932100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57198 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:39.857932100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57208 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:39.857932100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57207 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:39.858446400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57205 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:40.902071800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57206 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:41.957453000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57210 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:41.957453000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57209 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:41.960593700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57211 --> cmp2-lax1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:43.072329800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57212 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:35:43.885808100+08:00" level=info msg="[TCP] 127.0.0.1:57228 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:35:48.263522100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57223 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:04.284322100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57727 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:09.244074700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57736 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:14.384306500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57743 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:14.384306500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57761 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:14.384306500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57754 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:14.387812700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57748 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:14.387904900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57755 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:14.387904900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57749 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:14.387904900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57752 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:14.388944700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57750 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:15.123765500+08:00" level=info msg="[TCP] 127.0.0.1:57781 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:44:15.394758100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57753 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:16.402433800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57758 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:16.402433800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57759 --> ext1-maa2.steamserver.net:27025 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:44:16.402433800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57757 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:19.230069200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57959 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:24.397970200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57968 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:24.397970200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57969 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:24.397970200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57967 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:24.397970200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57970 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:24.398515600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57982 --> cmp2-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:24.398515600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57985 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:24.398515600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57979 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:24.398515600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57976 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:24.398515600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57978 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:25.219150300+08:00" level=info msg="[TCP] 127.0.0.1:57993 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:48:25.400145500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57977 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:26.455593900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57980 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:26.455674900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57981 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:26.455674900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57983 --> ext1-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:29.597327500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57992 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:33.652873000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57999 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:34.399349900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58014 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:38.824419800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58015 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:38.824419800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58012 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:38.824419800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58013 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:38.824419800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58028 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:38.824419800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58029 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:38.824419800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58034 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:39.399450000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58025 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:39.636136100+08:00" level=info msg="[TCP] 127.0.0.1:58045 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:48:39.880383500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58027 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:40.689233200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58030 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:40.942339700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58033 --> cmp3-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:40.942339700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58031 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:40.942339700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58032 --> cmp3-hkg1.steamserver.net:27021 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:48.079965800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58050 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:48.824840200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58068 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:49.399951500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58079 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:49.399951500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58069 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:53.233716200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58070 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:53.233716200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58067 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:53.233807100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58093 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:53.825318800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58082 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:54.085965500+08:00" level=info msg="[TCP] 127.0.0.1:58105 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:48:54.284630000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58081 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:54.284630000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58080 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:54.594789700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58087 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:55.342215800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58090 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:55.345786200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58091 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:55.345842200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58092 --> ext1-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:48:58.438396000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58100 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:36.460174800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58142 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:41.590613300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58154 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:41.590613300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58151 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:41.590613300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58152 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:41.590613300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58153 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:41.591757400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58162 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:41.591757400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58159 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:42.648480300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58160 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:42.648480300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58161 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:43.703704800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58164 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:43.703704800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58168 --> cmp2-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:43.707223700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58166 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:43.707292300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58165 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:49:44.514976000+08:00" level=info msg="[TCP] 127.0.0.1:58186 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:49:48.928369900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58181 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:22.930478000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58225 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:28.106944700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58237 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:28.106944700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58234 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:28.106944700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58235 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:28.107091900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58236 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:28.107091900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58246 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:28.107091900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58255 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:28.107091900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58243 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:28.966803600+08:00" level=info msg="[TCP] 127.0.0.1:58270 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:50:29.164063800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58244 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:29.164138900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58245 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:30.223414700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58251 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:30.223506300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58250 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:30.228015800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58253 --> cmp1-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:30.228109100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58252 --> cmp1-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:50:33.333095700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58269 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:23.327483500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58310 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:28.461035400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58322 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:28.461146600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58321 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:28.461146600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58319 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:28.461146600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58320 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:28.461146600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58328 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:28.461146600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58329 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:28.461146600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58327 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:28.461146600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58337 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:28.461146600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58330 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:29.320000400+08:00" level=info msg="[TCP] 127.0.0.1:58346 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:51:30.524645000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58333 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:30.524736000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58335 --> ext1-maa2.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:30.524736000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58334 --> cmp2-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:30.524736000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58332 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:51:33.688964900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58345 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:54:55.688240900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58501 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:00.829349500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58515 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:00.829349500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58514 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:00.829349500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58512 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:00.829349500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58513 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:00.829349500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58520 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:00.829349500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58523 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:00.829349500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58522 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:00.829349500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58521 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:02.837163200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58526 --> cmp1-hkg1.steamserver.net:27021 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:02.837163200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58525 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:02.837163200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58524 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:03.947488800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58529 --> cmp3-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T22:55:04.774813900+08:00" level=info msg="[TCP] 127.0.0.1:58548 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T22:55:09.147707200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58542 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:01:56.143017400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58841 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:01.167152300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58850 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:06.326892200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58859 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:06.326998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58861 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:06.326998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58858 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:06.326998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58860 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:06.326998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58870 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:06.326998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58869 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:06.326998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58876 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:06.326998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58868 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:06.326998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58867 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:06.326998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58874 --> ext2-maa2.steamserver.net:27036 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:07.188426000+08:00" level=info msg="[TCP] 127.0.0.1:58885 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T23:02:08.451206300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58871 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:08.451206300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58872 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:02:08.452324200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58873 --> ext2-bom2.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:04:56.585170100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59021 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:01.718387700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59031 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:01.718387700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59032 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:01.718387700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59033 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:01.718387700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59030 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:01.718387700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59042 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:01.718387700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59040 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:01.718387700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59039 --> ext2-maa2.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:01.718387700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59048 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:01.718387700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59041 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:01.718387700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59046 --> ext2-syd1.steamserver.net:27038 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:02.525613100+08:00" level=info msg="[TCP] 127.0.0.1:59059 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T23:05:03.773980100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59044 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:03.773980100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59045 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:03.777596300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59047 --> cmp1-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:05:06.952129400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59058 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:09.922121900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59370 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:14.965109200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59377 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:20.132221200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59389 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:20.132221200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59387 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:20.132347200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59388 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:20.132347200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59390 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:20.132347200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59399 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:20.132347200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59405 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:20.993857700+08:00" level=info msg="[TCP] 127.0.0.1:59414 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T23:12:21.185719600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59398 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:21.185719600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59397 --> cmp2-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:21.185719600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59396 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:22.243022800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59401 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:22.243104800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59404 --> cmp3-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:22.243104800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59402 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:12:22.243104800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59403 --> ext2-bom2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:15.358029700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59814 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:20.540382100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59828 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:20.540382100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59829 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:20.540608500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59827 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:20.540608500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59845 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:20.540608500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59830 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:20.540608500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59838 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:21.361238500+08:00" level=info msg="[TCP] 127.0.0.1:59861 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T23:21:21.597425700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59837 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:21.597425700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59835 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:21.597425700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59836 --> cmp2-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:21.597522400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59842 --> cmp1-sgp1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:22.656680600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59840 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:22.656680600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59841 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:22.660291500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59843 --> ext1-maa2.steamserver.net:27030 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:21:25.778290600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59859 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:35:51.739575200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60561 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:35:56.824744500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60570 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:01.995066200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60579 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:01.995066200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60581 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:01.995066200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60580 --> ext2-maa2.steamserver.net:27032 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:01.995066200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60578 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:01.995066200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60594 --> ext2-syd1.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:01.995066200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60596 --> ext2-maa2.steamserver.net:27037 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:01.995066200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60591 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:01.995066200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60595 --> ext2-maa2.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:02.841002000+08:00" level=info msg="[TCP] 127.0.0.1:60607 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-17T23:36:03.045409100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60588 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:03.045409100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60590 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:03.049060000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60589 --> cmp2-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:04.098157300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60592 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:36:04.098157300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60593 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T23:46:37.783919300+08:00" level=warning msg="Mihomo shutting down"
