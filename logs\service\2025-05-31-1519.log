Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-05-31T15:19:30.128899300+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-31T15:19:30.137633500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-31T15:19:30.137633500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-31T15:19:30.139179200+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-05-31T15:19:30.159399600+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-31T15:19:30.159399600+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-05-31T15:19:30.391273300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-31T15:19:30.391273300+08:00" level=info msg="Load GeoSite rule: private"
time="2025-05-31T15:19:30.407421000+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-31T15:19:30.410423500+08:00" level=info msg="Initial configuration complete, total time: 276ms"
time="2025-05-31T15:19:30.410423500+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-05-31T15:19:30.412425000+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-31T15:19:30.412425000+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-05-31T15:19:30.412425000+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-05-31T15:19:30.412425000+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider apple"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider BBC"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Line"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Disney"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider telegram"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider TVer"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider NowE"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider TVB"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider google"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Discord"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Lan"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-31T15:19:30.421433900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-31T15:19:30.651211000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.652755100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.654187100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.654187100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.655539200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.655539200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.656588800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.657921400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.657921400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.657921400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.664835600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.664835600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.666476800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.666476800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.667518000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.669352600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.669352600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.670704600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.672648900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.676110200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.676110200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.679279800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.686822400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.686822400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.688566800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.688566800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.689551000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.689551000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.689551000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.690255800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.693128200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.694153500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.694153500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.695488600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.695488600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.697824900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.697824900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.698354300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.702254500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.703417200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.705304400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.706560000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.706560000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.717348900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.718553400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.720127600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.721820100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.734892600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:30.737094100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-31T15:19:31.202470400+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-05-31T15:19:31.204202700+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-05-31T15:19:31.212933200+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-05-31T15:19:31.255567400+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-05-31T15:19:31.275138000+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-05-31T15:19:31.284514100+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-05-31T15:19:31.296635000+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-05-31T15:19:31.325063000+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-05-31T15:19:31.329032900+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-05-31T15:19:31.334716600+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-05-31T15:19:31.345127100+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-05-31T15:19:31.350713000+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-05-31T15:19:31.357659600+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-05-31T15:19:31.360140300+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-05-31T15:19:31.360140300+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-05-31T15:19:31.361231800+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-05-31T15:19:31.430148600+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-05-31T15:19:31.430655000+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-05-31T15:19:31.431652600+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-05-31T15:19:31.439314000+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-05-31T15:19:31.443779100+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-05-31T15:19:31.446343100+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-05-31T15:19:31.458680300+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-05-31T15:19:31.460191900+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-05-31T15:19:31.466422800+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-05-31T15:19:31.472337600+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-05-31T15:19:31.503922700+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-05-31T15:19:31.512453400+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-05-31T15:19:31.514959100+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-05-31T15:19:31.540901500+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-05-31T15:19:31.556124700+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-05-31T15:19:31.572738500+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-05-31T15:19:31.589146700+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-05-31T15:19:31.606148700+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-05-31T15:19:31.715598300+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-05-31T15:19:31.768410900+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-05-31T15:19:31.786258500+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-05-31T15:19:31.792413700+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-05-31T15:19:31.806642400+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-05-31T15:19:31.813209700+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-05-31T15:19:31.833327100+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-05-31T15:19:31.834680900+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-05-31T15:19:32.188174000+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-05-31T15:19:32.260466600+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-05-31T15:19:32.273617400+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-05-31T15:19:32.325244600+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-05-31T15:19:32.699843800+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-05-31T15:19:40.423321400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": net/http: TLS handshake timeout"
time="2025-05-31T15:19:40.423954100+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": net/http: TLS handshake timeout"
time="2025-05-31T15:19:40.428584400+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-31T15:19:40.428584400+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-31T15:19:40.428584400+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-31T15:19:40.428584400+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-31T15:19:41.108217000+08:00" level=info msg="[TCP] 127.0.0.1:50378 --> g.live.com:443 using GLOBAL"
time="2025-05-31T15:19:41.729174300+08:00" level=info msg="[TCP] 127.0.0.1:50398 --> oneclient.sfx.ms:443 using GLOBAL"
time="2025-05-31T15:19:42.057744200+08:00" level=error msg="您的ISP：中国辽宁省大连市【移动】  failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp ***********:50390->**************:19812: use of closed network connection"
time="2025-05-31T15:19:42.057744200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-31T18:58:50.077808500+08:00" level=info msg="[TCP] 127.0.0.1:60342 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T18:58:57.442679200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60347 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T18:59:02.443686700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60356 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T18:59:04.362856000+08:00" level=info msg="[TCP] 127.0.0.1:60366 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T18:59:13.922140500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60373 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T18:59:18.923364200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60380 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T18:59:19.118840300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60385 --> go.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T18:59:19.145938600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60387 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T18:59:24.146557900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60395 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T18:59:29.203452600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60404 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T18:59:34.204077400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60411 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T18:59:44.393330300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60424 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T18:59:49.394049000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60431 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:00:19.211275800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60456 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:00:24.212259300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60465 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:01:10.001057500+08:00" level=info msg="[TCP] 127.0.0.1:60516 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:03:13.651761100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60602 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:03:21.658412400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60611 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:03:34.664444400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60626 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:04:10.668884600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60654 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:05:00.952058500+08:00" level=info msg="[TCP] 127.0.0.1:60697 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:05:33.677752200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60719 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:06:09.869780400+08:00" level=info msg="[TCP] 127.0.0.1:60761 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:07:00.159945000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60802 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:07:19.557521200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60821 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:07:38.335871700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60838 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:07:45.908355800+08:00" level=info msg="[TCP] 127.0.0.1:60965 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:07:46.558355300+08:00" level=info msg="[TCP] 127.0.0.1:60967 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:07:47.340632000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60852 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:07:47.402643100+08:00" level=info msg="[TCP] 127.0.0.1:60973 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:07:47.983666700+08:00" level=info msg="[TCP] 127.0.0.1:60976 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:07:48.169943200+08:00" level=info msg="[TCP] 127.0.0.1:60978 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:07:51.545742300+08:00" level=info msg="[TCP] 127.0.0.1:60986 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-31T19:07:51.558202400+08:00" level=info msg="[TCP] 127.0.0.1:60988 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:07:51.720329100+08:00" level=info msg="[TCP] 127.0.0.1:60990 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:07:51.855308200+08:00" level=info msg="[TCP] 127.0.0.1:60992 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-31T19:07:52.235718300+08:00" level=info msg="[TCP] 127.0.0.1:60994 --> r.bing.com:443 using GLOBAL"
time="2025-05-31T19:07:52.237509900+08:00" level=info msg="[TCP] 127.0.0.1:60998 --> th.bing.com:443 using GLOBAL"
time="2025-05-31T19:07:52.240015800+08:00" level=info msg="[TCP] 127.0.0.1:60996 --> r.bing.com:443 using GLOBAL"
time="2025-05-31T19:07:52.244437800+08:00" level=info msg="[TCP] 127.0.0.1:61000 --> th.bing.com:443 using GLOBAL"
time="2025-05-31T19:07:52.896903900+08:00" level=info msg="[TCP] 127.0.0.1:61003 --> storage.live.com:443 using GLOBAL"
time="2025-05-31T19:07:53.979034100+08:00" level=info msg="[TCP] 127.0.0.1:61006 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:07:53.981247800+08:00" level=info msg="[TCP] 127.0.0.1:61008 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:07:53.986777300+08:00" level=info msg="[TCP] 127.0.0.1:61007 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:07:54.142207700+08:00" level=info msg="[TCP] 127.0.0.1:61012 --> login.live.com:443 using GLOBAL"
time="2025-05-31T19:07:54.326894600+08:00" level=info msg="[TCP] 127.0.0.1:61014 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:07:54.560210100+08:00" level=info msg="[TCP] 127.0.0.1:61016 --> login.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:07:54.639732000+08:00" level=info msg="[TCP] 127.0.0.1:61018 --> xpaywalletcdn-prod.azureedge.net:443 using GLOBAL"
time="2025-05-31T19:07:54.698904900+08:00" level=error msg="🇯🇵日本01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-31T19:07:54.698904900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-31T19:07:54.698904900+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-31T19:07:54.698904900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-31T19:07:56.382789900+08:00" level=info msg="[TCP] 127.0.0.1:61021 --> avatars.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:07:56.775671700+08:00" level=info msg="[TCP] 127.0.0.1:61026 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:07:56.775671700+08:00" level=info msg="[TCP] 127.0.0.1:61028 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:07:56.779421500+08:00" level=info msg="[TCP] 127.0.0.1:61032 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:07:56.779421500+08:00" level=info msg="[TCP] 127.0.0.1:61024 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:07:56.780925900+08:00" level=info msg="[TCP] 127.0.0.1:61030 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:07:57.606401800+08:00" level=info msg="[TCP] 127.0.0.1:61034 --> cdn.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:07:57.648539600+08:00" level=info msg="[TCP] 127.0.0.1:61036 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:07:58.382493000+08:00" level=info msg="[TCP] 127.0.0.1:61038 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:07:59.041888800+08:00" level=info msg="[TCP] 127.0.0.1:61041 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:07:59.041888800+08:00" level=info msg="[TCP] 127.0.0.1:61043 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:08:00.188452300+08:00" level=info msg="[TCP] 127.0.0.1:61046 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:08:00.240667200+08:00" level=info msg="[TCP] 127.0.0.1:61048 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:08:00.245822100+08:00" level=info msg="[TCP] 127.0.0.1:61050 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:00.245822100+08:00" level=info msg="[TCP] 127.0.0.1:61049 --> login.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:08:00.835703200+08:00" level=info msg="[TCP] 127.0.0.1:61054 --> store.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:00.836770400+08:00" level=info msg="[TCP] 127.0.0.1:61056 --> store.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:00.839510700+08:00" level=info msg="[TCP] 127.0.0.1:61066 --> avatars.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:00.841014900+08:00" level=info msg="[TCP] 127.0.0.1:61057 --> store.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:00.841043000+08:00" level=info msg="[TCP] 127.0.0.1:61063 --> store.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:00.842060100+08:00" level=info msg="[TCP] 127.0.0.1:61059 --> store.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:00.843771600+08:00" level=info msg="[TCP] 127.0.0.1:61061 --> store.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.359913100+08:00" level=info msg="[TCP] 127.0.0.1:61069 --> cdn.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.360980900+08:00" level=info msg="[TCP] 127.0.0.1:61075 --> shared.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.360980900+08:00" level=info msg="[TCP] 127.0.0.1:61072 --> cdn.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.362332200+08:00" level=info msg="[TCP] 127.0.0.1:61079 --> shared.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.363368100+08:00" level=info msg="[TCP] 127.0.0.1:61080 --> shared.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.364657800+08:00" level=info msg="[TCP] 127.0.0.1:61068 --> cdn.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.364657800+08:00" level=info msg="[TCP] 127.0.0.1:61070 --> cdn.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.365182500+08:00" level=info msg="[TCP] 127.0.0.1:61073 --> cdn.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.367148700+08:00" level=info msg="[TCP] 127.0.0.1:61076 --> shared.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.367148700+08:00" level=info msg="[TCP] 127.0.0.1:61088 --> store.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.367148700+08:00" level=info msg="[TCP] 127.0.0.1:61077 --> shared.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:01.669852500+08:00" level=info msg="[TCP] 127.0.0.1:61090 --> store.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:02.566426600+08:00" level=info msg="[TCP] 127.0.0.1:61093 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:08:02.579867800+08:00" level=info msg="[TCP] 127.0.0.1:61095 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:08:03.181934100+08:00" level=info msg="[TCP] 127.0.0.1:61098 --> cdn.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:03.263420400+08:00" level=info msg="[TCP] 127.0.0.1:61100 --> cdn.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:03.268943300+08:00" level=info msg="[TCP] 127.0.0.1:61102 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:08:03.460940900+08:00" level=info msg="[TCP] 127.0.0.1:61104 --> dl-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:08:03.987284100+08:00" level=info msg="[TCP] 127.0.0.1:61106 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-05-31T19:08:04.195021500+08:00" level=info msg="[TCP] 127.0.0.1:61108 --> app-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:08:05.048435100+08:00" level=info msg="[TCP] 127.0.0.1:61113 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:08:05.052231100+08:00" level=info msg="[TCP] 127.0.0.1:61111 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:08:08.509890000+08:00" level=info msg="[TCP] 127.0.0.1:61117 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:08:08.537657700+08:00" level=info msg="[TCP] 127.0.0.1:61119 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:08:15.841243400+08:00" level=info msg="[TCP] 127.0.0.1:61127 --> login.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:08:15.844789900+08:00" level=info msg="[TCP] 127.0.0.1:61126 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:16.646639700+08:00" level=info msg="[TCP] 127.0.0.1:61130 --> settings-win.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:08:18.647407000+08:00" level=info msg="[TCP] 127.0.0.1:61134 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:08:18.663973900+08:00" level=info msg="[TCP] 127.0.0.1:61136 --> cdn.cloudflare.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:08:52.233126900+08:00" level=info msg="[TCP] 127.0.0.1:61161 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:09:05.327694300+08:00" level=info msg="[TCP] 127.0.0.1:61171 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:09:05.600548900+08:00" level=info msg="[TCP] 127.0.0.1:61173 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:09:06.659179300+08:00" level=info msg="[TCP] 127.0.0.1:61177 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:09:14.801819300+08:00" level=info msg="[TCP] 127.0.0.1:61184 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:09:40.273322500+08:00" level=info msg="[TCP] 127.0.0.1:61203 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:09:41.356630000+08:00" level=info msg="[TCP] 127.0.0.1:61205 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:09:43.367690400+08:00" level=info msg="[TCP] 127.0.0.1:61209 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:09:44.753660100+08:00" level=info msg="[TCP] 127.0.0.1:61211 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:09:51.011188900+08:00" level=info msg="[TCP] 127.0.0.1:61217 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:09:56.934516400+08:00" level=info msg="[TCP] 127.0.0.1:61223 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-05-31T19:09:58.226731500+08:00" level=info msg="[TCP] 127.0.0.1:61227 --> activity.windows.com:443 using GLOBAL"
time="2025-05-31T19:10:00.843596700+08:00" level=info msg="[TCP] 127.0.0.1:61230 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:10:05.764245800+08:00" level=info msg="[TCP] 127.0.0.1:61235 --> r10.o.lencr.org:80 using GLOBAL"
time="2025-05-31T19:10:07.492271500+08:00" level=info msg="[TCP] 127.0.0.1:61239 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:10:07.550686200+08:00" level=info msg="[TCP] 127.0.0.1:61241 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:10:13.137773300+08:00" level=info msg="[TCP] 127.0.0.1:61246 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:10:13.188721200+08:00" level=info msg="[TCP] 127.0.0.1:61247 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:10:13.188721200+08:00" level=info msg="[TCP] 127.0.0.1:61249 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:10:13.191226600+08:00" level=info msg="[TCP] 127.0.0.1:61252 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:10:18.762870500+08:00" level=info msg="[TCP] 127.0.0.1:61258 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:10:28.708569700+08:00" level=info msg="[TCP] 127.0.0.1:61272 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:10:28.763051100+08:00" level=info msg="[TCP] 127.0.0.1:61274 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:10:44.654080000+08:00" level=info msg="[TCP] 127.0.0.1:61287 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:10:44.710634000+08:00" level=info msg="[TCP] 127.0.0.1:61289 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:10:50.923527200+08:00" level=info msg="[TCP] 127.0.0.1:61295 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:11:01.694096100+08:00" level=info msg="[TCP] 127.0.0.1:61305 --> activity.windows.com:443 using GLOBAL"
time="2025-05-31T19:11:07.985755000+08:00" level=info msg="[TCP] 127.0.0.1:61312 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:11:08.360477500+08:00" level=info msg="[TCP] 127.0.0.1:61317 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:11:09.892137500+08:00" level=info msg="[TCP] 127.0.0.1:61325 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:12:03.981333500+08:00" level=info msg="[TCP] 127.0.0.1:61367 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:12:14.978191400+08:00" level=info msg="[TCP] 127.0.0.1:61379 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:12:26.668947000+08:00" level=info msg="[TCP] 127.0.0.1:61390 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:12:26.788990200+08:00" level=info msg="[TCP] 127.0.0.1:61392 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:12:26.886130500+08:00" level=info msg="[TCP] 127.0.0.1:61394 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:12:42.086278000+08:00" level=info msg="[TCP] 127.0.0.1:61406 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:12:42.341612600+08:00" level=info msg="[TCP] 127.0.0.1:61408 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:12:57.705198500+08:00" level=info msg="[TCP] 127.0.0.1:61420 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:13:15.032446700+08:00" level=info msg="[TCP] 127.0.0.1:61433 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:13:33.749323300+08:00" level=info msg="[TCP] 127.0.0.1:61448 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:13:39.026820200+08:00" level=info msg="[TCP] 127.0.0.1:61453 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:13:59.467908800+08:00" level=info msg="[TCP] 127.0.0.1:61470 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:13:59.914403100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61466 --> sigma-fcount-webtech.proxima.nie.netease.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp 111.29.57.176:19812: i/o timeout"
time="2025-05-31T19:14:01.042606000+08:00" level=info msg="[TCP] 127.0.0.1:61473 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:14:03.350378900+08:00" level=info msg="[TCP] 127.0.0.1:61477 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:15:02.184026300+08:00" level=info msg="[TCP] 127.0.0.1:61518 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:15:07.095817600+08:00" level=info msg="[TCP] 127.0.0.1:61524 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:15:15.415257300+08:00" level=info msg="[TCP] 127.0.0.1:61531 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:15:22.752126900+08:00" level=info msg="[TCP] 127.0.0.1:61539 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:15:23.422861200+08:00" level=info msg="[TCP] 127.0.0.1:61541 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:15:50.927438500+08:00" level=info msg="[TCP] 127.0.0.1:61562 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:15:57.984868900+08:00" level=info msg="[TCP] 127.0.0.1:61568 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:16:01.639751900+08:00" level=info msg="[TCP] 127.0.0.1:61574 --> activity.windows.com:443 using GLOBAL"
time="2025-05-31T19:16:09.261001200+08:00" level=info msg="[TCP] 127.0.0.1:61590 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:16:09.291141100+08:00" level=info msg="[TCP] 127.0.0.1:61592 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:16:09.884978000+08:00" level=info msg="[TCP] 127.0.0.1:61594 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:16:11.086542900+08:00" level=info msg="[TCP] 127.0.0.1:61601 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:16:16.634035400+08:00" level=info msg="[TCP] 127.0.0.1:61613 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:16:16.635084000+08:00" level=info msg="[TCP] 127.0.0.1:61611 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:17:16.443818700+08:00" level=info msg="[TCP] 127.0.0.1:61656 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:17:23.166430300+08:00" level=info msg="[TCP] 127.0.0.1:61663 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:17:23.705376900+08:00" level=info msg="[TCP] 127.0.0.1:61665 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:17:34.134293500+08:00" level=info msg="[TCP] 127.0.0.1:61674 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:17:59.647313600+08:00" level=info msg="[TCP] 127.0.0.1:61694 --> tile-service.weather.microsoft.com:80 using GLOBAL"
time="2025-05-31T19:18:25.286788600+08:00" level=info msg="[TCP] 127.0.0.1:61715 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:18:25.302196400+08:00" level=info msg="[TCP] 127.0.0.1:61713 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:18:27.246777300+08:00" level=info msg="[TCP] 127.0.0.1:61719 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:18:32.570359700+08:00" level=info msg="[TCP] 127.0.0.1:61724 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:18:34.622835900+08:00" level=info msg="[TCP] 127.0.0.1:61728 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:19:32.661802800+08:00" level=error msg="https://t.me/xianzheyun666 官方TG群（唯一） failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp ***********:61788->**************:19812: use of closed network connection"
time="2025-05-31T19:19:32.661802800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-31T19:19:32.761394500+08:00" level=error msg="🇭🇰香港03 [1.5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp ***********:61791->**************:19811: use of closed network connection"
time="2025-05-31T19:19:32.761394500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-31T19:19:32.761394500+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp ***********:61793->**************:19842: use of closed network connection"
time="2025-05-31T19:19:32.761394500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-31T19:19:33.297580600+08:00" level=info msg="[TCP] 127.0.0.1:61797 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:19:39.324199200+08:00" level=info msg="[TCP] 127.0.0.1:61803 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:19:40.667138000+08:00" level=info msg="[TCP] 127.0.0.1:61807 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:19:47.835556900+08:00" level=info msg="[TCP] 127.0.0.1:61813 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:20:28.987213000+08:00" level=info msg="[TCP] 127.0.0.1:61844 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:20:40.755491700+08:00" level=info msg="[TCP] 127.0.0.1:61854 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:20:40.786678600+08:00" level=info msg="[TCP] 127.0.0.1:61856 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:20:43.413263600+08:00" level=info msg="[TCP] 127.0.0.1:61860 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:20:47.995692700+08:00" level=info msg="[TCP] 127.0.0.1:61867 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:20:47.997172700+08:00" level=info msg="[TCP] 127.0.0.1:61865 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:21:09.892742700+08:00" level=info msg="[TCP] 127.0.0.1:61892 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:21:48.860201900+08:00" level=info msg="[TCP] 127.0.0.1:61923 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:21:55.477025000+08:00" level=info msg="[TCP] 127.0.0.1:61929 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:22:27.525545500+08:00" level=info msg="[TCP] 127.0.0.1:61955 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:22:41.002931000+08:00" level=info msg="[TCP] 127.0.0.1:61965 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:22:58.719019200+08:00" level=info msg="[TCP] 127.0.0.1:61979 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:22:59.550725000+08:00" level=info msg="[TCP] 127.0.0.1:61982 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:23:05.981777700+08:00" level=info msg="[TCP] 127.0.0.1:61988 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:23:32.250730800+08:00" level=info msg="[TCP] 127.0.0.1:62007 --> settings-win.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:24:05.884952600+08:00" level=info msg="[TCP] 127.0.0.1:62031 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:24:11.647760600+08:00" level=info msg="[TCP] 127.0.0.1:62037 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:24:48.232069600+08:00" level=info msg="[TCP] 127.0.0.1:62064 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:24:51.699690800+08:00" level=info msg="[TCP] 127.0.0.1:62069 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:25:03.725612400+08:00" level=info msg="[TCP] 127.0.0.1:62079 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:25:51.711616900+08:00" level=info msg="[TCP] 127.0.0.1:62112 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:25:51.717175800+08:00" level=info msg="[TCP] 127.0.0.1:62113 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:25:52.999092000+08:00" level=info msg="[TCP] 127.0.0.1:62117 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:25:55.440678700+08:00" level=info msg="[TCP] 127.0.0.1:62126 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:25:55.495254400+08:00" level=info msg="[TCP] 127.0.0.1:62128 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:25:55.781091600+08:00" level=info msg="[TCP] 127.0.0.1:62130 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:25:55.791738000+08:00" level=info msg="[TCP] 127.0.0.1:62132 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:25:55.965078900+08:00" level=info msg="[TCP] 127.0.0.1:62122 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:25:55.979250300+08:00" level=info msg="[TCP] 127.0.0.1:62124 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:26:03.786344100+08:00" level=info msg="[TCP] 127.0.0.1:62140 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:26:03.846635900+08:00" level=info msg="[TCP] 127.0.0.1:62142 --> onedriveclucprodbn20036.blob.core.windows.net:443 using GLOBAL"
time="2025-05-31T19:26:04.108226800+08:00" level=info msg="[TCP] 127.0.0.1:62144 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:26:04.542960400+08:00" level=info msg="[TCP] 127.0.0.1:62147 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:26:04.572121900+08:00" level=info msg="[TCP] 127.0.0.1:62149 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:26:04.920719900+08:00" level=info msg="[TCP] 127.0.0.1:62151 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:26:05.805818500+08:00" level=info msg="[TCP] 127.0.0.1:62154 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:26:09.889450200+08:00" level=info msg="[TCP] 127.0.0.1:62168 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:26:11.315659500+08:00" level=info msg="[TCP] 127.0.0.1:62178 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:26:11.566802600+08:00" level=info msg="[TCP] 127.0.0.1:62180 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:26:15.323463900+08:00" level=info msg="[TCP] 127.0.0.1:62186 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:26:19.251100000+08:00" level=info msg="[TCP] 127.0.0.1:62191 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:26:20.010246200+08:00" level=info msg="[TCP] 127.0.0.1:62196 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:26:21.294349900+08:00" level=info msg="[TCP] 127.0.0.1:62200 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:28:00.470423100+08:00" level=info msg="[TCP] 127.0.0.1:62270 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:28:10.765970500+08:00" level=info msg="[TCP] 127.0.0.1:62281 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:28:10.784999500+08:00" level=info msg="[TCP] 127.0.0.1:62279 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:28:10.785917000+08:00" level=info msg="[TCP] 127.0.0.1:62282 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:28:10.786820900+08:00" level=info msg="[TCP] 127.0.0.1:62278 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:28:10.801331700+08:00" level=info msg="[TCP] 127.0.0.1:62286 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:28:11.909242700+08:00" level=info msg="[TCP] 127.0.0.1:62289 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:28:17.254367200+08:00" level=info msg="[TCP] 127.0.0.1:62294 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:28:26.232599600+08:00" level=info msg="[TCP] 127.0.0.1:62303 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:28:26.232599600+08:00" level=info msg="[TCP] 127.0.0.1:62305 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:28:35.751525700+08:00" level=info msg="[TCP] 127.0.0.1:62315 --> login.live.com:443 using GLOBAL"
time="2025-05-31T19:29:17.402669300+08:00" level=info msg="[TCP] 127.0.0.1:62346 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:29:17.404282800+08:00" level=info msg="[TCP] 127.0.0.1:62347 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:29:17.407854600+08:00" level=info msg="[TCP] 127.0.0.1:62349 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:29:17.432683400+08:00" level=info msg="[TCP] 127.0.0.1:62352 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:29:18.428213100+08:00" level=info msg="[TCP] 127.0.0.1:62355 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:29:23.967787900+08:00" level=info msg="[TCP] 127.0.0.1:62360 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:29:24.440743300+08:00" level=info msg="[TCP] 127.0.0.1:62363 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:29:32.774292200+08:00" level=info msg="[TCP] 127.0.0.1:62370 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:29:33.045501900+08:00" level=info msg="[TCP] 127.0.0.1:62372 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:29:34.049112400+08:00" level=info msg="[TCP] 127.0.0.1:62375 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:29:35.775300400+08:00" level=info msg="[TCP] 127.0.0.1:62378 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-31T19:29:35.775803500+08:00" level=info msg="[TCP] 127.0.0.1:62380 --> c.msn.cn:443 using GLOBAL"
time="2025-05-31T19:29:35.779302000+08:00" level=info msg="[TCP] 127.0.0.1:62382 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-05-31T19:29:35.785212300+08:00" level=info msg="[TCP] 127.0.0.1:62384 --> assets.msn.cn:443 using GLOBAL"
time="2025-05-31T19:29:35.802668900+08:00" level=info msg="[TCP] 127.0.0.1:62386 --> assets.msn.cn:443 using GLOBAL"
time="2025-05-31T19:29:35.973358300+08:00" level=info msg="[TCP] 127.0.0.1:62388 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:29:36.066526700+08:00" level=info msg="[TCP] 127.0.0.1:62390 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:29:36.080070300+08:00" level=info msg="[TCP] 127.0.0.1:62392 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-31T19:29:36.082471000+08:00" level=info msg="[TCP] 127.0.0.1:62393 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-31T19:29:36.089380200+08:00" level=info msg="[TCP] 127.0.0.1:62396 --> c.bing.com:443 using GLOBAL"
time="2025-05-31T19:29:36.107651000+08:00" level=info msg="[TCP] 127.0.0.1:62399 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-31T19:29:36.107651000+08:00" level=info msg="[TCP] 127.0.0.1:62398 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-31T19:29:36.309336400+08:00" level=info msg="[TCP] 127.0.0.1:62402 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:29:48.234911200+08:00" level=info msg="[TCP] 127.0.0.1:62412 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:29:48.655463300+08:00" level=info msg="[TCP] 127.0.0.1:62419 --> th.bing.com:443 using GLOBAL"
time="2025-05-31T19:29:48.655463300+08:00" level=info msg="[TCP] 127.0.0.1:62415 --> r.bing.com:443 using GLOBAL"
time="2025-05-31T19:29:48.655975800+08:00" level=info msg="[TCP] 127.0.0.1:62417 --> r.bing.com:443 using GLOBAL"
time="2025-05-31T19:29:48.659466800+08:00" level=info msg="[TCP] 127.0.0.1:62421 --> th.bing.com:443 using GLOBAL"
time="2025-05-31T19:29:48.842177200+08:00" level=info msg="[TCP] 127.0.0.1:62423 --> xpaywalletcdn-prod.azureedge.net:443 using GLOBAL"
time="2025-05-31T19:29:49.373653500+08:00" level=info msg="[TCP] 127.0.0.1:62426 --> storage.live.com:443 using GLOBAL"
time="2025-05-31T19:29:50.684523700+08:00" level=info msg="[TCP] 127.0.0.1:62428 --> login.live.com:443 using GLOBAL"
time="2025-05-31T19:30:01.580585700+08:00" level=info msg="[TCP] 127.0.0.1:62438 --> login.live.com:443 using GLOBAL"
time="2025-05-31T19:30:01.684106800+08:00" level=info msg="[TCP] 127.0.0.1:62440 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:30:04.980704600+08:00" level=info msg="[TCP] 127.0.0.1:62444 --> ntp.msn.com:443 using GLOBAL"
time="2025-05-31T19:30:05.479448900+08:00" level=info msg="[TCP] 127.0.0.1:62446 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:30:05.479448900+08:00" level=info msg="[TCP] 127.0.0.1:62456 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-05-31T19:30:05.480947300+08:00" level=info msg="[TCP] 127.0.0.1:62449 --> c.bing.com:443 using GLOBAL"
time="2025-05-31T19:30:05.480947300+08:00" level=info msg="[TCP] 127.0.0.1:62447 --> c.msn.com:443 using GLOBAL"
time="2025-05-31T19:30:05.481458700+08:00" level=info msg="[TCP] 127.0.0.1:62454 --> th.bing.com:443 using GLOBAL"
time="2025-05-31T19:30:05.481458700+08:00" level=info msg="[TCP] 127.0.0.1:62458 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-05-31T19:30:05.481970300+08:00" level=info msg="[TCP] 127.0.0.1:62452 --> api.msn.com:443 using GLOBAL"
time="2025-05-31T19:30:05.545875900+08:00" level=info msg="[TCP] 127.0.0.1:62460 --> assets.msn.com:443 using GLOBAL"
time="2025-05-31T19:30:05.695463400+08:00" level=info msg="[TCP] 127.0.0.1:62462 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-05-31T19:30:07.036528100+08:00" level=info msg="[TCP] 127.0.0.1:62465 --> assets.msn.com:443 using GLOBAL"
time="2025-05-31T19:30:24.183289500+08:00" level=info msg="[TCP] 127.0.0.1:62479 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:30:24.830385500+08:00" level=info msg="[TCP] 127.0.0.1:62486 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:30:24.830938200+08:00" level=info msg="[TCP] 127.0.0.1:62483 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:30:24.832302100+08:00" level=info msg="[TCP] 127.0.0.1:62484 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:30:25.971616600+08:00" level=info msg="[TCP] 127.0.0.1:62531 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:30:25.971616600+08:00" level=info msg="[TCP] 127.0.0.1:62533 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:30:26.643112800+08:00" level=info msg="[TCP] 127.0.0.1:62547 --> captcha.gtimg.com:443 using GLOBAL"
time="2025-05-31T19:30:27.081996300+08:00" level=info msg="[TCP] 127.0.0.1:62674 --> t.captcha.qq.com:443 using GLOBAL"
time="2025-05-31T19:30:27.706076100+08:00" level=info msg="[TCP] 127.0.0.1:62678 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:30:27.707206500+08:00" level=info msg="[TCP] 127.0.0.1:62680 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:30:27.910173500+08:00" level=info msg="[TCP] 127.0.0.1:62683 --> t.captcha.qq.com:443 using GLOBAL"
time="2025-05-31T19:30:27.913427700+08:00" level=info msg="[TCP] 127.0.0.1:62685 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:30:28.229231000+08:00" level=info msg="[TCP] 127.0.0.1:62690 --> business-ad.cdn-go.cn:443 using GLOBAL"
time="2025-05-31T19:30:29.022736200+08:00" level=info msg="[TCP] 127.0.0.1:62705 --> tpstelemetry.tencent.com:443 using GLOBAL"
time="2025-05-31T19:30:29.185200100+08:00" level=info msg="[TCP] 127.0.0.1:62710 --> tpstelemetry.tencent.com:443 using GLOBAL"
time="2025-05-31T19:30:30.276164800+08:00" level=info msg="[TCP] 127.0.0.1:62741 --> docs.qq.com:443 using GLOBAL"
time="2025-05-31T19:30:30.454967300+08:00" level=info msg="[TCP] 127.0.0.1:62752 --> tpstelemetry.tencent.com:443 using GLOBAL"
time="2025-05-31T19:30:30.610101500+08:00" level=info msg="[TCP] 127.0.0.1:62754 --> tpstelemetry.tencent.com:443 using GLOBAL"
time="2025-05-31T19:30:31.692474100+08:00" level=info msg="[TCP] 127.0.0.1:62766 --> report.gamecenter.qq.com:443 using GLOBAL"
time="2025-05-31T19:30:33.766113700+08:00" level=info msg="[TCP] 127.0.0.1:62769 --> tpstelemetry.tencent.com:443 using GLOBAL"
time="2025-05-31T19:30:40.975925800+08:00" level=info msg="[TCP] 127.0.0.1:62777 --> tpstelemetry.tencent.com:443 using GLOBAL"
time="2025-05-31T19:30:42.429442300+08:00" level=info msg="[TCP] 127.0.0.1:62779 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:30:47.026021300+08:00" level=info msg="[TCP] 127.0.0.1:62785 --> 127.0.0.1:33331 using GLOBAL"
time="2025-05-31T19:30:48.738559700+08:00" level=info msg="[TCP] 127.0.0.1:62787 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:30:49.397791300+08:00" level=info msg="[TCP] 127.0.0.1:62790 --> 127.0.0.1:33331 using GLOBAL"
time="2025-05-31T19:30:49.400239800+08:00" level=info msg="[TCP] 127.0.0.1:62794 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:30:49.402274300+08:00" level=info msg="[TCP] 127.0.0.1:62792 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:30:51.835055400+08:00" level=info msg="[TCP] 127.0.0.1:62799 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:30:51.835055400+08:00" level=info msg="[TCP] 127.0.0.1:62800 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:30:51.835055400+08:00" level=info msg="[TCP] 127.0.0.1:62805 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:30:51.837340700+08:00" level=info msg="[TCP] 127.0.0.1:62801 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:30:51.837340700+08:00" level=info msg="[TCP] 127.0.0.1:62797 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:30:52.046398800+08:00" level=info msg="[TCP] 127.0.0.1:62807 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:30:57.431955600+08:00" level=info msg="[TCP] 127.0.0.1:62825 --> test.steampowered.com:80 using GLOBAL"
time="2025-05-31T19:30:57.460400700+08:00" level=info msg="[TCP] 127.0.0.1:62828 --> ipv6check-http.steamserver.net:80 using GLOBAL"
time="2025-05-31T19:30:58.874726000+08:00" level=info msg="[TCP] 127.0.0.1:62835 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:30:59.074196000+08:00" level=info msg="[TCP] 127.0.0.1:62840 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:30:59.075741100+08:00" level=info msg="[TCP] 127.0.0.1:62838 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:30:59.591903400+08:00" level=info msg="[TCP] 127.0.0.1:62842 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:31:01.556663700+08:00" level=info msg="[TCP] 127.0.0.1:62845 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:31:01.866510100+08:00" level=info msg="[TCP] 127.0.0.1:62847 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:31:02.701884900+08:00" level=info msg="[TCP] 127.0.0.1:62851 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:31:02.702407200+08:00" level=info msg="[TCP] 127.0.0.1:62850 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:31:03.200693600+08:00" level=info msg="[TCP] 127.0.0.1:62854 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:31:03.525089300+08:00" level=info msg="[TCP] 127.0.0.1:62856 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:31:04.648540400+08:00" level=info msg="[TCP] 127.0.0.1:62860 --> tpstelemetry.tencent.com:443 using GLOBAL"
time="2025-05-31T19:31:09.882658100+08:00" level=info msg="[TCP] 127.0.0.1:62875 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:31:16.088007900+08:00" level=info msg="[TCP] 127.0.0.1:62884 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:31:17.249029800+08:00" level=info msg="[TCP] 127.0.0.1:62887 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:31:22.613985500+08:00" level=info msg="[TCP] 127.0.0.1:62892 --> redirector.gvt1.com:443 using GLOBAL"
time="2025-05-31T19:31:22.851807500+08:00" level=info msg="[TCP] 127.0.0.1:62895 --> r3---sn-i3j5cax-i3bz.gvt1.com:443 using GLOBAL"
time="2025-05-31T19:31:23.079516700+08:00" level=info msg="[TCP] 127.0.0.1:62897 --> r5---sn-i3belnll.gvt1.com:443 using GLOBAL"
time="2025-05-31T19:31:23.565842900+08:00" level=info msg="[TCP] 127.0.0.1:62900 --> r2---sn-i3b7knsd.gvt1.com:443 using GLOBAL"
time="2025-05-31T19:31:26.221193700+08:00" level=info msg="[TCP] 127.0.0.1:62913 --> settings-win.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:31:31.093601700+08:00" level=info msg="[TCP] 127.0.0.1:62921 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:31:31.109830300+08:00" level=info msg="[TCP] 127.0.0.1:62923 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:31:31.112212300+08:00" level=info msg="[TCP] 127.0.0.1:62925 --> su88.tech----123456----xysc886.com:443 using GLOBAL"
time="2025-05-31T19:31:31.526003000+08:00" level=info msg="[TCP] 127.0.0.1:62927 --> su88.tech----123456----xysc886.com:443 using GLOBAL"
time="2025-05-31T19:31:31.690222700+08:00" level=info msg="[TCP] 127.0.0.1:62919 --> su88.tech----123456----xysc886.com:80 using GLOBAL"
time="2025-05-31T19:31:31.693259700+08:00" level=info msg="[TCP] 127.0.0.1:62929 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:31:33.085207500+08:00" level=info msg="[TCP] 127.0.0.1:62934 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:31:36.292182300+08:00" level=info msg="[TCP] 127.0.0.1:62919 --> su88.tech----123456----xysc886.com:80 using GLOBAL"
time="2025-05-31T19:31:54.460474500+08:00" level=info msg="[TCP] 127.0.0.1:62952 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:31:56.343630600+08:00" level=info msg="[TCP] 127.0.0.1:62965 --> update.googleapis.com:443 using GLOBAL"
time="2025-05-31T19:32:00.446872400+08:00" level=info msg="[TCP] 127.0.0.1:62975 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:00.446872400+08:00" level=info msg="[TCP] 127.0.0.1:62974 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:00.446872400+08:00" level=info msg="[TCP] 127.0.0.1:62977 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:00.447384700+08:00" level=info msg="[TCP] 127.0.0.1:62973 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:00.447384700+08:00" level=info msg="[TCP] 127.0.0.1:62976 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:00.448295500+08:00" level=info msg="[TCP] 127.0.0.1:62978 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:00.448814900+08:00" level=info msg="[TCP] 127.0.0.1:62979 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:01.324405200+08:00" level=info msg="[TCP] 127.0.0.1:62987 --> redirector.gvt1.com:80 using GLOBAL"
time="2025-05-31T19:32:01.474761900+08:00" level=info msg="[TCP] 127.0.0.1:62987 --> r2---sn-i3b7knzl.gvt1.com:80 using GLOBAL"
time="2025-05-31T19:32:01.618086700+08:00" level=info msg="[TCP] 127.0.0.1:62987 --> r5---sn-i3b7knzs.gvt1.com:80 using GLOBAL"
time="2025-05-31T19:32:01.971141900+08:00" level=info msg="[TCP] 127.0.0.1:63002 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:32:02.446605200+08:00" level=info msg="[TCP] 127.0.0.1:62987 --> redirector.gvt1.com:80 using GLOBAL"
time="2025-05-31T19:32:02.479958700+08:00" level=info msg="[TCP] 127.0.0.1:62998 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:32:02.493625100+08:00" level=info msg="[TCP] 127.0.0.1:63000 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-31T19:32:04.135673800+08:00" level=info msg="[TCP] 127.0.0.1:63006 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:32:04.326407900+08:00" level=info msg="[TCP] 127.0.0.1:62987 --> r2---sn-i3j5cax-i3bd.gvt1.com:80 using GLOBAL"
time="2025-05-31T19:32:07.773863200+08:00" level=info msg="[TCP] 127.0.0.1:63013 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:07.791084800+08:00" level=info msg="[TCP] 127.0.0.1:63015 --> shared.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:07.971028800+08:00" level=info msg="[TCP] 127.0.0.1:63017 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:32:08.138825700+08:00" level=info msg="[TCP] 127.0.0.1:63020 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.138825700+08:00" level=info msg="[TCP] 127.0.0.1:63022 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.139341000+08:00" level=info msg="[TCP] 127.0.0.1:63019 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.139992500+08:00" level=info msg="[TCP] 127.0.0.1:63021 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.139992500+08:00" level=info msg="[TCP] 127.0.0.1:63024 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.140611700+08:00" level=info msg="[TCP] 127.0.0.1:63026 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.141630600+08:00" level=info msg="[TCP] 127.0.0.1:63025 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.141630600+08:00" level=info msg="[TCP] 127.0.0.1:63023 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.338068300+08:00" level=info msg="[TCP] 127.0.0.1:63036 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.339279700+08:00" level=info msg="[TCP] 127.0.0.1:63037 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.339818000+08:00" level=info msg="[TCP] 127.0.0.1:63035 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:32:08.995765300+08:00" level=info msg="[TCP] 127.0.0.1:63042 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:32:09.080044600+08:00" level=info msg="[TCP] 127.0.0.1:63044 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:32:09.289806700+08:00" level=info msg="[TCP] 127.0.0.1:63047 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:32:09.542898800+08:00" level=info msg="[TCP] 127.0.0.1:63049 --> steamcommunity-a.akamaihd.net:443 using GLOBAL"
time="2025-05-31T19:32:09.549852800+08:00" level=info msg="[TCP] 127.0.0.1:63051 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:32:09.552674800+08:00" level=info msg="[TCP] 127.0.0.1:63053 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:32:09.674616500+08:00" level=info msg="[TCP] 127.0.0.1:63056 --> avatars.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:09.682767100+08:00" level=info msg="[TCP] 127.0.0.1:63058 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:32:09.705435600+08:00" level=info msg="[TCP] 127.0.0.1:63060 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:09.706508100+08:00" level=info msg="[TCP] 127.0.0.1:63061 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:09.706508100+08:00" level=info msg="[TCP] 127.0.0.1:63062 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:09.733747400+08:00" level=info msg="[TCP] 127.0.0.1:63066 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:32:10.215226500+08:00" level=info msg="[TCP] 127.0.0.1:63068 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-05-31T19:32:10.795642900+08:00" level=info msg="[TCP] 127.0.0.1:63070 --> avatars.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:11.838741100+08:00" level=info msg="[TCP] 127.0.0.1:62987 --> r2---sn-i3j5cax-i3bd.gvt1.com:80 using GLOBAL"
time="2025-05-31T19:32:12.032216500+08:00" level=info msg="[TCP] 127.0.0.1:63074 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:12.036960400+08:00" level=info msg="[TCP] 127.0.0.1:63076 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:12.830688200+08:00" level=info msg="[TCP] 127.0.0.1:63079 --> cdn.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:13.021392500+08:00" level=info msg="[TCP] 127.0.0.1:63081 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:13.021900200+08:00" level=info msg="[TCP] 127.0.0.1:63083 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:13.023302700+08:00" level=info msg="[TCP] 127.0.0.1:63089 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:13.023302700+08:00" level=info msg="[TCP] 127.0.0.1:63084 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:13.023845200+08:00" level=info msg="[TCP] 127.0.0.1:63088 --> cdn.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:13.024517400+08:00" level=info msg="[TCP] 127.0.0.1:63087 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:13.025155700+08:00" level=info msg="[TCP] 127.0.0.1:63090 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:13.227340800+08:00" level=info msg="[TCP] 127.0.0.1:63095 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:15.015332000+08:00" level=info msg="[TCP] 127.0.0.1:63101 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:32:16.150929800+08:00" level=info msg="[TCP] 127.0.0.1:63104 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:32:16.505145300+08:00" level=info msg="[TCP] 127.0.0.1:63068 --> r11.o.lencr.org:80 using GLOBAL"
time="2025-05-31T19:32:16.895936500+08:00" level=info msg="[TCP] 127.0.0.1:63107 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:32:16.898046000+08:00" level=info msg="[TCP] 127.0.0.1:63109 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:32:16.898046000+08:00" level=info msg="[TCP] 127.0.0.1:63110 --> avatars.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:17.236937300+08:00" level=info msg="[TCP] 127.0.0.1:63113 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:32:19.885881200+08:00" level=info msg="[TCP] 127.0.0.1:62987 --> r2---sn-i3j5cax-i3bd.gvt1.com:80 using GLOBAL"
time="2025-05-31T19:32:20.131601800+08:00" level=info msg="[TCP] 127.0.0.1:63118 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:32:23.554474100+08:00" level=info msg="[TCP] 127.0.0.1:63122 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:32:31.488334900+08:00" level=info msg="[TCP] 127.0.0.1:63141 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:31.489555100+08:00" level=info msg="[TCP] 127.0.0.1:63147 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:31.490582500+08:00" level=info msg="[TCP] 127.0.0.1:63143 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:31.491598700+08:00" level=info msg="[TCP] 127.0.0.1:63144 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:31.491598700+08:00" level=info msg="[TCP] 127.0.0.1:63145 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:31.492907900+08:00" level=info msg="[TCP] 127.0.0.1:63148 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:31.681106000+08:00" level=info msg="[TCP] 127.0.0.1:63153 --> shared.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:31.681106000+08:00" level=info msg="[TCP] 127.0.0.1:63157 --> shared.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:31.683423200+08:00" level=info msg="[TCP] 127.0.0.1:63158 --> shared.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:31.684900100+08:00" level=info msg="[TCP] 127.0.0.1:63155 --> shared.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:31.861386200+08:00" level=info msg="[TCP] 127.0.0.1:63161 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:32:32.158052600+08:00" level=info msg="[TCP] 127.0.0.1:63163 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:32:35.827250000+08:00" level=info msg="[TCP] 127.0.0.1:63168 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:32:35.838029300+08:00" level=info msg="[TCP] 127.0.0.1:63170 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:32:36.191387600+08:00" level=info msg="[TCP] 127.0.0.1:63172 --> clan.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:36.312624300+08:00" level=info msg="[TCP] 127.0.0.1:63174 --> clan.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:36.969550100+08:00" level=info msg="[TCP] 127.0.0.1:63178 --> images.steamusercontent.com:443 using GLOBAL"
time="2025-05-31T19:32:38.660022200+08:00" level=info msg="[TCP] 127.0.0.1:63182 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:38.661162800+08:00" level=info msg="[TCP] 127.0.0.1:63180 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:38.662519700+08:00" level=info msg="[TCP] 127.0.0.1:63183 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:32:54.012139300+08:00" level=info msg="[TCP] 127.0.0.1:63196 --> steamcloud-hkg.oss-accelerate.aliyuncs.com:443 using GLOBAL"
time="2025-05-31T19:32:54.207535500+08:00" level=info msg="[TCP] 127.0.0.1:63068 --> ocsp2.globalsign.com:80 using GLOBAL"
time="2025-05-31T19:32:54.367847100+08:00" level=info msg="[TCP] 127.0.0.1:63068 --> ocsp.globalsign.com:80 using GLOBAL"
time="2025-05-31T19:32:57.490709000+08:00" level=info msg="[TCP] 127.0.0.1:63214 --> client-update.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:33:09.536828200+08:00" level=info msg="[TCP] 127.0.0.1:63224 --> update.googleapis.com:443 using GLOBAL"
time="2025-05-31T19:33:10.222589600+08:00" level=info msg="[TCP] 127.0.0.1:63226 --> update.googleapis.com:443 using GLOBAL"
time="2025-05-31T19:33:10.236447900+08:00" level=info msg="[TCP] 127.0.0.1:63228 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:33:10.318292600+08:00" level=info msg="[TCP] 127.0.0.1:63230 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:33:16.203987300+08:00" level=info msg="[TCP] 127.0.0.1:63237 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:33:16.203987300+08:00" level=info msg="[TCP] 127.0.0.1:63239 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:33:16.693707500+08:00" level=info msg="[TCP] 127.0.0.1:63241 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:33:17.570837700+08:00" level=info msg="[TCP] 127.0.0.1:63243 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:33:23.456303000+08:00" level=info msg="[TCP] 127.0.0.1:63249 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:33:23.463741800+08:00" level=info msg="[TCP] 127.0.0.1:63251 --> www.bing.com:443 using GLOBAL"
time="2025-05-31T19:33:23.972697200+08:00" level=info msg="[TCP] 127.0.0.1:63253 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:33:31.787466500+08:00" level=info msg="[TCP] 127.0.0.1:63272 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:33:31.819611700+08:00" level=info msg="[TCP] 127.0.0.1:63271 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:33:31.879695200+08:00" level=info msg="[TCP] 127.0.0.1:63275 --> cdn.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:33:32.556099900+08:00" level=info msg="[TCP] 127.0.0.1:63284 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.557240900+08:00" level=info msg="[TCP] 127.0.0.1:63279 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.557240900+08:00" level=info msg="[TCP] 127.0.0.1:63277 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.557240900+08:00" level=info msg="[TCP] 127.0.0.1:63278 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.557240900+08:00" level=info msg="[TCP] 127.0.0.1:63280 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.558312600+08:00" level=info msg="[TCP] 127.0.0.1:63281 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.558824200+08:00" level=info msg="[TCP] 127.0.0.1:63283 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.559980500+08:00" level=info msg="[TCP] 127.0.0.1:63282 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.733834200+08:00" level=info msg="[TCP] 127.0.0.1:63295 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.733834200+08:00" level=info msg="[TCP] 127.0.0.1:63296 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.733834200+08:00" level=info msg="[TCP] 127.0.0.1:63293 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:32.736094000+08:00" level=info msg="[TCP] 127.0.0.1:63294 --> clientconfig.akamai.steamstatic.com:80 using GLOBAL"
time="2025-05-31T19:33:33.670813100+08:00" level=info msg="[TCP] 127.0.0.1:63302 --> st.dl.eccdnx.com:80 using GLOBAL"
time="2025-05-31T19:33:33.919369000+08:00" level=info msg="[TCP] 127.0.0.1:63304 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:33:34.293303900+08:00" level=info msg="[TCP] 127.0.0.1:63306 --> st.dl.eccdnx.com:80 using GLOBAL"
time="2025-05-31T19:33:34.467991700+08:00" level=info msg="[TCP] 127.0.0.1:63309 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:33:34.991022700+08:00" level=info msg="[TCP] 127.0.0.1:63311 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:33:35.293140500+08:00" level=info msg="[TCP] 127.0.0.1:63315 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:33:35.294502700+08:00" level=info msg="[TCP] 127.0.0.1:63313 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:33:35.303527800+08:00" level=info msg="[TCP] 127.0.0.1:63314 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:33:35.668663100+08:00" level=info msg="[TCP] 127.0.0.1:63319 --> xz.pphimalayanrt.com:80 using GLOBAL"
time="2025-05-31T19:33:35.846780800+08:00" level=info msg="[TCP] 127.0.0.1:63321 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:33:35.974997500+08:00" level=info msg="[TCP] 127.0.0.1:63323 --> 219.76.13.167:80 using GLOBAL"
time="2025-05-31T19:33:39.077811700+08:00" level=info msg="[TCP] 127.0.0.1:63327 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:33:39.266326200+08:00" level=info msg="[TCP] 127.0.0.1:63329 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:33:41.010678600+08:00" level=info msg="[TCP] 127.0.0.1:63333 --> xz.pphimalayanrt.com:80 using GLOBAL"
time="2025-05-31T19:33:41.258410100+08:00" level=info msg="[TCP] 127.0.0.1:63335 --> 219.76.13.166:80 using GLOBAL"
time="2025-05-31T19:33:41.541822600+08:00" level=info msg="[TCP] 127.0.0.1:63337 --> dl.steam.clngaa.com:80 using GLOBAL"
time="2025-05-31T19:33:41.556945000+08:00" level=info msg="[TCP] 127.0.0.1:63339 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-05-31T19:33:42.690869300+08:00" level=info msg="[TCP] 127.0.0.1:63341 --> dl.steam.clngaa.com:80 using GLOBAL"
time="2025-05-31T19:33:42.691917200+08:00" level=info msg="[TCP] 127.0.0.1:63342 --> dl.steam.clngaa.com:80 using GLOBAL"
time="2025-05-31T19:33:42.944043300+08:00" level=info msg="[TCP] 127.0.0.1:63348 --> xz.pphimalayanrt.com:80 using GLOBAL"
time="2025-05-31T19:33:42.945100600+08:00" level=info msg="[TCP] 127.0.0.1:63347 --> xz.pphimalayanrt.com:80 using GLOBAL"
time="2025-05-31T19:33:43.552726900+08:00" level=info msg="[TCP] 127.0.0.1:63352 --> activity.windows.com:443 using GLOBAL"
time="2025-05-31T19:33:44.692531700+08:00" level=info msg="[TCP] 127.0.0.1:63354 --> dl.steam.clngaa.com:80 using GLOBAL"
time="2025-05-31T19:34:18.425919100+08:00" level=info msg="[TCP] 127.0.0.1:63388 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:34:18.807891800+08:00" level=info msg="[TCP] 127.0.0.1:63390 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:34:19.122698900+08:00" level=info msg="[TCP] 127.0.0.1:63392 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:34:20.268417000+08:00" level=info msg="[TCP] 127.0.0.1:63396 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:34:30.850859500+08:00" level=info msg="[TCP] 127.0.0.1:63415 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:34:34.362569600+08:00" level=info msg="[TCP] 127.0.0.1:63419 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:34:34.617067000+08:00" level=info msg="[TCP] 127.0.0.1:63422 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:34:35.838452500+08:00" level=info msg="[TCP] 127.0.0.1:63425 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:34:44.322714900+08:00" level=info msg="[TCP] 127.0.0.1:63433 --> activity.windows.com:443 using GLOBAL"
time="2025-05-31T19:34:49.985303900+08:00" level=info msg="[TCP] 127.0.0.1:63439 --> xz.pphimalayanrt.com:80 using GLOBAL"
time="2025-05-31T19:34:50.266850900+08:00" level=info msg="[TCP] 127.0.0.1:63441 --> 219.76.13.169:80 using GLOBAL"
time="2025-05-31T19:34:50.816645700+08:00" level=info msg="[TCP] 127.0.0.1:63444 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:34:50.876417900+08:00" level=info msg="[TCP] 127.0.0.1:63447 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:34:50.878140800+08:00" level=info msg="[TCP] 127.0.0.1:63448 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:34:51.428891400+08:00" level=info msg="[TCP] 127.0.0.1:63451 --> dl.steam.clngaa.com:80 using GLOBAL"
time="2025-05-31T19:34:51.592643700+08:00" level=info msg="[TCP] 127.0.0.1:63454 --> *************:80 using GLOBAL"
time="2025-05-31T19:34:52.272165900+08:00" level=info msg="[TCP] 127.0.0.1:63456 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:34:56.996200900+08:00" level=info msg="[TCP] 127.0.0.1:63472 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:35:03.483279100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63476 --> shared.steamstatic.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:03.957364900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63482 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:06.727166300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63488 --> store.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:06.727279500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63499 --> store.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:08.484717800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63494 --> shared.steamstatic.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:11.727331600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63507 --> store.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:11.727331600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63506 --> store.steampowered.com:443 error: dns resolve failed: couldn't find ip"
