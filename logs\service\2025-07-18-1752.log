Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-18T17:52:05.018897700+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-18T17:52:05.031435200+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-18T17:52:05.031435200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-18T17:52:05.031945500+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-18T17:52:05.055797400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-18T17:52:05.056798900+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-18T17:52:05.347127900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-18T17:52:05.347127900+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-18T17:52:05.365487600+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-18T17:52:05.369081000+08:00" level=info msg="Initial configuration complete, total time: 341ms"
time="2025-07-18T17:52:05.369589900+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-18T17:52:05.371126700+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-18T17:52:05.371126700+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-18T17:52:05.371126700+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-18T17:52:05.371126700+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider google"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider NowE"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider BBC"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider TVB"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Discord"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider TVer"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider telegram"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider apple"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Line"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Disney"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Lan"
time="2025-07-18T17:52:05.378929700+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-18T17:52:10.380655800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.380655800+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.382183600+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-18T17:52:10.382183600+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-18T17:52:10.382183600+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-18T17:52:10.381159300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-18T17:52:10.382183600+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-18T17:52:10.381675000+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-18T17:52:10.382183600+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-18T17:52:10.385361200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-18T17:52:10.385361200+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-18T17:52:10.385361200+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-18T17:52:10.385361200+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-18T17:52:11.564584500+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-18T17:52:11.565606100+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-18T17:52:11.565606100+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-18T17:52:11.566143000+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-18T17:52:11.566143000+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-18T17:52:11.566143000+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-18T17:52:11.567678800+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-18T17:52:11.569218100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-18T17:52:11.575381900+08:00" level=warning msg="[TUN] default interface changed by monitor,  => 以太网"
time="2025-07-18T17:52:12.026961500+08:00" level=info msg="[TUN] Tun adapter listening at: Meta([********/30],[fdfe:dcba:9876::1/126]), mtu: 9000, auto route: true, auto redir: false, ip stack: Mixed"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider TVer"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Disney"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider NowE"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider telegram"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Line"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider apple"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider TVB"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider BBC"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Discord"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Lan"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider google"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-18T17:52:12.027474700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-18T17:52:17.028303900+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-18T17:52:17.029325300+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T17:52:17.029325300+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-18T17:52:17.029325300+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-18T17:52:17.029325300+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-18T17:52:17.027791300+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-18T17:52:17.030110900+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-18T17:52:17.028809900+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-18T17:52:17.033619900+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-18T17:52:17.033619900+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-18T17:52:17.033619900+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-18T17:52:17.033619900+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-18T17:52:18.456033200+08:00" level=info msg="[TCP] ********:51695 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-18T17:52:18.640342900+08:00" level=info msg="[TCP] ********:51703 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:52:18.642388500+08:00" level=info msg="[TCP] ********:51706 --> statics.teams.cdn.office.net:443 using GLOBAL"
time="2025-07-18T17:52:18.657285900+08:00" level=info msg="[TCP] ********:51709 --> statics.teams.cdn.office.net:443 using GLOBAL"
time="2025-07-18T17:52:18.668294500+08:00" level=info msg="[TCP] ********:51712 --> statics.teams.cdn.office.net:443 using GLOBAL"
time="2025-07-18T17:52:18.688284700+08:00" level=info msg="[TCP] ********:51715 --> statics.teams.cdn.office.net:443 using GLOBAL"
time="2025-07-18T17:52:18.936865700+08:00" level=info msg="[TCP] ********:51724 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-18T17:52:21.795622400+08:00" level=info msg="[TCP] ********:51772 --> statics.teams.cdn.office.net:443 using GLOBAL"
time="2025-07-18T17:52:22.147786400+08:00" level=info msg="[TCP] ********:51777 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:52:24.176460000+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-18T17:52:24.176460000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T17:52:24.187783200+08:00" level=error msg="🇯🇵日本04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-18T17:52:24.187783200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T17:52:26.415607200+08:00" level=info msg="[TCP] ********:51792 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:52:29.202346600+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:62175 --> [2402:4e00:1900:1903:0:9687:defa:71c]:3478 using GLOBAL"
time="2025-07-18T17:52:29.326931200+08:00" level=info msg="[UDP] ********:62174 --> *************:3478 using GLOBAL"
time="2025-07-18T17:52:29.373301800+08:00" level=info msg="[UDP] ********:62173 --> *************:3478 using GLOBAL"
time="2025-07-18T17:52:32.197174300+08:00" level=info msg="[TCP] ********:51799 --> statics.teams.cdn.office.net:443 using GLOBAL"
time="2025-07-18T17:52:34.411249900+08:00" level=info msg="[TCP] ********:51803 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:52:35.386243100+08:00" level=info msg="[TCP] ********:51807 --> statics.teams.cdn.office.net:443 using GLOBAL"
time="2025-07-18T17:52:36.839791200+08:00" level=info msg="[TCP] ********:51811 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:52:55.539568500+08:00" level=info msg="[TCP] ********:51826 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-18T17:52:55.542538700+08:00" level=info msg="[TCP] ********:51827 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-18T17:53:05.245551100+08:00" level=info msg="[TCP] ********:51838 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:53:39.705398200+08:00" level=info msg="[TCP] ********:51865 --> activity.windows.com:443 using GLOBAL"
time="2025-07-18T17:53:56.189633300+08:00" level=info msg="[TCP] ********:51879 --> download.clashverge.dev:443 using GLOBAL"
time="2025-07-18T17:54:03.095275100+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-18T17:54:03.095786800+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-18T17:54:03.095786800+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-18T17:54:03.096293900+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-18T17:54:03.096293900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-18T17:54:03.096830800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-18T17:54:03.098361500+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-18T17:54:03.099897700+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-18T17:54:03.166120600+08:00" level=info msg="Start initial provider google"
time="2025-07-18T17:54:03.166120600+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-18T17:54:03.166120600+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Disney"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Lan"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider BBC"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider NowE"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Discord"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider TVB"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Line"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider TVer"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider apple"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider telegram"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-18T17:54:03.166401700+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-18T17:54:03.356448800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.356448800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.356448800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.358018700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.358018700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.358521300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.358521300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.358521300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.359304900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.359304900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.360022800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.360022800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.360947800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.360947800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.360947800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.360947800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.361450100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.363192400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.363192400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.363192400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.363192400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.363694900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.363694900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.363694900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.363694900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.363694900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.363694900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.364205700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.364205700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.368734900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.368734900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.369238000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.369238000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.369238000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.369238000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.369749300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.369749300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.369749300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.370262500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.370262500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.370262500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.370262500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.370262500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.370808900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.371323000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.380169500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.380169500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.380671800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.380671800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T17:54:03.901248100+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-18T17:54:03.903764600+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-18T17:54:03.912177800+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-18T17:54:03.916412200+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-18T17:54:03.917245400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-18T17:54:03.917748700+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-18T17:54:03.917748700+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-18T17:54:03.920089800+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-18T17:54:03.921862600+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-18T17:54:03.922548700+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-18T17:54:03.922548700+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-18T17:54:03.923052300+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-18T17:54:03.923565500+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-18T17:54:03.923565500+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-18T17:54:03.924150400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-18T17:54:03.924150400+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-18T17:54:03.924150400+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-18T17:54:03.924926300+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-18T17:54:03.924926300+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-18T17:54:03.925941900+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-18T17:54:03.925941900+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-18T17:54:03.926689600+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-18T17:54:03.927193100+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-18T17:54:03.927193100+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-18T17:54:03.927703200+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-18T17:54:03.927703200+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-18T17:54:03.928570300+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-18T17:54:03.928570300+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-18T17:54:03.929165300+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-18T17:54:03.929668500+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-18T17:54:03.930421700+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-18T17:54:03.930924200+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-18T17:54:03.930924200+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-18T17:54:03.931730300+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-18T17:54:03.932233600+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-18T17:54:03.932233600+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-18T17:54:03.932966500+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-18T17:54:03.932966500+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-18T17:54:03.932966500+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-18T17:54:03.933470300+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-18T17:54:03.934079300+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-18T17:54:03.934079300+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-18T17:54:03.934581800+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-18T17:54:03.935092900+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-18T17:54:03.935092900+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-18T17:54:03.935789600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-18T17:54:03.935789600+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-18T17:54:03.936514000+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-18T17:54:03.936514000+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-18T17:54:03.939385600+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-18T17:54:03.939385600+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-18T17:54:03.939385600+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-18T17:54:03.939385600+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-18T17:58:43.766968800+08:00" level=info msg="[TCP] 127.0.0.1:52691 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-18T17:58:46.902563200+08:00" level=info msg="[TCP] 127.0.0.1:52696 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:58:50.621713900+08:00" level=info msg="[TCP] 127.0.0.1:52703 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:58:54.235079000+08:00" level=info msg="[TCP] 127.0.0.1:52708 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-18T17:58:55.564967100+08:00" level=info msg="[TCP] 127.0.0.1:52715 --> my.microsoftpersonalcontent.com:443 using GLOBAL"
time="2025-07-18T17:58:55.566006200+08:00" level=info msg="[TCP] 127.0.0.1:52712 --> my.microsoftpersonalcontent.com:443 using GLOBAL"
time="2025-07-18T17:58:56.471811100+08:00" level=info msg="[TCP] 127.0.0.1:52718 --> login.live.com:443 using GLOBAL"
time="2025-07-18T17:58:57.376206100+08:00" level=info msg="[TCP] 127.0.0.1:52722 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:58:57.582566800+08:00" level=info msg="[TCP] 127.0.0.1:52725 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:58:58.192939500+08:00" level=info msg="[TCP] 127.0.0.1:52729 --> graph.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:58:58.194023400+08:00" level=info msg="[TCP] 127.0.0.1:52732 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 using GLOBAL"
time="2025-07-18T17:59:00.425042000+08:00" level=info msg="[TCP] 127.0.0.1:52736 --> www.bing.com:443 using GLOBAL"
time="2025-07-18T17:59:11.811031700+08:00" level=info msg="[TCP] 127.0.0.1:52747 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-18T17:59:13.098449300+08:00" level=info msg="[TCP] 127.0.0.1:52750 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-18T17:59:13.638037200+08:00" level=info msg="[TCP] 127.0.0.1:52754 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-18T17:59:18.204114700+08:00" level=info msg="[TCP] 127.0.0.1:52760 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T17:59:29.675649400+08:00" level=info msg="[TCP] 127.0.0.1:52770 --> augment.gallerycdn.azure.cn:443 using GLOBAL"
time="2025-07-18T17:59:35.488379200+08:00" level=info msg="[TCP] 127.0.0.1:52777 --> augment-assets.com:443 using GLOBAL"
time="2025-07-18T17:59:55.693706600+08:00" level=info msg="[TCP] 127.0.0.1:52793 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-18T18:00:01.352174800+08:00" level=info msg="[TCP] 127.0.0.1:52802 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-18T18:00:02.990695700+08:00" level=info msg="[TCP] 127.0.0.1:52806 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:02.995349100+08:00" level=info msg="[TCP] 127.0.0.1:52807 --> mcs.snssdk.com:443 using GLOBAL"
time="2025-07-18T18:00:03.046423200+08:00" level=info msg="[TCP] 127.0.0.1:52812 --> s3-imfile.feishucdn.com:443 using GLOBAL"
time="2025-07-18T18:00:03.059414900+08:00" level=info msg="[TCP] 127.0.0.1:52815 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:03.297794600+08:00" level=info msg="[TCP] 127.0.0.1:52818 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:00:03.328237000+08:00" level=info msg="[TCP] 127.0.0.1:52821 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:03.331511400+08:00" level=info msg="[TCP] 127.0.0.1:52822 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:03.374147500+08:00" level=info msg="[TCP] 127.0.0.1:52828 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:03.374977200+08:00" level=info msg="[TCP] 127.0.0.1:52827 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:03.580417100+08:00" level=info msg="[TCP] 127.0.0.1:52834 --> lf-package-cn.feishucdn.com:443 using GLOBAL"
time="2025-07-18T18:00:04.007250200+08:00" level=info msg="[TCP] 127.0.0.1:52837 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:04.129288300+08:00" level=info msg="[TCP] 127.0.0.1:52840 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:04.135194100+08:00" level=info msg="[TCP] 127.0.0.1:52843 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:04.455223400+08:00" level=info msg="[TCP] 127.0.0.1:52846 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:04.474831400+08:00" level=info msg="[TCP] 127.0.0.1:52849 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:04.474831400+08:00" level=info msg="[TCP] 127.0.0.1:52850 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:04.805484400+08:00" level=info msg="[TCP] 127.0.0.1:52855 --> internal-api-lark-api.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:04.918044800+08:00" level=info msg="[TCP] 127.0.0.1:52858 --> internal-api-lark-api.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:05.079333800+08:00" level=info msg="[TCP] 127.0.0.1:52862 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:05.922171200+08:00" level=info msg="[TCP] 127.0.0.1:52865 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:07.186136600+08:00" level=info msg="[TCP] 127.0.0.1:52869 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:00:10.242267000+08:00" level=info msg="[TCP] 127.0.0.1:52874 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-18T18:00:10.742950300+08:00" level=info msg="[TCP] 127.0.0.1:52882 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-18T18:00:12.184564200+08:00" level=info msg="[TCP] 127.0.0.1:52886 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-18T18:00:12.451377500+08:00" level=info msg="[TCP] 127.0.0.1:52889 --> api.github.com:443 using GLOBAL"
time="2025-07-18T18:00:12.469160800+08:00" level=info msg="[TCP] 127.0.0.1:52892 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-18T18:00:12.531695200+08:00" level=info msg="[TCP] 127.0.0.1:52895 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:00:12.630472800+08:00" level=info msg="[TCP] 127.0.0.1:52900 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-18T18:00:12.630976400+08:00" level=info msg="[TCP] 127.0.0.1:52908 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-18T18:00:12.630976400+08:00" level=info msg="[TCP] 127.0.0.1:52899 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-18T18:00:12.630976400+08:00" level=info msg="[TCP] 127.0.0.1:52911 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-18T18:00:12.633615700+08:00" level=info msg="[TCP] 127.0.0.1:52901 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-18T18:00:12.767750000+08:00" level=info msg="[TCP] 127.0.0.1:52915 --> api.github.com:443 using GLOBAL"
time="2025-07-18T18:00:12.770480400+08:00" level=info msg="[TCP] 127.0.0.1:52914 --> api.github.com:443 using GLOBAL"
time="2025-07-18T18:00:13.447390600+08:00" level=info msg="[TCP] 127.0.0.1:52920 --> education.github.com:443 using GLOBAL"
time="2025-07-18T18:00:14.357952200+08:00" level=info msg="[TCP] 127.0.0.1:52925 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-18T18:00:15.487203900+08:00" level=info msg="[TCP] 127.0.0.1:52928 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:00:17.429703100+08:00" level=info msg="[TCP] 127.0.0.1:52933 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:00:21.601033400+08:00" level=info msg="[TCP] 127.0.0.1:52938 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:00:46.771029100+08:00" level=info msg="[TCP] 127.0.0.1:52959 --> www.msn.com:443 using GLOBAL"
time="2025-07-18T18:00:46.780042200+08:00" level=info msg="[TCP] 127.0.0.1:52962 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-18T18:00:46.940697400+08:00" level=info msg="[TCP] 127.0.0.1:52965 --> windows.msn.com:443 using GLOBAL"
time="2025-07-18T18:00:46.997039700+08:00" level=info msg="[TCP] 127.0.0.1:52968 --> deff.nelreports.net:443 using GLOBAL"
time="2025-07-18T18:00:47.301958200+08:00" level=info msg="[TCP] 127.0.0.1:52971 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-18T18:00:50.827485800+08:00" level=info msg="[TCP] 127.0.0.1:52977 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:00:56.777038400+08:00" level=info msg="[TCP] 127.0.0.1:52983 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-07-18T18:00:56.905992300+08:00" level=info msg="[TCP] 127.0.0.1:52987 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-07-18T18:00:56.945292500+08:00" level=info msg="[TCP] 127.0.0.1:52990 --> spoprod-a.akamaihd.net:443 using GLOBAL"
time="2025-07-18T18:00:57.303274900+08:00" level=info msg="[TCP] 127.0.0.1:52993 --> assets.msn.com:443 using GLOBAL"
time="2025-07-18T18:00:58.487614000+08:00" level=info msg="[TCP] 127.0.0.1:52997 --> api.msn.com:443 using GLOBAL"
time="2025-07-18T18:00:58.491316900+08:00" level=info msg="[TCP] 127.0.0.1:53000 --> graph.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:00:59.007245600+08:00" level=info msg="[TCP] 127.0.0.1:53003 --> assets.msn.com:443 using GLOBAL"
time="2025-07-18T18:00:59.316422400+08:00" level=info msg="[TCP] 127.0.0.1:53006 --> assets.msn.com:443 using GLOBAL"
time="2025-07-18T18:00:59.319733000+08:00" level=info msg="[TCP] 127.0.0.1:53009 --> windows.msn.com:443 using GLOBAL"
time="2025-07-18T18:01:00.161124200+08:00" level=info msg="[TCP] 127.0.0.1:53014 --> th.bing.com:443 using GLOBAL"
time="2025-07-18T18:01:00.162179700+08:00" level=info msg="[TCP] 127.0.0.1:53013 --> th.bing.com:443 using GLOBAL"
time="2025-07-18T18:01:00.284979000+08:00" level=info msg="[TCP] 127.0.0.1:53019 --> th.bing.com:443 using GLOBAL"
time="2025-07-18T18:01:00.370079400+08:00" level=info msg="[TCP] 127.0.0.1:53023 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-18T18:01:00.371117700+08:00" level=info msg="[TCP] 127.0.0.1:53024 --> th.bing.com:443 using GLOBAL"
time="2025-07-18T18:01:00.371117700+08:00" level=info msg="[TCP] 127.0.0.1:53022 --> th.bing.com:443 using GLOBAL"
time="2025-07-18T18:01:00.373138900+08:00" level=info msg="[TCP] 127.0.0.1:53031 --> api.msn.com:443 using GLOBAL"
time="2025-07-18T18:01:00.497904900+08:00" level=info msg="[TCP] 127.0.0.1:53034 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-18T18:01:00.539859200+08:00" level=info msg="[TCP] 127.0.0.1:53037 --> www.bing.com:443 using GLOBAL"
time="2025-07-18T18:01:00.809179900+08:00" level=info msg="[TCP] 127.0.0.1:53040 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-18T18:01:00.952792100+08:00" level=info msg="[TCP] 127.0.0.1:53043 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-18T18:01:04.242636800+08:00" level=info msg="[TCP] 127.0.0.1:53048 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:01:07.469719700+08:00" level=info msg="[TCP] 127.0.0.1:53054 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-18T18:01:07.993036200+08:00" level=info msg="[TCP] 127.0.0.1:53057 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-07-18T18:02:00.601089800+08:00" level=info msg="[TCP] 127.0.0.1:53095 --> www.bing.com:443 using GLOBAL"
time="2025-07-18T18:02:36.350840100+08:00" level=info msg="[TCP] 127.0.0.1:53123 --> ms-python.gallerycdn.azure.cn:443 using GLOBAL"
time="2025-07-18T18:02:39.752770000+08:00" level=info msg="[TCP] 127.0.0.1:53128 --> augment.gallerycdn.azure.cn:443 using GLOBAL"
time="2025-07-18T18:02:40.948553500+08:00" level=info msg="[TCP] 127.0.0.1:53132 --> augment.gallery.vsassets.io:443 using GLOBAL"
time="2025-07-18T18:02:42.072483200+08:00" level=info msg="[TCP] 127.0.0.1:53136 --> augment.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-18T18:02:51.580671200+08:00" level=info msg="[TCP] 127.0.0.1:53145 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-18T18:02:55.089562600+08:00" level=info msg="[TCP] 127.0.0.1:53150 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:03:12.848317000+08:00" level=info msg="[TCP] 127.0.0.1:53165 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-18T18:03:22.054737000+08:00" level=info msg="[TCP] 127.0.0.1:53181 --> api.github.com:443 using GLOBAL"
time="2025-07-18T18:03:22.227691900+08:00" level=info msg="[TCP] 127.0.0.1:53184 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-18T18:03:22.475476100+08:00" level=info msg="[TCP] 127.0.0.1:53187 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:03:23.059049900+08:00" level=info msg="[TCP] 127.0.0.1:53191 --> education.github.com:443 using GLOBAL"
time="2025-07-18T18:03:23.947308800+08:00" level=info msg="[TCP] 127.0.0.1:53194 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-18T18:03:25.290507400+08:00" level=info msg="[TCP] 127.0.0.1:53198 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:03:27.036296200+08:00" level=info msg="[TCP] 127.0.0.1:53202 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:03:33.680074200+08:00" level=info msg="[TCP] 127.0.0.1:53211 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:04:42.708370300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53276 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:04:50.276107100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53287 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:14:03.707031600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53806 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:14:08.707128900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53814 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:01.905054900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53918 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:01.905054900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53927 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:01.918295800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53923 --> www.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:06.905071100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53942 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:06.905071100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53941 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:06.918314800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53943 --> www.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:11.905522500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53962 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:11.905522500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53961 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:11.921309100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53956 --> browser.events.data.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:16.905713800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53980 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:16.905713800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53970 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:16.905713800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53979 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:16.905713800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53971 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:16.921432500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53972 --> browser.events.data.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:16.921432500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53981 --> browser.events.data.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:18.146442600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53978 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:18.146527700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53982 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:21.906121500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53992 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:21.906121500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53991 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:21.921683900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53997 --> browser.events.data.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:16:23.146569100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54003 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:41.537914900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54453 --> browser.events.data.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:41.538024900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54454 --> staticview.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:41.538024900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54460 --> browser.events.data.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:41.698627100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54459 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:41.698728100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54462 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:43.238075600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54470 --> graph.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:46.538561400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54482 --> browser.events.data.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:46.538561400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54483 --> staticview.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:46.538561400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54484 --> browser.events.data.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:46.699279800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54490 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:46.699279800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54489 --> assets.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:48.239085500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54494 --> graph.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:51.538822700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54508 --> browser.events.data.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:51.540541700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54509 --> c.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:56.539174700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54524 --> browser.events.data.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:21:56.540680600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54525 --> c.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:23:38.827072900+08:00" level=warning msg="Mihomo shutting down"
