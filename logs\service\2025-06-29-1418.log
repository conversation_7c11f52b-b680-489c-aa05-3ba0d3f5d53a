Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-29T14:18:55.157183000+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-29T14:18:55.167158800+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-29T14:18:55.167158800+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-29T14:18:55.168233500+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-29T14:18:55.191293700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-29T14:18:55.191293700+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-29T14:18:55.482888800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-29T14:18:55.482963700+08:00" level=info msg="Load GeoSite rule: private"
time="2025-06-29T14:18:55.495746400+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-29T14:18:55.498415200+08:00" level=info msg="Initial configuration complete, total time: 335ms"
time="2025-06-29T14:18:55.499414200+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-06-29T14:18:55.500415500+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-29T14:18:55.500415500+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-06-29T14:18:55.500415500+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-06-29T14:18:55.500415500+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider google"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider NowE"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider BBC"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Lan"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Line"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider TVer"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Discord"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider apple"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider telegram"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider TVB"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Disney"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-29T14:18:55.506033400+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-29T14:19:00.508085900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-29T14:19:00.508179200+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.510289300+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-29T14:19:00.510289300+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-29T14:19:00.510289300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-29T14:19:00.509724700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.509724700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.508179200+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-29T14:19:00.508687300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-29T14:19:00.510801400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-29T14:19:00.509222200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-29T14:19:00.511821400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-29T14:19:00.509724700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-29T14:19:00.511315600+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-29T14:19:00.511821400+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-29T14:19:00.514388500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-29T14:19:00.514388500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-29T14:19:00.514388500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-29T14:19:00.514388500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-29T14:19:01.714534000+08:00" level=info msg="[TCP] 127.0.0.1:60308 --> api.steampowered.com:443 using GLOBAL"
time="2025-06-29T14:19:04.808601400+08:00" level=info msg="[TCP] 127.0.0.1:60333 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T14:19:04.809875300+08:00" level=info msg="[TCP] 127.0.0.1:60335 --> c.msn.cn:443 using GLOBAL"
time="2025-06-29T14:19:04.811038200+08:00" level=info msg="[TCP] 127.0.0.1:60336 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-29T14:19:04.823599000+08:00" level=info msg="[TCP] 127.0.0.1:60339 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-29T14:19:04.878086400+08:00" level=info msg="[TCP] 127.0.0.1:60341 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-29T14:19:05.001175000+08:00" level=info msg="[TCP] 127.0.0.1:60343 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T14:19:05.123379500+08:00" level=info msg="[TCP] 127.0.0.1:60346 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T14:19:05.124941500+08:00" level=info msg="[TCP] 127.0.0.1:60345 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T14:19:05.178121600+08:00" level=info msg="[TCP] 127.0.0.1:60349 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T14:19:05.181091600+08:00" level=info msg="[TCP] 127.0.0.1:60351 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-29T14:19:05.181091600+08:00" level=info msg="[TCP] 127.0.0.1:60350 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T14:19:05.389370300+08:00" level=info msg="[TCP] 127.0.0.1:60356 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T14:19:05.611383200+08:00" level=info msg="[TCP] 127.0.0.1:60358 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T14:19:05.869510500+08:00" level=info msg="[TCP] 127.0.0.1:60360 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T14:19:07.428098000+08:00" level=info msg="[TCP] 127.0.0.1:60363 --> c.bing.com:443 using GLOBAL"
time="2025-06-29T14:19:09.554881100+08:00" level=info msg="[TCP] 127.0.0.1:60366 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T14:19:10.059950200+08:00" level=info msg="[TCP] 127.0.0.1:60370 --> r.bing.com:443 using GLOBAL"
time="2025-06-29T14:19:10.059950200+08:00" level=info msg="[TCP] 127.0.0.1:60372 --> th.bing.com:443 using GLOBAL"
time="2025-06-29T14:19:10.060467300+08:00" level=info msg="[TCP] 127.0.0.1:60368 --> r.bing.com:443 using GLOBAL"
time="2025-06-29T14:19:10.063121900+08:00" level=info msg="[TCP] 127.0.0.1:60373 --> th.bing.com:443 using GLOBAL"
time="2025-06-29T14:19:11.357799500+08:00" level=info msg="[TCP] 127.0.0.1:60377 --> storage.live.com:443 using GLOBAL"
time="2025-06-29T14:19:11.616536600+08:00" level=info msg="[TCP] 127.0.0.1:60380 --> cdn.sapphire.microsoftapp.net:443 using GLOBAL"
time="2025-06-29T14:19:12.410983000+08:00" level=info msg="[TCP] 127.0.0.1:60382 --> login.live.com:443 using GLOBAL"
time="2025-06-29T14:19:14.817905500+08:00" level=info msg="[TCP] 127.0.0.1:60386 --> ntp.msn.com:443 using GLOBAL"
time="2025-06-29T14:19:17.352678500+08:00" level=info msg="[TCP] 127.0.0.1:60389 --> assets.msn.com:443 using GLOBAL"
time="2025-06-29T14:19:17.355305300+08:00" level=info msg="[TCP] 127.0.0.1:60392 --> c.msn.com:443 using GLOBAL"
time="2025-06-29T14:19:17.355305300+08:00" level=info msg="[TCP] 127.0.0.1:60390 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T14:19:17.355837100+08:00" level=info msg="[TCP] 127.0.0.1:60402 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-29T14:19:17.356421100+08:00" level=info msg="[TCP] 127.0.0.1:60399 --> api.msn.com:443 using GLOBAL"
time="2025-06-29T14:19:17.356421100+08:00" level=info msg="[TCP] 127.0.0.1:60393 --> c.bing.com:443 using GLOBAL"
time="2025-06-29T14:19:17.357125900+08:00" level=info msg="[TCP] 127.0.0.1:60397 --> assets.msn.com:443 using GLOBAL"
time="2025-06-29T14:19:17.358631600+08:00" level=info msg="[TCP] 127.0.0.1:60401 --> th.bing.com:443 using GLOBAL"
time="2025-06-29T14:19:17.361962200+08:00" level=info msg="[TCP] 127.0.0.1:60405 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-06-29T14:19:18.557345600+08:00" level=info msg="[TCP] 127.0.0.1:60408 --> login.live.com:443 using GLOBAL"
time="2025-06-29T14:19:19.404049500+08:00" level=info msg="[TCP] 127.0.0.1:60410 --> tse1.mm.bing.net:443 using GLOBAL"
time="2025-06-29T14:19:20.741430100+08:00" level=info msg="[TCP] 127.0.0.1:60415 --> api.msn.com:443 using GLOBAL"
time="2025-06-29T14:19:26.765437400+08:00" level=info msg="[TCP] 127.0.0.1:60422 --> download.clashverge.dev:443 using GLOBAL"
time="2025-06-29T19:12:34.169207200+08:00" level=info msg="[TCP] 127.0.0.1:62541 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:12:35.177895200+08:00" level=info msg="[TCP] 127.0.0.1:62546 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:12:40.437739300+08:00" level=info msg="[TCP] 127.0.0.1:62560 --> www.apple.com:443 using GLOBAL"
time="2025-06-29T19:12:41.290395800+08:00" level=error msg="🇰🇷韩国03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:12:41.290395800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:12:41.300983200+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:12:41.300983200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:12:42.168002700+08:00" level=info msg="[TCP] 127.0.0.1:62563 --> www.github.com:443 using GLOBAL"
time="2025-06-29T19:12:42.970922300+08:00" level=info msg="[TCP] 127.0.0.1:62566 --> github.com:443 using GLOBAL"
time="2025-06-29T19:12:44.258740900+08:00" level=info msg="[TCP] 127.0.0.1:62569 --> www.youtube.com:443 using GLOBAL"
time="2025-06-29T19:12:46.154147900+08:00" level=info msg="[TCP] 127.0.0.1:62572 --> www.google.com:443 using GLOBAL"
time="2025-06-29T19:12:55.182508600+08:00" level=info msg="[TCP] 127.0.0.1:62597 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:12:55.334139000+08:00" level=info msg="[TCP] 127.0.0.1:62599 --> www.apple.com:443 using GLOBAL"
time="2025-06-29T19:12:56.220515700+08:00" level=info msg="[TCP] 127.0.0.1:62603 --> www.github.com:443 using GLOBAL"
time="2025-06-29T19:12:56.561168300+08:00" level=info msg="[TCP] 127.0.0.1:62606 --> github.com:443 using GLOBAL"
time="2025-06-29T19:12:57.322312500+08:00" level=info msg="[TCP] 127.0.0.1:62611 --> www.google.com:443 using GLOBAL"
time="2025-06-29T19:12:58.468736800+08:00" level=info msg="[TCP] 127.0.0.1:62614 --> www.youtube.com:443 using GLOBAL"
time="2025-06-29T19:13:09.168485600+08:00" level=info msg="[TCP] 127.0.0.1:62644 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:13:09.171122900+08:00" level=info msg="[TCP] 127.0.0.1:62646 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T19:13:13.037264400+08:00" level=info msg="[TCP] 127.0.0.1:62664 --> array510.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:13:14.117806400+08:00" level=info msg="[TCP] 127.0.0.1:62666 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:13:28.999702900+08:00" level=info msg="[TCP] 127.0.0.1:62682 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:13:35.128277000+08:00" level=info msg="[TCP] 127.0.0.1:62688 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:14:04.337333500+08:00" level=info msg="[TCP] 127.0.0.1:62742 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:15:00.168512900+08:00" level=info msg="[TCP] 127.0.0.1:62798 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:15:08.864646900+08:00" level=info msg="[TCP] 127.0.0.1:62809 --> res.wx.qq.com:443 using GLOBAL"
time="2025-06-29T19:15:08.873643200+08:00" level=info msg="[TCP] 127.0.0.1:62811 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T19:15:08.874700500+08:00" level=info msg="[TCP] 127.0.0.1:62813 --> mmbiz.qpic.cn:443 using GLOBAL"
time="2025-06-29T19:15:08.979447700+08:00" level=info msg="[TCP] 127.0.0.1:62815 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-06-29T19:15:09.105546800+08:00" level=info msg="[TCP] 127.0.0.1:62817 --> mmbiz.qpic.cn:443 using GLOBAL"
time="2025-06-29T19:15:09.221005900+08:00" level=info msg="[TCP] 127.0.0.1:62821 --> wx.qlogo.cn:443 using GLOBAL"
time="2025-06-29T19:15:09.828396300+08:00" level=info msg="[TCP] 127.0.0.1:62832 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-06-29T19:15:09.914231900+08:00" level=info msg="[TCP] 127.0.0.1:62834 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T19:15:10.106099900+08:00" level=info msg="[TCP] 127.0.0.1:62836 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-06-29T19:15:16.334600300+08:00" level=info msg="[TCP] 127.0.0.1:62850 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-06-29T19:15:17.360513100+08:00" level=info msg="[TCP] 127.0.0.1:62853 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-06-29T19:15:19.704279700+08:00" level=info msg="[TCP] 127.0.0.1:62856 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-06-29T19:15:24.123492800+08:00" level=info msg="[TCP] 127.0.0.1:62862 --> mmbiz.qpic.cn:80 using GLOBAL"
time="2025-06-29T19:15:24.781018700+08:00" level=info msg="[TCP] 127.0.0.1:62867 --> mmbiz.qpic.cn:80 using GLOBAL"
time="2025-06-29T19:15:25.463723900+08:00" level=info msg="[TCP] 127.0.0.1:62870 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-06-29T19:15:32.715899900+08:00" level=info msg="[TCP] 127.0.0.1:62877 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-06-29T19:16:09.170314700+08:00" level=info msg="[TCP] 127.0.0.1:62924 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:16:28.954299000+08:00" level=info msg="[TCP] 127.0.0.1:62958 --> array513.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:16:30.031923700+08:00" level=info msg="[TCP] 127.0.0.1:62960 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:16:42.504626600+08:00" level=info msg="[TCP] 127.0.0.1:62978 --> go.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:17:28.996219200+08:00" level=info msg="[TCP] 127.0.0.1:63012 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:18:09.170187900+08:00" level=info msg="[TCP] 127.0.0.1:63048 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T19:18:41.661520900+08:00" level=info msg="[TCP] 127.0.0.1:63099 --> wx.qlogo.cn:443 using GLOBAL"
time="2025-06-29T19:19:00.187837800+08:00" level=info msg="[TCP] 127.0.0.1:63114 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:19:05.229779800+08:00" level=info msg="[TCP] 127.0.0.1:63120 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:20:09.186208200+08:00" level=info msg="[TCP] 127.0.0.1:63183 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:21:29.000311100+08:00" level=info msg="[TCP] 127.0.0.1:63243 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:23:00.176345100+08:00" level=info msg="[TCP] 127.0.0.1:63314 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:23:44.254584900+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:23:44.254584900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:23:44.254584900+08:00" level=error msg="🇸🇬新加坡01 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:23:44.254584900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:23:46.170287200+08:00" level=info msg="[TCP] 127.0.0.1:63500 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:23:58.172236200+08:00" level=info msg="[TCP] 127.0.0.1:63511 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T19:24:06.485138200+08:00" level=info msg="[TCP] 127.0.0.1:63533 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:24:09.172089200+08:00" level=info msg="[TCP] 127.0.0.1:63542 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:24:09.177609100+08:00" level=info msg="[TCP] 127.0.0.1:63544 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T19:24:09.191152900+08:00" level=info msg="[TCP] 127.0.0.1:63547 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:24:09.489002000+08:00" level=info msg="[TCP] 127.0.0.1:63550 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:24:09.492219900+08:00" level=info msg="[TCP] 127.0.0.1:63549 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:24:47.252112900+08:00" level=info msg="[TCP] 127.0.0.1:63677 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:25:20.959938000+08:00" level=info msg="[TCP] 127.0.0.1:63717 --> go.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:25:29.004217500+08:00" level=info msg="[TCP] 127.0.0.1:63724 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:26:03.060900100+08:00" level=info msg="[TCP] 127.0.0.1:63755 --> my.microsoftpersonalcontent.com:443 using GLOBAL"
time="2025-06-29T19:26:04.459257400+08:00" level=info msg="[TCP] 127.0.0.1:63758 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:27:09.176891600+08:00" level=info msg="[TCP] 127.0.0.1:63804 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:27:09.485475500+08:00" level=info msg="[TCP] 127.0.0.1:63807 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:27:09.487627600+08:00" level=info msg="[TCP] 127.0.0.1:63808 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:28:29.004229600+08:00" level=info msg="[TCP] 127.0.0.1:63881 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:28:36.475209100+08:00" level=info msg="[TCP] 127.0.0.1:63889 --> api.steampowered.com:443 using GLOBAL"
time="2025-06-29T19:28:42.709684200+08:00" level=info msg="[TCP] 127.0.0.1:63925 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-29T19:28:42.709684200+08:00" level=info msg="[TCP] 127.0.0.1:63924 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-29T19:28:52.999870100+08:00" level=info msg="[TCP] 127.0.0.1:64045 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:28:56.171212900+08:00" level=info msg="[TCP] 127.0.0.1:64051 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:28:58.891649400+08:00" level=info msg="[TCP] 127.0.0.1:64059 --> www.apple.com:443 using GLOBAL"
time="2025-06-29T19:28:59.641844100+08:00" level=info msg="[TCP] 127.0.0.1:64061 --> www.github.com:443 using GLOBAL"
time="2025-06-29T19:29:00.356782200+08:00" level=info msg="[TCP] 127.0.0.1:64063 --> www.google.com:443 using GLOBAL"
time="2025-06-29T19:29:00.418923900+08:00" level=info msg="[TCP] 127.0.0.1:64065 --> github.com:443 using GLOBAL"
time="2025-06-29T19:29:01.223561600+08:00" level=info msg="[TCP] 127.0.0.1:64068 --> www.youtube.com:443 using GLOBAL"
time="2025-06-29T19:29:02.468409000+08:00" level=error msg="🇯🇵日本01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:29:02.468409000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:29:02.469013200+08:00" level=error msg="🇯🇵日本03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:29:02.469013200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:29:02.469013200+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:29:02.469013200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:29:02.469528700+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:29:02.469528700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:29:02.479879200+08:00" level=error msg="🇯🇵日本03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:29:02.479879200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:29:02.479879200+08:00" level=error msg="🇸🇬新加坡01 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:29:02.479879200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:29:02.479879200+08:00" level=error msg="🇯🇵日本01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-29T19:29:02.479879200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-29T19:29:07.546795800+08:00" level=info msg="[TCP] 127.0.0.1:64074 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:29:09.169888800+08:00" level=info msg="[TCP] 127.0.0.1:64077 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:29:09.174018900+08:00" level=info msg="[TCP] 127.0.0.1:64079 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T19:29:09.484912100+08:00" level=info msg="[TCP] 127.0.0.1:64086 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:29:09.484912100+08:00" level=info msg="[TCP] 127.0.0.1:64084 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:29:09.487540300+08:00" level=info msg="[TCP] 127.0.0.1:64082 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:29:09.487540300+08:00" level=info msg="[TCP] 127.0.0.1:64083 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:29:09.489719500+08:00" level=info msg="[TCP] 127.0.0.1:64085 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:30:00.180994400+08:00" level=info msg="[TCP] 127.0.0.1:64143 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:30:09.188414300+08:00" level=info msg="[TCP] 127.0.0.1:64151 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T19:31:09.176683100+08:00" level=info msg="[TCP] 127.0.0.1:64249 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:31:09.481838400+08:00" level=info msg="[TCP] 127.0.0.1:64253 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:31:09.491186600+08:00" level=info msg="[TCP] 127.0.0.1:64255 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:31:44.542617300+08:00" level=info msg="[TCP] 127.0.0.1:64307 --> array513.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:31:45.729768200+08:00" level=info msg="[TCP] 127.0.0.1:64309 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:31:48.779436300+08:00" level=info msg="[TCP] 127.0.0.1:64314 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:32:29.000711500+08:00" level=info msg="[TCP] 127.0.0.1:64346 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:32:42.066696200+08:00" level=info msg="[TCP] 127.0.0.1:64360 --> api.onedrive.com:443 using GLOBAL"
time="2025-06-29T19:32:54.085555500+08:00" level=info msg="[TCP] 127.0.0.1:64370 --> storage.live.com:443 using GLOBAL"
time="2025-06-29T19:32:55.273086400+08:00" level=info msg="[TCP] 127.0.0.1:64373 --> onedriveclucprodbn20050.blob.core.windows.net:443 using GLOBAL"
time="2025-06-29T19:33:06.656162800+08:00" level=info msg="[TCP] 127.0.0.1:64383 --> clients2.google.com:443 using GLOBAL"
time="2025-06-29T19:33:06.657254800+08:00" level=info msg="[TCP] 127.0.0.1:64384 --> update.googleapis.com:443 using GLOBAL"
time="2025-06-29T19:33:06.658553300+08:00" level=info msg="[TCP] 127.0.0.1:64386 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:33:07.755723000+08:00" level=info msg="[TCP] 127.0.0.1:64389 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:33:08.065081200+08:00" level=info msg="[TCP] 127.0.0.1:64393 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:33:08.069870300+08:00" level=info msg="[TCP] 127.0.0.1:64392 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:33:08.072306900+08:00" level=info msg="[TCP] 127.0.0.1:64394 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:34:00.182537100+08:00" level=info msg="[TCP] 127.0.0.1:64473 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:34:08.422510800+08:00" level=info msg="[TCP] 127.0.0.1:64480 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:34:45.500295200+08:00" level=info msg="[TCP] 127.0.0.1:64516 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:35:09.177550800+08:00" level=info msg="[TCP] 127.0.0.1:64546 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:35:09.484230800+08:00" level=info msg="[TCP] 127.0.0.1:64550 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:35:09.486179400+08:00" level=info msg="[TCP] 127.0.0.1:64551 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:36:28.999175500+08:00" level=info msg="[TCP] 127.0.0.1:64625 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:37:14.668392000+08:00" level=info msg="[TCP] 127.0.0.1:64713 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:38:00.181465400+08:00" level=info msg="[TCP] 127.0.0.1:64755 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:38:06.781453400+08:00" level=info msg="[TCP] 127.0.0.1:64761 --> updatecollect.wps.cn:80 using GLOBAL"
time="2025-06-29T19:38:07.901374000+08:00" level=info msg="[TCP] 127.0.0.1:64764 --> updatecollect.wps.cn:80 using GLOBAL"
time="2025-06-29T19:38:08.754558700+08:00" level=info msg="[TCP] 127.0.0.1:64766 --> updatepro.wps.cn:80 using GLOBAL"
time="2025-06-29T19:38:09.740757900+08:00" level=info msg="[TCP] 127.0.0.1:64769 --> updatepro.wps.cn:80 using GLOBAL"
time="2025-06-29T19:38:51.760116000+08:00" level=info msg="[TCP] 127.0.0.1:64825 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:39:09.169207700+08:00" level=info msg="[TCP] 127.0.0.1:64838 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:39:09.301710400+08:00" level=info msg="[TCP] 127.0.0.1:64841 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:39:09.477806700+08:00" level=info msg="[TCP] 127.0.0.1:64844 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:39:09.480959200+08:00" level=info msg="[TCP] 127.0.0.1:64846 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:40:29.009385800+08:00" level=info msg="[TCP] 127.0.0.1:64986 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:42:00.175420500+08:00" level=info msg="[TCP] 127.0.0.1:65063 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:43:09.173159800+08:00" level=info msg="[TCP] 127.0.0.1:65153 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:43:09.481522100+08:00" level=info msg="[TCP] 127.0.0.1:65157 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:43:09.491252200+08:00" level=info msg="[TCP] 127.0.0.1:65159 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:43:14.044558000+08:00" level=info msg="[TCP] 127.0.0.1:65164 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:43:29.975771100+08:00" level=info msg="[TCP] 127.0.0.1:65178 --> array510.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:43:31.194617100+08:00" level=info msg="[TCP] 127.0.0.1:65181 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:44:10.205791400+08:00" level=info msg="[TCP] 127.0.0.1:65246 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:44:28.999534200+08:00" level=info msg="[TCP] 127.0.0.1:65274 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:44:35.817591800+08:00" level=info msg="[TCP] 127.0.0.1:65280 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:45:55.180304600+08:00" level=info msg="[TCP] 127.0.0.1:65397 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:45:55.484792900+08:00" level=info msg="[TCP] 127.0.0.1:65399 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:45:58.178943800+08:00" level=info msg="[TCP] 127.0.0.1:65403 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T19:45:58.471259800+08:00" level=info msg="[TCP] 127.0.0.1:65405 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T19:46:00.179869900+08:00" level=info msg="[TCP] 127.0.0.1:65408 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:00.967312300+08:00" level=info msg="[TCP] 127.0.0.1:65410 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:03.165724400+08:00" level=info msg="[TCP] 127.0.0.1:65419 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:03.448489600+08:00" level=info msg="[TCP] 127.0.0.1:65423 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:09.168011700+08:00" level=info msg="[TCP] 127.0.0.1:65456 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T19:46:09.170908700+08:00" level=info msg="[TCP] 127.0.0.1:65459 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:09.170908700+08:00" level=info msg="[TCP] 127.0.0.1:65458 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T19:46:09.172815400+08:00" level=info msg="[TCP] 127.0.0.1:65462 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:09.484761000+08:00" level=info msg="[TCP] 127.0.0.1:65466 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:09.486819700+08:00" level=info msg="[TCP] 127.0.0.1:65465 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:09.486819700+08:00" level=info msg="[TCP] 127.0.0.1:65467 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:09.486819700+08:00" level=info msg="[TCP] 127.0.0.1:65468 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:09.488066600+08:00" level=info msg="[TCP] 127.0.0.1:65464 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:46:12.178776900+08:00" level=info msg="[TCP] 127.0.0.1:65477 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:47:23.162271300+08:00" level=info msg="[TCP] 127.0.0.1:49248 --> array514.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:47:24.335539200+08:00" level=info msg="[TCP] 127.0.0.1:49254 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:47:28.998009700+08:00" level=info msg="[TCP] 127.0.0.1:49263 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:49:00.178846200+08:00" level=info msg="[TCP] 127.0.0.1:49458 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:49:11.115040200+08:00" level=info msg="[TCP] 127.0.0.1:49485 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:50:09.190813300+08:00" level=info msg="[TCP] 127.0.0.1:49612 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:50:09.494463300+08:00" level=info msg="[TCP] 127.0.0.1:49615 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:50:09.498205800+08:00" level=info msg="[TCP] 127.0.0.1:49616 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:50:21.180129400+08:00" level=info msg="[TCP] 127.0.0.1:49641 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:51:09.168070900+08:00" level=info msg="[TCP] 127.0.0.1:49742 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:51:09.169210700+08:00" level=info msg="[TCP] 127.0.0.1:49744 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T19:51:09.479431000+08:00" level=info msg="[TCP] 127.0.0.1:49748 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:51:09.481582200+08:00" level=info msg="[TCP] 127.0.0.1:49751 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:51:09.486783400+08:00" level=info msg="[TCP] 127.0.0.1:49750 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:51:09.489504700+08:00" level=info msg="[TCP] 127.0.0.1:49749 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:51:29.005462600+08:00" level=info msg="[TCP] 127.0.0.1:49785 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:52:09.165971700+08:00" level=info msg="[TCP] 127.0.0.1:49916 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T19:53:00.183638900+08:00" level=info msg="[TCP] 127.0.0.1:49975 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:54:09.184710900+08:00" level=info msg="[TCP] 127.0.0.1:50084 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:54:09.492231600+08:00" level=info msg="[TCP] 127.0.0.1:50087 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:54:09.497881800+08:00" level=info msg="[TCP] 127.0.0.1:50086 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T19:54:11.989536200+08:00" level=info msg="[TCP] 127.0.0.1:50092 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T19:55:00.682181400+08:00" level=info msg="[TCP] 127.0.0.1:50153 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:55:00.683603200+08:00" level=info msg="[TCP] 127.0.0.1:50151 --> cn.bing.com:443 using GLOBAL"
time="2025-06-29T19:55:08.249443700+08:00" level=info msg="[TCP] 127.0.0.1:50161 --> app-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-29T19:55:09.493909700+08:00" level=info msg="[TCP] 127.0.0.1:50165 --> a.tampermonkey.net:443 using GLOBAL"
time="2025-06-29T19:55:09.555281900+08:00" level=info msg="[TCP] 127.0.0.1:50167 --> www.google.com:443 using GLOBAL"
time="2025-06-29T19:55:10.116427500+08:00" level=info msg="[TCP] 127.0.0.1:50169 --> cdnjs.cloudflare.com:443 using GLOBAL"
time="2025-06-29T19:55:10.265649700+08:00" level=info msg="[TCP] 127.0.0.1:50171 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T19:55:10.425460100+08:00" level=info msg="[TCP] 127.0.0.1:50174 --> cdnjs.cloudflare.com:443 using GLOBAL"
time="2025-06-29T19:55:10.427264900+08:00" level=info msg="[TCP] 127.0.0.1:50173 --> cdnjs.cloudflare.com:443 using GLOBAL"
time="2025-06-29T19:55:11.227979200+08:00" level=info msg="[TCP] 127.0.0.1:50178 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T19:55:11.534166100+08:00" level=info msg="[TCP] 127.0.0.1:50181 --> img.jyeoo.net:443 using GLOBAL"
time="2025-06-29T19:55:11.534166100+08:00" level=info msg="[TCP] 127.0.0.1:50180 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T19:55:13.422352600+08:00" level=info msg="[TCP] 127.0.0.1:50185 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T19:55:13.609616200+08:00" level=info msg="[TCP] 127.0.0.1:50187 --> t2.gstatic.com:443 using GLOBAL"
time="2025-06-29T19:55:17.222115000+08:00" level=info msg="[TCP] 127.0.0.1:50192 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T19:55:18.641052400+08:00" level=info msg="[TCP] 127.0.0.1:50200 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T19:55:18.641052400+08:00" level=info msg="[TCP] 127.0.0.1:50199 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T19:55:18.641052400+08:00" level=info msg="[TCP] 127.0.0.1:50198 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T19:55:18.894747800+08:00" level=info msg="[TCP] 127.0.0.1:50205 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T19:55:19.053153200+08:00" level=info msg="[TCP] 127.0.0.1:50208 --> img.jyeoo.net:443 using GLOBAL"
time="2025-06-29T19:55:19.163214400+08:00" level=info msg="[TCP] 127.0.0.1:50215 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T19:55:19.258449000+08:00" level=info msg="[TCP] 127.0.0.1:50218 --> open.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T19:55:19.546680600+08:00" level=info msg="[TCP] 127.0.0.1:50221 --> res.wx.qq.com:443 using GLOBAL"
time="2025-06-29T19:55:19.734845500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50225 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.734845500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50227 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.734845500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50226 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.734845500+08:00" level=info msg="[TCP] 127.0.0.1:50224 --> localhost.weixin.qq.com:14013 using GLOBAL"
time="2025-06-29T19:55:19.734845500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50228 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.734845500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50229 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.736512700+08:00" level=info msg="[TCP] 127.0.0.1:50236 --> localhost.weixin.qq.com:14013 using GLOBAL"
time="2025-06-29T19:55:19.744986700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50226 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.744986700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50225 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.744986700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50228 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.744986700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50229 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.744986700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50227 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.758351200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50226 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.759387000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50229 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.760807000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50227 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.762311100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50225 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.762859200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50228 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.779782600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50228 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.780313300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50226 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.781836400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50229 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.787723700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50225 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.789979000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50227 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.799928400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50225 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.822970100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50228 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.824713200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50226 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.831903400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50229 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.840318300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50228 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.864084600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50227 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.868137600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50228 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.893121200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50225 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.904296800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50226 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.924384600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50229 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:19.932841500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50227 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.035697800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50229 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.068969000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50225 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.108289000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50226 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.189005000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50227 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.248243200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50229 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.260407300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50225 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.501815500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50228 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.515901000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50226 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.571485300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50227 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.687437100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50226 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:20.721478300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50226 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.058227800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50225 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.086513900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50229 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.387854700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50228 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.572144400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50227 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.722869700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50285 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.733965400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50285 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.750609400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50285 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.778312300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50285 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.790660700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50285 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.830583200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50228 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.860361100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50285 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.931496400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50300 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.941617000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50300 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.956097300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50300 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.965924500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50229 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:21.978432700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50300 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.051183300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50285 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.054863400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50300 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.058234800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50225 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.208050500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50300 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.361095300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50300 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.451673500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50300 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.540747300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50285 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.572913300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50227 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.852677000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50285 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.966983000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50331 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.977513400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50331 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:22.995008800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50331 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.028242500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50331 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.058293600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50337 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.061467100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50331 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.068303400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50337 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.084125000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50337 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.103874800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50337 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.124782600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50337 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.210593700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50337 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.220203600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50331 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.293294300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50346 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.304325900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50346 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.316365100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50346 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.345656800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50346 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.418483200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50300 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.425277500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50346 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.445260800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50337 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.488104700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50331 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.581648400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50346 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.832078200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50285 --> localhost.weixin.qq.com:14015 error: connect failed: dial tcp 127.0.0.1:14015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.887446900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50346 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.912315500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50331 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:23.979961500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50337 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:24.008348700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50346 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:24.135620400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50210 --> api-os.growingio.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T19:55:24.183461600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50331 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:24.279669000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50300 --> localhost.weixin.qq.com:13014 error: connect failed: dial tcp 127.0.0.1:13014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:24.927636900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50331 --> localhost.weixin.qq.com:13015 error: connect failed: dial tcp 127.0.0.1:13015: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:24.979984500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50337 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:24.991837800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50346 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:25.008457600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50337 --> localhost.weixin.qq.com:14014 error: connect failed: dial tcp 127.0.0.1:14014: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:25.992097100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50346 --> localhost.weixin.qq.com:13013 error: connect failed: dial tcp 127.0.0.1:13013: connectex: No connection could be made because the target machine actively refused it."
time="2025-06-29T19:55:29.136391400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50361 --> api-os.growingio.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T20:04:09.149088900+08:00" level=info msg="[TCP] 127.0.0.1:51204 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T20:04:09.162316900+08:00" level=info msg="[TCP] 127.0.0.1:51205 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:04:09.171503900+08:00" level=info msg="[TCP] 127.0.0.1:51207 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:04:09.182697600+08:00" level=info msg="[TCP] 127.0.0.1:51203 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T20:04:13.774597800+08:00" level=info msg="[TCP] 127.0.0.1:51229 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T20:04:21.170092400+08:00" level=info msg="[TCP] 127.0.0.1:51252 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:04:27.506877600+08:00" level=info msg="[TCP] 127.0.0.1:51262 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-06-29T20:04:33.171186100+08:00" level=info msg="[TCP] 127.0.0.1:51273 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:04:33.173733800+08:00" level=info msg="[TCP] 127.0.0.1:51275 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:04:44.173346600+08:00" level=info msg="[TCP] 127.0.0.1:51292 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:04:45.505985300+08:00" level=info msg="[TCP] 127.0.0.1:51295 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:04:55.178165100+08:00" level=info msg="[TCP] 127.0.0.1:51311 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:04:55.179277800+08:00" level=info msg="[TCP] 127.0.0.1:51309 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:05:00.170970800+08:00" level=info msg="[TCP] 127.0.0.1:51321 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:05:03.058861400+08:00" level=info msg="[TCP] 127.0.0.1:51325 --> update.googleapis.com:443 using GLOBAL"
time="2025-06-29T20:05:06.171723900+08:00" level=info msg="[TCP] 127.0.0.1:51329 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:05:09.192637400+08:00" level=info msg="[TCP] 127.0.0.1:51334 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T20:05:17.173636000+08:00" level=info msg="[TCP] 127.0.0.1:51351 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:05:17.174139400+08:00" level=info msg="[TCP] 127.0.0.1:51353 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:05:28.172533500+08:00" level=info msg="[TCP] 127.0.0.1:51372 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:05:39.177730700+08:00" level=info msg="[TCP] 127.0.0.1:51392 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:05:39.181244800+08:00" level=info msg="[TCP] 127.0.0.1:51393 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:05:47.323501300+08:00" level=info msg="[TCP] 127.0.0.1:51404 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:06:00.175927500+08:00" level=info msg="[TCP] 127.0.0.1:51415 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:06:09.176209700+08:00" level=info msg="[TCP] 127.0.0.1:51424 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T20:06:22.171608500+08:00" level=info msg="[TCP] 127.0.0.1:51450 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:06:33.174250500+08:00" level=info msg="[TCP] 127.0.0.1:51491 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:06:44.167019800+08:00" level=info msg="[TCP] 127.0.0.1:51529 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:06:55.167156700+08:00" level=info msg="[TCP] 127.0.0.1:51568 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:07:06.176199500+08:00" level=info msg="[TCP] 127.0.0.1:51602 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:07:09.181531800+08:00" level=info msg="[TCP] 127.0.0.1:51610 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:07:17.169262400+08:00" level=info msg="[TCP] 127.0.0.1:51635 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:07:28.169515600+08:00" level=info msg="[TCP] 127.0.0.1:51664 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:07:39.169729000+08:00" level=info msg="[TCP] 127.0.0.1:51708 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:07:50.168107200+08:00" level=info msg="[TCP] 127.0.0.1:51724 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:08:01.171574000+08:00" level=info msg="[TCP] 127.0.0.1:51733 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:08:12.181817000+08:00" level=info msg="[TCP] 127.0.0.1:51750 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:08:23.171403700+08:00" level=info msg="[TCP] 127.0.0.1:51763 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:08:34.172951400+08:00" level=info msg="[TCP] 127.0.0.1:51796 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:08:45.177504400+08:00" level=info msg="[TCP] 127.0.0.1:51836 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:08:56.176070500+08:00" level=info msg="[TCP] 127.0.0.1:51852 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:09:00.173316500+08:00" level=info msg="[TCP] 127.0.0.1:51856 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:09:05.174432800+08:00" level=info msg="[TCP] 127.0.0.1:51870 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T20:09:07.173800300+08:00" level=info msg="[TCP] 127.0.0.1:51873 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:09:12.799000900+08:00" level=info msg="[TCP] 127.0.0.1:51885 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T20:09:14.659927200+08:00" level=info msg="[TCP] 127.0.0.1:51889 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T20:09:18.171324700+08:00" level=info msg="[TCP] 127.0.0.1:51894 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:09:29.176395500+08:00" level=info msg="[TCP] 127.0.0.1:51907 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:09:29.394631600+08:00" level=info msg="[TCP] 127.0.0.1:51910 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:09:40.172057000+08:00" level=info msg="[TCP] 127.0.0.1:51921 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:09:51.180307700+08:00" level=info msg="[TCP] 127.0.0.1:51940 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:10:02.170200800+08:00" level=info msg="[TCP] 127.0.0.1:51977 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:10:09.196557100+08:00" level=info msg="[TCP] 127.0.0.1:52002 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:10:13.169006800+08:00" level=info msg="[TCP] 127.0.0.1:52016 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:11:34.168987600+08:00" level=info msg="[TCP] 127.0.0.1:52204 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T20:11:39.988875000+08:00" level=info msg="[TCP] 127.0.0.1:52213 --> array510.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:11:41.134458300+08:00" level=info msg="[TCP] 127.0.0.1:52215 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:11:41.445054300+08:00" level=info msg="[TCP] 127.0.0.1:52217 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T20:11:45.012065200+08:00" level=info msg="[TCP] 127.0.0.1:52222 --> api.steampowered.com:443 using GLOBAL"
time="2025-06-29T20:11:47.683385700+08:00" level=info msg="[TCP] 127.0.0.1:52226 --> api-os.growingio.com:443 using GLOBAL"
time="2025-06-29T20:11:48.722393900+08:00" level=info msg="[TCP] 127.0.0.1:52228 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:11:48.756412500+08:00" level=info msg="[TCP] 127.0.0.1:52231 --> img.jyeoo.net:443 using GLOBAL"
time="2025-06-29T20:11:48.756412500+08:00" level=info msg="[TCP] 127.0.0.1:52230 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T20:11:48.904895300+08:00" level=info msg="[TCP] 127.0.0.1:52234 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T20:11:49.898283200+08:00" level=info msg="[TCP] 127.0.0.1:52237 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T20:11:56.039897400+08:00" level=info msg="[TCP] 127.0.0.1:52245 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T20:11:56.298810200+08:00" level=info msg="[TCP] 127.0.0.1:52247 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T20:11:56.300151400+08:00" level=info msg="[TCP] 127.0.0.1:52249 --> img.jyeoo.net:443 using GLOBAL"
time="2025-06-29T20:11:57.321122000+08:00" level=info msg="[TCP] 127.0.0.1:52264 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T20:17:16.173481800+08:00" level=info msg="[TCP] 127.0.0.1:52981 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:17:22.179981200+08:00" level=info msg="[TCP] 127.0.0.1:53003 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:17:22.179981200+08:00" level=info msg="[TCP] 127.0.0.1:53002 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:17:22.195207200+08:00" level=info msg="[TCP] 127.0.0.1:53006 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:17:28.671289700+08:00" level=info msg="[TCP] 127.0.0.1:53023 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-06-29T20:17:29.025268600+08:00" level=info msg="[TCP] 127.0.0.1:53025 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-06-29T20:17:29.044825100+08:00" level=info msg="[TCP] 127.0.0.1:53027 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-06-29T20:17:29.114067200+08:00" level=info msg="[TCP] 127.0.0.1:53032 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T20:17:29.114067200+08:00" level=info msg="[TCP] 127.0.0.1:53034 --> cn.bing.com:443 using GLOBAL"
time="2025-06-29T20:17:29.114067200+08:00" level=info msg="[TCP] 127.0.0.1:53030 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T20:17:29.119193800+08:00" level=info msg="[TCP] 127.0.0.1:53036 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T20:17:29.337194500+08:00" level=info msg="[TCP] 127.0.0.1:53039 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T20:17:31.144023800+08:00" level=info msg="[TCP] 127.0.0.1:53047 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:17:36.522667500+08:00" level=info msg="[TCP] 127.0.0.1:53062 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T20:17:36.523626300+08:00" level=info msg="[TCP] 127.0.0.1:53060 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T20:17:36.524922800+08:00" level=info msg="[TCP] 127.0.0.1:53064 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T20:17:36.816108500+08:00" level=info msg="[TCP] 127.0.0.1:53066 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-29T20:17:43.185138000+08:00" level=info msg="[TCP] 127.0.0.1:53099 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:17:54.179877900+08:00" level=info msg="[TCP] 127.0.0.1:53111 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:17:58.169729600+08:00" level=info msg="[TCP] 127.0.0.1:53118 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T20:18:00.175933200+08:00" level=info msg="[TCP] 127.0.0.1:53121 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:18:01.383309200+08:00" level=info msg="[TCP] 127.0.0.1:53124 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:18:01.450509900+08:00" level=info msg="[TCP] 127.0.0.1:53126 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:18:01.472734000+08:00" level=info msg="[TCP] 127.0.0.1:53128 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T20:18:01.474513800+08:00" level=info msg="[TCP] 127.0.0.1:53131 --> c.msn.cn:443 using GLOBAL"
time="2025-06-29T20:18:01.475355100+08:00" level=info msg="[TCP] 127.0.0.1:53133 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-29T20:18:01.482858000+08:00" level=info msg="[TCP] 127.0.0.1:53135 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-29T20:18:01.551976800+08:00" level=info msg="[TCP] 127.0.0.1:53137 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-29T20:18:01.643737800+08:00" level=info msg="[TCP] 127.0.0.1:53139 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:18:01.780858200+08:00" level=info msg="[TCP] 127.0.0.1:53142 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T20:18:01.781925900+08:00" level=info msg="[TCP] 127.0.0.1:53143 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T20:18:01.859517700+08:00" level=info msg="[TCP] 127.0.0.1:53146 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T20:18:01.860613100+08:00" level=info msg="[TCP] 127.0.0.1:53147 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T20:18:01.961699100+08:00" level=info msg="[TCP] 127.0.0.1:53150 --> c.bing.com:443 using GLOBAL"
time="2025-06-29T20:18:02.555736900+08:00" level=info msg="[TCP] 127.0.0.1:53153 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-29T20:18:04.192528300+08:00" level=info msg="[TCP] 127.0.0.1:53162 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:18:05.611209000+08:00" level=info msg="[TCP] 127.0.0.1:53167 --> x.com:443 using GLOBAL"
time="2025-06-29T20:18:05.614788500+08:00" level=info msg="[TCP] 127.0.0.1:53168 --> abs.twimg.com:443 using GLOBAL"
time="2025-06-29T20:18:05.700130300+08:00" level=info msg="[TCP] 127.0.0.1:53179 --> api.x.com:443 using GLOBAL"
time="2025-06-29T20:18:05.700130300+08:00" level=info msg="[TCP] 127.0.0.1:53172 --> video.twimg.com:443 using GLOBAL"
time="2025-06-29T20:18:05.700130300+08:00" level=info msg="[TCP] 127.0.0.1:53177 --> pbs.twimg.com:443 using GLOBAL"
time="2025-06-29T20:18:05.701558500+08:00" level=info msg="[TCP] 127.0.0.1:53171 --> api.twitter.com:443 using GLOBAL"
time="2025-06-29T20:18:05.702708800+08:00" level=info msg="[TCP] 127.0.0.1:53175 --> t.co:443 using GLOBAL"
time="2025-06-29T20:18:05.716218900+08:00" level=info msg="[TCP] 127.0.0.1:53181 --> abs.twimg.com:443 using GLOBAL"
time="2025-06-29T20:18:05.717492500+08:00" level=info msg="[TCP] 127.0.0.1:53183 --> abs-0.twimg.com:443 using GLOBAL"
time="2025-06-29T20:18:07.395526800+08:00" level=info msg="[TCP] 127.0.0.1:53187 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-29T20:18:07.569075100+08:00" level=info msg="[TCP] 127.0.0.1:53189 --> api.x.com:443 using GLOBAL"
time="2025-06-29T20:18:08.258233300+08:00" level=info msg="[TCP] 127.0.0.1:53191 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:18:08.449053800+08:00" level=info msg="[TCP] 127.0.0.1:53194 --> ntp.msn.com:443 using GLOBAL"
time="2025-06-29T20:18:08.995374800+08:00" level=info msg="[TCP] 127.0.0.1:53204 --> assets.msn.com:443 using GLOBAL"
time="2025-06-29T20:18:08.995374800+08:00" level=info msg="[TCP] 127.0.0.1:53200 --> c.msn.com:443 using GLOBAL"
time="2025-06-29T20:18:08.997136600+08:00" level=info msg="[TCP] 127.0.0.1:53197 --> assets.msn.com:443 using GLOBAL"
time="2025-06-29T20:18:08.997136600+08:00" level=info msg="[TCP] 127.0.0.1:53207 --> api.msn.com:443 using GLOBAL"
time="2025-06-29T20:18:08.997663100+08:00" level=info msg="[TCP] 127.0.0.1:53198 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:18:08.998171400+08:00" level=info msg="[TCP] 127.0.0.1:53203 --> c.bing.com:443 using GLOBAL"
time="2025-06-29T20:18:09.178249500+08:00" level=info msg="[TCP] 127.0.0.1:53209 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-29T20:18:09.188537300+08:00" level=info msg="[TCP] 127.0.0.1:53211 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:18:09.624477500+08:00" level=info msg="[TCP] 127.0.0.1:53213 --> login.live.com:443 using GLOBAL"
time="2025-06-29T20:18:09.772710400+08:00" level=info msg="[TCP] 127.0.0.1:53215 --> video.twimg.com:443 using GLOBAL"
time="2025-06-29T20:18:12.186975700+08:00" level=info msg="[TCP] 127.0.0.1:53219 --> assets.msn.com:443 using GLOBAL"
time="2025-06-29T20:18:14.719882300+08:00" level=info msg="[TCP] 127.0.0.1:53222 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:18:59.789995700+08:00" level=info msg="[TCP] 127.0.0.1:53324 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-06-29T20:18:59.795055100+08:00" level=info msg="[TCP] 127.0.0.1:53326 --> c.msn.com:443 using GLOBAL"
time="2025-06-29T20:18:59.798345100+08:00" level=info msg="[TCP] 127.0.0.1:53328 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-29T20:18:59.859339200+08:00" level=info msg="[TCP] 127.0.0.1:53334 --> r.bing.com:443 using GLOBAL"
time="2025-06-29T20:18:59.860431600+08:00" level=info msg="[TCP] 127.0.0.1:53335 --> r.msftstatic.com:443 using GLOBAL"
time="2025-06-29T20:18:59.860431600+08:00" level=info msg="[TCP] 127.0.0.1:53337 --> assets.msn.com:443 using GLOBAL"
time="2025-06-29T20:18:59.860944300+08:00" level=info msg="[TCP] 127.0.0.1:53330 --> assets.msn.com:443 using GLOBAL"
time="2025-06-29T20:18:59.863268500+08:00" level=info msg="[TCP] 127.0.0.1:53332 --> r.bing.com:443 using GLOBAL"
time="2025-06-29T20:18:59.863268500+08:00" level=info msg="[TCP] 127.0.0.1:53338 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:18:59.863783300+08:00" level=info msg="[TCP] 127.0.0.1:53342 --> r.msftstatic.com:443 using GLOBAL"
time="2025-06-29T20:18:59.978759400+08:00" level=info msg="[TCP] 127.0.0.1:53344 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:19:00.094727300+08:00" level=info msg="[TCP] 127.0.0.1:53346 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-06-29T20:19:00.096714400+08:00" level=info msg="[TCP] 127.0.0.1:53348 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-06-29T20:19:00.156700800+08:00" level=info msg="[TCP] 127.0.0.1:53351 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-06-29T20:19:00.159364800+08:00" level=info msg="[TCP] 127.0.0.1:53350 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-06-29T20:19:00.161956100+08:00" level=info msg="[TCP] 127.0.0.1:53354 --> c.bing.com:443 using GLOBAL"
time="2025-06-29T20:19:00.857028500+08:00" level=info msg="[TCP] 127.0.0.1:53357 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-06-29T20:19:01.168639900+08:00" level=info msg="[TCP] 127.0.0.1:53359 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:19:04.771867700+08:00" level=info msg="[TCP] 127.0.0.1:53363 --> ntp.msn.com:443 using GLOBAL"
time="2025-06-29T20:19:05.258331900+08:00" level=info msg="[TCP] 127.0.0.1:53366 --> api.msn.com:443 using GLOBAL"
time="2025-06-29T20:19:05.258877700+08:00" level=info msg="[TCP] 127.0.0.1:53368 --> th.bing.com:443 using GLOBAL"
time="2025-06-29T20:19:05.260421800+08:00" level=info msg="[TCP] 127.0.0.1:53370 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-06-29T20:19:09.168712100+08:00" level=info msg="[TCP] 127.0.0.1:53374 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:19:09.176785100+08:00" level=info msg="[TCP] 127.0.0.1:53376 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:19:09.181671300+08:00" level=info msg="[TCP] 127.0.0.1:53378 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T20:19:14.772358000+08:00" level=info msg="[TCP] 127.0.0.1:53394 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:19:15.791490100+08:00" level=info msg="[TCP] 127.0.0.1:53397 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T20:19:35.952373000+08:00" level=info msg="[TCP] 127.0.0.1:53420 --> web.telegram.org:443 using GLOBAL"
time="2025-06-29T20:19:35.956620700+08:00" level=info msg="[TCP] 127.0.0.1:53419 --> web.telegram.org:443 using GLOBAL"
time="2025-06-29T20:19:38.316646000+08:00" level=info msg="[TCP] 127.0.0.1:53430 --> telegram.me:443 using GLOBAL"
time="2025-06-29T20:19:38.317700700+08:00" level=info msg="[TCP] 127.0.0.1:53429 --> t.me:443 using GLOBAL"
time="2025-06-29T20:19:39.318566600+08:00" level=info msg="[TCP] 127.0.0.1:53437 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:19:39.681511700+08:00" level=info msg="[TCP] 127.0.0.1:53439 --> web.telegram.org:443 using GLOBAL"
time="2025-06-29T20:19:40.029399000+08:00" level=info msg="[TCP] 127.0.0.1:53442 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-06-29T20:19:40.825650900+08:00" level=info msg="[TCP] 127.0.0.1:53444 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-06-29T20:19:41.826430900+08:00" level=info msg="[TCP] 127.0.0.1:53448 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-06-29T20:19:42.242490700+08:00" level=info msg="[TCP] 127.0.0.1:53452 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-06-29T20:19:42.252016300+08:00" level=info msg="[TCP] 127.0.0.1:53454 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-06-29T20:19:42.380935800+08:00" level=info msg="[TCP] 127.0.0.1:53456 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-06-29T20:19:45.494764200+08:00" level=info msg="[TCP] 127.0.0.1:53460 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-06-29T20:19:54.584469800+08:00" level=info msg="[TCP] 127.0.0.1:53477 --> r.bing.com:443 using GLOBAL"
time="2025-06-29T20:19:54.584469800+08:00" level=info msg="[TCP] 127.0.0.1:53478 --> r.bing.com:443 using GLOBAL"
time="2025-06-29T20:19:56.750695100+08:00" level=info msg="[TCP] 127.0.0.1:53484 --> www.youtube.com:443 using GLOBAL"
time="2025-06-29T20:19:56.751907900+08:00" level=info msg="[TCP] 127.0.0.1:53483 --> www.youtube.com:443 using GLOBAL"
time="2025-06-29T20:19:56.752927700+08:00" level=info msg="[TCP] 127.0.0.1:53485 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:19:57.*********+08:00" level=info msg="[TCP] 127.0.0.1:53489 --> i.ytimg.com:443 using GLOBAL"
time="2025-06-29T20:19:57.*********+08:00" level=info msg="[TCP] 127.0.0.1:53491 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-06-29T20:19:57.*********+08:00" level=info msg="[TCP] 127.0.0.1:53493 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:19:57.*********+08:00" level=info msg="[TCP] 127.0.0.1:53495 --> accounts.google.com:443 using GLOBAL"
time="2025-06-29T20:19:58.*********+08:00" level=info msg="[TCP] 127.0.0.1:53498 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-29T20:19:58.*********+08:00" level=info msg="[TCP] 127.0.0.1:53500 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:19:58.*********+08:00" level=info msg="[TCP] 127.0.0.1:53502 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:19:58.*********+08:00" level=info msg="[TCP] 127.0.0.1:53504 --> www.google.com:443 using GLOBAL"
time="2025-06-29T20:19:58.*********+08:00" level=info msg="[TCP] 127.0.0.1:53506 --> youtube.com:443 using GLOBAL"
time="2025-06-29T20:19:58.*********+08:00" level=info msg="[TCP] 127.0.0.1:53508 --> www.google.com.tw:443 using GLOBAL"
time="2025-06-29T20:19:58.*********+08:00" level=info msg="[TCP] 127.0.0.1:53510 --> xpaywalletcdn-prod.azureedge.net:443 using GLOBAL"
time="2025-06-29T20:19:58.*********+08:00" level=info msg="[TCP] 127.0.0.1:53512 --> static.doubleclick.net:443 using GLOBAL"
time="2025-06-29T20:19:58.971306300+08:00" level=info msg="[TCP] 127.0.0.1:53514 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:19:59.139445400+08:00" level=info msg="[TCP] 127.0.0.1:53516 --> play.google.com:443 using GLOBAL"
time="2025-06-29T20:19:59.139445400+08:00" level=info msg="[TCP] 127.0.0.1:53518 --> play.google.com:443 using GLOBAL"
time="2025-06-29T20:19:59.525606700+08:00" level=info msg="[TCP] 127.0.0.1:53520 --> ntp.msn.com:443 using GLOBAL"
time="2025-06-29T20:20:00.168565100+08:00" level=info msg="[TCP] 127.0.0.1:53523 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:20:00.431120600+08:00" level=info msg="[TCP] 127.0.0.1:53526 --> c.msn.com:443 using GLOBAL"
time="2025-06-29T20:20:00.431120600+08:00" level=info msg="[TCP] 127.0.0.1:53537 --> th.bing.com:443 using GLOBAL"
time="2025-06-29T20:20:00.432276400+08:00" level=info msg="[TCP] 127.0.0.1:53529 --> assets.msn.com:443 using GLOBAL"
time="2025-06-29T20:20:00.432276400+08:00" level=info msg="[TCP] 127.0.0.1:53539 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-29T20:20:00.432786400+08:00" level=info msg="[TCP] 127.0.0.1:53533 --> api.msn.com:443 using GLOBAL"
time="2025-06-29T20:20:00.432902800+08:00" level=info msg="[TCP] 127.0.0.1:53527 --> c.bing.com:443 using GLOBAL"
time="2025-06-29T20:20:00.433410300+08:00" level=info msg="[TCP] 127.0.0.1:53525 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:20:00.433410300+08:00" level=info msg="[TCP] 127.0.0.1:53532 --> assets.msn.com:443 using GLOBAL"
time="2025-06-29T20:20:00.*********+08:00" level=info msg="[TCP] 127.0.0.1:53541 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-06-29T20:20:00.*********+08:00" level=info msg="[TCP] 127.0.0.1:53543 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:20:03.*********+08:00" level=info msg="[TCP] 127.0.0.1:53548 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:20:05.*********+08:00" level=info msg="[TCP] 127.0.0.1:53555 --> accounts.google.com:443 using GLOBAL"
time="2025-06-29T20:20:05.*********+08:00" level=info msg="[TCP] 127.0.0.1:53557 --> accounts.google.com:443 using GLOBAL"
time="2025-06-29T20:20:06.*********+08:00" level=info msg="[TCP] 127.0.0.1:53559 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:20:06.*********+08:00" level=info msg="[TCP] 127.0.0.1:53561 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:20:06.*********+08:00" level=info msg="[TCP] 127.0.0.1:53563 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-29T20:20:06.*********+08:00" level=info msg="[TCP] 127.0.0.1:53565 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:20:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:53568 --> accounts.youtube.com:443 using GLOBAL"
time="2025-06-29T20:20:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:53570 --> play.google.com:443 using GLOBAL"
time="2025-06-29T20:20:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:53572 --> play.google.com:443 using GLOBAL"
time="2025-06-29T20:20:09.*********+08:00" level=info msg="[TCP] 127.0.0.1:53575 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T20:20:10.*********+08:00" level=info msg="[TCP] 127.0.0.1:53579 --> ssl.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:20:14.*********+08:00" level=info msg="[TCP] 127.0.0.1:53584 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:20:17.*********+08:00" level=info msg="[TCP] 127.0.0.1:53589 --> play.google.com:443 using GLOBAL"
time="2025-06-29T20:20:29.*********+08:00" level=info msg="[TCP] 127.0.0.1:53609 --> accounts.youtube.com:443 using GLOBAL"
time="2025-06-29T20:20:29.*********+08:00" level=info msg="[TCP] 127.0.0.1:53611 --> accounts.google.com.sg:443 using GLOBAL"
time="2025-06-29T20:20:29.*********+08:00" level=info msg="[TCP] 127.0.0.1:53615 --> accounts.google.com.tw:443 using GLOBAL"
time="2025-06-29T20:20:30.*********+08:00" level=info msg="[TCP] 127.0.0.1:53618 --> i.ytimg.com:443 using GLOBAL"
time="2025-06-29T20:20:30.*********+08:00" level=info msg="[TCP] 127.0.0.1:53620 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:20:32.*********+08:00" level=info msg="[TCP] 127.0.0.1:53623 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-29T20:20:32.*********+08:00" level=info msg="[TCP] 127.0.0.1:53626 --> rr4---sn-q4fl6nlz.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:32.*********+08:00" level=info msg="[TCP] 127.0.0.1:53625 --> rr4---sn-q4fl6nlz.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:32.*********+08:00" level=info msg="[TCP] 127.0.0.1:53629 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:20:32.*********+08:00" level=info msg="[TCP] 127.0.0.1:53631 --> www.google.com.tw:443 using GLOBAL"
time="2025-06-29T20:20:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:53634 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:20:33.357390900+08:00" level=info msg="[TCP] 127.0.0.1:53636 --> yt3.ggpht.com:443 using GLOBAL"
time="2025-06-29T20:20:33.357390900+08:00" level=info msg="[TCP] 127.0.0.1:53638 --> yt3.ggpht.com:443 using GLOBAL"
time="2025-06-29T20:20:33.358743300+08:00" level=info msg="[TCP] 127.0.0.1:53639 --> yt3.ggpht.com:443 using GLOBAL"
time="2025-06-29T20:20:33.391624500+08:00" level=info msg="[TCP] 127.0.0.1:53642 --> yt3.ggpht.com:443 using GLOBAL"
time="2025-06-29T20:20:33.397549100+08:00" level=info msg="[TCP] 127.0.0.1:53644 --> www.youtube.com:443 using GLOBAL"
time="2025-06-29T20:20:33.410002700+08:00" level=info msg="[TCP] 127.0.0.1:53646 --> play.google.com:443 using GLOBAL"
time="2025-06-29T20:20:33.425220300+08:00" level=info msg="[TCP] 127.0.0.1:53648 --> rr4---sn-q4fl6nlz.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:53650 --> rr4---sn-q4fl6nlz.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:53652 --> yt3.ggpht.com:443 using GLOBAL"
time="2025-06-29T20:20:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:53654 --> youtube.com:443 using GLOBAL"
time="2025-06-29T20:20:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:53656 --> accounts.youtube.com:443 using GLOBAL"
time="2025-06-29T20:20:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:53658 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:20:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:53660 --> rr3---sn-un57enel.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:53662 --> rr4---sn-q4fl6nlz.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:53664 --> rr4---sn-q4fl6nlz.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:53666 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:20:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:53668 --> static.doubleclick.net:443 using GLOBAL"
time="2025-06-29T20:20:35.*********+08:00" level=info msg="[TCP] 127.0.0.1:53671 --> rr3---sn-un57enel.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:36.*********+08:00" level=info msg="[TCP] 127.0.0.1:53674 --> rr5---sn-un57enel.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:38.394826400+08:00" level=info msg="[TCP] 127.0.0.1:53677 --> rr2---sn-un57enez.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:40.110935400+08:00" level=info msg="[TCP] 127.0.0.1:53682 --> rr5---sn-un57sne7.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:41.347962700+08:00" level=info msg="[TCP] 127.0.0.1:53685 --> rr5---sn-un57sne7.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:42.442140200+08:00" level=info msg="[TCP] 127.0.0.1:53688 --> rr3---sn-un57enez.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:46.904951600+08:00" level=info msg="[TCP] 127.0.0.1:53696 --> rr4---sn-un57sn7y.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:48.270917300+08:00" level=info msg="[TCP] 127.0.0.1:53699 --> rr5---sn-un57ene6.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:48.801108500+08:00" level=info msg="[TCP] 127.0.0.1:53701 --> rr5---sn-un57ene6.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:48.808088700+08:00" level=info msg="[TCP] 127.0.0.1:53703 --> tpc.googlesyndication.com:443 using GLOBAL"
time="2025-06-29T20:20:48.822404900+08:00" level=info msg="[TCP] 127.0.0.1:53705 --> rr2---sn-un57snee.googlevideo.com:443 using GLOBAL"
time="2025-06-29T20:20:49.132086800+08:00" level=info msg="[TCP] 127.0.0.1:53707 --> tpc.googlesyndication.com:443 using GLOBAL"
time="2025-06-29T20:20:49.660248600+08:00" level=info msg="[TCP] 127.0.0.1:53709 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-29T20:20:49.683263600+08:00" level=info msg="[TCP] 127.0.0.1:53711 --> pagead2.googlesyndication.com:443 using GLOBAL"
time="2025-06-29T20:20:49.843975800+08:00" level=info msg="[TCP] 127.0.0.1:53714 --> lh6.googleusercontent.com:443 using GLOBAL"
time="2025-06-29T20:20:50.728528200+08:00" level=info msg="[TCP] 127.0.0.1:53716 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-29T20:20:51.258736200+08:00" level=info msg="[TCP] 127.0.0.1:53720 --> edgeassetservice.azureedge.net:443 using GLOBAL"
time="2025-06-29T20:20:56.028655400+08:00" level=info msg="[TCP] 127.0.0.1:53735 --> i1.hdslb.com:443 using GLOBAL"
time="2025-06-29T20:20:56.469467400+08:00" level=info msg="[TCP] 127.0.0.1:53737 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-29T20:21:00.172511700+08:00" level=info msg="[TCP] 127.0.0.1:53741 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:21:06.368809400+08:00" level=info msg="[TCP] 127.0.0.1:53747 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:21:09.172478100+08:00" level=info msg="[TCP] 127.0.0.1:53751 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:21:09.181041100+08:00" level=info msg="[TCP] 127.0.0.1:53753 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T20:21:14.887720400+08:00" level=info msg="[TCP] 127.0.0.1:53760 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:21:55.183470900+08:00" level=info msg="[TCP] 127.0.0.1:53798 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:21:55.185008500+08:00" level=info msg="[TCP] 127.0.0.1:53796 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:22:06.173428800+08:00" level=info msg="[TCP] 127.0.0.1:53807 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:22:09.177572000+08:00" level=info msg="[TCP] 127.0.0.1:53812 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:22:14.949903300+08:00" level=info msg="[TCP] 127.0.0.1:53818 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:22:16.189537300+08:00" level=info msg="[TCP] 127.0.0.1:53820 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:22:27.175568700+08:00" level=info msg="[TCP] 127.0.0.1:53831 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:22:38.165625500+08:00" level=info msg="[TCP] 127.0.0.1:53842 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:22:49.168528400+08:00" level=info msg="[TCP] 127.0.0.1:53858 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:23:00.166334900+08:00" level=info msg="[TCP] 127.0.0.1:53869 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:23:11.173899300+08:00" level=info msg="[TCP] 127.0.0.1:53895 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:23:15.002139000+08:00" level=info msg="[TCP] 127.0.0.1:53904 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:23:22.165787800+08:00" level=info msg="[TCP] 127.0.0.1:53913 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:23:33.168200600+08:00" level=info msg="[TCP] 127.0.0.1:53945 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:23:44.168870800+08:00" level=info msg="[TCP] 127.0.0.1:53975 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:23:55.172476900+08:00" level=info msg="[TCP] 127.0.0.1:54010 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:24:00.181519900+08:00" level=info msg="[TCP] 127.0.0.1:54023 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:24:06.174697800+08:00" level=info msg="[TCP] 127.0.0.1:54045 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:24:06.174697800+08:00" level=info msg="[TCP] 127.0.0.1:54044 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:24:15.061363400+08:00" level=info msg="[TCP] 127.0.0.1:54099 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:24:17.174193900+08:00" level=info msg="[TCP] 127.0.0.1:54107 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T20:24:48.181807800+08:00" level=info msg="[TCP] 127.0.0.1:54155 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:24:54.586834700+08:00" level=info msg="[TCP] 127.0.0.1:54166 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T20:25:09.184690100+08:00" level=info msg="[TCP] 127.0.0.1:54184 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T20:25:15.115893200+08:00" level=info msg="[TCP] 127.0.0.1:54191 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T20:25:22.174040900+08:00" level=info msg="[TCP] 127.0.0.1:54199 --> play.google.com:443 using GLOBAL"
time="2025-06-29T20:25:32.179473700+08:00" level=info msg="[TCP] 127.0.0.1:54208 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:25:32.179473700+08:00" level=info msg="[TCP] 127.0.0.1:54207 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-29T20:32:58.077938900+08:00" level=info msg="[TCP] 127.0.0.1:55130 --> storage.live.com:443 using GLOBAL"
time="2025-06-29T20:54:48.014845800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57602 --> api.onedrive.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T21:17:58.028833500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59838 --> my.microsoftpersonalcontent.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-29T21:33:06.024447100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61771 --> storage.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-30T00:14:28.850583200+08:00" level=warning msg="Mihomo shutting down"
