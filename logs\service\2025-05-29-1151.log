Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-05-29T11:51:13.866395300+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-29T11:51:13.874094100+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-29T11:51:13.874094100+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-29T11:51:13.875630900+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-05-29T11:51:13.898298500+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-29T11:51:13.898298500+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-05-29T11:51:14.175445000+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-29T11:51:14.175445000+08:00" level=info msg="Load GeoSite rule: private"
time="2025-05-29T11:51:14.192302700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-29T11:51:14.194853300+08:00" level=info msg="Initial configuration complete, total time: 323ms"
time="2025-05-29T11:51:14.195874300+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-05-29T11:51:14.197490000+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-29T11:51:14.198000600+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-05-29T11:51:14.198000600+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-05-29T11:51:14.198000600+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Lan"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider NowE"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider apple"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider BBC"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider TVer"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Discord"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Line"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider telegram"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Disney"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider TVB"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider google"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-29T11:51:14.206689200+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-29T11:51:16.336413800+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-29T11:51:16.336923800+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-29T11:51:16.336923800+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-29T11:51:16.337432900+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-29T11:51:16.337432900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-29T11:51:16.337432900+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-29T11:51:16.338453700+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-05-29T11:51:19.209265900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209370500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209265900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-05-29T11:51:19.209265900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209265900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209265900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-05-29T11:51:19.209265900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209265900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209370500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-05-29T11:51:19.209370500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-05-29T11:51:19.209370500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209370500+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-05-29T11:51:19.209880300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209880300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209880300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209880300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209880300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209880300+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-05-29T11:51:19.209880300+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-05-29T11:51:19.209880300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209880300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.209880300+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-05-29T11:51:19.209880300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-05-29T11:51:19.209880300+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-05-29T11:51:19.209880300+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-05-29T11:51:19.209880300+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-05-29T11:51:19.210390700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210390700+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-05-29T11:51:19.210900900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:19.210900900+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-05-29T11:51:19.257276500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-29T11:51:19.257276500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-29T11:51:19.257276500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-29T11:51:19.257276500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider BBC"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Line"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider telegram"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider TVer"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider google"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider apple"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider NowE"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Lan"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Discord"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider TVB"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Disney"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-29T11:51:19.258801200+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260165300+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-05-29T11:51:24.260688600+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.259662400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-05-29T11:51:24.259662400+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-05-29T11:51:24.260695800+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-05-29T11:51:24.260185900+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-05-29T11:51:24.262734300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-29T11:51:24.262734300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-29T11:51:24.262734300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-29T11:51:24.262734300+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-29T11:51:24.565202200+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-29T11:51:24.565727600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-29T11:51:24.565727600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-29T11:51:24.566745500+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-29T11:51:24.566745500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-29T11:51:24.566745500+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-29T11:51:24.568274500+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider TVer"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Line"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Discord"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Disney"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider telegram"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Lan"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider BBC"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider apple"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider TVB"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider google"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider NowE"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-29T11:51:24.569296300+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-29T11:51:24.789924000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.789924000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.789924000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.791110400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.791110400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.791110400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.792606400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.792606400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.792606400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.792606400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.792606400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.793120000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.793120000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.795732000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.795732000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.795732000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.795732000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.795732000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.795732000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.795732000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.796245200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.796245200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.796245200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.796245200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.796245200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.801627100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.801627100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.801627100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.801627100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.801627100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802139200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802139200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802139200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802139200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802649000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802649000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802649000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802649000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802649000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802649000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802649000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.802649000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.803158400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.803158400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.803158400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.803158400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.812364300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.812364300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:24.812364300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-05-29T11:51:25.366120300+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-05-29T11:51:25.384347200+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-05-29T11:51:25.384347200+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-05-29T11:51:25.395928400+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-05-29T11:51:25.402907000+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-05-29T11:51:25.403601600+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-05-29T11:51:25.407837500+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-05-29T11:51:25.413867600+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-05-29T11:51:25.416169000+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-05-29T11:51:25.416812500+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-05-29T11:51:25.423486100+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-05-29T11:51:25.426523300+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-05-29T11:51:25.426523300+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-05-29T11:51:25.430193800+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-05-29T11:51:25.437399800+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-05-29T11:51:25.445337400+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-05-29T11:51:25.445337400+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-05-29T11:51:25.460238600+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-05-29T11:51:25.462719300+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-05-29T11:51:25.468732800+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-05-29T11:51:25.468732800+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-05-29T11:51:25.472495000+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-05-29T11:51:25.493355100+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-05-29T11:51:25.496872700+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-05-29T11:51:25.511624800+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-05-29T11:51:25.516851400+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-05-29T11:51:25.519485200+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-05-29T11:51:25.539211900+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-05-29T11:51:25.563152300+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-05-29T11:51:25.638509900+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-05-29T11:51:25.814143500+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-05-29T11:51:25.893095100+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-05-29T11:51:25.969675400+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-05-29T11:51:26.000210300+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-05-29T11:51:26.433122900+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-05-29T11:51:26.440429500+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-05-29T11:51:26.441029700+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-05-29T11:51:26.442223300+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-05-29T11:51:26.455615900+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-05-29T11:51:26.480893500+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-05-29T11:51:26.493545900+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-05-29T11:51:26.506643200+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-05-29T11:51:26.526967800+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-05-29T11:51:26.575292500+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-05-29T11:51:27.085723900+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-05-29T11:51:27.102234000+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-05-29T11:51:27.182593200+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-05-29T11:51:27.508176700+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-05-29T11:51:27.518822400+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-05-29T11:51:27.523522300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-29T11:51:27.523522300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-29T11:51:27.523522300+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-29T11:51:27.523522300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-29T12:07:53.131313300+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-29T12:07:53.131313300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-29T12:07:53.131313300+08:00" level=error msg="🇰🇷韩国03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-29T12:07:53.131313300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-29T12:07:53.131313300+08:00" level=error msg="🇰🇷韩国01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-29T12:07:53.131313300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-29T12:07:53.143041000+08:00" level=error msg="🇰🇷韩国01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-29T12:07:53.143041000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-29T12:08:01.175919500+08:00" level=info msg="[TCP] 127.0.0.1:52014 --> xdwlyx.com.cn:443 using GLOBAL"
time="2025-05-29T12:08:11.373540800+08:00" level=info msg="[TCP] 127.0.0.1:52024 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-29T12:08:11.710744700+08:00" level=info msg="[TCP] 127.0.0.1:52026 --> web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:08:14.601572800+08:00" level=info msg="[TCP] 127.0.0.1:52030 --> t.me:443 using GLOBAL"
time="2025-05-29T12:08:14.605374100+08:00" level=info msg="[TCP] 127.0.0.1:52032 --> telegram.me:443 using GLOBAL"
time="2025-05-29T12:08:14.825310100+08:00" level=info msg="[TCP] 127.0.0.1:52034 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-29T12:08:14.911808800+08:00" level=info msg="[TCP] 127.0.0.1:52036 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-29T12:08:14.913057000+08:00" level=info msg="[TCP] 127.0.0.1:52038 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-29T12:08:15.773754400+08:00" level=info msg="[TCP] 127.0.0.1:52040 --> web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:08:16.241467500+08:00" level=info msg="[TCP] 127.0.0.1:52043 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:08:18.276606200+08:00" level=info msg="[TCP] 127.0.0.1:52046 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:08:20.067101800+08:00" level=info msg="[TCP] 127.0.0.1:52050 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:08:20.149204700+08:00" level=info msg="[TCP] 127.0.0.1:52052 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:08:20.150517100+08:00" level=info msg="[TCP] 127.0.0.1:52053 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:08:20.728833800+08:00" level=info msg="[TCP] 127.0.0.1:52056 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:08:21.523623800+08:00" level=info msg="[TCP] 127.0.0.1:52058 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:08:22.448952400+08:00" level=info msg="[TCP] 127.0.0.1:52061 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:08:24.879978700+08:00" level=info msg="[TCP] 127.0.0.1:52064 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-29T12:08:26.459982100+08:00" level=info msg="[TCP] 127.0.0.1:52068 --> www.bing.com:443 using GLOBAL"
time="2025-05-29T12:09:04.062852700+08:00" level=info msg="[TCP] 127.0.0.1:52094 --> t.me:443 using GLOBAL"
time="2025-05-29T12:09:04.710857600+08:00" level=info msg="[TCP] 127.0.0.1:52096 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:09:06.085999800+08:00" level=info msg="[TCP] 127.0.0.1:52100 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:09:06.122672800+08:00" level=info msg="[TCP] 127.0.0.1:52102 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:09:06.311129200+08:00" level=info msg="[TCP] 127.0.0.1:52105 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:09:06.312386600+08:00" level=info msg="[TCP] 127.0.0.1:52104 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:09:07.084827300+08:00" level=info msg="[TCP] 127.0.0.1:52108 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:09:08.660778100+08:00" level=info msg="[TCP] 127.0.0.1:52112 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:09:09.495664900+08:00" level=info msg="[TCP] 127.0.0.1:52114 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-29T12:09:26.519445600+08:00" level=info msg="[TCP] 127.0.0.1:52127 --> www.bing.com:443 using GLOBAL"
time="2025-05-29T12:10:17.733039000+08:00" level=info msg="[TCP] 127.0.0.1:52163 --> zws1-1.web.telegram.org:443 using GLOBAL"
