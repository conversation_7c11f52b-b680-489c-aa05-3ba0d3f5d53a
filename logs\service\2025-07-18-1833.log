Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-18T18:33:38.027583300+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-18T18:33:38.036314500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-18T18:33:38.036314500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-18T18:33:38.037362800+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-18T18:33:38.059339300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-18T18:33:38.059339300+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-18T18:33:38.382572500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-18T18:33:38.383075400+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-18T18:33:38.397471200+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-18T18:33:38.401474800+08:00" level=info msg="Initial configuration complete, total time: 369ms"
time="2025-07-18T18:33:38.401474800+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-18T18:33:38.402977900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-18T18:33:38.402977900+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-18T18:33:38.402977900+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-18T18:33:38.402977900+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider apple"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider telegram"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Discord"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider BBC"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider NowE"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider TVer"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Disney"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Lan"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider TVB"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Line"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider google"
time="2025-07-18T18:33:38.411036000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-18T18:33:43.413392900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-18T18:33:43.413532500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-18T18:33:43.413532500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-18T18:33:43.413532500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-18T18:33:43.413532500+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-18T18:33:43.413532500+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-18T18:33:43.413532500+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-18T18:33:43.413532500+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.413532500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-18T18:33:43.415112800+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-18T18:33:43.415112800+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-18T18:33:43.415112800+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-18T18:33:43.414092900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-18T18:33:43.415112800+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-18T18:33:43.414600200+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-18T18:33:43.417174000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-18T18:33:43.417174000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-18T18:33:43.417174000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-18T18:33:43.417174000+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-18T18:33:48.417214500+08:00" level=error msg="🇻🇳越南01 [1.5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T18:33:48.417214500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T18:33:48.718284300+08:00" level=error msg="🇭🇰香港04 [1.5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp ************:51164->**************:19815: use of closed network connection"
time="2025-07-18T18:33:48.718284300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T18:33:48.933392200+08:00" level=info msg="[TCP] 127.0.0.1:51262 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:33:48.946119600+08:00" level=info msg="[TCP] 127.0.0.1:51266 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-18T18:33:48.958934400+08:00" level=info msg="[TCP] 127.0.0.1:51271 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-18T18:33:48.974889800+08:00" level=info msg="[TCP] 127.0.0.1:51277 --> google.com:443 using GLOBAL"
time="2025-07-18T18:33:48.974889800+08:00" level=info msg="[TCP] 127.0.0.1:51274 --> login.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:33:48.976754600+08:00" level=info msg="[TCP] 127.0.0.1:51280 --> google.com:443 using GLOBAL"
time="2025-07-18T18:33:48.979076700+08:00" level=info msg="[TCP] 127.0.0.1:51281 --> google.com:443 using GLOBAL"
time="2025-07-18T18:33:48.983421200+08:00" level=info msg="[TCP] 127.0.0.1:51284 --> google.com:443 using GLOBAL"
time="2025-07-18T18:33:49.082413600+08:00" level=info msg="[TCP] 127.0.0.1:51291 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:33:49.100160500+08:00" level=info msg="[TCP] 127.0.0.1:51297 --> google.com:443 using GLOBAL"
time="2025-07-18T18:33:49.101416400+08:00" level=info msg="[TCP] 127.0.0.1:51294 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-18T18:33:49.216781900+08:00" level=info msg="[TCP] 127.0.0.1:51301 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:33:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:51304 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-18T18:33:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:51307 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:33:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:51310 --> us.i.posthog.com:443 using GLOBAL"
time="2025-07-18T18:33:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:51313 --> accounts.google.com:443 using GLOBAL"
time="2025-07-18T18:33:50.*********+08:00" level=error msg="🇻🇳越南01 [1.5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T18:33:50.*********+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T18:33:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:51317 --> us.i.posthog.com:443 using GLOBAL"
time="2025-07-18T18:33:52.*********+08:00" level=info msg="[TCP] 127.0.0.1:51321 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-07-18T18:33:52.*********+08:00" level=info msg="[TCP] 127.0.0.1:51327 --> ssl.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:33:52.*********+08:00" level=info msg="[TCP] 127.0.0.1:51324 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:33:53.899424600+08:00" level=info msg="[TCP] 127.0.0.1:51331 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:33:55.258632900+08:00" level=info msg="[TCP] 127.0.0.1:51336 --> play.google.com:443 using GLOBAL"
time="2025-07-18T18:33:55.258632900+08:00" level=info msg="[TCP] 127.0.0.1:51335 --> play.google.com:443 using GLOBAL"
time="2025-07-18T18:33:55.293241100+08:00" level=info msg="[TCP] 127.0.0.1:51341 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:33:56.008694900+08:00" level=info msg="[TCP] 127.0.0.1:51344 --> play.google.com:443 using GLOBAL"
time="2025-07-18T18:33:57.750141100+08:00" level=info msg="[TCP] 127.0.0.1:51348 --> auth.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:33:58.322205400+08:00" level=info msg="[TCP] 127.0.0.1:51352 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-18T18:33:58.325215700+08:00" level=info msg="[TCP] 127.0.0.1:51355 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:33:58.338595300+08:00" level=info msg="[TCP] 127.0.0.1:51358 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-18T18:34:01.508719800+08:00" level=info msg="[TCP] 127.0.0.1:51363 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:01.858156200+08:00" level=info msg="[TCP] 127.0.0.1:51366 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:04.778708200+08:00" level=info msg="[TCP] 127.0.0.1:51372 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:06.134340600+08:00" level=info msg="[TCP] 127.0.0.1:51378 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:07.326681200+08:00" level=info msg="[TCP] 127.0.0.1:51384 --> cdn.auth0.com:443 using GLOBAL"
time="2025-07-18T18:34:07.716988300+08:00" level=info msg="[TCP] 127.0.0.1:51387 --> www.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:34:07.735362700+08:00" level=info msg="[TCP] 127.0.0.1:51390 --> cdn.auth0.com:443 using GLOBAL"
time="2025-07-18T18:34:08.049592300+08:00" level=info msg="[TCP] 127.0.0.1:51393 --> cdn.auth0.com:443 using GLOBAL"
time="2025-07-18T18:34:09.177094800+08:00" level=info msg="[TCP] 127.0.0.1:51398 --> fs.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:09.486647800+08:00" level=info msg="[TCP] 127.0.0.1:51401 --> fs.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:10.901315300+08:00" level=info msg="[TCP] 127.0.0.1:51406 --> cdn.auth0.com:443 using GLOBAL"
time="2025-07-18T18:34:14.232621700+08:00" level=info msg="[TCP] 127.0.0.1:51411 --> www.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:34:22.339952800+08:00" level=info msg="[TCP] 127.0.0.1:51422 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:34:22.342410100+08:00" level=info msg="[TCP] 127.0.0.1:51423 --> lf-package-cn.feishucdn.com:443 using GLOBAL"
time="2025-07-18T18:34:22.972498200+08:00" level=info msg="[TCP] 127.0.0.1:51429 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-07-18T18:34:23.128899200+08:00" level=info msg="[TCP] 127.0.0.1:51432 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:24.285835300+08:00" level=info msg="[TCP] 127.0.0.1:51436 --> www.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:34:25.473490900+08:00" level=info msg="[TCP] 127.0.0.1:51440 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-18T18:34:29.512867700+08:00" level=info msg="[TCP] 127.0.0.1:51445 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-18T18:34:30.434120200+08:00" level=info msg="[TCP] 127.0.0.1:51449 --> www.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:34:31.797102000+08:00" level=info msg="[TCP] 127.0.0.1:51453 --> www.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:34:31.805670800+08:00" level=info msg="[TCP] 127.0.0.1:51456 --> storage.googleapis.com:443 using GLOBAL"
time="2025-07-18T18:34:31.875261400+08:00" level=info msg="[TCP] 127.0.0.1:51459 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:34:31.886572700+08:00" level=info msg="[TCP] 127.0.0.1:51462 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-18T18:34:31.914920600+08:00" level=info msg="[TCP] 127.0.0.1:51465 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-07-18T18:34:32.022768700+08:00" level=info msg="[TCP] 127.0.0.1:51468 --> api.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:34:32.024454900+08:00" level=info msg="[TCP] 127.0.0.1:51471 --> analytics.twitter.com:443 using GLOBAL"
time="2025-07-18T18:34:32.024454900+08:00" level=info msg="[TCP] 127.0.0.1:51472 --> us-assets.i.posthog.com:443 using GLOBAL"
time="2025-07-18T18:34:32.216520600+08:00" level=info msg="[TCP] 127.0.0.1:51477 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-07-18T18:34:32.720903600+08:00" level=info msg="[TCP] 127.0.0.1:51481 --> google.com:443 using GLOBAL"
time="2025-07-18T18:34:32.720903600+08:00" level=info msg="[TCP] 127.0.0.1:51484 --> google.com:443 using GLOBAL"
time="2025-07-18T18:34:32.866978100+08:00" level=info msg="[TCP] 127.0.0.1:51487 --> google.com:443 using GLOBAL"
time="2025-07-18T18:34:32.879022800+08:00" level=info msg="[TCP] 127.0.0.1:51490 --> google.com:443 using GLOBAL"
time="2025-07-18T18:34:32.997098600+08:00" level=info msg="[TCP] 127.0.0.1:51493 --> google.com:443 using GLOBAL"
time="2025-07-18T18:34:35.657699400+08:00" level=info msg="[TCP] 127.0.0.1:51498 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-18T18:34:37.030903400+08:00" level=info msg="[TCP] 127.0.0.1:51501 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:37.922547700+08:00" level=info msg="[TCP] 127.0.0.1:51505 --> www.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:34:38.021640400+08:00" level=info msg="[TCP] 127.0.0.1:51508 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:38.172561900+08:00" level=info msg="[TCP] 127.0.0.1:51511 --> us-assets.i.posthog.com:443 using GLOBAL"
time="2025-07-18T18:34:38.270693600+08:00" level=info msg="[TCP] 127.0.0.1:51514 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:38.577518500+08:00" level=info msg="[TCP] 127.0.0.1:51517 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-07-18T18:34:42.976941800+08:00" level=info msg="[TCP] 127.0.0.1:51525 --> www.bing.com:443 using GLOBAL"
time="2025-07-18T18:34:44.365914900+08:00" level=info msg="[TCP] 127.0.0.1:51529 --> js-232.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:34:44.432814100+08:00" level=info msg="[TCP] 127.0.0.1:51532 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:34:44.540234300+08:00" level=info msg="[TCP] 127.0.0.1:51535 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:34:45.135077600+08:00" level=info msg="[TCP] 127.0.0.1:51539 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:34:45.600239800+08:00" level=info msg="[TCP] 127.0.0.1:51542 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:34:45.607576800+08:00" level=info msg="[TCP] 127.0.0.1:51545 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:34:46.219959000+08:00" level=info msg="[TCP] 127.0.0.1:51549 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:34:46.219959000+08:00" level=info msg="[TCP] 127.0.0.1:51548 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:34:46.819308200+08:00" level=info msg="[TCP] 127.0.0.1:51555 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:34:50.520776100+08:00" level=info msg="[TCP] 127.0.0.1:51560 --> js-232.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:34:51.031712500+08:00" level=info msg="[TCP] 127.0.0.1:51567 --> net.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.032741700+08:00" level=info msg="[TCP] 127.0.0.1:51564 --> ingest.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.034138400+08:00" level=info msg="[TCP] 127.0.0.1:51570 --> net.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.067048800+08:00" level=info msg="[TCP] 127.0.0.1:51573 --> net.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.073722600+08:00" level=info msg="[TCP] 127.0.0.1:51576 --> ingest.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.079852900+08:00" level=info msg="[TCP] 127.0.0.1:51579 --> ingest.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.227880600+08:00" level=info msg="[TCP] 127.0.0.1:51582 --> auth.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:34:51.254930800+08:00" level=info msg="[TCP] 127.0.0.1:51585 --> ingest.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.397479500+08:00" level=info msg="[TCP] 127.0.0.1:51588 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.414773900+08:00" level=info msg="[TCP] 127.0.0.1:51591 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.462585200+08:00" level=info msg="[TCP] 127.0.0.1:51594 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.525784900+08:00" level=info msg="[TCP] 127.0.0.1:51597 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.544733400+08:00" level=info msg="[TCP] 127.0.0.1:51600 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.797883800+08:00" level=info msg="[TCP] 127.0.0.1:51603 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:51.862127400+08:00" level=info msg="[TCP] 127.0.0.1:51607 --> turn.cloudflare.com:5349 using GLOBAL"
time="2025-07-18T18:34:51.862127400+08:00" level=info msg="[TCP] 127.0.0.1:51610 --> turn.cloudflare.com:3478 using GLOBAL"
time="2025-07-18T18:34:51.862127400+08:00" level=info msg="[TCP] 127.0.0.1:51606 --> turn.cloudflare.com:3478 using GLOBAL"
time="2025-07-18T18:34:51.863614500+08:00" level=info msg="[TCP] 127.0.0.1:51615 --> turn.cloudflare.com:5349 using GLOBAL"
time="2025-07-18T18:34:51.863614500+08:00" level=info msg="[TCP] 127.0.0.1:51612 --> turn.cloudflare.com:3478 using GLOBAL"
time="2025-07-18T18:34:51.863614500+08:00" level=info msg="[TCP] 127.0.0.1:51617 --> turn.cloudflare.com:5349 using GLOBAL"
time="2025-07-18T18:34:51.864374600+08:00" level=info msg="[TCP] 127.0.0.1:51616 --> turn.cloudflare.com:3478 using GLOBAL"
time="2025-07-18T18:34:51.864374600+08:00" level=info msg="[TCP] 127.0.0.1:51613 --> turn.cloudflare.com:5349 using GLOBAL"
time="2025-07-18T18:34:51.864933800+08:00" level=info msg="[TCP] 127.0.0.1:51614 --> turn.cloudflare.com:3478 using GLOBAL"
time="2025-07-18T18:34:51.865723700+08:00" level=info msg="[TCP] 127.0.0.1:51611 --> turn.cloudflare.com:5349 using GLOBAL"
time="2025-07-18T18:34:51.865723700+08:00" level=info msg="[TCP] 127.0.0.1:51623 --> turn.cloudflare.com:5349 using GLOBAL"
time="2025-07-18T18:34:51.868041000+08:00" level=info msg="[TCP] 127.0.0.1:51618 --> turn.cloudflare.com:3478 using GLOBAL"
time="2025-07-18T18:34:53.654361700+08:00" level=info msg="[TCP] 127.0.0.1:51643 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:34:53.977610800+08:00" level=info msg="[TCP] 127.0.0.1:51647 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-07-18T18:34:57.550545600+08:00" level=info msg="[TCP] 127.0.0.1:51652 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:57.951675800+08:00" level=info msg="[TCP] 127.0.0.1:51655 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-18T18:34:58.567484900+08:00" level=info msg="[TCP] 127.0.0.1:51658 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:58.913456500+08:00" level=info msg="[TCP] 127.0.0.1:51664 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:58.914499700+08:00" level=info msg="[TCP] 127.0.0.1:51661 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:59.128477300+08:00" level=info msg="[TCP] 127.0.0.1:51668 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:34:59.202677100+08:00" level=info msg="[TCP] 127.0.0.1:51671 --> net1.prod.verisoul.ai:443 using GLOBAL"
time="2025-07-18T18:35:15.880975900+08:00" level=info msg="[TCP] 127.0.0.1:51685 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:35:16.464761100+08:00" level=info msg="[TCP] 127.0.0.1:51688 --> mintlify.b-cdn.net:443 using GLOBAL"
time="2025-07-18T18:35:16.916718000+08:00" level=info msg="[TCP] 127.0.0.1:51691 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-18T18:35:16.931486700+08:00" level=info msg="[TCP] 127.0.0.1:51694 --> cdn.segment.com:443 using GLOBAL"
time="2025-07-18T18:35:16.934322300+08:00" level=info msg="[TCP] 127.0.0.1:51695 --> events-proxy.mintlify.com:443 using GLOBAL"
time="2025-07-18T18:35:17.247729700+08:00" level=info msg="[TCP] 127.0.0.1:51700 --> events-proxy.mintlify.com:443 using GLOBAL"
time="2025-07-18T18:35:17.247729700+08:00" level=info msg="[TCP] 127.0.0.1:51701 --> events-proxy.mintlify.com:443 using GLOBAL"
time="2025-07-18T18:35:17.259126100+08:00" level=info msg="[TCP] 127.0.0.1:51706 --> cdn.getkoala.com:443 using GLOBAL"
time="2025-07-18T18:35:17.666327400+08:00" level=info msg="[TCP] 127.0.0.1:51710 --> cdn.segment.com:443 using GLOBAL"
time="2025-07-18T18:35:22.580835600+08:00" level=info msg="[TCP] 127.0.0.1:51717 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-18T18:35:23.412664300+08:00" level=info msg="[TCP] 127.0.0.1:51722 --> cdn.getkoala.com:443 using GLOBAL"
time="2025-07-18T18:35:23.703004600+08:00" level=info msg="[TCP] 127.0.0.1:51725 --> api.getkoala.com:443 using GLOBAL"
time="2025-07-18T18:35:23.786808900+08:00" level=info msg="[TCP] 127.0.0.1:51728 --> cdn.segment.com:443 using GLOBAL"
time="2025-07-18T18:35:24.218201300+08:00" level=info msg="[TCP] 127.0.0.1:51731 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:35:24.220847100+08:00" level=info msg="[TCP] 127.0.0.1:51732 --> api.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:35:24.244292900+08:00" level=info msg="[TCP] 127.0.0.1:51737 --> analytics.twitter.com:443 using GLOBAL"
time="2025-07-18T18:35:25.520090600+08:00" level=info msg="[TCP] 127.0.0.1:51741 --> api.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:35:26.582967000+08:00" level=info msg="[TCP] 127.0.0.1:51746 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:35:35.249669700+08:00" level=info msg="[TCP] 127.0.0.1:51754 --> login.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:35:36.406690100+08:00" level=info msg="[TCP] 127.0.0.1:51758 --> www.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:35:36.424615400+08:00" level=info msg="[TCP] 127.0.0.1:51761 --> evs.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:35:36.476147100+08:00" level=info msg="[TCP] 127.0.0.1:51764 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-18T18:35:36.503851100+08:00" level=info msg="[TCP] 127.0.0.1:51767 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:35:36.527012200+08:00" level=info msg="[TCP] 127.0.0.1:51770 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-07-18T18:35:36.813791400+08:00" level=info msg="[TCP] 127.0.0.1:51774 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-07-18T18:35:37.*********+08:00" level=info msg="[TCP] 127.0.0.1:51780 --> google.com:443 using GLOBAL"
time="2025-07-18T18:35:37.*********+08:00" level=info msg="[TCP] 127.0.0.1:51777 --> google.com:443 using GLOBAL"
time="2025-07-18T18:35:37.*********+08:00" level=info msg="[TCP] 127.0.0.1:51783 --> us.i.posthog.com:443 using GLOBAL"
time="2025-07-18T18:35:37.*********+08:00" level=info msg="[TCP] 127.0.0.1:51786 --> accounts.google.com:443 using GLOBAL"
time="2025-07-18T18:35:37.*********+08:00" level=info msg="[TCP] 127.0.0.1:51789 --> accounts.google.com:443 using GLOBAL"
time="2025-07-18T18:35:39.*********+08:00" level=info msg="[TCP] 127.0.0.1:51793 --> accounts.google.com:443 using GLOBAL"
time="2025-07-18T18:35:42.*********+08:00" level=info msg="[TCP] 127.0.0.1:51799 --> www.augmentcode.com:443 using GLOBAL"
time="2025-07-18T18:35:43.*********+08:00" level=info msg="[TCP] 127.0.0.1:51802 --> www.bing.com:443 using GLOBAL"
time="2025-07-18T18:35:44.*********+08:00" level=info msg="[TCP] 127.0.0.1:51805 --> play.google.com:443 using GLOBAL"
time="2025-07-18T18:36:17.*********+08:00" level=info msg="[TCP] 127.0.0.1:51833 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-18T18:36:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:51857 --> store.steampowered.com:443 using GLOBAL"
time="2025-07-18T18:37:43.*********+08:00" level=info msg="[TCP] 127.0.0.1:51897 --> www.bing.com:443 using GLOBAL"
time="2025-07-18T18:38:04.*********+08:00" level=info msg="[TCP] 127.0.0.1:51915 --> activity.windows.com:443 using GLOBAL"
time="2025-07-18T18:38:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:51920 --> updatecollect.wps.cn:80 using GLOBAL"
time="2025-07-18T18:38:07.783144900+08:00" level=info msg="[TCP] 127.0.0.1:51923 --> updatecollect.wps.cn:80 using GLOBAL"
time="2025-07-18T18:38:08.350958400+08:00" level=info msg="[TCP] 127.0.0.1:51927 --> updatepro.wps.cn:80 using GLOBAL"
time="2025-07-18T18:38:08.789744400+08:00" level=info msg="[TCP] 127.0.0.1:51930 --> updatepro.wps.cn:80 using GLOBAL"
time="2025-07-18T18:38:10.421468900+08:00" level=info msg="[TCP] 127.0.0.1:51934 --> activity.windows.com:443 using GLOBAL"
time="2025-07-18T18:38:43.631025200+08:00" level=info msg="[TCP] 127.0.0.1:52183 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:38:44.051788800+08:00" level=info msg="[TCP] 127.0.0.1:52186 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:38:44.554960500+08:00" level=info msg="[TCP] 127.0.0.1:52190 --> github.com:443 using GLOBAL"
time="2025-07-18T18:38:44.754819900+08:00" level=info msg="[TCP] 127.0.0.1:52193 --> www.youtube.com:443 using GLOBAL"
time="2025-07-18T18:38:45.486578600+08:00" level=info msg="[TCP] 127.0.0.1:52197 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:38:46.378444300+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-18T18:38:46.378444300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T18:38:46.389584600+08:00" level=error msg="🇯🇵日本01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-18T18:38:46.389584600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T18:38:46.389584600+08:00" level=error msg="🇯🇵日本04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-18T18:38:46.389584600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T18:38:53.936142800+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-18T18:38:53.936658900+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-18T18:38:53.936658900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-18T18:38:53.937170500+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-18T18:38:53.937170500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-18T18:38:53.937687700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-18T18:38:53.939149600+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-18T18:38:53.940165300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-18T18:38:53.946343100+08:00" level=warning msg="[TUN] default interface changed by monitor,  => 以太网"
time="2025-07-18T18:38:54.379463600+08:00" level=info msg="[TUN] Tun adapter listening at: Meta([********/30],[fdfe:dcba:9876::1/126]), mtu: 9000, auto route: true, auto redir: false, ip stack: Mixed"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider TVB"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Discord"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider TVer"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider apple"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider telegram"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider BBC"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Disney"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider google"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Lan"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Line"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider NowE"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-18T18:38:54.379975600+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-18T18:38:54.547106000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.547653200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.548386900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.549393000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.549903100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.549903100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.549903100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.550094500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.550094500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.550094500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.551512400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.553320700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.553320700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.553320700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.553320700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.553320700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.554961600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.554961600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.554961600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.554961600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.554961600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.555465100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.555533100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.557181500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.557342000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.557858600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.558217300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.558217300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.559143700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.559143700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.559785000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.559785000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.559785000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.559785000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.559785000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.559785000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.560367600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.564703900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.564703900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.565206300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.565206300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.565206300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.565206300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.565206300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.565718600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.565718600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.566230700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.566230700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.576642300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:54.772572500+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-18T18:38:54.775544600+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-18T18:38:54.779661900+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-18T18:38:54.793081800+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-18T18:38:54.795469700+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-18T18:38:54.796687100+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-18T18:38:54.800090700+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-18T18:38:54.804322600+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-18T18:38:54.810423500+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-18T18:38:54.821677500+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-18T18:38:54.825770900+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-18T18:38:54.827446600+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-18T18:38:54.831574200+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-18T18:38:54.834223900+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-18T18:38:54.843872700+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-18T18:38:54.849798900+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-18T18:38:55.588060800+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-18T18:38:55.607624800+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-18T18:38:55.619453800+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-18T18:38:55.620130400+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-18T18:38:55.626408800+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-18T18:38:55.642470400+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-18T18:38:55.644467600+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-18T18:38:55.656847600+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-18T18:38:55.661460400+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-18T18:38:55.664112200+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-18T18:38:55.665873700+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-18T18:38:55.676397300+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-18T18:38:55.679783500+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-18T18:38:55.685238700+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-18T18:38:55.686832500+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-18T18:38:55.693870400+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-18T18:38:55.695184300+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-18T18:38:55.700266000+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-18T18:38:55.735556300+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-18T18:38:55.739461700+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-18T18:38:55.774863100+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-18T18:38:55.816123000+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-18T18:38:55.920069100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:38:55.952023700+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-18T18:39:01.668696200+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-18T18:39:01.668696200+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-18T18:39:01.668696200+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-18T18:39:01.669200400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-18T18:39:01.672982200+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-18T18:39:01.924535900+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-18T18:39:01.925199000+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-18T18:39:01.925199000+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-18T18:39:01.929202400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-18T18:39:02.140466200+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-18T18:39:02.141489500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-18T18:39:02.141489500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-18T18:39:02.141863900+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-18T18:39:02.141863900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-18T18:39:02.142377800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-18T18:39:02.143404300+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-18T18:39:03.208812000+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/users/sign_in\": EOF"
time="2025-07-18T18:39:03.211113400+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-18T18:39:03.211113400+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-18T18:39:03.211113400+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-18T18:39:03.211113400+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-18T18:39:03.212131000+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-18T18:39:03.217495700+08:00" level=warning msg="[TUN] default interface changed by monitor,  => 以太网"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Line"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider apple"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider NowE"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Lan"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Discord"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider BBC"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider TVB"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Disney"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider telegram"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider TVer"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider google"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-18T18:39:03.218098700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-18T18:39:03.414644000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.414644000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.414644000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.418147300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.419477000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.419477000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.420797000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.421473000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.421473000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.422656400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.422656400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.422656400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.422656400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.423159000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425022800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425022800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425022800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425022800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425535300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425535300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425535300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425535300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425535300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425994600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.425994600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.431027700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.431027700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.431530500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.431530500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.431623300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432146800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432146800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432146800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432146800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432146800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432146800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432146800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432146800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432146800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432667200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432667200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432667200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.432667200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.433179700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.433179700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.433713200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.443126300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.443629300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:03.443629300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-18T18:39:04.489874300+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-18T18:39:04.501874100+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-18T18:39:04.524895200+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-18T18:39:04.527401100+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-18T18:39:04.531298600+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-18T18:39:04.533409900+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-18T18:39:04.534064100+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-18T18:39:04.534644600+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-18T18:39:04.535889400+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-18T18:39:04.536392900+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-18T18:39:04.536903100+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-18T18:39:04.549511800+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-18T18:39:04.550943000+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-18T18:39:04.551498700+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-18T18:39:04.552194300+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-18T18:39:04.554342100+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-18T18:39:04.556552100+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-18T18:39:04.559400500+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-18T18:39:04.579021400+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-18T18:39:04.579525000+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-18T18:39:04.588850100+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-18T18:39:04.591400600+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-18T18:39:04.694395800+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-18T18:39:04.695389800+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-18T18:39:04.695893400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-18T18:39:04.701584400+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-18T18:39:04.711109600+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-18T18:39:04.721677800+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-18T18:39:04.721677800+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-18T18:39:04.725312400+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-18T18:39:04.734243800+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-18T18:39:04.737232700+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-18T18:39:04.737914100+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-18T18:39:04.738483500+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-18T18:39:04.741063500+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-18T18:39:04.744603800+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-18T18:39:04.744603800+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-18T18:39:04.754924900+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-18T18:39:10.628674800+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-18T18:39:10.628674800+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-18T18:39:10.628674800+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-18T18:39:10.629177300+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-18T18:39:10.629177300+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-18T18:39:10.629177300+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-18T18:39:10.630639000+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-18T18:39:10.630639000+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-18T18:39:10.632641900+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-18T18:39:10.632641900+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-18T18:39:10.633382100+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-18T18:39:10.637325100+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-18T18:39:10.637325100+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-18T18:39:10.637325100+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-18T18:39:10.637325100+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-18T18:39:10.708887500+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:54639 --> [2402:4e00:1900:1902:0:9687:ded8:f101]:3478 using GLOBAL"
time="2025-07-18T18:39:10.868457100+08:00" level=info msg="[UDP] ********:54638 --> **************:3478 using GLOBAL"
time="2025-07-18T18:39:10.913794100+08:00" level=info msg="[UDP] ********:54637 --> **************:3478 using GLOBAL"
time="2025-07-18T18:39:11.426352900+08:00" level=info msg="[TCP] ********:52702 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:11.964664200+08:00" level=info msg="[TCP] ********:52727 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:11.964664200+08:00" level=info msg="[TCP] ********:52728 --> www.youtube.com:443 using GLOBAL"
time="2025-07-18T18:39:11.965197600+08:00" level=info msg="[TCP] ********:52730 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:11.965707800+08:00" level=info msg="[TCP] ********:52729 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:11.965707800+08:00" level=info msg="[TCP] ********:52731 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:11.966731200+08:00" level=info msg="[TCP] ********:52752 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:11.966731200+08:00" level=info msg="[TCP] ********:52755 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:11.967242100+08:00" level=info msg="[TCP] ********:52751 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:11.967242100+08:00" level=info msg="[TCP] ********:52744 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:11.969645000+08:00" level=info msg="[TCP] ********:52741 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:11.969645000+08:00" level=info msg="[TCP] ********:52743 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:11.969645000+08:00" level=info msg="[TCP] ********:52761 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:11.969645000+08:00" level=info msg="[TCP] ********:52760 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:12.515922200+08:00" level=info msg="[TCP] ********:52775 --> 120.233.22.42:8080 using GLOBAL"
time="2025-07-18T18:39:12.683679500+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:55722 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-18T18:39:12.704325300+08:00" level=info msg="[TCP] ********:52780 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:12.745257400+08:00" level=info msg="[TCP] ********:52783 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:12.756816000+08:00" level=info msg="[TCP] ********:52786 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:12.758566000+08:00" level=info msg="[TCP] ********:52789 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:13.311401000+08:00" level=info msg="[TCP] ********:52804 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:14.034690500+08:00" level=info msg="[TCP] ********:52814 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:39:14.413233500+08:00" level=info msg="[TCP] ********:52824 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:14.467943500+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:53559 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-18T18:39:14.772810600+08:00" level=info msg="[TCP] ********:52831 --> nav.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:39:15.164797500+08:00" level=info msg="[TCP] ********:52836 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:15.921481600+08:00" level=info msg="[TCP] ********:52844 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:16.522201600+08:00" level=error msg="🇭🇰香港04 [1.5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-18T18:39:16.522201600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-18T18:39:16.709051700+08:00" level=info msg="[TCP] ********:52847 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:17.477559800+08:00" level=info msg="[TCP] ********:52850 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:18.237391200+08:00" level=info msg="[TCP] ********:52854 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:19.042246700+08:00" level=info msg="[TCP] ********:52860 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:19.798517600+08:00" level=info msg="[TCP] ********:52863 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:20.567012900+08:00" level=info msg="[TCP] ********:52866 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:21.337715800+08:00" level=info msg="[TCP] ********:52870 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:22.127918500+08:00" level=info msg="[TCP] ********:52874 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:22.910445900+08:00" level=info msg="[TCP] ********:52877 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:23.044842100+08:00" level=info msg="[TCP] ********:52880 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:39:23.740641500+08:00" level=info msg="[TCP] ********:52886 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:23.740641500+08:00" level=info msg="[TCP] ********:52887 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:23.740641500+08:00" level=info msg="[TCP] ********:52889 --> www.youtube.com:443 using GLOBAL"
time="2025-07-18T18:39:23.741144700+08:00" level=info msg="[TCP] ********:52897 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:23.742422200+08:00" level=info msg="[TCP] ********:52885 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:23.742422200+08:00" level=info msg="[TCP] ********:52899 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:23.742422200+08:00" level=info msg="[TCP] ********:52895 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:23.742422200+08:00" level=info msg="[TCP] ********:52888 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:23.743967600+08:00" level=info msg="[TCP] ********:52890 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:23.743967600+08:00" level=info msg="[TCP] ********:52893 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:23.744469500+08:00" level=info msg="[TCP] ********:52884 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:23.747140300+08:00" level=info msg="[TCP] ********:52892 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:23.747140300+08:00" level=info msg="[TCP] ********:52896 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:23.747705000+08:00" level=info msg="[TCP] ********:52898 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:23.747705000+08:00" level=info msg="[TCP] ********:52891 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:23.747705000+08:00" level=info msg="[TCP] ********:52894 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:23.971434100+08:00" level=info msg="[TCP] ********:52933 --> **************:80 using GLOBAL"
time="2025-07-18T18:39:24.480607600+08:00" level=info msg="[TCP] ********:52936 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:24.491132800+08:00" level=info msg="[TCP] ********:52939 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:24.536888200+08:00" level=info msg="[TCP] ********:52942 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:24.540462600+08:00" level=info msg="[TCP] ********:52945 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:24.546768500+08:00" level=info msg="[TCP] ********:52948 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:25.818858900+08:00" level=info msg="[TCP] ********:52953 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:25.819367700+08:00" level=info msg="[TCP] ********:52954 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:25.820997900+08:00" level=info msg="[TCP] ********:52952 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:25.820997900+08:00" level=info msg="[TCP] ********:52956 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:25.820997900+08:00" level=info msg="[TCP] ********:52955 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:25.821714500+08:00" level=info msg="[TCP] ********:52966 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:25.823119900+08:00" level=info msg="[TCP] ********:52968 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:25.823119900+08:00" level=info msg="[TCP] ********:52973 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:25.824353500+08:00" level=info msg="[TCP] ********:52975 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:25.824353500+08:00" level=info msg="[TCP] ********:52974 --> www.youtube.com:443 using GLOBAL"
time="2025-07-18T18:39:25.824353500+08:00" level=info msg="[TCP] ********:52980 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:25.824353500+08:00" level=info msg="[TCP] ********:52989 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:25.827028600+08:00" level=info msg="[TCP] ********:52992 --> www.github.com:443 using GLOBAL"
time="2025-07-18T18:39:25.827028600+08:00" level=info msg="[TCP] ********:52995 --> www.apple.com:443 using GLOBAL"
time="2025-07-18T18:39:25.827538500+08:00" level=info msg="[TCP] ********:52983 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:25.827538500+08:00" level=info msg="[TCP] ********:52986 --> www.google.com:443 using GLOBAL"
time="2025-07-18T18:39:26.562249500+08:00" level=info msg="[TCP] ********:53000 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:26.571210600+08:00" level=info msg="[TCP] ********:53003 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:26.612110400+08:00" level=info msg="[TCP] ********:53006 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:26.614239300+08:00" level=info msg="[TCP] ********:53009 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:26.670186900+08:00" level=info msg="[TCP] ********:53013 --> github.com:443 using GLOBAL"
time="2025-07-18T18:39:29.739260500+08:00" level=info msg="[TCP] ********:53019 --> *************:80 using GLOBAL"
time="2025-07-18T18:39:32.379834500+08:00" level=info msg="[TCP] ********:53023 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-18T18:39:35.561624800+08:00" level=info msg="[TCP] ********:53028 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:39:35.650520700+08:00" level=info msg="[TCP] ********:53032 --> *************:80 using GLOBAL"
time="2025-07-18T18:39:41.426609100+08:00" level=info msg="[TCP] ********:53041 --> 120.233.22.121:80 using GLOBAL"
time="2025-07-18T18:39:45.196611100+08:00" level=info msg="[TCP] ********:53048 --> ************:80 using GLOBAL"
time="2025-07-18T18:39:45.696258900+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:53051 --> [2409:8c54:871:4003::19]:443 using GLOBAL"
time="2025-07-18T18:39:47.114528500+08:00" level=info msg="[TCP] ********:53055 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-18T18:39:47.118906800+08:00" level=info msg="[TCP] ********:53056 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-18T18:39:48.115617500+08:00" level=info msg="[TCP] ********:53063 --> ipv6.msftconnecttest.com:80 using GLOBAL"
time="2025-07-18T18:39:48.115617500+08:00" level=info msg="[TCP] ********:53062 --> ipv6.msftconnecttest.com:80 using GLOBAL"
time="2025-07-18T18:39:48.183809100+08:00" level=info msg="[TCP] ********:53068 --> *************:443 using GLOBAL"
time="2025-07-18T18:39:51.109856200+08:00" level=info msg="[TCP] ********:53074 --> *************:80 using GLOBAL"
time="2025-07-18T18:39:51.423362200+08:00" level=info msg="[TCP] ********:53078 --> **************:80 using GLOBAL"
time="2025-07-18T18:39:57.227483500+08:00" level=info msg="[TCP] ********:53086 --> **************:443 using GLOBAL"
time="2025-07-18T18:40:03.043309700+08:00" level=info msg="[TCP] ********:53094 --> *************:443 using GLOBAL"
time="2025-07-18T18:40:09.917914700+08:00" level=info msg="[TCP] ********:53103 --> *************:443 using GLOBAL"
time="2025-07-18T18:40:10.854625700+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:53107 --> szextshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:40:10.940701700+08:00" level=info msg="[TCP] ********:53110 --> fd.api.iris.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:10.941726200+08:00" level=info msg="[TCP] ********:53112 --> fd.api.iris.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:10.942236700+08:00" level=info msg="[TCP] ********:53111 --> fd.api.iris.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:10.942748200+08:00" level=info msg="[TCP] ********:53113 --> fd.api.iris.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:11.228591600+08:00" level=info msg="[TCP] ********:53122 --> fd.api.iris.microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:12.867592400+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:53126 --> [2409:8c54:871:5ff5::71]:80 using GLOBAL"
time="2025-07-18T18:40:15.599765700+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:53131 --> [2409:8c54:1040:6::4f]:80 using GLOBAL"
time="2025-07-18T18:40:15.699306500+08:00" level=info msg="[TCP] ********:53135 --> 120.233.22.121:443 using GLOBAL"
time="2025-07-18T18:40:20.696570500+08:00" level=info msg="[TCP] ********:53141 --> store-images.s-microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:20.726306100+08:00" level=info msg="[TCP] ********:53144 --> store-images.s-microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:20.772216000+08:00" level=info msg="[TCP] ********:53147 --> res.public.onecdn.static.microsoft:443 using GLOBAL"
time="2025-07-18T18:40:20.814275200+08:00" level=info msg="[TCP] ********:53150 --> store-images.s-microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:20.865595500+08:00" level=info msg="[TCP] ********:53153 --> store-images.s-microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:21.172196400+08:00" level=info msg="[TCP] ********:53157 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-07-18T18:40:21.518881500+08:00" level=info msg="[TCP] ********:53161 --> ************:443 using GLOBAL"
time="2025-07-18T18:40:23.974828700+08:00" level=info msg="[TCP] ********:53165 --> res.public.onecdn.static.microsoft:443 using GLOBAL"
time="2025-07-18T18:40:23.997950700+08:00" level=info msg="[TCP] ********:53168 --> store-images.s-microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:24.332923500+08:00" level=info msg="[TCP] ********:53172 --> store-images.s-microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:24.374897000+08:00" level=info msg="[TCP] ********:53175 --> store-images.s-microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:24.421419300+08:00" level=info msg="[TCP] ********:53178 --> store-images.s-microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:24.945075500+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:53181 --> [2409:8c54:871:2003::15]:80 using GLOBAL"
time="2025-07-18T18:40:25.119003100+08:00" level=info msg="[TCP] ********:53184 --> store-images.s-microsoft.com:443 using GLOBAL"
time="2025-07-18T18:40:27.349723200+08:00" level=info msg="[TCP] ********:53190 --> *************:443 using GLOBAL"
time="2025-07-18T18:40:33.205266500+08:00" level=info msg="[TCP] ********:53198 --> **************:443 using GLOBAL"
time="2025-07-18T18:40:37.700573400+08:00" level=info msg="[TCP] ********:53204 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-18T18:40:38.279358600+08:00" level=info msg="[UDP] ********:65205 --> 223.5.5.5:853 using GLOBAL"
time="2025-07-18T18:40:38.307927600+08:00" level=info msg="[UDP] ********:65208 --> 223.5.5.5:853 using GLOBAL"
time="2025-07-18T18:40:38.317309100+08:00" level=info msg="[TCP] ********:53208 --> **************:8080 using GLOBAL"
time="2025-07-18T18:40:38.421748800+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:53210 --> szminorshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-18T18:40:40.088232500+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-18T18:40:40.088978000+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-18T18:40:40.088978000+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-18T18:40:40.089488700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-18T18:40:40.089591700+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-18T18:40:40.089591700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-18T18:40:40.091126100+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-18T18:40:40.092171500+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider TVer"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider telegram"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Lan"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider TVB"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Disney"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider BBC"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Discord"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider NowE"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider google"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider apple"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider Line"
time="2025-07-18T18:40:40.160778100+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-18T18:40:45.161088700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.162188700+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-18T18:40:45.162188700+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-18T18:40:45.162188700+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.162188700+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-18T18:40:45.162188700+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-18T18:40:45.162188700+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-18T18:40:45.162188700+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-18T18:40:45.161677300+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-18T18:40:45.162188700+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-18T18:40:45.161174000+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-18T18:40:45.162188700+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-18T18:40:45.162188700+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-18T18:40:45.167321300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-18T18:40:45.167321300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-18T18:40:45.167321300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-18T18:40:45.167833300+08:00" level=info msg="Start initial Compatible provider default"
