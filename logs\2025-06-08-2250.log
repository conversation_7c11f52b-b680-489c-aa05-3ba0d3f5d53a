2025-06-08 22:50:55 INFO - try to run core in service mode
2025-06-08 22:50:55 INFO - start service: {"core_type": "verge-mihomo-alpha", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-06-08-2250.log", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml"}
2025-06-08 22:50:55 INFO - Initializing hotkeys, global hotkey enabled: true
2025-06-08 22:50:55 INFO - No hotkeys configured
2025-06-08 22:50:55 INFO - Starting to create window
2025-06-08 22:50:55 INFO - Creating new window
2025-06-08 22:50:55 INFO - Window created successfully, attempting to show
2025-06-08 22:50:55 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-08 22:50:55 INFO - Successfully registered hotkey Control+Q for quit
2025-06-08 22:53:44 INFO - Starting to create window
2025-06-08 22:53:44 INFO - Found existing window, trying to show it
2025-06-08 22:53:44 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-08 22:53:44 INFO - Successfully registered hotkey Control+Q for quit
2025-06-08 22:53:51 INFO - stop the core by service
2025-06-08 22:53:52 ERROR - Failed to unregister hotkey: HotKey { mods: Modifiers(CONTROL), key: KeyQ, id: 524323 }
