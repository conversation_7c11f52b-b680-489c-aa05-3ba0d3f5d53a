Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-19T19:48:38.310762900+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-19T19:48:38.336707900+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-19T19:48:38.337209900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-19T19:48:38.337720700+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-19T19:48:38.362334000+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-19T19:48:38.362334000+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-19T19:48:38.613100000+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-19T19:48:38.613100000+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-19T19:48:38.624437600+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-19T19:48:38.628009700+08:00" level=info msg="Initial configuration complete, total time: 294ms"
time="2025-07-19T19:48:38.629008700+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-19T19:48:38.630009600+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-19T19:48:38.631011300+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-19T19:48:38.631011300+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-19T19:48:38.631011300+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider BBC"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Disney"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider google"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Lan"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider apple"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider TVB"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider telegram"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider NowE"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Line"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Discord"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-19T19:48:38.640805700+08:00" level=info msg="Start initial provider TVer"
time="2025-07-19T19:48:40.595583500+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-19T19:48:40.597133600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-19T19:48:40.597133600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-19T19:48:40.598149600+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-19T19:48:40.598149600+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-19T19:48:40.598149600+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-19T19:48:40.599683700+08:00" level=info msg="Initial configuration complete, total time: 4ms"
time="2025-07-19T19:48:43.642884700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.642971300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.642971300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.642971300+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-19T19:48:43.642971300+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-19T19:48:43.642971300+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-19T19:48:43.643984100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.643984100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.643984100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.643984100+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-19T19:48:43.643984100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.643984100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.643984100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.643984100+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-19T19:48:43.643984100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.643984100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.643984100+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-19T19:48:43.643984100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.643984100+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-19T19:48:43.643984100+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-19T19:48:43.643984100+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-19T19:48:43.643984100+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-19T19:48:43.643984100+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-19T19:48:43.643984100+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-19T19:48:43.645036800+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.646080000+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.646080000+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.646080000+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.646080000+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.645574600+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-19T19:48:43.646080000+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-19T19:48:43.646080000+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-19T19:48:43.645574600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:43.646080000+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-19T19:48:43.646080000+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-19T19:48:43.646080000+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-19T19:48:43.646080000+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-19T19:48:43.648299500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-19T19:48:43.648299500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-19T19:48:43.648299500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-19T19:48:43.648299500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider TVer"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Lan"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider BBC"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Line"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider apple"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider Discord"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider TVB"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider google"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider NowE"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider telegram"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider Disney"
time="2025-07-19T19:48:43.649360100+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-19T19:48:43.649868400+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-19T19:48:48.651002600+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-19T19:48:48.651002600+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-19T19:48:48.651002600+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-19T19:48:48.651002600+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.651002600+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-19T19:48:48.650492800+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-19T19:48:48.651002600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-19T19:48:48.651516200+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-19T19:48:48.651002600+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-19T19:48:48.649990000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:48:48.651516200+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-19T19:48:48.651516200+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-19T19:48:48.656283600+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-19T19:48:48.656283600+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-19T19:48:48.656283600+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-19T19:48:48.656283600+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-19T19:48:48.980018700+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-19T19:48:48.980521600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-19T19:48:48.980521600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-19T19:48:48.981034400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-19T19:48:48.981034400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-19T19:48:48.981566400+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-19T19:48:48.982628100+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-19T19:48:48.983652900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider apple"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Lan"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Disney"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider google"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider TVer"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider NowE"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider TVB"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider telegram"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider BBC"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Discord"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Line"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-19T19:48:48.984165000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-19T19:48:49.007947600+08:00" level=info msg="[TCP] 127.0.0.1:59280 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-19T19:48:49.015387500+08:00" level=info msg="[TCP] 127.0.0.1:59283 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:48:49.015387500+08:00" level=info msg="[TCP] 127.0.0.1:59286 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:48:49.144286700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.144286700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.144791800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.145307400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.145818200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.145818200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.145818200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.147155900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.149042700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.149042700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.149620100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.149620100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.149620100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.149620100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.150123800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.150123800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.150123800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155334400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155334400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155334400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155334400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155837100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155837100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155837100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155837100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155837100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155837100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.155837100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.156356600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.156356600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.156356600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.156356600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.156870000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.156870000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.156870000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.164378900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.165392800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.165392800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.165392800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.165392800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.165392800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.165906300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.165906300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.165906300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.165906300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.165906300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.166415000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.166415000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.167433600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.216079100+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-19T19:48:49.223755100+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-19T19:48:49.237729600+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-19T19:48:49.243003100+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-19T19:48:49.252766800+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-19T19:48:49.256906600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-19T19:48:49.270641000+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-19T19:48:49.272223400+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-19T19:48:49.272223400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-19T19:48:49.273754200+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-19T19:48:49.273754200+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-19T19:48:49.708192400+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-19T19:48:49.720267500+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-19T19:48:49.728599200+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-19T19:48:49.732523600+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-19T19:48:49.734153600+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-07-19T19:48:49.740815900+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-19T19:48:49.755981800+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-19T19:48:49.761012400+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-07-19T19:48:49.763336500+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-19T19:48:49.764527800+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-19T19:48:49.773493300+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-19T19:48:49.812418200+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-19T19:48:49.817453100+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-19T19:48:49.833676500+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-19T19:48:49.833676500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.836721600+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-19T19:48:49.839881800+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-19T19:48:49.848953300+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-19T19:48:49.849456200+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-19T19:48:49.849456200+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-19T19:48:49.863380000+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-19T19:48:49.867071400+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-19T19:48:49.871451800+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-19T19:48:49.875468900+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-19T19:48:49.881893900+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-19T19:48:49.884022600+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-19T19:48:49.886206100+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-19T19:48:49.901711000+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-19T19:48:49.901711000+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-19T19:48:49.903823300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-19T19:48:49.908097200+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-19T19:48:49.928272300+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/users/sign_in\": net/http: HTTP/1.x transport connection broken: unexpected EOF"
time="2025-07-19T19:48:49.933006800+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-19T19:48:49.983530800+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-19T19:48:50.061477200+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-19T19:48:50.106988300+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-19T19:48:50.126086300+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-19T19:48:50.549131000+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-19T19:48:50.717853100+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-19T19:48:50.843709800+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-19T19:48:50.846399400+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-19T19:48:50.846399400+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-19T19:48:50.846399400+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-19T19:48:50.846399400+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-19T19:48:50.905595900+08:00" level=error msg="🇮🇳印度-下载专享[0.5x] ipv6 only failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-19T19:48:50.905595900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-19T19:48:52.002382200+08:00" level=info msg="[TCP] 127.0.0.1:59578 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:48:52.130619800+08:00" level=info msg="[TCP] 127.0.0.1:59586 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:48:54.102056900+08:00" level=error msg="🇻🇳越南01 [1.5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-19T19:48:54.102056900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-19T19:48:54.921805800+08:00" level=error msg="🇻🇳越南01 [1.5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-19T19:48:54.921805800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-19T19:48:55.448742500+08:00" level=info msg="[TCP] 127.0.0.1:59666 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:48:56.191156500+08:00" level=info msg="[TCP] 127.0.0.1:59669 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:49:16.174442700+08:00" level=info msg="[TCP] 127.0.0.1:59687 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:49:22.959886600+08:00" level=info msg="[TCP] 127.0.0.1:59694 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:49:25.399991500+08:00" level=info msg="[TCP] 127.0.0.1:59699 --> www.bing.com:443 using GLOBAL"
time="2025-07-19T19:49:38.547741900+08:00" level=info msg="[TCP] 127.0.0.1:59711 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:49:49.009492800+08:00" level=info msg="[TCP] 127.0.0.1:59721 --> console.volcengine.com:443 using GLOBAL"
time="2025-07-19T19:49:49.011185200+08:00" level=info msg="[TCP] 127.0.0.1:59724 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:49:52.001210400+08:00" level=info msg="[TCP] 127.0.0.1:59729 --> mon.zijieapi.com:443 using GLOBAL"
time="2025-07-19T19:49:59.995492500+08:00" level=info msg="[TCP] 127.0.0.1:59737 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-19T19:50:04.990182400+08:00" level=info msg="[TCP] 127.0.0.1:59747 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:50:05.197877500+08:00" level=info msg="[TCP] 127.0.0.1:59750 --> cn.bing.com:443 using GLOBAL"
time="2025-07-19T19:50:07.337021000+08:00" level=info msg="[TCP] 127.0.0.1:59754 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:07.513069900+08:00" level=info msg="[TCP] 127.0.0.1:59757 --> developers.google.cn:443 using GLOBAL"
time="2025-07-19T19:50:07.616689300+08:00" level=info msg="[TCP] 127.0.0.1:59760 --> developers.google.cn:443 using GLOBAL"
time="2025-07-19T19:50:07.674594900+08:00" level=info msg="[TCP] 127.0.0.1:59763 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:50:07.757714800+08:00" level=info msg="[TCP] 127.0.0.1:59766 --> www.bing.com:443 using GLOBAL"
time="2025-07-19T19:50:08.710995900+08:00" level=info msg="[TCP] 127.0.0.1:59770 --> fonts.googleapis.cn:443 using GLOBAL"
time="2025-07-19T19:50:08.730428800+08:00" level=info msg="[TCP] 127.0.0.1:59779 --> fonts.googleapis.cn:443 using GLOBAL"
time="2025-07-19T19:50:08.731481800+08:00" level=info msg="[TCP] 127.0.0.1:59773 --> www.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:08.731481800+08:00" level=info msg="[TCP] 127.0.0.1:59782 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:50:08.732533200+08:00" level=info msg="[TCP] 127.0.0.1:59776 --> fonts.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:08.732533200+08:00" level=info msg="[TCP] 127.0.0.1:59788 --> fonts.googleapis.cn:443 using GLOBAL"
time="2025-07-19T19:50:08.733513800+08:00" level=info msg="[TCP] 127.0.0.1:59792 --> www.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:08.734138000+08:00" level=info msg="[TCP] 127.0.0.1:59789 --> www.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:08.735298300+08:00" level=info msg="[TCP] 127.0.0.1:59785 --> apis.google.com:443 using GLOBAL"
time="2025-07-19T19:50:08.859109900+08:00" level=info msg="[TCP] 127.0.0.1:59798 --> fonts.googleapis.cn:443 using GLOBAL"
time="2025-07-19T19:50:08.909605900+08:00" level=info msg="[TCP] 127.0.0.1:59801 --> fonts.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:09.134575000+08:00" level=info msg="[TCP] 127.0.0.1:59804 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:50:09.908965300+08:00" level=info msg="[TCP] 127.0.0.1:59807 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:10.589489500+08:00" level=info msg="[TCP] 127.0.0.1:59811 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:11.891865900+08:00" level=info msg="[TCP] 127.0.0.1:59815 --> apis.google.com:443 using GLOBAL"
time="2025-07-19T19:50:12.720096300+08:00" level=info msg="[TCP] 127.0.0.1:59818 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:14.346579000+08:00" level=info msg="[TCP] 127.0.0.1:59823 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:14.581267300+08:00" level=info msg="[TCP] 127.0.0.1:59826 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-19T19:50:14.592565800+08:00" level=info msg="[TCP] 127.0.0.1:59829 --> gstatic.com:443 using GLOBAL"
time="2025-07-19T19:50:14.599263100+08:00" level=info msg="[TCP] 127.0.0.1:59832 --> gstatic.com:443 using GLOBAL"
time="2025-07-19T19:50:14.599263100+08:00" level=info msg="[TCP] 127.0.0.1:59835 --> gstatic.com:443 using GLOBAL"
time="2025-07-19T19:50:14.935096700+08:00" level=info msg="[TCP] 127.0.0.1:59838 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:50:14.935096700+08:00" level=info msg="[TCP] 127.0.0.1:59839 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:50:14.936814500+08:00" level=info msg="[TCP] 127.0.0.1:59842 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:50:17.726342700+08:00" level=info msg="[TCP] 127.0.0.1:59849 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-19T19:50:18.404757900+08:00" level=info msg="[TCP] 127.0.0.1:59852 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-19T19:50:18.423526300+08:00" level=info msg="[TCP] 127.0.0.1:59855 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:50:18.514990300+08:00" level=info msg="[TCP] 127.0.0.1:59858 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:18.532838800+08:00" level=info msg="[TCP] 127.0.0.1:59861 --> apis.google.com:443 using GLOBAL"
time="2025-07-19T19:50:18.721380000+08:00" level=info msg="[TCP] 127.0.0.1:59864 --> apis.google.com:443 using GLOBAL"
time="2025-07-19T19:50:18.777780400+08:00" level=info msg="[TCP] 127.0.0.1:59867 --> apis.google.com:443 using GLOBAL"
time="2025-07-19T19:50:19.423274400+08:00" level=info msg="[TCP] 127.0.0.1:59871 --> feedback-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:50:20.505021600+08:00" level=info msg="[TCP] 127.0.0.1:59875 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:22.612298000+08:00" level=info msg="[TCP] 127.0.0.1:59879 --> feedback-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:50:23.054332100+08:00" level=info msg="[TCP] 127.0.0.1:59882 --> apis.google.com:443 using GLOBAL"
time="2025-07-19T19:50:25.458357300+08:00" level=info msg="[TCP] 127.0.0.1:59887 --> www.bing.com:443 using GLOBAL"
time="2025-07-19T19:50:25.529041900+08:00" level=info msg="[TCP] 127.0.0.1:59890 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-19T19:50:25.567657400+08:00" level=info msg="[TCP] 127.0.0.1:59893 --> www.bing.com:443 using GLOBAL"
time="2025-07-19T19:50:27.026172400+08:00" level=info msg="[TCP] 127.0.0.1:59897 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:50:27.027752600+08:00" level=info msg="[TCP] 127.0.0.1:59900 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:50:27.206651600+08:00" level=info msg="[TCP] 127.0.0.1:59903 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:50:28.421134000+08:00" level=info msg="[TCP] 127.0.0.1:59907 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:50:29.523685500+08:00" level=info msg="[TCP] 127.0.0.1:59912 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:30.993082800+08:00" level=info msg="[TCP] 127.0.0.1:59915 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:50:31.431803400+08:00" level=info msg="[TCP] 127.0.0.1:59919 --> www.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:31.440631300+08:00" level=info msg="[TCP] 127.0.0.1:59922 --> fonts.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:32.207708200+08:00" level=info msg="[TCP] 127.0.0.1:59926 --> fonts.googleapis.cn:443 using GLOBAL"
time="2025-07-19T19:50:32.229048400+08:00" level=info msg="[TCP] 127.0.0.1:59930 --> fonts.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:32.229048400+08:00" level=info msg="[TCP] 127.0.0.1:59929 --> www.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:32.230377200+08:00" level=info msg="[TCP] 127.0.0.1:59935 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:50:32.230377200+08:00" level=info msg="[TCP] 127.0.0.1:59938 --> apis.google.com:443 using GLOBAL"
time="2025-07-19T19:50:32.559692100+08:00" level=info msg="[TCP] 127.0.0.1:59941 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:50:32.751732000+08:00" level=info msg="[TCP] 127.0.0.1:59944 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-19T19:50:32.896015900+08:00" level=info msg="[TCP] 127.0.0.1:59947 --> apis.google.com:443 using GLOBAL"
time="2025-07-19T19:50:32.968293600+08:00" level=info msg="[TCP] 127.0.0.1:59950 --> feedback-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:50:33.006773700+08:00" level=info msg="[TCP] 127.0.0.1:59956 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:50:33.006773700+08:00" level=info msg="[TCP] 127.0.0.1:59953 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:33.326616600+08:00" level=info msg="[TCP] 127.0.0.1:59959 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:50:33.351099000+08:00" level=info msg="[TCP] 127.0.0.1:59962 --> content-developerprofiles-pa.googleapis.com:443 using GLOBAL"
time="2025-07-19T19:50:33.721005400+08:00" level=info msg="[TCP] 127.0.0.1:59965 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:35.757155500+08:00" level=info msg="[TCP] 127.0.0.1:59970 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:35.761461900+08:00" level=info msg="[TCP] 127.0.0.1:59973 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:50:36.160593300+08:00" level=info msg="[TCP] 127.0.0.1:59976 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:50:36.478735100+08:00" level=info msg="[TCP] 127.0.0.1:59979 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:50:36.482502100+08:00" level=info msg="[TCP] 127.0.0.1:59980 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:50:36.875066400+08:00" level=info msg="[TCP] 127.0.0.1:59985 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:36.908666100+08:00" level=info msg="[TCP] 127.0.0.1:59988 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:37.036791500+08:00" level=info msg="[TCP] 127.0.0.1:59991 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:38.832493000+08:00" level=info msg="[TCP] 127.0.0.1:59996 --> www.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:39.488604700+08:00" level=info msg="[TCP] 127.0.0.1:59999 --> content-developerprofiles-pa.googleapis.com:443 using GLOBAL"
time="2025-07-19T19:50:39.598652200+08:00" level=info msg="[TCP] 127.0.0.1:60002 --> www.gstatic.cn:443 using GLOBAL"
time="2025-07-19T19:50:40.110718900+08:00" level=info msg="[TCP] 127.0.0.1:60005 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-19T19:50:40.366774500+08:00" level=info msg="[TCP] 127.0.0.1:60009 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:41.620811200+08:00" level=info msg="[TCP] 127.0.0.1:60016 --> googledownloads.cn:443 using GLOBAL"
time="2025-07-19T19:50:41.621313300+08:00" level=info msg="[TCP] 127.0.0.1:60013 --> googledownloads.cn:443 using GLOBAL"
time="2025-07-19T19:50:41.751479000+08:00" level=info msg="[TCP] 127.0.0.1:60019 --> googledownloads.cn:443 using GLOBAL"
time="2025-07-19T19:50:47.540356100+08:00" level=info msg="[TCP] 127.0.0.1:60026 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:48.998731100+08:00" level=info msg="[TCP] 127.0.0.1:60029 --> console.volcengine.com:443 using GLOBAL"
time="2025-07-19T19:50:48.998731100+08:00" level=info msg="[TCP] 127.0.0.1:60033 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:50:48.999235200+08:00" level=info msg="[TCP] 127.0.0.1:60032 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-19T19:50:50.997102000+08:00" level=info msg="[TCP] 127.0.0.1:60040 --> mon.zijieapi.com:443 using GLOBAL"
time="2025-07-19T19:50:51.300688400+08:00" level=info msg="[TCP] 127.0.0.1:60043 --> mon.zijieapi.com:443 using GLOBAL"
time="2025-07-19T19:50:54.706520500+08:00" level=info msg="[TCP] 127.0.0.1:60048 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:50:56.934875400+08:00" level=info msg="[TCP] 127.0.0.1:60053 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-19T19:51:08.695329200+08:00" level=info msg="[TCP] 127.0.0.1:60064 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:51:10.997454700+08:00" level=info msg="[TCP] 127.0.0.1:60071 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:51:10.998606800+08:00" level=info msg="[TCP] 127.0.0.1:60068 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-19T19:51:12.871927700+08:00" level=info msg="[TCP] 127.0.0.1:60075 --> aistudio.google.com:443 using GLOBAL"
time="2025-07-19T19:51:12.975570500+08:00" level=info msg="[TCP] 127.0.0.1:60079 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:51:12.975570500+08:00" level=info msg="[TCP] 127.0.0.1:60078 --> aistudio.google.com:443 using GLOBAL"
time="2025-07-19T19:51:16.133671800+08:00" level=info msg="[TCP] 127.0.0.1:60086 --> aistudio.google.com:443 using GLOBAL"
time="2025-07-19T19:51:18.031053100+08:00" level=info msg="[TCP] 127.0.0.1:60094 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-19T19:51:18.031656000+08:00" level=info msg="[TCP] 127.0.0.1:60091 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:51:18.031656000+08:00" level=info msg="[TCP] 127.0.0.1:60097 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:18.064234500+08:00" level=info msg="[TCP] 127.0.0.1:60100 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-19T19:51:18.853150900+08:00" level=info msg="[TCP] 127.0.0.1:60107 --> apis.google.com:443 using GLOBAL"
time="2025-07-19T19:51:18.879568000+08:00" level=info msg="[TCP] 127.0.0.1:60110 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:51:18.909230300+08:00" level=info msg="[TCP] 127.0.0.1:60113 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:18.924430300+08:00" level=info msg="[TCP] 127.0.0.1:60116 --> waa-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:18.954802700+08:00" level=info msg="[TCP] 127.0.0.1:60119 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:51:19.032573600+08:00" level=info msg="[TCP] 127.0.0.1:60122 --> aistudio.google.com:443 using GLOBAL"
time="2025-07-19T19:51:19.091442200+08:00" level=info msg="[TCP] 127.0.0.1:60125 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:19.092953600+08:00" level=info msg="[TCP] 127.0.0.1:60128 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:19.099266400+08:00" level=info msg="[TCP] 127.0.0.1:60131 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:51:19.179840900+08:00" level=info msg="[TCP] 127.0.0.1:60134 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:51:19.319131300+08:00" level=info msg="[TCP] 127.0.0.1:60137 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-19T19:51:19.481273200+08:00" level=info msg="[TCP] 127.0.0.1:60143 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:51:19.481273200+08:00" level=info msg="[TCP] 127.0.0.1:60142 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:51:19.481273200+08:00" level=info msg="[TCP] 127.0.0.1:60144 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:51:19.482766600+08:00" level=info msg="[TCP] 127.0.0.1:60141 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:51:19.921776400+08:00" level=info msg="[TCP] 127.0.0.1:60154 --> people-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:19.923281700+08:00" level=info msg="[TCP] 127.0.0.1:60153 --> people-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:20.234098100+08:00" level=info msg="[TCP] 127.0.0.1:60159 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:51:20.258797300+08:00" level=info msg="[TCP] 127.0.0.1:60162 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:51:20.607680700+08:00" level=info msg="[TCP] 127.0.0.1:60166 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:21.019724100+08:00" level=info msg="[TCP] 127.0.0.1:60169 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-07-19T19:51:21.579018400+08:00" level=info msg="[TCP] 127.0.0.1:60172 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:51:21.958336000+08:00" level=info msg="[TCP] 127.0.0.1:60175 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:51:21.992674300+08:00" level=info msg="[TCP] 127.0.0.1:60178 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-19T19:51:25.097420500+08:00" level=info msg="[TCP] 127.0.0.1:60183 --> waa-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:25.478240700+08:00" level=info msg="[TCP] 127.0.0.1:60186 --> waa-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:25.506790100+08:00" level=info msg="[TCP] 127.0.0.1:60189 --> www.bing.com:443 using GLOBAL"
time="2025-07-19T19:51:25.984173800+08:00" level=info msg="[TCP] 127.0.0.1:60193 --> www.google.com:443 using GLOBAL"
time="2025-07-19T19:51:26.288951900+08:00" level=info msg="[TCP] 127.0.0.1:60196 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:51:26.621338300+08:00" level=info msg="[TCP] 127.0.0.1:60200 --> aistudio.google.com:443 using GLOBAL"
time="2025-07-19T19:51:26.748325300+08:00" level=info msg="[TCP] 127.0.0.1:60203 --> aistudio.google.com:443 using GLOBAL"
time="2025-07-19T19:51:26.768418900+08:00" level=info msg="[TCP] 127.0.0.1:60206 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:51:26.996994000+08:00" level=info msg="[TCP] 127.0.0.1:60209 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:51:27.349709400+08:00" level=info msg="[TCP] 127.0.0.1:60212 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:28.698718200+08:00" level=info msg="[TCP] 127.0.0.1:60216 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:51:29.126500500+08:00" level=info msg="[TCP] 127.0.0.1:60219 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:30.881600400+08:00" level=info msg="[TCP] 127.0.0.1:60224 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:51:31.999323300+08:00" level=info msg="[TCP] 127.0.0.1:60231 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:51:31.999323300+08:00" level=info msg="[TCP] 127.0.0.1:60228 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-19T19:51:32.993710900+08:00" level=info msg="[TCP] 127.0.0.1:60235 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:51:35.902744700+08:00" level=info msg="[TCP] 127.0.0.1:60240 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:51:36.223314200+08:00" level=info msg="[TCP] 127.0.0.1:60243 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:51:39.205755100+08:00" level=info msg="[TCP] 127.0.0.1:60248 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:41.710445300+08:00" level=info msg="[TCP] 127.0.0.1:60252 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:51:42.406509400+08:00" level=info msg="[TCP] 127.0.0.1:60256 --> aistudio.google.com:443 using GLOBAL"
time="2025-07-19T19:51:42.519444300+08:00" level=info msg="[TCP] 127.0.0.1:60259 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:51:42.821989100+08:00" level=info msg="[TCP] 127.0.0.1:60262 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:51:43.188086200+08:00" level=info msg="[TCP] 127.0.0.1:60265 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:51:44.531502400+08:00" level=info msg="[TCP] 127.0.0.1:60269 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:51:44.733868400+08:00" level=info msg="[TCP] 127.0.0.1:60272 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:51:45.836605600+08:00" level=info msg="[TCP] 127.0.0.1:60276 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:46.415378000+08:00" level=info msg="[TCP] 127.0.0.1:60279 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:47.257292200+08:00" level=info msg="[TCP] 127.0.0.1:60283 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:48.996848100+08:00" level=info msg="[TCP] 127.0.0.1:60287 --> console.volcengine.com:443 using GLOBAL"
time="2025-07-19T19:51:48.996848100+08:00" level=info msg="[TCP] 127.0.0.1:60290 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:51:49.005611500+08:00" level=info msg="[TCP] 127.0.0.1:60293 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:51:50.889421200+08:00" level=info msg="[TCP] 127.0.0.1:60298 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:50.893760200+08:00" level=info msg="[TCP] 127.0.0.1:60299 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:51.075731300+08:00" level=info msg="[TCP] 127.0.0.1:60304 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:51.157031900+08:00" level=info msg="[TCP] 127.0.0.1:60307 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:51:51.992778400+08:00" level=info msg="[TCP] 127.0.0.1:60311 --> mon.zijieapi.com:443 using GLOBAL"
time="2025-07-19T19:51:52.300011700+08:00" level=info msg="[TCP] 127.0.0.1:60314 --> mon.zijieapi.com:443 using GLOBAL"
time="2025-07-19T19:52:03.540551500+08:00" level=info msg="[TCP] 127.0.0.1:60326 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:52:04.004594700+08:00" level=info msg="[TCP] 127.0.0.1:60329 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-19T19:52:04.008263300+08:00" level=info msg="[TCP] 127.0.0.1:60332 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:52:12.954432500+08:00" level=info msg="[TCP] 127.0.0.1:60341 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:52:25.563801000+08:00" level=info msg="[TCP] 127.0.0.1:60352 --> www.bing.com:443 using GLOBAL"
time="2025-07-19T19:52:31.725719400+08:00" level=info msg="[TCP] 127.0.0.1:60359 --> www.bing.com:443 using GLOBAL"
time="2025-07-19T19:52:37.713923800+08:00" level=info msg="[TCP] 127.0.0.1:60366 --> console.cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:37.835401300+08:00" level=info msg="[TCP] 127.0.0.1:60372 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:52:37.837958500+08:00" level=info msg="[TCP] 127.0.0.1:60369 --> console.cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:37.970879900+08:00" level=info msg="[TCP] 127.0.0.1:60375 --> console.cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:37.*********+08:00" level=info msg="[TCP] 127.0.0.1:60384 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:52:37.*********+08:00" level=info msg="[TCP] 127.0.0.1:60378 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:52:37.*********+08:00" level=info msg="[TCP] 127.0.0.1:60381 --> console.cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:38.*********+08:00" level=info msg="[TCP] 127.0.0.1:60388 --> accounts.google.com:443 using GLOBAL"
time="2025-07-19T19:52:39.*********+08:00" level=info msg="[TCP] 127.0.0.1:60392 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:52:39.*********+08:00" level=info msg="[TCP] 127.0.0.1:60395 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:52:39.*********+08:00" level=info msg="[TCP] 127.0.0.1:60398 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:52:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:60401 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:52:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:60404 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:52:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:60407 --> cloudusersettings-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:60410 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:40.307648100+08:00" level=info msg="[TCP] 127.0.0.1:60413 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:52:40.331057800+08:00" level=info msg="[TCP] 127.0.0.1:60416 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-19T19:52:40.459229000+08:00" level=info msg="[TCP] 127.0.0.1:60419 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:52:40.634502100+08:00" level=info msg="[TCP] 127.0.0.1:60422 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:52:41.001282200+08:00" level=info msg="[TCP] 127.0.0.1:60425 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:52:41.236311700+08:00" level=info msg="[TCP] 127.0.0.1:60429 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:52:41.450788700+08:00" level=info msg="[TCP] 127.0.0.1:60432 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:52:41.465095900+08:00" level=info msg="[TCP] 127.0.0.1:60435 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:41.479156000+08:00" level=info msg="[TCP] 127.0.0.1:60438 --> waa-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:41.495949000+08:00" level=info msg="[TCP] 127.0.0.1:60441 --> cloudusersettings-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:41.498424500+08:00" level=info msg="[TCP] 127.0.0.1:60444 --> cloudusersettings-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:41.572154200+08:00" level=info msg="[TCP] 127.0.0.1:60450 --> cloudusersettings-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:41.572154200+08:00" level=info msg="[TCP] 127.0.0.1:60447 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-19T19:52:41.574752900+08:00" level=info msg="[TCP] 127.0.0.1:60453 --> cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:41.605402100+08:00" level=info msg="[TCP] 127.0.0.1:60456 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:41.607717100+08:00" level=info msg="[TCP] 127.0.0.1:60459 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-07-19T19:52:41.953598000+08:00" level=info msg="[TCP] 127.0.0.1:60462 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:52:41.968906700+08:00" level=info msg="[TCP] 127.0.0.1:60465 --> xpaywalletcdn-prod.azureedge.net:443 using GLOBAL"
time="2025-07-19T19:52:42.034488300+08:00" level=info msg="[TCP] 127.0.0.1:60468 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.038055600+08:00" level=info msg="[TCP] 127.0.0.1:60474 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.039919300+08:00" level=info msg="[TCP] 127.0.0.1:60471 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.042566500+08:00" level=info msg="[TCP] 127.0.0.1:60477 --> reauth.cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.056217100+08:00" level=info msg="[TCP] 127.0.0.1:60480 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.220830300+08:00" level=info msg="[TCP] 127.0.0.1:60483 --> cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.340659500+08:00" level=info msg="[TCP] 127.0.0.1:60487 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:52:42.345084400+08:00" level=info msg="[TCP] 127.0.0.1:60490 --> cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.390395700+08:00" level=info msg="[TCP] 127.0.0.1:60493 --> www.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.436521400+08:00" level=info msg="[TCP] 127.0.0.1:60497 --> cloudusersettings-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.436521400+08:00" level=info msg="[TCP] 127.0.0.1:60496 --> cloudusersettings-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.473070700+08:00" level=info msg="[TCP] 127.0.0.1:60502 --> cloudusersettings-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.566981300+08:00" level=info msg="[TCP] 127.0.0.1:60505 --> cloudusersettings-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.594922600+08:00" level=info msg="[TCP] 127.0.0.1:60508 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:52:42.626849900+08:00" level=info msg="[TCP] 127.0.0.1:60511 --> cloudusersettings-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.630215900+08:00" level=info msg="[TCP] 127.0.0.1:60512 --> cloudusersettings-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:42.916801700+08:00" level=info msg="[TCP] 127.0.0.1:60517 --> ssl.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:52:43.014271600+08:00" level=info msg="[TCP] 127.0.0.1:60520 --> cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:43.215295200+08:00" level=info msg="[TCP] 127.0.0.1:60523 --> cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:43.218500900+08:00" level=info msg="[TCP] 127.0.0.1:60524 --> cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:45.155761700+08:00" level=info msg="[TCP] 127.0.0.1:60530 --> xpaywalletcdn-prod.azureedge.net:443 using GLOBAL"
time="2025-07-19T19:52:45.995220900+08:00" level=info msg="[TCP] 127.0.0.1:60534 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-19T19:52:46.348917300+08:00" level=info msg="[TCP] 127.0.0.1:60537 --> reauth.cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:52:48.238346600+08:00" level=info msg="[TCP] 127.0.0.1:60541 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:48.241803500+08:00" level=info msg="[TCP] 127.0.0.1:60544 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:48.376905400+08:00" level=info msg="[TCP] 127.0.0.1:60548 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:52:48.627009500+08:00" level=info msg="[TCP] 127.0.0.1:60551 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:49.001054000+08:00" level=info msg="[TCP] 127.0.0.1:60554 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-19T19:52:49.001375600+08:00" level=info msg="[TCP] 127.0.0.1:60560 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:52:49.004769300+08:00" level=info msg="[TCP] 127.0.0.1:60555 --> console.volcengine.com:443 using GLOBAL"
time="2025-07-19T19:52:49.077751100+08:00" level=info msg="[TCP] 127.0.0.1:60563 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:49.387098300+08:00" level=info msg="[TCP] 127.0.0.1:60566 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:49.388117400+08:00" level=info msg="[TCP] 127.0.0.1:60567 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:49.388815800+08:00" level=info msg="[TCP] 127.0.0.1:60575 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:49.390839900+08:00" level=info msg="[TCP] 127.0.0.1:60568 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:49.391799700+08:00" level=info msg="[TCP] 127.0.0.1:60576 --> cloudconsole-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:52:49.996392900+08:00" level=info msg="[TCP] 127.0.0.1:60581 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:52:56.307878600+08:00" level=info msg="[TCP] 127.0.0.1:60590 --> console.volcengine.com:443 using GLOBAL"
time="2025-07-19T19:52:57.821061300+08:00" level=info msg="[TCP] 127.0.0.1:60594 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-19T19:53:00.273409600+08:00" level=info msg="[TCP] 127.0.0.1:60598 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:53:04.994066100+08:00" level=info msg="[TCP] 127.0.0.1:60604 --> mon.zijieapi.com:443 using GLOBAL"
time="2025-07-19T19:53:05.303222400+08:00" level=info msg="[TCP] 127.0.0.1:60607 --> mon.zijieapi.com:443 using GLOBAL"
time="2025-07-19T19:53:09.179186300+08:00" level=info msg="[TCP] 127.0.0.1:60613 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:53:11.148371800+08:00" level=info msg="[TCP] 127.0.0.1:60617 --> mon.zijieapi.com:443 using GLOBAL"
time="2025-07-19T19:53:16.571402800+08:00" level=info msg="[TCP] 127.0.0.1:60628 --> monitoring.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:53:16.571402800+08:00" level=info msg="[TCP] 127.0.0.1:60624 --> monitoring.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:53:16.574522600+08:00" level=info msg="[TCP] 127.0.0.1:60627 --> monitoring.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:53:16.909697000+08:00" level=info msg="[TCP] 127.0.0.1:60633 --> monitoring.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:53:25.616819700+08:00" level=info msg="[TCP] 127.0.0.1:60642 --> www.bing.com:443 using GLOBAL"
time="2025-07-19T19:53:33.214169200+08:00" level=info msg="[TCP] 127.0.0.1:60651 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:53:38.390549900+08:00" level=info msg="[TCP] 127.0.0.1:60657 --> aistudio.google.com:443 using GLOBAL"
time="2025-07-19T19:53:38.395241400+08:00" level=info msg="[TCP] 127.0.0.1:60660 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:53:40.543018300+08:00" level=info msg="[TCP] 127.0.0.1:60665 --> waa-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:53:40.760579800+08:00" level=info msg="[TCP] 127.0.0.1:60668 --> people-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:53:41.062476400+08:00" level=info msg="[TCP] 127.0.0.1:60671 --> people-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:53:41.991692100+08:00" level=info msg="[TCP] 127.0.0.1:60675 --> console.cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:53:43.087207800+08:00" level=info msg="[TCP] 127.0.0.1:60679 --> play.google.com:443 using GLOBAL"
time="2025-07-19T19:53:45.661387700+08:00" level=info msg="[TCP] 127.0.0.1:60683 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:53:45.729163900+08:00" level=info msg="[TCP] 127.0.0.1:60686 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-19T19:53:46.697306700+08:00" level=info msg="[TCP] 127.0.0.1:60690 --> waa-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:53:47.131085100+08:00" level=info msg="[TCP] 127.0.0.1:60693 --> aistudio.google.com:443 using GLOBAL"
time="2025-07-19T19:53:49.010960800+08:00" level=info msg="[TCP] 127.0.0.1:60698 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:53:49.010960800+08:00" level=info msg="[TCP] 127.0.0.1:60701 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:53:51.993039900+08:00" level=info msg="[TCP] 127.0.0.1:60706 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-19T19:53:52.998236100+08:00" level=info msg="[TCP] 127.0.0.1:60709 --> cloud.google.com:443 using GLOBAL"
time="2025-07-19T19:53:55.659349900+08:00" level=info msg="[TCP] 127.0.0.1:60714 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:54:01.066584500+08:00" level=info msg="[TCP] 127.0.0.1:60723 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-19T19:54:14.960723800+08:00" level=info msg="[TCP] 127.0.0.1:60738 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-19T19:54:17.541119700+08:00" level=info msg="[TCP] 127.0.0.1:60742 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:54:18.126654000+08:00" level=info msg="[TCP] 127.0.0.1:60746 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:54:18.427289900+08:00" level=info msg="[TCP] 127.0.0.1:60749 --> alkalimakersuite-pa.clients6.google.com:443 using GLOBAL"
time="2025-07-19T19:54:19.966137300+08:00" level=info msg="[TCP] 127.0.0.1:60753 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:54:25.669481600+08:00" level=info msg="[TCP] 127.0.0.1:60760 --> www.bing.com:443 using GLOBAL"
time="2025-07-19T19:54:35.355123900+08:00" level=info msg="[TCP] 127.0.0.1:60769 --> download.clashverge.dev:443 using GLOBAL"
time="2025-07-19T19:54:35.382594100+08:00" level=info msg="[TCP] 127.0.0.1:60772 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-19T19:54:42.306508400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60777 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T19:54:42.335400200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60790 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-19T23:30:49.867349300+08:00" level=warning msg="Mihomo shutting down"
