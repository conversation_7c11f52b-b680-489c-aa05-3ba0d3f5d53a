2025-05-29 11:51:13 INFO - try to run core in service mode
2025-05-29 11:51:13 INFO - start service: {"core_type": "verge-mihomo-alpha", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-05-29-1151.log", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe"}
2025-05-29 11:51:13 INFO - Initializing hotkeys, global hotkey enabled: true
2025-05-29 11:51:13 INFO - No hotkeys configured
2025-05-29 11:51:13 INFO - Starting to create window
2025-05-29 11:51:13 INFO - Creating new window
2025-05-29 11:51:14 INFO - Window created successfully, attempting to show
2025-05-29 11:51:14 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 11:51:14 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 11:51:14 INFO - running timer task `R3SfPp0qApId`
2025-05-29 11:51:16 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 11:51:16 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 12:03:44 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 12:03:44 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 12:05:23 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 12:05:23 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 12:05:23 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 12:05:23 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 12:05:49 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 12:05:49 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 12:07:41 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 12:07:41 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 12:07:55 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 12:07:55 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 12:10:16 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 12:10:16 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 12:16:32 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 12:16:32 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 12:17:47 INFO - Starting to create window
2025-05-29 12:17:47 INFO - Found existing window, trying to show it
2025-05-29 12:17:47 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-29 12:17:47 INFO - Successfully registered hotkey Control+Q for quit
2025-05-29 13:31:06 INFO - stop the core by service
2025-05-29 13:31:06 ERROR - Failed to unregister hotkey: HotKey { mods: Modifiers(CONTROL), key: KeyQ, id: 524323 }
