2025-06-29 01:09:08 INFO - try to run core in service mode
2025-06-29 01:09:08 INFO - start service: {"config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "core_type": "verge-mihomo-alpha", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-06-29-0109.log", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml"}
2025-06-29 01:09:08 INFO - Initializing hotkeys, global hotkey enabled: true
2025-06-29 01:09:08 INFO - No hotkeys configured
2025-06-29 01:09:08 INFO - Starting to create window
2025-06-29 01:09:08 INFO - Creating new window
2025-06-29 01:09:08 INFO - Window created successfully, attempting to show
2025-06-29 01:09:08 INFO - running timer task `R3SfPp0qApId`
2025-06-29 01:09:08 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-29 01:09:08 INFO - Successfully registered hotkey Control+Q for quit
2025-06-29 01:14:15 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-29 01:14:15 INFO - Successfully registered hotkey Control+Q for quit
2025-06-29 01:14:15 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-29 01:14:15 INFO - Successfully registered hotkey Control+Q for quit
2025-06-29 01:20:31 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-29 01:20:31 INFO - Successfully registered hotkey Control+Q for quit
2025-06-29 01:34:57 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-29 01:34:57 INFO - Successfully registered hotkey Control+Q for quit
2025-06-29 01:34:57 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-29 01:34:57 INFO - Successfully registered hotkey Control+Q for quit
2025-06-29 01:40:15 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-29 01:40:15 INFO - Successfully registered hotkey Control+Q for quit
2025-06-29 01:40:15 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-29 01:40:15 INFO - Successfully registered hotkey Control+Q for quit
