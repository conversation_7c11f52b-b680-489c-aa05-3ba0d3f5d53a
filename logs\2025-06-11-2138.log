2025-06-11 21:38:13 INFO - try to run core in service mode
2025-06-11 21:38:13 INFO - start service: {"config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-06-11-2138.log", "core_type": "verge-mihomo-alpha", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe"}
2025-06-11 21:38:13 INFO - Initializing hotkeys, global hotkey enabled: true
2025-06-11 21:38:13 INFO - No hotkeys configured
2025-06-11 21:38:13 INFO - Starting to create window
2025-06-11 21:38:13 INFO - Creating new window
2025-06-11 21:38:13 INFO - Window created successfully, attempting to show
2025-06-11 21:38:13 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-11 21:38:13 INFO - Successfully registered hotkey Control+Q for quit
2025-06-11 21:38:14 INFO - running timer task `R3SfPp0qApId`
2025-06-11 22:06:37 INFO - Starting to create window
2025-06-11 22:06:37 INFO - Found existing window, trying to show it
2025-06-11 22:06:37 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-11 22:06:37 INFO - Successfully registered hotkey Control+Q for quit
2025-06-11 22:06:51 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-11 22:06:51 INFO - Successfully registered hotkey Control+Q for quit
2025-06-11 22:08:35 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-11 22:08:35 INFO - Successfully registered hotkey Control+Q for quit
