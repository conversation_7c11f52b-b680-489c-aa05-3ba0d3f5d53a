Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-22T12:42:42.631579000+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-22T12:42:42.660875700+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-22T12:42:42.660875700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-22T12:42:42.665644500+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-22T12:42:42.694403800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-22T12:42:42.694403800+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-22T12:42:43.008047300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-22T12:42:43.008047300+08:00" level=info msg="Load GeoSite rule: private"
time="2025-06-22T12:42:43.030567000+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-22T12:42:43.033636700+08:00" level=info msg="Initial configuration complete, total time: 391ms"
time="2025-06-22T12:42:43.034656700+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-06-22T12:42:43.035867500+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-22T12:42:43.036379200+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-06-22T12:42:43.036379200+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-06-22T12:42:43.036379200+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider TVer"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider telegram"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider BBC"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider NowE"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Line"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider apple"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider google"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Discord"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Lan"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider Disney"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider TVB"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-22T12:42:43.054867600+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-22T12:42:45.725725600+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-22T12:42:45.726236700+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-22T12:42:45.726236700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-22T12:42:45.727257200+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-22T12:42:45.727257200+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-22T12:42:45.727257200+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-22T12:42:45.728280500+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-22T12:42:48.058462900+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-22T12:42:48.059478900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-22T12:42:48.060026400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-22T12:42:48.060534200+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-22T12:42:48.059513500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T12:42:48.060534200+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-22T12:42:48.065138000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-22T12:42:48.065138000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-22T12:42:48.065138000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-22T12:42:48.065138000+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider google"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider TVB"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider telegram"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Lan"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Line"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Disney"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider apple"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider NowE"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider BBC"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider TVer"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider Discord"
time="2025-06-22T12:42:48.066670300+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-22T12:42:48.276475300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.276475300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.276996900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.276996900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.277960500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.278473500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.278473500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.279052900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.279052900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.279052900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.279052900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.280628600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.280628600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.280628600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.280628600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.280628600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.280628600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.280628600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.281139000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283179900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283179900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283179900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283179900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283179900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283179900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283691800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283691800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283691800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283691800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283691800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283691800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283691800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283691800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.283691800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.284194800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.284202000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.289212500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.289212500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.289212500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.289212500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.289212500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.289212500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.289716500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.289724900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.289724900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:48.839803100+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-06-22T12:42:48.844047600+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-06-22T12:42:48.849896400+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-06-22T12:42:48.849896400+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-06-22T12:42:48.852408600+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-06-22T12:42:48.852408600+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-06-22T12:42:48.869449300+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-06-22T12:42:48.872784800+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-06-22T12:42:48.874091700+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-06-22T12:42:48.876162900+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-06-22T12:42:48.882370800+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-06-22T12:42:48.883762600+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-06-22T12:42:48.890289800+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-06-22T12:42:48.904211300+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-06-22T12:42:48.906421700+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-06-22T12:42:48.917248600+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-06-22T12:42:48.918035400+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-06-22T12:42:48.920097700+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-06-22T12:42:48.921523300+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-06-22T12:42:48.921523300+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-06-22T12:42:48.923489600+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-06-22T12:42:48.924616800+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-06-22T12:42:48.930406500+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-06-22T12:42:48.934286800+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-06-22T12:42:48.938409400+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-06-22T12:42:48.939160500+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-06-22T12:42:48.947199500+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-06-22T12:42:48.950488300+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-06-22T12:42:48.951748000+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-06-22T12:42:48.957111600+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-06-22T12:42:48.957828700+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-06-22T12:42:48.961932700+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-06-22T12:42:48.962755400+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-06-22T12:42:48.967893600+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-06-22T12:42:49.002704100+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-06-22T12:42:49.025559700+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-06-22T12:42:49.283241500+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-06-22T12:42:49.289144200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:49.292248400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:49.293323900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:49.293323900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:49.308230300+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-06-22T12:42:49.348730500+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-06-22T12:42:49.660782000+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-06-22T12:42:49.666831500+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-06-22T12:42:49.670688700+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-06-22T12:42:49.772914700+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-06-22T12:42:49.883023200+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-06-22T12:42:49.899488500+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-06-22T12:42:49.912571600+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-06-22T12:42:50.106211200+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-06-22T12:42:50.170584700+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-06-22T12:42:50.176157300+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-06-22T12:42:50.178576400+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-22T12:42:50.179087800+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-22T12:42:50.179087800+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-22T12:42:50.179087800+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-22T12:42:50.512670300+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-22T12:42:50.513179500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-22T12:42:50.513179500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-22T12:42:50.513589300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-22T12:42:50.513589300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-22T12:42:50.514092900+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-22T12:42:50.515122500+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider google"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider apple"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Lan"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider TVB"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider BBC"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Discord"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider telegram"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider NowE"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Disney"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Line"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider TVer"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-22T12:42:50.516651100+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-22T12:42:50.671325200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.671325200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.672531000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.673153400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.674213000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.674213000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.674213000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.674213000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.674213000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.675366200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.675366200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.675366200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.675366200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.675366200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.675366200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.675366200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.675900400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.675900400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.678494000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.678494000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.678494000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.678494000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.678494000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.678494000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679004700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679004700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679004700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679004700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679004700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679004700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679004700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679004700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679004700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679516000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679516000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679516000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679516000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679516000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.679516000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.685727300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.685727300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.685727300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.685727300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.685727300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.685727300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.686255800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:50.686255800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:51.211398800+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-06-22T12:42:51.214244900+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-06-22T12:42:51.217171500+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-06-22T12:42:51.230180800+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-06-22T12:42:51.236006700+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-06-22T12:42:51.239359200+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-06-22T12:42:51.240020800+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-06-22T12:42:51.243853400+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-06-22T12:42:51.245890500+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-06-22T12:42:51.255672000+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-06-22T12:42:51.260156600+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-06-22T12:42:51.262776500+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-06-22T12:42:51.266576700+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-06-22T12:42:51.272343000+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-06-22T12:42:51.273364500+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-06-22T12:42:51.282860500+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-06-22T12:42:51.289246500+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-06-22T12:42:51.290537600+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-06-22T12:42:51.296622900+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-06-22T12:42:51.297442500+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-06-22T12:42:51.297442500+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-06-22T12:42:51.297442500+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-06-22T12:42:51.299171400+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-06-22T12:42:51.302985200+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-06-22T12:42:51.312228000+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-06-22T12:42:51.316722800+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-06-22T12:42:51.316722800+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-06-22T12:42:51.318310500+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-06-22T12:42:51.319770200+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-06-22T12:42:51.327034600+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-06-22T12:42:51.338722600+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-06-22T12:42:51.338722600+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-06-22T12:42:51.350445100+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-06-22T12:42:51.361001800+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-06-22T12:42:51.363761200+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-06-22T12:42:51.375037400+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-06-22T12:42:51.384088300+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-06-22T12:42:51.389033400+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-06-22T12:42:51.461571600+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-06-22T12:42:51.465637900+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-06-22T12:42:51.643319400+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-06-22T12:42:51.679733500+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-06-22T12:42:51.680248700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:51.682987100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-22T12:42:51.694842400+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-06-22T12:42:51.781569800+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-06-22T12:42:51.847075700+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-06-22T12:42:52.251697100+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-06-22T12:42:52.348415000+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-06-22T12:42:52.383579100+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-06-22T12:42:52.643897400+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-06-22T12:42:52.649574900+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-22T12:42:52.649574900+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-22T12:42:52.649574900+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-22T12:42:52.649574900+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-22T12:42:53.589489600+08:00" level=info msg="[TCP] 127.0.0.1:51793 --> www.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:42:53.909353400+08:00" level=info msg="[TCP] 127.0.0.1:51801 --> clients4.google.com:443 using GLOBAL"
time="2025-06-22T12:42:54.394518000+08:00" level=info msg="[TCP] 127.0.0.1:51814 --> android.clients.google.com:443 using GLOBAL"
time="2025-06-22T12:42:58.535972300+08:00" level=info msg="[TCP] 127.0.0.1:51828 --> chrome.google.com:443 using GLOBAL"
time="2025-06-22T12:42:58.537539600+08:00" level=info msg="[TCP] 127.0.0.1:51829 --> chrome.google.com:443 using GLOBAL"
time="2025-06-22T12:43:05.*********+08:00" level=info msg="[TCP] 127.0.0.1:51842 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-22T12:43:06.*********+08:00" level=info msg="[TCP] 127.0.0.1:51838 --> www.google.com:443 using GLOBAL"
time="2025-06-22T12:43:06.*********+08:00" level=info msg="[TCP] 127.0.0.1:51844 --> api.steampowered.com:443 using GLOBAL"
time="2025-06-22T12:43:06.*********+08:00" level=info msg="[TCP] 127.0.0.1:51846 --> accounts.google.com:443 using GLOBAL"
time="2025-06-22T12:43:06.*********+08:00" level=info msg="[TCP] 127.0.0.1:51849 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-22T12:43:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:51855 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-22T12:43:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:51853 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-22T12:43:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:51852 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-22T12:43:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:51851 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-22T12:43:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:51854 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-22T12:43:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:51863 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:43:07.*********+08:00" level=info msg="[TCP] 127.0.0.1:51865 --> chromewebstore.google.com:443 using GLOBAL"
time="2025-06-22T12:43:08.777432700+08:00" level=info msg="[TCP] 127.0.0.1:51867 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:43:08.778546600+08:00" level=info msg="[TCP] 127.0.0.1:51868 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-22T12:43:08.875929500+08:00" level=info msg="[TCP] 127.0.0.1:51871 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-22T12:43:08.877142400+08:00" level=info msg="[TCP] 127.0.0.1:51872 --> lh3.google.com:443 using GLOBAL"
time="2025-06-22T12:43:09.042723900+08:00" level=info msg="[TCP] 127.0.0.1:51875 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-22T12:43:09.056349800+08:00" level=info msg="[TCP] 127.0.0.1:51880 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-22T12:43:09.057315300+08:00" level=info msg="[TCP] 127.0.0.1:51877 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-22T12:43:09.058450500+08:00" level=info msg="[TCP] 127.0.0.1:51879 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-22T12:43:09.058958300+08:00" level=info msg="[TCP] 127.0.0.1:51883 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-22T12:43:09.058958300+08:00" level=info msg="[TCP] 127.0.0.1:51885 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-22T12:43:09.059956200+08:00" level=info msg="[TCP] 127.0.0.1:51878 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-22T12:43:09.069294300+08:00" level=info msg="[TCP] 127.0.0.1:51889 --> ssl.gstatic.com:443 using GLOBAL"
time="2025-06-22T12:43:09.072665600+08:00" level=info msg="[TCP] 127.0.0.1:51891 --> ssl.gstatic.com:443 using GLOBAL"
time="2025-06-22T12:43:09.095009800+08:00" level=info msg="[TCP] 127.0.0.1:51893 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-22T12:43:09.096275600+08:00" level=info msg="[TCP] 127.0.0.1:51894 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-22T12:43:09.177009000+08:00" level=info msg="[TCP] 127.0.0.1:51897 --> ssl.gstatic.com:443 using GLOBAL"
time="2025-06-22T12:43:09.191613300+08:00" level=info msg="[TCP] 127.0.0.1:51899 --> content-autofill.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:43:09.193430000+08:00" level=info msg="[TCP] 127.0.0.1:51901 --> translate.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:43:09.205066400+08:00" level=info msg="[TCP] 127.0.0.1:51903 --> translate.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:43:09.281330200+08:00" level=info msg="[TCP] 127.0.0.1:51905 --> ogads-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-22T12:43:09.281330200+08:00" level=info msg="[TCP] 127.0.0.1:51907 --> apis.google.com:443 using GLOBAL"
time="2025-06-22T12:43:09.397781800+08:00" level=info msg="[TCP] 127.0.0.1:51909 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-06-22T12:43:09.414130500+08:00" level=info msg="[TCP] 127.0.0.1:51911 --> play.google.com:443 using GLOBAL"
time="2025-06-22T12:43:09.439676200+08:00" level=info msg="[TCP] 127.0.0.1:51913 --> play.google.com:443 using GLOBAL"
time="2025-06-22T12:43:09.494212500+08:00" level=info msg="[TCP] 127.0.0.1:51915 --> apis.google.com:443 using GLOBAL"
time="2025-06-22T12:43:09.506493300+08:00" level=info msg="[TCP] 127.0.0.1:51917 --> translate.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:43:09.565033300+08:00" level=info msg="[TCP] 127.0.0.1:51919 --> play.google.com:443 using GLOBAL"
time="2025-06-22T12:43:09.732192400+08:00" level=info msg="[TCP] 127.0.0.1:51921 --> play.google.com:443 using GLOBAL"
time="2025-06-22T12:43:09.826685500+08:00" level=info msg="[TCP] 127.0.0.1:51923 --> feedback-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-22T12:43:09.906448000+08:00" level=info msg="[TCP] 127.0.0.1:51925 --> www.google-analytics.com:443 using GLOBAL"
time="2025-06-22T12:43:09.920473700+08:00" level=info msg="[TCP] 127.0.0.1:51927 --> translate.google.com:443 using GLOBAL"
time="2025-06-22T12:43:10.014032300+08:00" level=info msg="[TCP] 127.0.0.1:51929 --> translate-pa.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:43:10.015261700+08:00" level=info msg="[TCP] 127.0.0.1:51931 --> translate-pa.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:43:10.016733100+08:00" level=info msg="[TCP] 127.0.0.1:51933 --> translate-pa.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:43:11.588913000+08:00" level=info msg="[TCP] 127.0.0.1:51936 --> ogads-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-22T12:43:13.290958500+08:00" level=info msg="[TCP] 127.0.0.1:51938 --> ogs.google.com:443 using GLOBAL"
time="2025-06-22T12:43:26.938495900+08:00" level=info msg="[TCP] 127.0.0.1:51947 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-22T12:43:30.701326500+08:00" level=info msg="[TCP] 127.0.0.1:51951 --> img.youtube.com:443 using GLOBAL"
time="2025-06-22T12:43:32.283317200+08:00" level=info msg="[TCP] 127.0.0.1:51954 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-22T12:43:33.800941500+08:00" level=info msg="[TCP] 127.0.0.1:51957 --> www.googleapis.com:443 using GLOBAL"
time="2025-06-22T12:43:33.803034100+08:00" level=info msg="[TCP] 127.0.0.1:51959 --> clients2.google.com:443 using GLOBAL"
time="2025-06-22T12:43:34.233703500+08:00" level=info msg="[TCP] 127.0.0.1:51961 --> clients2.googleusercontent.com:443 using GLOBAL"
time="2025-06-22T12:43:35.071644800+08:00" level=info msg="[TCP] 127.0.0.1:51963 --> sb-ssl.google.com:443 using GLOBAL"
time="2025-06-22T12:43:40.874937100+08:00" level=info msg="[TCP] 127.0.0.1:51967 --> www.tampermonkey.net:443 using GLOBAL"
time="2025-06-22T12:43:42.175527000+08:00" level=info msg="[TCP] 127.0.0.1:51970 --> play.google.com:443 using GLOBAL"
time="2025-06-22T14:31:48.235297200+08:00" level=warning msg="Mihomo shutting down"
