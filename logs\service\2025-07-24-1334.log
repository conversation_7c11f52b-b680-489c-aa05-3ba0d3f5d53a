Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-24T13:34:54.678793700+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-24T13:34:54.688695800+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-24T13:34:54.689210500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-24T13:34:54.689720800+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-24T13:34:54.715724800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-24T13:34:54.715724800+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-24T13:34:55.037601000+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-24T13:34:55.037601000+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-24T13:34:55.053158700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-24T13:34:55.056366700+08:00" level=info msg="Initial configuration complete, total time: 371ms"
time="2025-07-24T13:34:55.056879800+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-24T13:34:55.057899900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-24T13:34:55.057899900+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-24T13:34:55.057899900+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-24T13:34:55.058412900+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Lan"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider apple"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider google"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider telegram"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Disney"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider TVer"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Line"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider NowE"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Discord"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider BBC"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider TVB"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-24T13:34:55.066128400+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-24T13:35:00.067555300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.067555300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.067555300+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-24T13:35:00.067555300+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-24T13:35:00.068067100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068067100+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069090700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-24T13:35:00.069090700+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-24T13:35:00.069090700+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-24T13:35:00.069090700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069665800+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-24T13:35:00.069090700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069090700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069665800+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-24T13:35:00.069090700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069665800+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-24T13:35:00.069665800+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-24T13:35:00.069090700+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-24T13:35:00.069090700+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-24T13:35:00.069163100+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069090700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-24T13:35:00.070180100+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069665800+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-24T13:35:00.069665800+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-24T13:35:00.069090700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.069665800+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-24T13:35:00.070180100+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-24T13:35:00.068576500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T13:35:00.070180100+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-24T13:35:00.070180100+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-24T13:35:00.070180100+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-24T13:35:00.070180100+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-24T13:35:00.071496600+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-24T13:35:00.071496600+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-24T13:35:00.071496600+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-24T13:35:00.071496600+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-24T23:18:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:64955 --> www.google.com:443 using GLOBAL"
time="2025-07-24T23:18:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:64957 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:18:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:64960 --> www.google.com:443 using GLOBAL"
time="2025-07-24T23:18:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:64965 --> accounts.google.com:443 using GLOBAL"
time="2025-07-24T23:18:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:64967 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:18:35.*********+08:00" level=info msg="[TCP] 127.0.0.1:64969 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:18:35.*********+08:00" level=info msg="[TCP] 127.0.0.1:64971 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:18:35.*********+08:00" level=info msg="[TCP] 127.0.0.1:64985 --> www.trae.ai:443 using GLOBAL"
time="2025-07-24T23:18:36.*********+08:00" level=info msg="[TCP] 127.0.0.1:64993 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:18:36.*********+08:00" level=info msg="[TCP] 127.0.0.1:64995 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:18:36.*********+08:00" level=info msg="[TCP] 127.0.0.1:64997 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-24T23:18:36.*********+08:00" level=info msg="[TCP] 127.0.0.1:64999 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:18:37.159945100+08:00" level=info msg="[TCP] 127.0.0.1:65002 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:18:37.179903200+08:00" level=info msg="[TCP] 127.0.0.1:65004 --> vcs-s-sg.byteintl.com:443 using GLOBAL"
time="2025-07-24T23:18:37.191878500+08:00" level=info msg="[TCP] 127.0.0.1:65006 --> api.trae.cn:443 using GLOBAL"
time="2025-07-24T23:18:37.226002700+08:00" level=info msg="[TCP] 127.0.0.1:65008 --> libraweb-sg.tiktok.com:443 using GLOBAL"
time="2025-07-24T23:18:37.235908500+08:00" level=info msg="[TCP] 127.0.0.1:65011 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:18:37.499712100+08:00" level=info msg="[TCP] 127.0.0.1:65013 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:18:37.939568400+08:00" level=info msg="[TCP] 127.0.0.1:65018 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:18:37.951350000+08:00" level=info msg="[TCP] 127.0.0.1:65020 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-24T23:18:37.955861300+08:00" level=info msg="[TCP] 127.0.0.1:65022 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:18:37.973693500+08:00" level=info msg="[TCP] 127.0.0.1:65024 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:18:38.071700800+08:00" level=info msg="[TCP] 127.0.0.1:65026 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:18:38.259495500+08:00" level=info msg="[TCP] 127.0.0.1:65028 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:18:38.427106400+08:00" level=info msg="[TCP] 127.0.0.1:65030 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:18:39.626237500+08:00" level=info msg="[TCP] 127.0.0.1:65038 --> www.google.cn:443 using GLOBAL"
time="2025-07-24T23:18:39.645648700+08:00" level=info msg="[TCP] 127.0.0.1:65040 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-24T23:18:39.823355600+08:00" level=info msg="[TCP] 127.0.0.1:65042 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-24T23:18:41.390523800+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-24T23:18:41.390523800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-24T23:18:41.391035900+08:00" level=error msg="🇰🇷韩国04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-24T23:18:41.391035900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-24T23:18:42.136137700+08:00" level=info msg="[TCP] 127.0.0.1:65081 --> vcs-s-sg.byteintl.com:443 using GLOBAL"
time="2025-07-24T23:18:42.690238400+08:00" level=info msg="[TCP] 127.0.0.1:65083 --> lf-rc1.yhgfb-static.com:443 using GLOBAL"
time="2025-07-24T23:18:44.259752300+08:00" level=info msg="[TCP] 127.0.0.1:65087 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:18:45.903369600+08:00" level=info msg="[TCP] 127.0.0.1:65098 --> lf-rc1.yhgfb-static.com:443 using GLOBAL"
time="2025-07-24T23:18:47.136176000+08:00" level=info msg="[TCP] 127.0.0.1:65102 --> lf-rc1.yhgfb-static.com:443 using GLOBAL"
time="2025-07-24T23:18:47.457185600+08:00" level=info msg="[TCP] 127.0.0.1:65104 --> lf-rc1.yhgfb-static.com:443 using GLOBAL"
time="2025-07-24T23:18:47.495021000+08:00" level=info msg="[TCP] 127.0.0.1:65106 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:18:47.541128000+08:00" level=info msg="[TCP] 127.0.0.1:65108 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:18:47.809135500+08:00" level=info msg="[TCP] 127.0.0.1:65111 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:18:47.811844100+08:00" level=info msg="[TCP] 127.0.0.1:65110 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:18:47.849113400+08:00" level=info msg="[TCP] 127.0.0.1:65115 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:18:47.852928500+08:00" level=info msg="[TCP] 127.0.0.1:65116 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:18:55.*********+08:00" level=info msg="[TCP] 127.0.0.1:65134 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:18:57.*********+08:00" level=info msg="[TCP] 127.0.0.1:65353 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T23:18:59.*********+08:00" level=info msg="[TCP] 127.0.0.1:65358 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:00.*********+08:00" level=info msg="[TCP] 127.0.0.1:65361 --> accounts.google.com:443 using GLOBAL"
time="2025-07-24T23:19:00.*********+08:00" level=info msg="[TCP] 127.0.0.1:65369 --> www.trae.ai:443 using GLOBAL"
time="2025-07-24T23:19:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:65375 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:19:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:65378 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:65380 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:19:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:65382 --> mon.zijieapi.com:443 using GLOBAL"
time="2025-07-24T23:19:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:65385 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:19:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:65387 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-24T23:19:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:65390 --> vcs-s-sg.byteintl.com:443 using GLOBAL"
time="2025-07-24T23:19:01.972974000+08:00" level=info msg="[TCP] 127.0.0.1:65392 --> api.trae.cn:443 using GLOBAL"
time="2025-07-24T23:19:02.613394800+08:00" level=info msg="[TCP] 127.0.0.1:65394 --> libraweb-sg.tiktok.com:443 using GLOBAL"
time="2025-07-24T23:19:02.631486100+08:00" level=info msg="[TCP] 127.0.0.1:65396 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:02.995103400+08:00" level=info msg="[TCP] 127.0.0.1:65406 --> lf-rc1.yhgfb-static.com:443 using GLOBAL"
time="2025-07-24T23:19:02.998278300+08:00" level=info msg="[TCP] 127.0.0.1:65403 --> servicewechat.com:443 using GLOBAL"
time="2025-07-24T23:19:03.225399100+08:00" level=info msg="[TCP] 127.0.0.1:65408 --> lf-rc1.yhgfb-static.com:443 using GLOBAL"
time="2025-07-24T23:19:03.272024700+08:00" level=info msg="[TCP] 127.0.0.1:65410 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:03.316097100+08:00" level=info msg="[TCP] 127.0.0.1:65412 --> lf-rc1.yhgfb-static.com:443 using GLOBAL"
time="2025-07-24T23:19:04.064238000+08:00" level=info msg="[TCP] 127.0.0.1:65414 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:19:04.072277500+08:00" level=info msg="[TCP] 127.0.0.1:65417 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-24T23:19:04.085626000+08:00" level=info msg="[TCP] 127.0.0.1:65419 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:19:04.100569600+08:00" level=info msg="[TCP] 127.0.0.1:65421 --> www.google.com:443 using GLOBAL"
time="2025-07-24T23:19:04.388784400+08:00" level=info msg="[TCP] 127.0.0.1:65425 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-24T23:19:04.395089300+08:00" level=info msg="[TCP] 127.0.0.1:65424 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:19:04.403554500+08:00" level=info msg="[TCP] 127.0.0.1:65428 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:19:04.410937800+08:00" level=info msg="[TCP] 127.0.0.1:65429 --> www.google.com:443 using GLOBAL"
time="2025-07-24T23:19:04.505596700+08:00" level=info msg="[TCP] 127.0.0.1:65433 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:19:04.835669900+08:00" level=info msg="[TCP] 127.0.0.1:65435 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-24T23:19:04.978768200+08:00" level=info msg="[TCP] 127.0.0.1:65438 --> servicewechat.com:443 using GLOBAL"
time="2025-07-24T23:19:05.482225700+08:00" level=info msg="[TCP] 127.0.0.1:65440 --> servicewechat.com:443 using GLOBAL"
time="2025-07-24T23:19:06.689266800+08:00" level=info msg="[TCP] 127.0.0.1:65444 --> www.google.co.jp:443 using GLOBAL"
time="2025-07-24T23:19:16.592283600+08:00" level=info msg="[TCP] 127.0.0.1:65484 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-24T23:19:21.853397100+08:00" level=info msg="[TCP] 127.0.0.1:65494 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:19:25.756128700+08:00" level=info msg="[TCP] 127.0.0.1:65501 --> csp.withgoogle.com:443 using GLOBAL"
time="2025-07-24T23:19:42.710009800+08:00" level=info msg="[TCP] 127.0.0.1:49178 --> clientservices.googleapis.com:443 using GLOBAL"
time="2025-07-24T23:19:53.663967000+08:00" level=info msg="[TCP] 127.0.0.1:49196 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:19:54.770033500+08:00" level=info msg="[TCP] 127.0.0.1:49199 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:55.278168800+08:00" level=info msg="[TCP] 127.0.0.1:49201 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:55.943253300+08:00" level=info msg="[TCP] 127.0.0.1:49203 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:55.951051900+08:00" level=info msg="[TCP] 127.0.0.1:49205 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:19:55.989413400+08:00" level=info msg="[TCP] 127.0.0.1:49208 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:55.991699700+08:00" level=info msg="[TCP] 127.0.0.1:49207 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:55.993386600+08:00" level=info msg="[TCP] 127.0.0.1:49209 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:56.014167400+08:00" level=info msg="[TCP] 127.0.0.1:49214 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-24T23:19:56.014167400+08:00" level=info msg="[TCP] 127.0.0.1:49216 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:56.242354000+08:00" level=info msg="[TCP] 127.0.0.1:49218 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:56.264909700+08:00" level=info msg="[TCP] 127.0.0.1:49220 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:19:56.322037400+08:00" level=info msg="[TCP] 127.0.0.1:49222 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:49225 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:19:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:49227 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:19:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:49230 --> www.trae.ai:443 using GLOBAL"
time="2025-07-24T23:19:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:49229 --> accounts.google.com:443 using GLOBAL"
time="2025-07-24T23:19:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:49232 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:19:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:49239 --> lf-headquarters-speed.yhgfb-cn-static.com:443 using GLOBAL"
time="2025-07-24T23:19:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:49236 --> sf16-short-sg.bytedapm.com:443 using GLOBAL"
time="2025-07-24T23:19:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:49237 --> lf-c-flwb.bytetos.com:443 using GLOBAL"
time="2025-07-24T23:19:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:49242 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:19:57.*********+08:00" level=info msg="[TCP] 127.0.0.1:49245 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T23:19:59.*********+08:00" level=info msg="[TCP] 127.0.0.1:49249 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:19:59.*********+08:00" level=info msg="[TCP] 127.0.0.1:49272 --> sf16-short-va.bytedapm.com:443 using GLOBAL"
time="2025-07-24T23:19:59.963640700+08:00" level=info msg="[TCP] 127.0.0.1:49279 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-24T23:20:00.003264400+08:00" level=info msg="[TCP] 127.0.0.1:49281 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:20:00.920155700+08:00" level=info msg="[TCP] 127.0.0.1:49332 --> vcs-s-sg.byteintl.com:443 using GLOBAL"
time="2025-07-24T23:20:01.432508000+08:00" level=info msg="[TCP] 127.0.0.1:49334 --> api.trae.cn:443 using GLOBAL"
time="2025-07-24T23:20:02.055506900+08:00" level=info msg="[TCP] 127.0.0.1:49337 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:20:02.057526800+08:00" level=info msg="[TCP] 127.0.0.1:49339 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-24T23:20:02.074428900+08:00" level=info msg="[TCP] 127.0.0.1:49342 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-24T23:20:02.089608100+08:00" level=info msg="[TCP] 127.0.0.1:49344 --> www.google.com:443 using GLOBAL"
time="2025-07-24T23:20:02.344265500+08:00" level=info msg="[TCP] 127.0.0.1:49346 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:02.374788800+08:00" level=info msg="[TCP] 127.0.0.1:49348 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:20:02.376159600+08:00" level=info msg="[TCP] 127.0.0.1:49349 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-24T23:20:02.389801700+08:00" level=info msg="[TCP] 127.0.0.1:49356 --> www.google.com:443 using GLOBAL"
time="2025-07-24T23:20:02.389801700+08:00" level=info msg="[TCP] 127.0.0.1:49353 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-24T23:20:02.391739700+08:00" level=info msg="[TCP] 127.0.0.1:49354 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-24T23:20:02.394273500+08:00" level=info msg="[TCP] 127.0.0.1:49355 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-24T23:20:02.394273500+08:00" level=info msg="[TCP] 127.0.0.1:49352 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-24T23:20:02.662122500+08:00" level=info msg="[TCP] 127.0.0.1:49363 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-24T23:20:29.547377800+08:00" level=info msg="[TCP] 127.0.0.1:49586 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:20:30.509316600+08:00" level=info msg="[TCP] 127.0.0.1:49591 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:20:30.605492300+08:00" level=info msg="[TCP] 127.0.0.1:49596 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:30.609762200+08:00" level=info msg="[TCP] 127.0.0.1:49594 --> www.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:30.609762200+08:00" level=info msg="[TCP] 127.0.0.1:49597 --> sf16-short-sg.bytedapm.com:443 using GLOBAL"
time="2025-07-24T23:20:30.618271500+08:00" level=info msg="[TCP] 127.0.0.1:49600 --> lf-c-flwb.bytetos.com:443 using GLOBAL"
time="2025-07-24T23:20:30.625196900+08:00" level=info msg="[TCP] 127.0.0.1:49601 --> lf-headquarters-speed.yhgfb-cn-static.com:443 using GLOBAL"
time="2025-07-24T23:20:30.626075600+08:00" level=info msg="[TCP] 127.0.0.1:49604 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:20:30.929120800+08:00" level=info msg="[TCP] 127.0.0.1:49623 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:20:31.793839400+08:00" level=info msg="[TCP] 127.0.0.1:49672 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:20:32.201851700+08:00" level=info msg="[TCP] 127.0.0.1:49674 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:20:32.248940200+08:00" level=info msg="[TCP] 127.0.0.1:49676 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:32.503639900+08:00" level=info msg="[TCP] 127.0.0.1:49679 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-24T23:20:32.534528100+08:00" level=info msg="[TCP] 127.0.0.1:49682 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:20:32.707538400+08:00" level=info msg="[TCP] 127.0.0.1:49685 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:32.709688900+08:00" level=info msg="[TCP] 127.0.0.1:49684 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:32.874411000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49580 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T23:20:33.218919600+08:00" level=info msg="[TCP] 127.0.0.1:49691 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:33.224441100+08:00" level=info msg="[TCP] 127.0.0.1:49693 --> api.trae.cn:443 using GLOBAL"
time="2025-07-24T23:20:33.516046900+08:00" level=info msg="[TCP] 127.0.0.1:49699 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:33.520895300+08:00" level=info msg="[TCP] 127.0.0.1:49702 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:34.161183400+08:00" level=info msg="[TCP] 127.0.0.1:49704 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:20:34.164615500+08:00" level=info msg="[TCP] 127.0.0.1:49706 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-24T23:20:34.196918000+08:00" level=info msg="[TCP] 127.0.0.1:49708 --> www.google.com:443 using GLOBAL"
time="2025-07-24T23:20:34.203505500+08:00" level=info msg="[TCP] 127.0.0.1:49710 --> api-us-east.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:34.205404400+08:00" level=info msg="[TCP] 127.0.0.1:49711 --> api-us-east.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:34.372731800+08:00" level=info msg="[TCP] 127.0.0.1:49714 --> libraweb-sg.tiktok.com:443 using GLOBAL"
time="2025-07-24T23:20:34.379397800+08:00" level=info msg="[TCP] 127.0.0.1:49715 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:20:34.390922900+08:00" level=info msg="[TCP] 127.0.0.1:49718 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:20:34.449077900+08:00" level=info msg="[TCP] 127.0.0.1:49720 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-24T23:20:34.463938500+08:00" level=info msg="[TCP] 127.0.0.1:49722 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-24T23:20:34.474683600+08:00" level=info msg="[TCP] 127.0.0.1:49724 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:20:34.490307500+08:00" level=info msg="[TCP] 127.0.0.1:49726 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:20:34.495480100+08:00" level=info msg="[TCP] 127.0.0.1:49728 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:20:34.507920500+08:00" level=info msg="[TCP] 127.0.0.1:49730 --> www.google.com:443 using GLOBAL"
time="2025-07-24T23:20:38.038086300+08:00" level=info msg="[TCP] 127.0.0.1:49740 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:20:42.309595200+08:00" level=info msg="[TCP] 127.0.0.1:49766 --> api-us-east.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:42.472500100+08:00" level=info msg="[TCP] 127.0.0.1:49799 --> api.trae.ai:443 using GLOBAL"
time="2025-07-24T23:20:44.008897300+08:00" level=info msg="[TCP] 127.0.0.1:49831 --> sf16-short-va.bytedapm.com:443 using GLOBAL"
time="2025-07-24T23:20:45.840440900+08:00" level=info msg="[TCP] 127.0.0.1:49837 --> clientservices.googleapis.com:443 using GLOBAL"
time="2025-07-24T23:20:54.887467800+08:00" level=info msg="[TCP] 127.0.0.1:49855 --> sf16-short-sg.bytedapm.com:443 using GLOBAL"
time="2025-07-24T23:20:54.889387900+08:00" level=info msg="[TCP] 127.0.0.1:49858 --> sf16-short-va.bytedapm.com:443 using GLOBAL"
time="2025-07-24T23:20:54.894318000+08:00" level=info msg="[TCP] 127.0.0.1:49860 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-24T23:20:57.198246100+08:00" level=info msg="[TCP] 127.0.0.1:49879 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T23:20:57.621642600+08:00" level=info msg="[TCP] 127.0.0.1:49883 --> github.com:443 using GLOBAL"
time="2025-07-24T23:21:02.055663900+08:00" level=info msg="[TCP] 127.0.0.1:49914 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:21:02.628151600+08:00" level=info msg="[TCP] 127.0.0.1:49918 --> vcs-s-sg.byteintl.com:443 using GLOBAL"
time="2025-07-24T23:21:04.788997700+08:00" level=info msg="[TCP] 127.0.0.1:49936 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:21:07.019993900+08:00" level=info msg="[TCP] 127.0.0.1:49941 --> sf16-short-va.bytedapm.com:443 using GLOBAL"
time="2025-07-24T23:21:11.*********+08:00" level=info msg="[TCP] 127.0.0.1:49951 --> sf16-tcc-tos-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:21:11.*********+08:00" level=info msg="[TCP] 127.0.0.1:49947 --> api.trae.cn:443 using GLOBAL"
time="2025-07-24T23:21:11.*********+08:00" level=info msg="[TCP] 127.0.0.1:49949 --> sf16-tcc-tos-sg.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:21:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:49955 --> sf16-passport-sg.ibytedtos.com:443 using GLOBAL"
time="2025-07-24T23:21:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:49957 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:21:14.*********+08:00" level=info msg="[TCP] 127.0.0.1:49960 --> api-sg-central.trae.ai:443 using GLOBAL"
time="2025-07-24T23:21:14.*********+08:00" level=info msg="[TCP] 127.0.0.1:49967 --> api-sg-central.trae.ai:443 using GLOBAL"
time="2025-07-24T23:21:14.*********+08:00" level=info msg="[TCP] 127.0.0.1:49964 --> api-sg-central.trae.ai:443 using GLOBAL"
time="2025-07-24T23:21:14.*********+08:00" level=info msg="[TCP] 127.0.0.1:49963 --> api-sg-central.trae.ai:443 using GLOBAL"
time="2025-07-24T23:21:14.*********+08:00" level=info msg="[TCP] 127.0.0.1:49968 --> api-sg-central.trae.ai:443 using GLOBAL"
time="2025-07-24T23:21:15.*********+08:00" level=info msg="[TCP] 127.0.0.1:49972 --> api-sg-central.trae.ai:443 using GLOBAL"
time="2025-07-24T23:21:16.*********+08:00" level=info msg="[TCP] 127.0.0.1:49974 --> api-sg-central.trae.ai:443 using GLOBAL"
time="2025-07-24T23:21:16.391681200+08:00" level=info msg="[TCP] 127.0.0.1:49976 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:21:17.912481700+08:00" level=info msg="[TCP] 127.0.0.1:49978 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:21:19.029177400+08:00" level=info msg="[TCP] 127.0.0.1:50000 --> lf-cdn.trae.ai:443 using GLOBAL"
time="2025-07-24T23:21:19.032229100+08:00" level=info msg="[TCP] 127.0.0.1:50002 --> www.google.com:443 using GLOBAL"
time="2025-07-24T23:21:25.064380500+08:00" level=info msg="[TCP] 127.0.0.1:50027 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-07-24T23:21:26.205834900+08:00" level=info msg="[TCP] 127.0.0.1:50029 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-07-24T23:21:26.512396400+08:00" level=info msg="[TCP] 127.0.0.1:50031 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-07-24T23:21:35.985369100+08:00" level=info msg="[TCP] 127.0.0.1:50064 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-24T23:21:36.983329300+08:00" level=info msg="[TCP] 127.0.0.1:50069 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:21:37.171107400+08:00" level=info msg="[TCP] 127.0.0.1:50071 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:21:40.654039000+08:00" level=info msg="[TCP] 127.0.0.1:50078 --> clients2.google.com:443 using GLOBAL"
time="2025-07-24T23:21:40.655476600+08:00" level=info msg="[TCP] 127.0.0.1:50080 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:21:42.150642200+08:00" level=info msg="[TCP] 127.0.0.1:50083 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:21:42.446733800+08:00" level=info msg="[TCP] 127.0.0.1:50086 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:21:42.451619200+08:00" level=info msg="[TCP] 127.0.0.1:50087 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:21:42.451619200+08:00" level=info msg="[TCP] 127.0.0.1:50089 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T23:21:42.563382900+08:00" level=info msg="[TCP] 127.0.0.1:50092 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T23:21:42.585529200+08:00" level=info msg="[TCP] 127.0.0.1:50098 --> sf16-tcc-tos-va.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:21:42.585529200+08:00" level=info msg="[TCP] 127.0.0.1:50095 --> api-us-east.trae.ai:443 using GLOBAL"
time="2025-07-24T23:21:42.587321200+08:00" level=info msg="[TCP] 127.0.0.1:50094 --> sf16-tcc-tos-sg.byteoversea.com:443 using GLOBAL"
time="2025-07-24T23:21:43.535334300+08:00" level=info msg="[TCP] 127.0.0.1:50101 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-24T23:21:43.553463600+08:00" level=info msg="[TCP] 127.0.0.1:50103 --> google.com:443 using GLOBAL"
time="2025-07-24T23:21:43.557546000+08:00" level=info msg="[TCP] 127.0.0.1:50104 --> google.com:443 using GLOBAL"
time="2025-07-24T23:21:43.857879300+08:00" level=info msg="[TCP] 127.0.0.1:50107 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-24T23:21:47.291035000+08:00" level=info msg="[TCP] 127.0.0.1:50116 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-24T23:21:48.100372900+08:00" level=info msg="[TCP] 127.0.0.1:50119 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T01:04:35.232449100+08:00" level=warning msg="Mihomo shutting down"
