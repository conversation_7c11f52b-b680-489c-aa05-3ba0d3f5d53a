2025-05-28 00:42:50 INFO - try to run core in service mode
2025-05-28 00:42:50 INFO - start service: {"config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "core_type": "verge-mihomo-alpha", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-05-28-0042.log"}
2025-05-28 00:42:50 INFO - Initializing hotkeys, global hotkey enabled: true
2025-05-28 00:42:50 INFO - No hotkeys configured
2025-05-28 00:42:50 INFO - Starting to create window
2025-05-28 00:42:50 INFO - Creating new window
2025-05-28 00:42:51 INFO - Window created successfully, attempting to show
2025-05-28 00:42:51 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:42:51 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:42:54 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:42:54 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:42:58 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:42:58 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:43:32 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:43:32 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:43:42 INFO - stop the core by service
2025-05-28 00:43:42 ERROR - Failed to unregister hotkey: HotKey { mods: Modifiers(CONTROL), key: KeyQ, id: 524323 }
