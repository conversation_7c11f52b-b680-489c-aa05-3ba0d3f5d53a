2025-05-31 15:19:30 INFO - try to run core in service mode
2025-05-31 15:19:30 INFO - start service: {"config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-05-31-1519.log", "core_type": "verge-mihomo-alpha", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml"}
2025-05-31 15:19:30 INFO - Initializing hotkeys, global hotkey enabled: true
2025-05-31 15:19:30 INFO - No hotkeys configured
2025-05-31 15:19:30 INFO - Starting to create window
2025-05-31 15:19:30 INFO - Creating new window
2025-05-31 15:19:30 INFO - Window created successfully, attempting to show
2025-05-31 15:19:30 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 15:19:30 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 18:55:10 INFO - Starting to create window
2025-05-31 18:55:10 INFO - Found existing window, trying to show it
2025-05-31 18:55:10 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 18:55:10 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 18:58:40 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 18:58:40 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:07:40 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:07:40 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:11:17 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:11:17 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:28:03 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:28:03 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:30:53 INFO - Starting to create window
2025-05-31 19:30:53 INFO - Found existing window, trying to show it
2025-05-31 19:30:53 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:30:53 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:34:48 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:34:48 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:34:48 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:34:48 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:35:14 INFO - stop the core by service
2025-05-31 19:35:14 ERROR - Failed to unregister hotkey: HotKey { mods: Modifiers(CONTROL), key: KeyQ, id: 524323 }
