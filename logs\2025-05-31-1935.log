2025-05-31 19:35:23 INFO - try to run core in service mode
2025-05-31 19:35:23 INFO - start service: {"config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "core_type": "verge-mihomo-alpha", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-05-31-1935.log"}
2025-05-31 19:35:23 INFO - Initializing hotkeys, global hotkey enabled: true
2025-05-31 19:35:23 INFO - No hotkeys configured
2025-05-31 19:35:23 INFO - Starting to create window
2025-05-31 19:35:23 INFO - Creating new window
2025-05-31 19:35:24 INFO - Window created successfully, attempting to show
2025-05-31 19:35:24 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:35:24 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:36:23 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:36:23 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:36:23 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:36:23 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:37:28 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:37:28 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:39:27 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:39:27 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:39:27 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:39:27 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:40:01 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:40:01 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:40:01 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:40:01 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:40:37 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:40:37 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:40:37 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-31 19:40:37 INFO - Successfully registered hotkey Control+Q for quit
2025-05-31 19:40:42 INFO - stop the core by service
2025-05-31 19:40:42 ERROR - Failed to unregister hotkey: HotKey { mods: Modifiers(CONTROL), key: KeyQ, id: 524323 }
