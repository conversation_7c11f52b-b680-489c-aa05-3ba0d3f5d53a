Spawning process: C:\Program Files\Clash Verge\verge-mihomo.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-05-28T00:22:50.642243700+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-28T00:22:50.671957400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-28T00:22:50.671957400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-28T00:22:50.675535500+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-05-28T00:22:50.675535500+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-05-28T00:22:50.677584800+08:00" level=info msg="Sniffer is closed"
time="2025-05-28T00:22:50.678097900+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-05-28T00:22:50.679124700+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-28T00:23:30.074674300+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-28T00:23:30.075186300+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-28T00:23:30.075186300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-28T00:23:30.076207500+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-05-28T00:23:30.093068300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-28T00:23:30.093068300+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-05-28T00:23:30.280893900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-28T00:23:30.280893900+08:00" level=info msg="Load GeoSite rule: private"
time="2025-05-28T00:23:30.294906300+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-28T00:23:30.296908000+08:00" level=info msg="Initial configuration complete, total time: 222ms"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Lan"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Line"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider BBC"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Disney"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider TVer"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider telegram"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider apple"
time="2025-05-28T00:23:30.298910300+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider TVB"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider NowE"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Discord"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-28T00:23:30.297909900+08:00" level=info msg="Start initial provider google"
time="2025-05-28T00:23:35.623093600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.623093600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.623093600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.624122100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.624122100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.624122100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.625304400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.625304400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.626477300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.626477300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.626477300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.626477300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.626477300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.626477300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.626477300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.629714000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.629714000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.629714000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.629714000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.629714000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.629714000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.629714000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.630227300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.630227300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.630227300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.630227300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.630227300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.630227300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.630227300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.630227300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.630227300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.635699400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.635699400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.635699400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.635699400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.635699400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.635699400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.635699400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.636212800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.636212800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.636212800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.636212800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.636212800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.636212800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.636212800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.636212800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.636724900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.636724900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:35.642976100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:36.234269200+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-05-28T00:23:36.265987600+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-05-28T00:23:36.307910500+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-05-28T00:23:36.309920500+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-05-28T00:23:36.316648000+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-05-28T00:23:36.320515400+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-05-28T00:23:36.321979800+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-05-28T00:23:36.346345000+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-05-28T00:23:36.347391400+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-05-28T00:23:36.352933900+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-05-28T00:23:36.355163300+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-05-28T00:23:36.356967200+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-05-28T00:23:36.357476000+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-05-28T00:23:36.372685900+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-05-28T00:23:36.410217300+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-05-28T00:23:36.414362500+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-05-28T00:23:36.423167200+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-05-28T00:23:36.425335500+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-05-28T00:23:36.431398600+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-05-28T00:23:36.435820300+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-05-28T00:23:36.435820300+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-05-28T00:23:36.438208200+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-05-28T00:23:36.442168300+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-05-28T00:23:36.469502800+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-05-28T00:23:36.501123900+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-05-28T00:23:36.506422700+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-05-28T00:23:36.509328000+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-05-28T00:23:36.511651700+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-05-28T00:23:36.517538600+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-05-28T00:23:36.526059600+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-05-28T00:23:36.530350800+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-05-28T00:23:36.534612500+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-05-28T00:23:36.540063200+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-05-28T00:23:36.545713500+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-05-28T00:23:36.567517700+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-05-28T00:23:36.615179500+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-05-28T00:23:36.664716900+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-05-28T00:23:36.664716900+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-05-28T00:23:36.676160800+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-05-28T00:23:36.693313800+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-05-28T00:23:36.872008400+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-05-28T00:23:36.958004600+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-05-28T00:23:36.958004600+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-05-28T00:23:37.346877500+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-05-28T00:23:37.425278300+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-05-28T00:23:37.758515000+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-05-28T00:23:38.049515000+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-05-28T00:23:38.226613100+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-28T00:23:38.227119600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-28T00:23:38.227119600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-28T00:23:38.227625900+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-28T00:23:38.228132200+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-28T00:23:38.228132200+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-28T00:23:38.229148000+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-05-28T00:23:40.300398600+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": net/http: TLS handshake timeout"
time="2025-05-28T00:23:41.175411500+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-05-28T00:23:41.179998100+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-28T00:23:41.179998100+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-28T00:23:41.179998100+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-28T00:23:41.179998100+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-28T00:23:41.181544900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Disney"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider NowE"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider TVB"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider TVer"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider BBC"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider telegram"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Discord"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Line"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider google"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider apple"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-28T00:23:41.182056900+08:00" level=info msg="Start initial provider Lan"
time="2025-05-28T00:23:46.242414400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.242414400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.242414400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.243301600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.243301600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.244286400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.244286400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.245244900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.245244900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.245244900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.246789400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.246789400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.246789400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.246789400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.246789400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.246789400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.246789400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.247301300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.247301300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.247301300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.247301300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.247301300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.247301300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.247301300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252375500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252375500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252375500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252375500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252375500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252891300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252891300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252891300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252891300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252891300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252891300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252891300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252891300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252891300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.252891300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.253401800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.253401800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.253401800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.253401800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.253401800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.253401800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.253401800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.260189300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.260189300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.260189300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:23:46.338255400+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-05-28T00:23:46.340556300+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-05-28T00:23:46.342238700+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-05-28T00:23:46.356252500+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-05-28T00:23:46.360178900+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-05-28T00:23:46.370830600+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-05-28T00:23:46.886986200+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-05-28T00:23:46.886986200+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-05-28T00:23:46.897003500+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-05-28T00:23:46.897885700+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-05-28T00:23:46.939863800+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-05-28T00:23:46.968896900+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-05-28T00:23:46.971328400+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-05-28T00:23:46.991675500+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-05-28T00:23:47.003313400+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-05-28T00:23:47.017246600+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-05-28T00:23:47.019383900+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-05-28T00:23:47.025184300+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-05-28T00:23:47.032960900+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-05-28T00:23:47.035274400+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-05-28T00:23:47.043738900+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-05-28T00:23:47.046593400+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-05-28T00:23:47.047901800+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-05-28T00:23:47.057231500+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-05-28T00:23:47.092087500+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-05-28T00:23:47.105930700+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-05-28T00:23:47.116353200+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-05-28T00:23:47.139967800+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-05-28T00:23:47.148800300+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-05-28T00:23:47.150570200+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-05-28T00:23:47.154247500+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-05-28T00:23:47.164266500+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-05-28T00:23:47.196347500+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-05-28T00:23:47.249261000+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-05-28T00:23:47.256675900+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-05-28T00:23:47.383274800+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-05-28T00:23:47.385043700+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-05-28T00:23:47.406173400+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-05-28T00:23:47.428781200+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-05-28T00:23:47.435272300+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-05-28T00:23:47.452156700+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-05-28T00:23:47.465363400+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-05-28T00:23:47.469126000+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-05-28T00:23:47.555035900+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-05-28T00:23:47.606419300+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-05-28T00:23:47.618207600+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-05-28T00:23:47.634347100+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-05-28T00:23:47.707855300+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-05-28T00:23:55.259509500+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-05-28T00:23:55.263609100+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-28T00:23:55.263609100+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-28T00:23:55.263609100+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-28T00:23:55.263609100+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
