Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-26T16:04:19.490565900+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-26T16:04:19.500390800+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-26T16:04:19.500390800+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-26T16:04:19.501932700+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-26T16:04:19.523924700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-26T16:04:19.524440000+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-26T16:04:19.833759800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-26T16:04:19.833759800+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-26T16:04:19.850680800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-26T16:04:19.854264700+08:00" level=info msg="Initial configuration complete, total time: 358ms"
time="2025-07-26T16:04:19.855292700+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-26T16:04:19.855805400+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-26T16:04:19.856318300+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-26T16:04:19.856318300+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-26T16:04:19.856831700+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Line"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider telegram"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Discord"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider BBC"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Disney"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Lan"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider apple"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider NowE"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider google"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider TVer"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider TVB"
time="2025-07-26T16:04:19.864655800+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-26T16:04:20.045200500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.045200500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.046719900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.046719900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.046719900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.046719900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.048393500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.048393500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.048393500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.050255500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.050255500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.050255500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.050255500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.050255500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.050758400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.050758400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.050758400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.056246200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.056246200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.056246200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.056246200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.056246200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.056748800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.056748800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.057264000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.057264000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.057264000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.057264000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.057264000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.057776000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.057776000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.057776000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.057776000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.058290000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.058290000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.058290000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.058290000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.058801300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.058801300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.058801300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.074021000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.074523300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.074523300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.074523300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.074523300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.075552400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.075552400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.076063700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.076063700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:20.095762000+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-26T16:04:20.098348600+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-26T16:04:20.098348600+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-26T16:04:20.100855100+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-26T16:04:20.100855100+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-26T16:04:20.100855100+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-26T16:04:20.102884700+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-26T16:04:20.104203300+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-26T16:04:20.105183800+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-26T16:04:20.105183800+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-26T16:04:20.105183800+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-26T16:04:20.106051400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-26T16:04:20.107559500+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-26T16:04:20.107559500+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-26T16:04:20.108862400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-26T16:04:20.111443600+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-26T16:04:20.111443600+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-26T16:04:20.116957800+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-26T16:04:20.118460700+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-26T16:04:20.118460700+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-26T16:04:20.118460700+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-26T16:04:20.118981300+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-26T16:04:20.120240600+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-26T16:04:20.121116200+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-26T16:04:20.121116200+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-26T16:04:20.121619100+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-26T16:04:20.122828400+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-26T16:04:20.122828400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-26T16:04:20.122828400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-26T16:04:20.122828400+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-26T16:04:20.124082100+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-26T16:04:20.124082100+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-26T16:04:20.124753300+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-26T16:04:20.133231400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-26T16:04:20.134946700+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-26T16:04:20.137650900+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-26T16:04:20.137650900+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-26T16:04:20.140019800+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-26T16:04:20.145489700+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-26T16:04:20.194374800+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-26T16:04:21.172444900+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-26T16:04:21.195662900+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-26T16:04:21.234775300+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-26T16:04:21.236245600+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-26T16:04:21.261049700+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-26T16:04:21.296452500+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-26T16:04:21.346318300+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-26T16:04:21.379635300+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-26T16:04:21.443085800+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-26T16:04:21.445644400+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-26T16:04:21.445644400+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-26T16:04:21.446155600+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-26T16:04:21.446155600+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-26T16:04:22.539377900+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-26T16:04:22.539881200+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-26T16:04:22.539881200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-26T16:04:22.540395300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-26T16:04:22.540395300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-26T16:04:22.540395300+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-26T16:04:22.541929400+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider apple"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider telegram"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Line"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Discord"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider google"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Lan"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider NowE"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider BBC"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider Disney"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider TVB"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider TVer"
time="2025-07-26T16:04:22.543475200+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-26T16:04:22.699329300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.699832100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.699832100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.699832100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.699832100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.702095700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.702095700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.702095700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.704150200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.704150200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.704653300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.704653300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.704653300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.710183800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.710183800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.710183800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.710786700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.711290400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.711290400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.711290400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.711804700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.712320100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.712320100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.713344800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.713344800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.730468700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.730468700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.730971600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.730971600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.731514200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.731514200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.732044500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.732044500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.732557100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.732557100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.732557100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.733069300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.733069300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.733582500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.735234900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.735737200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.736243000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.736243000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.736754400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.738901000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.738901000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.739926000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.740447900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.740447900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:22.772355300+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-26T16:04:22.772355300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-26T16:04:22.772355300+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-26T16:04:22.772866000+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-26T16:04:22.772866000+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-26T16:04:22.772866000+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-26T16:04:22.772866000+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-26T16:04:22.772866000+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-26T16:04:22.772866000+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-26T16:04:22.772866000+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-26T16:04:22.773402200+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-26T16:04:22.773402200+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-26T16:04:22.773402200+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-26T16:04:22.773402200+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-26T16:04:22.787383700+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-26T16:04:22.787383700+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-26T16:04:22.787383700+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-26T16:04:22.787383700+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-26T16:04:22.787383700+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-26T16:04:22.789577900+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-26T16:04:22.790341900+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-26T16:04:22.790844600+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-26T16:04:22.790844600+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-26T16:04:22.790844600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-26T16:04:22.828561600+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-26T16:04:22.828561600+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-26T16:04:22.828561600+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-26T16:04:22.828561600+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-26T16:04:22.828561600+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-26T16:04:22.830098700+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-26T16:04:22.831172800+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-26T16:04:22.831172800+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-26T16:04:22.831172800+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-26T16:04:22.831172800+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-26T16:04:22.850184800+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-26T16:04:22.882047200+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-26T16:04:22.882047200+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-26T16:04:22.882047200+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-26T16:04:22.883051200+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-26T16:04:22.886122400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-26T16:04:23.825905700+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-26T16:04:23.989791100+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-26T16:04:24.156657600+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-26T16:04:24.168930200+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-26T16:04:24.363727800+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-26T16:04:25.110567500+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-26T16:04:25.168875800+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-26T16:04:25.265732800+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-26T16:04:25.281615100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.214956000+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-26T16:04:26.219755300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-26T16:04:26.219755300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-26T16:04:26.219755300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-26T16:04:26.219755300+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-26T16:04:26.566596400+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-26T16:04:26.567102300+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-26T16:04:26.567102300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-26T16:04:26.567674700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-26T16:04:26.568177200+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-26T16:04:26.568177200+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-26T16:04:26.569195000+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider apple"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider BBC"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider Line"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider google"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider Discord"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider telegram"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider TVB"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider Lan"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider Disney"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-26T16:04:26.570718000+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider TVer"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider NowE"
time="2025-07-26T16:04:26.571231900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-26T16:04:26.877635600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.878934800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.878934800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.879823600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.879823600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.879823600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.879823600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.879823600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.883073500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.883577300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.883577300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.883577300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.883577300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.884093300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.884093300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.890741500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.890741500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.890741500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.891245500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.891762700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.891762700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.891762700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.891762700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.892273900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.892273900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.892273900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.892273900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.892824700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.892824700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.892824700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.893336600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.893412000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.893412000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.893412000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.893412000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.893412000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.893914400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.893914400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.893914400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.893914400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.894432900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.894483900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.894483900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.915624200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.915624200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.915624200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.916126600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.916637500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.916637500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T16:04:26.930791800+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-26T16:04:26.930791800+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-26T16:04:26.932610200+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-26T16:04:26.933545100+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-26T16:04:26.933545100+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-26T16:04:26.935424900+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-26T16:04:26.938334300+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-26T16:04:26.940438400+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-26T16:04:26.942466700+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-26T16:04:26.958333300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-26T16:04:26.958333300+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-26T16:04:26.958333300+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-26T16:04:26.958333300+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-26T16:04:26.958333300+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-26T16:04:26.958333300+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-26T16:04:26.958333300+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-26T16:04:26.958836100+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-26T16:04:26.960209700+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-26T16:04:26.960209700+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-26T16:04:26.960209700+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-26T16:04:26.960209700+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-26T16:04:26.960209700+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-26T16:04:26.960209700+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-26T16:04:26.962891900+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-26T16:04:26.962891900+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-26T16:04:26.962891900+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-26T16:04:26.965070800+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-26T16:04:26.965070800+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-26T16:04:26.965070800+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-26T16:04:26.967580800+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-26T16:04:26.975089800+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-26T16:04:26.977643300+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-26T16:04:26.978541500+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-26T16:04:26.982985600+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-26T16:04:26.982985600+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-26T16:04:27.992515600+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-26T16:04:28.067022400+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-26T16:04:28.157412000+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-26T16:04:28.194721900+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-26T16:04:28.225766600+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-26T16:04:28.234777100+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-26T16:04:28.320116600+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-26T16:04:28.663756200+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-26T16:04:28.978560200+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-26T16:04:29.009437200+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-26T16:04:29.071216600+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-26T16:04:29.139140000+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-26T16:04:29.255210400+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-26T16:04:29.620684600+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-26T16:04:29.627129100+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-26T16:04:29.627129100+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-26T16:04:29.627129100+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-26T16:04:29.627129100+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-26T16:04:32.626092400+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T16:04:32.626092400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T16:25:32.491753900+08:00" level=error msg="🇯🇵日本03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T16:25:32.491753900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T16:25:32.491753900+08:00" level=error msg="🇸🇬新加坡04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T16:25:32.491753900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T16:26:01.543743000+08:00" level=info msg="[TCP] 127.0.0.1:53758 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:26:16.562502200+08:00" level=info msg="[TCP] 127.0.0.1:53771 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:26:17.532827500+08:00" level=info msg="[TCP] 127.0.0.1:53775 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:26:17.639659900+08:00" level=info msg="[TCP] 127.0.0.1:53778 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:26:52.960213600+08:00" level=info msg="[TCP] 127.0.0.1:53809 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:26:55.034126100+08:00" level=info msg="[TCP] 127.0.0.1:53814 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:27:07.980538800+08:00" level=info msg="[TCP] 127.0.0.1:53825 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:27:08.599951100+08:00" level=info msg="[TCP] 127.0.0.1:53830 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:27:09.714854100+08:00" level=info msg="[TCP] 127.0.0.1:53833 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:27:11.833263300+08:00" level=info msg="[TCP] 127.0.0.1:53838 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:27:15.948503600+08:00" level=info msg="[TCP] 127.0.0.1:53843 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:27:16.056408100+08:00" level=info msg="[TCP] 127.0.0.1:53846 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:27:16.160701500+08:00" level=info msg="[TCP] 127.0.0.1:53849 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:27:16.785013300+08:00" level=info msg="[TCP] 127.0.0.1:53852 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:27:17.904687900+08:00" level=info msg="[TCP] 127.0.0.1:53857 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:27:20.016772200+08:00" level=info msg="[TCP] 127.0.0.1:53860 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:27:24.123382000+08:00" level=info msg="[TCP] 127.0.0.1:53867 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:27:24.240189500+08:00" level=info msg="[TCP] 127.0.0.1:53870 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:27:24.355757400+08:00" level=info msg="[TCP] 127.0.0.1:53873 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:27:39.787759600+08:00" level=info msg="[TCP] 127.0.0.1:53889 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T16:27:39.892441000+08:00" level=info msg="[TCP] 127.0.0.1:53892 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T16:27:55.426847000+08:00" level=info msg="[TCP] 127.0.0.1:53907 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:27:55.535603200+08:00" level=info msg="[TCP] 127.0.0.1:53910 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:27:56.036026500+08:00" level=info msg="[TCP] 127.0.0.1:53913 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:27:59.148794400+08:00" level=info msg="[TCP] 127.0.0.1:53918 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:28:06.268469700+08:00" level=info msg="[TCP] 127.0.0.1:53926 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:28:27.589189000+08:00" level=info msg="[TCP] 127.0.0.1:53945 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:28:27.692848500+08:00" level=info msg="[TCP] 127.0.0.1:53948 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:28:31.018158200+08:00" level=info msg="[TCP] 127.0.0.1:53957 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:28:31.136590100+08:00" level=info msg="[TCP] 127.0.0.1:53960 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:28:31.746745800+08:00" level=info msg="[TCP] 127.0.0.1:53963 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:28:32.863522800+08:00" level=info msg="[TCP] 127.0.0.1:53966 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:28:33.375780400+08:00" level=info msg="[TCP] 127.0.0.1:53969 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:28:34.988712000+08:00" level=info msg="[TCP] 127.0.0.1:53974 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:28:36.174632300+08:00" level=info msg="[TCP] 127.0.0.1:53977 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:28:36.283658200+08:00" level=info msg="[TCP] 127.0.0.1:53980 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:28:55.037658500+08:00" level=info msg="[TCP] 127.0.0.1:54000 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:28:55.142364500+08:00" level=info msg="[TCP] 127.0.0.1:54003 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:28:55.429969700+08:00" level=info msg="[TCP] 127.0.0.1:54007 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:28:55.536186300+08:00" level=info msg="[TCP] 127.0.0.1:54010 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:29:07.197062000+08:00" level=info msg="[TCP] 127.0.0.1:54234 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:29:07.301108600+08:00" level=info msg="[TCP] 127.0.0.1:54238 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:29:07.914314400+08:00" level=info msg="[TCP] 127.0.0.1:54242 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:29:09.033124900+08:00" level=info msg="[TCP] 127.0.0.1:54245 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:29:09.820540700+08:00" level=info msg="[TCP] 127.0.0.1:54248 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:29:09.924651800+08:00" level=info msg="[TCP] 127.0.0.1:54251 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:29:10.543434100+08:00" level=info msg="[TCP] 127.0.0.1:54255 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:29:17.060730100+08:00" level=info msg="[TCP] 127.0.0.1:54265 --> files.pythonhosted.org:443 using GLOBAL"
time="2025-07-26T16:29:39.319032200+08:00" level=info msg="[TCP] 127.0.0.1:54286 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-26T16:29:39.423869800+08:00" level=info msg="[TCP] 127.0.0.1:54289 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-26T16:29:41.487968600+08:00" level=info msg="[TCP] 127.0.0.1:54294 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:29:41.550498200+08:00" level=info msg="[TCP] 127.0.0.1:54297 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:29:41.654036500+08:00" level=info msg="[TCP] 127.0.0.1:54300 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:29:44.496367200+08:00" level=info msg="[TCP] 127.0.0.1:54306 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:29:44.603199800+08:00" level=info msg="[TCP] 127.0.0.1:54309 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:29:51.211529500+08:00" level=info msg="[TCP] 127.0.0.1:54316 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:29:51.318387600+08:00" level=info msg="[TCP] 127.0.0.1:54319 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:29:55.433685000+08:00" level=info msg="[TCP] 127.0.0.1:54324 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:30:16.306164700+08:00" level=info msg="[TCP] 127.0.0.1:54341 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T16:30:16.412014700+08:00" level=info msg="[TCP] 127.0.0.1:54344 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T16:30:51.987336500+08:00" level=info msg="[TCP] 127.0.0.1:54374 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-26T16:30:55.035250400+08:00" level=info msg="[TCP] 127.0.0.1:54380 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:30:55.142823400+08:00" level=info msg="[TCP] 127.0.0.1:54383 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:31:00.016355300+08:00" level=info msg="[TCP] 127.0.0.1:54388 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-26T16:31:16.677418000+08:00" level=info msg="[TCP] 127.0.0.1:54406 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-26T16:31:46.428756400+08:00" level=info msg="[TCP] 127.0.0.1:54647 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:31:49.246392400+08:00" level=info msg="[TCP] 127.0.0.1:54651 --> nav.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:31:52.337039100+08:00" level=info msg="[TCP] 127.0.0.1:54657 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:31:53.846763300+08:00" level=info msg="[TCP] 127.0.0.1:54661 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:31:55.424081700+08:00" level=info msg="[TCP] 127.0.0.1:54664 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:32:12.920170000+08:00" level=info msg="[TCP] 127.0.0.1:54677 --> aefd.nelreports.net:443 using GLOBAL"
time="2025-07-26T16:32:55.038010400+08:00" level=info msg="[TCP] 127.0.0.1:54715 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:32:56.530671700+08:00" level=info msg="[TCP] 127.0.0.1:54720 --> watson.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:33:04.018316000+08:00" level=info msg="[TCP] 127.0.0.1:54727 --> files.pythonhosted.org:443 using GLOBAL"
time="2025-07-26T16:33:09.410765300+08:00" level=info msg="[TCP] 127.0.0.1:54733 --> config.edge.skype.com:443 using GLOBAL"
time="2025-07-26T16:33:24.089071000+08:00" level=info msg="[TCP] 127.0.0.1:54747 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:33:39.109903600+08:00" level=info msg="[TCP] 127.0.0.1:54760 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:33:39.594443000+08:00" level=info msg="[TCP] 127.0.0.1:54763 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:33:40.527906400+08:00" level=info msg="[TCP] 127.0.0.1:54765 --> files.pythonhosted.org:443 using GLOBAL"
time="2025-07-26T16:33:48.234537000+08:00" level=info msg="[TCP] 127.0.0.1:54772 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:33:48.884316400+08:00" level=info msg="[TCP] 127.0.0.1:54775 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T16:34:00.369268300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54782 --> api.vc.bilibili.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:02.636039700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54786 --> nav.smartscreen.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:03.546620000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54789 --> v10.events.data.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:05.369822600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54793 --> api.vc.bilibili.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:07.636112600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54796 --> nav.smartscreen.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:23.103698000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54808 --> collector.github.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:23.165259900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54810 --> www.google-analytics.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:24.404175600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54815 --> nexus-websocket-a.intercom.io:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:24.493816600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54817 --> functional.events.data.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:25.449438200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54828 --> www.google-analytics.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-26T16:34:27.868058500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54822 --> chat.deepseek.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:27.887474700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54824 --> gator.volces.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:28.104230800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54826 --> collector.github.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:28.166125600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54828 --> www.google-analytics.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:29.405077400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54830 --> nexus-websocket-a.intercom.io:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:29.494366500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54832 --> functional.events.data.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:32.869073300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54838 --> chat.deepseek.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:32.887706300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54840 --> gator.volces.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:37.903500500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54845 --> gator.volces.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:39.814621900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54849 --> functional.events.data.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:42.904177400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54854 --> gator.volces.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:44.482370800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54856 --> vscode-sync.trafficmanager.net:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:44.815156700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54858 --> functional.events.data.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:48.759921500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54866 --> nexus-websocket-a.intercom.io:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:49.483211600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54868 --> vscode-sync.trafficmanager.net:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:53.760926100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54872 --> nexus-websocket-a.intercom.io:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:54.670439700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54876 --> functional.events.data.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:58.510075200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54880 --> chat.deepseek.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:58.539845400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54882 --> gator.volces.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:59.568502400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54885 --> gator.volces.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:59.585568200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54887 --> www.google-analytics.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:34:59.671504400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54889 --> functional.events.data.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:35:00.532934700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54892 --> apmplus.volces.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:35:00.540974800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54894 --> mobile.events.data.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:35:00.595116200+08:00" level=info msg="[TCP] 127.0.0.1:54913 --> apmplus.volces.com:443 using GLOBAL"
time="2025-07-26T16:35:00.599001200+08:00" level=info msg="[TCP] 127.0.0.1:54915 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:35:00.626748600+08:00" level=info msg="[TCP] 127.0.0.1:54909 --> gator.volces.com:443 using GLOBAL"
time="2025-07-26T16:35:00.646941300+08:00" level=info msg="[TCP] 127.0.0.1:54911 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T16:35:00.928438700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54897 --> i0.hdslb.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:35:00.932510400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54899 --> api.vc.bilibili.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:35:00.983482000+08:00" level=info msg="[TCP] 127.0.0.1:54917 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:35:00.988894300+08:00" level=info msg="[TCP] 127.0.0.1:54919 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:35:01.433519200+08:00" level=info msg="[TCP] 127.0.0.1:54921 --> gator.volces.com:443 using GLOBAL"
time="2025-07-26T16:35:01.586779600+08:00" level=info msg="[TCP] 127.0.0.1:54904 --> chat.deepseek.com:443 using GLOBAL"
time="2025-07-26T16:35:01.614285800+08:00" level=info msg="[TCP] 127.0.0.1:54906 --> gator.volces.com:443 using GLOBAL"
time="2025-07-26T16:35:01.858434000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54901 --> functional.events.data.microsoft.com:443 error: n1.huawei-api.top:19825 connect error: connect failed: dial tcp **************:19825: i/o timeout"
time="2025-07-26T16:35:01.914852500+08:00" level=info msg="[TCP] 127.0.0.1:54925 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:35:16.602659300+08:00" level=info msg="[TCP] 127.0.0.1:54937 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T16:35:18.561259300+08:00" level=info msg="[TCP] 127.0.0.1:54941 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:35:18.726985400+08:00" level=info msg="[TCP] 127.0.0.1:54943 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:35:20.306151200+08:00" level=info msg="[TCP] 127.0.0.1:54947 --> login.live.com:443 using GLOBAL"
time="2025-07-26T16:35:20.478895800+08:00" level=info msg="[TCP] 127.0.0.1:54949 --> login.live.com:443 using GLOBAL"
time="2025-07-26T16:35:22.045177100+08:00" level=info msg="[TCP] 127.0.0.1:54951 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T16:35:30.614171900+08:00" level=info msg="[TCP] 127.0.0.1:54961 --> static.deepseek.com:443 using GLOBAL"
time="2025-07-26T16:35:39.415811000+08:00" level=info msg="[TCP] 127.0.0.1:54969 --> static.deepseek.com:443 using GLOBAL"
time="2025-07-26T16:35:47.874179400+08:00" level=info msg="[TCP] 127.0.0.1:54979 --> gator.volces.com:443 using GLOBAL"
time="2025-07-26T16:35:47.885517400+08:00" level=info msg="[TCP] 127.0.0.1:54978 --> chat.deepseek.com:443 using GLOBAL"
time="2025-07-26T16:42:32.447077600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55363 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:32.447181800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55374 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:34.884393400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55370 --> github.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:34.884393400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55373 --> github.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:34.885327000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55371 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:34.885327000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55372 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:37.447587700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55384 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:37.447587700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55383 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:38.905464300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55386 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:39.884577000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55392 --> github.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:39.884577000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55391 --> github.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:39.885773500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55398 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:39.885773500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55397 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:43.906449800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55407 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:44.005392800+08:00" level=info msg="[TCP] 127.0.0.1:55425 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:42:44.289485100+08:00" level=info msg="[TCP] 127.0.0.1:55427 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:42:44.886223200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55420 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:44.941914700+08:00" level=info msg="[TCP] 127.0.0.1:55435 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:42:44.968962600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55418 --> www.google-analytics.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:45.027637600+08:00" level=info msg="[TCP] 127.0.0.1:55437 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T16:42:45.472262700+08:00" level=info msg="[TCP] 127.0.0.1:55439 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:42:45.800598500+08:00" level=info msg="[TCP] 127.0.0.1:55441 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:42:45.818730300+08:00" level=info msg="[TCP] 127.0.0.1:55443 --> github.com:443 using GLOBAL"
time="2025-07-26T16:42:45.923089300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55419 --> github.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-26T16:42:45.978998000+08:00" level=info msg="[TCP] 127.0.0.1:55445 --> github.com:443 using GLOBAL"
time="2025-07-26T16:42:46.811287100+08:00" level=info msg="[TCP] 127.0.0.1:55447 --> release-assets.githubusercontent.com:443 using GLOBAL"
time="2025-07-26T16:42:47.812496200+08:00" level=info msg="[TCP] 127.0.0.1:55450 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:42:48.492391900+08:00" level=info msg="[TCP] 127.0.0.1:55453 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:42:53.369752400+08:00" level=info msg="[TCP] 127.0.0.1:55461 --> release-assets.githubusercontent.com:443 using GLOBAL"
time="2025-07-26T16:42:53.840984700+08:00" level=info msg="[TCP] 127.0.0.1:55464 --> dl-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:43:17.077547200+08:00" level=info msg="[TCP] 127.0.0.1:55484 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T16:43:27.990118900+08:00" level=info msg="[TCP] 127.0.0.1:55496 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T16:43:43.989550400+08:00" level=info msg="[TCP] 127.0.0.1:55510 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:44:08.423815000+08:00" level=info msg="[TCP] 127.0.0.1:55531 --> github.com:443 using GLOBAL"
time="2025-07-26T16:44:18.073392200+08:00" level=info msg="[TCP] 127.0.0.1:55754 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-26T16:44:20.799998500+08:00" level=info msg="[TCP] 127.0.0.1:55758 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:44:21.897877100+08:00" level=error msg="🇸🇬新加坡01 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T16:44:21.897877100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T16:44:21.898398900+08:00" level=error msg="🇸🇬新加坡04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T16:44:21.898398900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T16:44:21.898916700+08:00" level=error msg="🇰🇷韩国04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T16:44:21.898916700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T16:44:21.907411000+08:00" level=error msg="🇸🇬新加坡04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T16:44:21.907411000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T16:44:30.794115800+08:00" level=info msg="[TCP] 127.0.0.1:55769 --> release-assets.githubusercontent.com:443 using GLOBAL"
time="2025-07-26T16:44:32.757546500+08:00" level=info msg="[TCP] 127.0.0.1:55773 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:44:37.994089100+08:00" level=info msg="[TCP] 127.0.0.1:55781 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T16:44:42.097370700+08:00" level=info msg="[TCP] 127.0.0.1:55785 --> ocsp.comodoca.com:80 using GLOBAL"
time="2025-07-26T16:44:43.641018100+08:00" level=info msg="[TCP] 127.0.0.1:55785 --> ocsp.sectigo.com:80 using GLOBAL"
time="2025-07-26T16:44:43.986134100+08:00" level=info msg="[TCP] 127.0.0.1:55790 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:44:44.454056700+08:00" level=info msg="[TCP] 127.0.0.1:55792 --> app-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:45:17.182804000+08:00" level=info msg="[TCP] 127.0.0.1:55818 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T16:45:43.985368400+08:00" level=info msg="[TCP] 127.0.0.1:55838 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:45:51.616895500+08:00" level=info msg="[TCP] 127.0.0.1:55846 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:45:54.358644800+08:00" level=info msg="[TCP] 127.0.0.1:55850 --> v20.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:46:02.993184700+08:00" level=info msg="[TCP] 127.0.0.1:55856 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-26T16:46:40.054764500+08:00" level=info msg="[TCP] 127.0.0.1:55888 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T16:46:43.979164200+08:00" level=info msg="[TCP] 127.0.0.1:55892 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:47:24.505986500+08:00" level=info msg="[TCP] 127.0.0.1:55925 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:47:27.259082000+08:00" level=info msg="[TCP] 127.0.0.1:55929 --> activity.windows.com:443 using GLOBAL"
time="2025-07-26T16:47:28.734897400+08:00" level=info msg="[TCP] 127.0.0.1:55933 --> activity.windows.com:443 using GLOBAL"
time="2025-07-26T16:47:32.397307800+08:00" level=info msg="[TCP] 127.0.0.1:55937 --> login.live.com:443 using GLOBAL"
time="2025-07-26T16:47:33.794210900+08:00" level=info msg="[TCP] 127.0.0.1:55939 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-07-26T16:47:35.167549800+08:00" level=info msg="[TCP] 127.0.0.1:55943 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:47:37.623658400+08:00" level=info msg="[TCP] 127.0.0.1:55946 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:47:39.201936600+08:00" level=info msg="[TCP] 127.0.0.1:55949 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:47:43.985788000+08:00" level=info msg="[TCP] 127.0.0.1:55955 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:48:08.262415100+08:00" level=info msg="[TCP] 127.0.0.1:55975 --> cn.bing.com:443 using GLOBAL"
time="2025-07-26T16:48:11.315767700+08:00" level=info msg="[TCP] 127.0.0.1:55978 --> gator.volces.com:443 using GLOBAL"
time="2025-07-26T16:48:17.351443800+08:00" level=info msg="[TCP] 127.0.0.1:55991 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T16:48:24.549160000+08:00" level=info msg="[TCP] 127.0.0.1:56000 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:48:43.988730700+08:00" level=info msg="[TCP] 127.0.0.1:56021 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T16:49:05.888233300+08:00" level=info msg="[TCP] 127.0.0.1:56041 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:49:07.711882300+08:00" level=info msg="[TCP] 127.0.0.1:56045 --> nav.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:49:10.500445200+08:00" level=info msg="[TCP] 127.0.0.1:56050 --> chat.deepseek.com:443 using GLOBAL"
time="2025-07-26T16:49:11.323299200+08:00" level=info msg="[TCP] 127.0.0.1:56052 --> gator.volces.com:443 using GLOBAL"
time="2025-07-26T16:49:11.662433600+08:00" level=info msg="[TCP] 127.0.0.1:56054 --> static.deepseek.com:443 using GLOBAL"
time="2025-07-26T16:49:12.797904100+08:00" level=info msg="[TCP] 127.0.0.1:56058 --> static.deepseek.com:443 using GLOBAL"
time="2025-07-26T16:49:17.404311000+08:00" level=info msg="[TCP] 127.0.0.1:56064 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T16:49:18.795672400+08:00" level=info msg="[TCP] 127.0.0.1:56068 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-26T16:49:22.070801300+08:00" level=info msg="[TCP] 127.0.0.1:56076 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T16:49:43.986133300+08:00" level=info msg="[TCP] 127.0.0.1:56100 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T16:50:26.865000000+08:00" level=info msg="[TCP] 127.0.0.1:56138 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:50:29.047442200+08:00" level=info msg="[TCP] 127.0.0.1:56142 --> files.pythonhosted.org:443 using GLOBAL"
time="2025-07-26T16:50:35.347119600+08:00" level=info msg="[TCP] 127.0.0.1:56148 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:50:36.146255300+08:00" level=info msg="[TCP] 127.0.0.1:56150 --> files.pythonhosted.org:443 using GLOBAL"
time="2025-07-26T16:50:39.119255600+08:00" level=info msg="[TCP] 127.0.0.1:56155 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:50:40.335823300+08:00" level=info msg="[TCP] 127.0.0.1:56157 --> pypi.org:443 using GLOBAL"
time="2025-07-26T16:50:43.994863300+08:00" level=info msg="[TCP] 127.0.0.1:56163 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:19:00.677379500+08:00" level=error msg="🇸🇬新加坡04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T17:19:00.677379500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T17:19:00.686875400+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T17:19:00.686875400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T17:19:08.987087300+08:00" level=info msg="[TCP] 127.0.0.1:58149 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:19:19.098266500+08:00" level=info msg="[TCP] 127.0.0.1:58160 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:19:43.982578100+08:00" level=info msg="[TCP] 127.0.0.1:58182 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T17:20:43.987318800+08:00" level=info msg="[TCP] 127.0.0.1:58263 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:20:44.602358000+08:00" level=info msg="[TCP] 127.0.0.1:58265 --> clash-verge-rev.github.io:443 using GLOBAL"
time="2025-07-26T17:20:44.604993200+08:00" level=info msg="[TCP] 127.0.0.1:58266 --> clash-verge-rev.github.io:443 using GLOBAL"
time="2025-07-26T17:20:45.085222400+08:00" level=info msg="[TCP] 127.0.0.1:58269 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:20:45.092391000+08:00" level=info msg="[TCP] 127.0.0.1:58271 --> www.clashverge.dev:443 using GLOBAL"
time="2025-07-26T17:20:45.430912600+08:00" level=info msg="[TCP] 127.0.0.1:58273 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T17:20:45.608397600+08:00" level=info msg="[TCP] 127.0.0.1:58276 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:20:45.700731400+08:00" level=info msg="[TCP] 127.0.0.1:58278 --> cn.bing.com:443 using GLOBAL"
time="2025-07-26T17:20:46.186641800+08:00" level=info msg="[TCP] 127.0.0.1:58280 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-26T17:20:46.195509500+08:00" level=info msg="[TCP] 127.0.0.1:58282 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-26T17:20:46.753820900+08:00" level=info msg="[TCP] 127.0.0.1:58285 --> github.com:443 using GLOBAL"
time="2025-07-26T17:20:47.209262600+08:00" level=info msg="[TCP] 127.0.0.1:58288 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:20:47.211156900+08:00" level=info msg="[TCP] 127.0.0.1:58290 --> api.github.com:443 using GLOBAL"
time="2025-07-26T17:20:47.211156900+08:00" level=info msg="[TCP] 127.0.0.1:58292 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-26T17:20:47.216773000+08:00" level=info msg="[TCP] 127.0.0.1:58294 --> api.github.com:443 using GLOBAL"
time="2025-07-26T17:20:47.898452300+08:00" level=info msg="[TCP] 127.0.0.1:58297 --> avatars.githubusercontent.com:443 using GLOBAL"
time="2025-07-26T17:20:47.899816900+08:00" level=info msg="[TCP] 127.0.0.1:58296 --> github.githubassets.com:443 using GLOBAL"
time="2025-07-26T17:21:04.224707400+08:00" level=info msg="[TCP] 127.0.0.1:58315 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:21:19.210502000+08:00" level=info msg="[TCP] 127.0.0.1:58329 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:21:28.160137700+08:00" level=info msg="[TCP] 127.0.0.1:58340 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:21:28.168611400+08:00" level=info msg="[TCP] 127.0.0.1:58339 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:21:36.906439100+08:00" level=info msg="[TCP] 127.0.0.1:58352 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-26T17:21:43.986247000+08:00" level=info msg="[TCP] 127.0.0.1:58360 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T17:21:45.884039800+08:00" level=info msg="[TCP] 127.0.0.1:58364 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:21:49.666987300+08:00" level=info msg="[TCP] 127.0.0.1:58370 --> ntp.msn.com:443 using GLOBAL"
time="2025-07-26T17:21:51.534137000+08:00" level=info msg="[TCP] 127.0.0.1:58375 --> c.msn.com:443 using GLOBAL"
time="2025-07-26T17:21:51.534640900+08:00" level=info msg="[TCP] 127.0.0.1:58374 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:21:51.536846500+08:00" level=info msg="[TCP] 127.0.0.1:58377 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:21:51.538861900+08:00" level=info msg="[TCP] 127.0.0.1:58386 --> api.msn.com:443 using GLOBAL"
time="2025-07-26T17:21:51.538861900+08:00" level=info msg="[TCP] 127.0.0.1:58379 --> c.bing.com:443 using GLOBAL"
time="2025-07-26T17:21:51.538861900+08:00" level=info msg="[TCP] 127.0.0.1:58380 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:21:51.540300000+08:00" level=info msg="[TCP] 127.0.0.1:58383 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:21:51.543874200+08:00" level=info msg="[TCP] 127.0.0.1:58388 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-07-26T17:21:51.675147800+08:00" level=info msg="[TCP] 127.0.0.1:58390 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-26T17:21:52.544753200+08:00" level=info msg="[TCP] 127.0.0.1:58393 --> login.live.com:443 using GLOBAL"
time="2025-07-26T17:21:53.577797100+08:00" level=info msg="[TCP] 127.0.0.1:58401 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-26T17:21:53.577797100+08:00" level=info msg="[TCP] 127.0.0.1:58398 --> r.bing.com:443 using GLOBAL"
time="2025-07-26T17:21:53.579207100+08:00" level=info msg="[TCP] 127.0.0.1:58399 --> r.bing.com:443 using GLOBAL"
time="2025-07-26T17:21:53.584785800+08:00" level=info msg="[TCP] 127.0.0.1:58404 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-26T17:22:10.877051100+08:00" level=info msg="[TCP] 127.0.0.1:58419 --> r.bing.com:443 using GLOBAL"
time="2025-07-26T17:22:10.881206300+08:00" level=info msg="[TCP] 127.0.0.1:58421 --> r.bing.com:443 using GLOBAL"
time="2025-07-26T17:22:10.882536100+08:00" level=info msg="[TCP] 127.0.0.1:58422 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:22:10.882536100+08:00" level=info msg="[TCP] 127.0.0.1:58424 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:22:13.756339300+08:00" level=info msg="[TCP] 127.0.0.1:58429 --> storage.live.com:443 using GLOBAL"
time="2025-07-26T17:22:14.643250600+08:00" level=info msg="[TCP] 127.0.0.1:58434 --> pytorch.org:443 using GLOBAL"
time="2025-07-26T17:22:14.646072800+08:00" level=info msg="[TCP] 127.0.0.1:58433 --> pytorch.org:443 using GLOBAL"
time="2025-07-26T17:22:14.750570100+08:00" level=info msg="[TCP] 127.0.0.1:58438 --> login.live.com:443 using GLOBAL"
time="2025-07-26T17:22:14.808463800+08:00" level=info msg="[TCP] 127.0.0.1:58440 --> pytorch.org:443 using GLOBAL"
time="2025-07-26T17:22:15.467286600+08:00" level=info msg="[TCP] 127.0.0.1:58443 --> use.fontawesome.com:443 using GLOBAL"
time="2025-07-26T17:22:15.470817300+08:00" level=info msg="[TCP] 127.0.0.1:58444 --> use.fontawesome.com:443 using GLOBAL"
time="2025-07-26T17:22:15.743099100+08:00" level=info msg="[TCP] 127.0.0.1:58447 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:22:16.176193600+08:00" level=info msg="[TCP] 127.0.0.1:58449 --> cdn.sapphire.microsoftapp.net:443 using GLOBAL"
time="2025-07-26T17:22:17.032792900+08:00" level=info msg="[TCP] 127.0.0.1:58452 --> js.hsforms.net:443 using GLOBAL"
time="2025-07-26T17:22:17.997000400+08:00" level=info msg="[TCP] 127.0.0.1:58457 --> forms.hsforms.com:443 using GLOBAL"
time="2025-07-26T17:22:18.004803800+08:00" level=info msg="[TCP] 127.0.0.1:58455 --> use.fontawesome.com:443 using GLOBAL"
time="2025-07-26T17:22:18.158164500+08:00" level=info msg="[TCP] 127.0.0.1:58459 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-26T17:22:18.181393700+08:00" level=info msg="[TCP] 127.0.0.1:58461 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:22:18.693002200+08:00" level=info msg="[TCP] 127.0.0.1:58464 --> forms-na1.hsforms.com:443 using GLOBAL"
time="2025-07-26T17:22:18.795253300+08:00" level=info msg="[TCP] 127.0.0.1:58466 --> forms-na1.hsforms.com:443 using GLOBAL"
time="2025-07-26T17:22:18.795767000+08:00" level=info msg="[TCP] 127.0.0.1:58468 --> js.hs-scripts.com:443 using GLOBAL"
time="2025-07-26T17:22:18.799609700+08:00" level=info msg="[TCP] 127.0.0.1:58469 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-26T17:22:18.799609700+08:00" level=info msg="[TCP] 127.0.0.1:58470 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:22:18.838497700+08:00" level=info msg="[TCP] 127.0.0.1:58474 --> s.w.org:443 using GLOBAL"
time="2025-07-26T17:22:18.997035000+08:00" level=info msg="[TCP] 127.0.0.1:58476 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:22:19.375646500+08:00" level=info msg="[TCP] 127.0.0.1:58478 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T17:22:19.380864300+08:00" level=info msg="[TCP] 127.0.0.1:58480 --> js.hs-banner.com:443 using GLOBAL"
time="2025-07-26T17:22:19.384453900+08:00" level=info msg="[TCP] 127.0.0.1:58482 --> js.hsadspixel.net:443 using GLOBAL"
time="2025-07-26T17:22:19.459279000+08:00" level=info msg="[TCP] 127.0.0.1:58484 --> js.hsleadflows.net:443 using GLOBAL"
time="2025-07-26T17:22:19.633767500+08:00" level=info msg="[TCP] 127.0.0.1:58486 --> js.usemessages.com:443 using GLOBAL"
time="2025-07-26T17:22:19.723683900+08:00" level=info msg="[TCP] 127.0.0.1:58488 --> js.hubspot.com:443 using GLOBAL"
time="2025-07-26T17:22:19.741217300+08:00" level=info msg="[TCP] 127.0.0.1:58490 --> js.hs-analytics.net:443 using GLOBAL"
time="2025-07-26T17:22:20.499525900+08:00" level=info msg="[TCP] 127.0.0.1:58492 --> api.hubspot.com:443 using GLOBAL"
time="2025-07-26T17:22:21.112287200+08:00" level=info msg="[TCP] 127.0.0.1:58495 --> api.hubapi.com:443 using GLOBAL"
time="2025-07-26T17:22:21.530669700+08:00" level=info msg="[TCP] 127.0.0.1:58498 --> cta-service-cms2.hubspot.com:443 using GLOBAL"
time="2025-07-26T17:22:21.537885400+08:00" level=info msg="[TCP] 127.0.0.1:58500 --> forms.hubspot.com:443 using GLOBAL"
time="2025-07-26T17:22:22.014779100+08:00" level=info msg="[TCP] 127.0.0.1:58503 --> js.zi-scripts.com:443 using GLOBAL"
time="2025-07-26T17:22:22.018609000+08:00" level=info msg="[TCP] 127.0.0.1:58502 --> js-agent.newrelic.com:443 using GLOBAL"
time="2025-07-26T17:22:22.091636200+08:00" level=info msg="[TCP] 127.0.0.1:58506 --> track.hubspot.com:443 using GLOBAL"
time="2025-07-26T17:22:22.093779600+08:00" level=info msg="[TCP] 127.0.0.1:58507 --> track.hubspot.com:443 using GLOBAL"
time="2025-07-26T17:22:22.093779600+08:00" level=info msg="[TCP] 127.0.0.1:58509 --> track.hubspot.com:443 using GLOBAL"
time="2025-07-26T17:22:22.353418500+08:00" level=info msg="[TCP] 127.0.0.1:58512 --> track.hubspot.com:443 using GLOBAL"
time="2025-07-26T17:22:22.405885000+08:00" level=info msg="[TCP] 127.0.0.1:58516 --> js.zi-scripts.com:443 using GLOBAL"
time="2025-07-26T17:22:22.410427800+08:00" level=info msg="[TCP] 127.0.0.1:58514 --> connect.facebook.net:443 using GLOBAL"
time="2025-07-26T17:22:22.433084500+08:00" level=info msg="[TCP] 127.0.0.1:58518 --> use.fontawesome.com:443 using GLOBAL"
time="2025-07-26T17:22:22.539513300+08:00" level=info msg="[TCP] 127.0.0.1:58520 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:22:23.330345300+08:00" level=info msg="[TCP] 127.0.0.1:58523 --> perf-na1.hsforms.com:443 using GLOBAL"
time="2025-07-26T17:22:23.332678300+08:00" level=info msg="[TCP] 127.0.0.1:58522 --> snap.licdn.com:443 using GLOBAL"
time="2025-07-26T17:22:23.994502600+08:00" level=info msg="[TCP] 127.0.0.1:58527 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-26T17:22:24.280227800+08:00" level=info msg="[TCP] 127.0.0.1:58529 --> docs.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:22:24.281590900+08:00" level=info msg="[TCP] 127.0.0.1:58531 --> docs.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:22:24.284100800+08:00" level=info msg="[TCP] 127.0.0.1:58533 --> docs.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:22:24.287548000+08:00" level=info msg="[TCP] 127.0.0.1:58535 --> docs.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:22:25.758454000+08:00" level=info msg="[TCP] 127.0.0.1:58538 --> forms.hsforms.com:443 using GLOBAL"
time="2025-07-26T17:22:26.215193800+08:00" level=info msg="[TCP] 127.0.0.1:58540 --> ws.zoominfo.com:443 using GLOBAL"
time="2025-07-26T17:22:27.318616600+08:00" level=info msg="[TCP] 127.0.0.1:58543 --> ws.zoominfo.com:443 using GLOBAL"
time="2025-07-26T17:22:27.526310900+08:00" level=info msg="[TCP] 127.0.0.1:58546 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-26T17:22:27.544759900+08:00" level=info msg="[TCP] 127.0.0.1:58548 --> www.google.com:443 using GLOBAL"
time="2025-07-26T17:22:27.583939200+08:00" level=info msg="[TCP] 127.0.0.1:58550 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-26T17:22:28.069732600+08:00" level=info msg="[TCP] 127.0.0.1:58553 --> www.facebook.com:443 using GLOBAL"
time="2025-07-26T17:22:28.069732600+08:00" level=info msg="[TCP] 127.0.0.1:58552 --> www.facebook.com:443 using GLOBAL"
time="2025-07-26T17:22:40.902543400+08:00" level=info msg="[TCP] 127.0.0.1:58567 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-26T17:22:43.705504700+08:00" level=info msg="[TCP] 127.0.0.1:58571 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:22:43.991481900+08:00" level=info msg="[TCP] 127.0.0.1:58573 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:22:56.813811000+08:00" level=info msg="[TCP] 127.0.0.1:58598 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:22:56.814619200+08:00" level=info msg="[TCP] 127.0.0.1:58599 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:22:56.815790800+08:00" level=info msg="[TCP] 127.0.0.1:58600 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:22:56.818508400+08:00" level=info msg="[TCP] 127.0.0.1:58603 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:22:57.896521100+08:00" level=info msg="[TCP] 127.0.0.1:58608 --> ws.zoominfo.com:443 using GLOBAL"
time="2025-07-26T17:23:01.569957400+08:00" level=info msg="[TCP] 127.0.0.1:58614 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T17:23:07.496388400+08:00" level=info msg="[TCP] 127.0.0.1:58620 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:23:10.874232500+08:00" level=info msg="[TCP] 127.0.0.1:58624 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:23:16.955382100+08:00" level=info msg="[TCP] 127.0.0.1:58645 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-26T17:23:16.961370700+08:00" level=info msg="[TCP] 127.0.0.1:58644 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-26T17:23:19.323285500+08:00" level=info msg="[TCP] 127.0.0.1:58652 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:24:30.121122500+08:00" level=info msg="[TCP] 127.0.0.1:58947 --> ws.zoominfo.com:443 using GLOBAL"
time="2025-07-26T17:24:33.988531000+08:00" level=info msg="[TCP] 127.0.0.1:58962 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:24:35.390075600+08:00" level=error msg="🇸🇬新加坡01 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T17:24:35.390075600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T17:24:39.596382200+08:00" level=info msg="[TCP] 127.0.0.1:58975 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:24:44.002381700+08:00" level=info msg="[TCP] 127.0.0.1:58986 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:24:46.566631800+08:00" level=info msg="[TCP] 127.0.0.1:58993 --> ws.zoominfo.com:443 using GLOBAL"
time="2025-07-26T17:24:56.211598800+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-26T17:24:56.212113700+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-26T17:24:56.212113700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-26T17:24:56.213514200+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-26T17:24:56.213514200+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-26T17:24:56.214089800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-26T17:24:56.216071000+08:00" level=info msg="Initial configuration complete, total time: 4ms"
time="2025-07-26T17:24:56.217088800+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-26T17:24:56.242715100+08:00" level=warning msg="[TUN] default interface changed by monitor,  => 以太网"
time="2025-07-26T17:24:56.623566900+08:00" level=info msg="[TUN] Tun adapter listening at: Meta([********/30],[fdfe:dcba:9876::1/126]), mtu: 9000, auto route: true, auto redir: false, ip stack: Mixed"
time="2025-07-26T17:24:56.623566900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider apple"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-26T17:24:56.624999900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-26T17:24:56.624999900+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-26T17:24:56.624999900+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-26T17:24:56.624999900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-26T17:24:56.625502600+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-26T17:24:56.625502600+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-26T17:24:56.626025100+08:00" level=info msg="Start initial provider Lan"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider Disney"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider Line"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider TVer"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-26T17:24:56.624069500+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider NowE"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider Discord"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider google"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider telegram"
time="2025-07-26T17:24:56.624212300+08:00" level=info msg="Start initial provider TVB"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider BBC"
time="2025-07-26T17:24:56.624715000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-26T17:24:56.811938700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.811938700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.814008300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.814008300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.814008300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.815104800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.815104800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.816350500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.817359500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.817359500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.818542200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.818542200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.818542200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.818542200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.818542200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.819044500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.819044500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.819044500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.819044500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.822729700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.822729700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.822729700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.822729700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.822729700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.822729700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.822729700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823232200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823232200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823232200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823232200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823232200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823232200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823746300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823746300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823746300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823746300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823746300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823746300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823746300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.823746300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.833429500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.833429500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.833429500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.833429500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.833429500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.833429500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.833429500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.833932200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:56.833932200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:57.899220100+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-26T17:24:57.902271200+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-26T17:24:57.913506700+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-07-26T17:24:57.915661000+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-26T17:24:57.917124800+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-26T17:24:57.920450600+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-26T17:24:57.938556600+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-26T17:24:57.944193900+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-26T17:24:57.946194200+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-26T17:24:57.954199500+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-26T17:24:57.962273800+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-07-26T17:24:57.975231700+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-26T17:24:57.975231700+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-26T17:24:57.981984800+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-26T17:24:58.021070200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:58.021070200+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-26T17:24:58.022634500+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-26T17:24:58.023497400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:24:58.030925200+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-26T17:24:58.032291100+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-26T17:24:58.086075200+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-26T17:24:58.087386900+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-26T17:24:58.116470000+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-26T17:24:58.130936300+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-26T17:24:58.134634500+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-26T17:24:58.134634500+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-26T17:24:58.135465200+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-26T17:24:58.143788500+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-26T17:24:58.145131400+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-26T17:24:58.151931000+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-26T17:24:58.157025000+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-26T17:24:58.158565900+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-26T17:24:58.161105600+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-26T17:24:58.171437600+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-26T17:24:58.177029200+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-26T17:24:58.187100200+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-26T17:24:58.255034000+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-26T17:24:58.273127100+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-26T17:24:58.300376300+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-26T17:24:58.396399900+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-07-26T17:24:58.421884100+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-26T17:24:58.433527000+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-26T17:24:58.439659300+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-26T17:24:58.599660400+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-26T17:24:58.759957400+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-26T17:24:58.761503600+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-26T17:24:59.430800400+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-26T17:24:59.739802800+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-26T17:24:59.784497000+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-07-26T17:24:59.852965200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:25:00.781861900+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-26T17:25:01.766046800+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-26T17:25:01.770735500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-26T17:25:01.770735500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-26T17:25:01.770735500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-26T17:25:01.770735500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-26T17:25:02.088037600+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:59396 --> szextshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-26T17:25:02.089570900+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:59394 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-26T17:25:02.455824800+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:59439 --> [2409:8c54:1040:f::1b]:8080 using GLOBAL"
time="2025-07-26T17:25:02.458387200+08:00" level=info msg="[TCP] ********:59441 --> 39.156.140.245:8080 using GLOBAL"
time="2025-07-26T17:25:02.460091100+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:59443 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-26T17:25:02.685011700+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:53882 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-26T17:25:02.816797500+08:00" level=error msg="🇯🇵日本04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-26T17:25:02.816797500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-26T17:25:02.869795700+08:00" level=info msg="[UDP] ********:63281 --> *********:853 using GLOBAL"
time="2025-07-26T17:25:03.535361200+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:54632 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-26T17:25:04.671858300+08:00" level=info msg="[TCP] ********:59477 --> kv801.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:25:04.888567200+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:63964 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-26T17:25:04.964678300+08:00" level=info msg="[TCP] ********:59479 --> **************:443 using GLOBAL"
time="2025-07-26T17:25:05.906377800+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:53882 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-26T17:25:06.130873500+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:63964 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-26T17:25:06.320979800+08:00" level=info msg="[UDP] ********:63281 --> *********:853 using GLOBAL"
time="2025-07-26T17:25:08.236961400+08:00" level=info msg="[TCP] ********:59486 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:25:08.253638800+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:54632 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-26T17:25:08.349460700+08:00" level=info msg="[TCP] ********:59489 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:25:08.862450200+08:00" level=info msg="[TCP] ********:59493 --> kv801.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:25:08.971467800+08:00" level=info msg="[TCP] ********:59496 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:25:10.086827600+08:00" level=info msg="[TCP] ********:59499 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:25:11.096978400+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:63964 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-26T17:25:11.364730900+08:00" level=info msg="[TCP] ********:59504 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-26T17:25:11.984712000+08:00" level=info msg="[TCP] ********:59507 --> kv801.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:25:11.988241000+08:00" level=info msg="[TCP] ********:59508 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:25:12.208728300+08:00" level=info msg="[TCP] ********:59511 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:25:13.284628100+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:54632 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-26T17:25:13.378998000+08:00" level=info msg="[TCP] ********:59513 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 using GLOBAL"
time="2025-07-26T17:25:13.617271500+08:00" level=info msg="[TCP] ********:59515 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 using GLOBAL"
time="2025-07-26T17:25:14.479441300+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:49607 --> [2402:4e00:1900:1903:0:9687:df13:8edb]:3478 using GLOBAL"
time="2025-07-26T17:25:14.588529500+08:00" level=info msg="[UDP] ********:49606 --> *************:3478 using GLOBAL"
time="2025-07-26T17:25:15.319609500+08:00" level=info msg="[UDP] ********:49605 --> *************:3478 using GLOBAL"
time="2025-07-26T17:25:16.948110300+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:53882 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-26T17:25:17.628404700+08:00" level=info msg="[TCP] ********:59520 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:25:17.718384300+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:63964 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-26T17:25:19.444602900+08:00" level=info msg="[TCP] ********:59523 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:25:22.072211600+08:00" level=info msg="[TCP] ********:59527 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:25:22.*********+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:59529 --> szshort.weixin.qq.com:80 using GLOBAL"
time="2025-07-26T17:25:22.*********+08:00" level=info msg="[TCP] ********:59531 --> ecs.office.com:443 using GLOBAL"
time="2025-07-26T17:25:23.*********+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:54632 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-26T17:25:23.*********+08:00" level=info msg="[TCP] ********:59534 --> accounts.google.com:443 using GLOBAL"
time="2025-07-26T17:25:25.*********+08:00" level=info msg="[TCP] ********:59537 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:25:26.*********+08:00" level=info msg="[TCP] ********:59541 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:25:26.*********+08:00" level=info msg="[TCP] ********:59540 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:25:26.*********+08:00" level=info msg="[TCP] ********:59544 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:25:28.*********+08:00" level=info msg="[TCP] ********:59547 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:25:28.*********+08:00" level=info msg="[TCP] ********:59549 --> ecs.office.com:443 using GLOBAL"
time="2025-07-26T17:25:29.*********+08:00" level=info msg="[TCP] ********:59551 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:25:31.*********+08:00" level=info msg="[TCP] ********:59556 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:25:38.957932500+08:00" level=info msg="[TCP] ********:59567 --> www.facebook.com:443 using GLOBAL"
time="2025-07-26T17:25:38.971007100+08:00" level=info msg="[TCP] ********:59569 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-26T17:25:38.978467400+08:00" level=info msg="[TCP] ********:59570 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-26T17:25:40.603012300+08:00" level=info msg="[TCP] ********:59574 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:25:42.851034100+08:00" level=info msg="[TCP] ********:59578 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-26T17:25:43.585725000+08:00" level=warning msg="[UDP] Resolve Ip error: couldn't find ip"
time="2025-07-26T17:25:43.966579700+08:00" level=info msg="[TCP] ********:59584 --> settings-win.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:25:43.996096500+08:00" level=info msg="[TCP] ********:59586 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T17:25:44.062600700+08:00" level=info msg="[TCP] ********:59588 --> settings-win.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:25:45.131091900+08:00" level=info msg="[TCP] ********:59591 --> ws.zoominfo.com:443 using GLOBAL"
time="2025-07-26T17:25:45.670299000+08:00" level=info msg="[TCP] ********:59594 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:25:50.512695900+08:00" level=info msg="[TCP] ********:59598 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:25:50.630188000+08:00" level=info msg="[TCP] ********:59600 --> login.live.com:443 using GLOBAL"
time="2025-07-26T17:25:51.368020800+08:00" level=info msg="[TCP] ********:59603 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T17:25:56.849923800+08:00" level=info msg="[TCP] ********:59612 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:25:56.852667600+08:00" level=info msg="[TCP] ********:59608 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:25:56.853973100+08:00" level=info msg="[TCP] ********:59609 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:26:05.093069800+08:00" level=info msg="[TCP] ********:59620 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-07-26T17:26:10.639721800+08:00" level=info msg="[TCP] ********:59626 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T17:26:11.104813600+08:00" level=info msg="[TCP] ********:59628 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T17:26:13.655445300+08:00" level=info msg="[TCP] ********:59633 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:26:14.491930900+08:00" level=info msg="[TCP] ********:59635 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-26T17:26:14.491930900+08:00" level=info msg="[TCP] ********:59636 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-26T17:26:14.496558400+08:00" level=info msg="[TCP] ********:59637 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-26T17:26:14.496558400+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:49607 --> [2402:4e00:1900:1903:0:9687:df13:8edb]:3478 using GLOBAL"
time="2025-07-26T17:26:14.604884600+08:00" level=info msg="[UDP] ********:49606 --> *************:3478 using GLOBAL"
time="2025-07-26T17:26:15.335792700+08:00" level=info msg="[UDP] ********:49605 --> *************:3478 using GLOBAL"
time="2025-07-26T17:26:22.771872700+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:59647 --> [2409:8c54:1040:1f::12a]:80 using GLOBAL"
time="2025-07-26T17:26:24.981371400+08:00" level=info msg="[TCP] ********:59650 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:26:26.854118500+08:00" level=info msg="[TCP] ********:59653 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:26:29.548149600+08:00" level=info msg="[TCP] ********:59658 --> ipv6.msftconnecttest.com:80 using GLOBAL"
time="2025-07-26T17:26:29.556011000+08:00" level=info msg="[TCP] ********:59657 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-26T17:26:30.199296200+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-26T17:26:30.199809400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-26T17:26:30.199809400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-26T17:26:30.200343800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-26T17:26:30.200343800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-26T17:26:30.200343800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-26T17:26:30.201874200+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-26T17:26:30.203402400+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-26T17:26:30.283388000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-26T17:26:30.283891400+08:00" level=info msg="Start initial provider NowE"
time="2025-07-26T17:26:30.283891400+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-26T17:26:30.285077200+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-26T17:26:30.285077200+08:00" level=info msg="Start initial provider Lan"
time="2025-07-26T17:26:30.285077200+08:00" level=info msg="Start initial provider telegram"
time="2025-07-26T17:26:30.285077200+08:00" level=info msg="Start initial provider BBC"
time="2025-07-26T17:26:30.286597400+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-26T17:26:30.286597400+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-26T17:26:30.286597400+08:00" level=info msg="Start initial provider apple"
time="2025-07-26T17:26:30.286597400+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-26T17:26:30.287719300+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-26T17:26:30.287719300+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-26T17:26:30.287719300+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-26T17:26:30.288221800+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-26T17:26:30.288956800+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-26T17:26:30.288956800+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-26T17:26:30.286597400+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-26T17:26:30.290128400+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-26T17:26:30.290128400+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-26T17:26:30.291223700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-26T17:26:30.287719300+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider Disney"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider google"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider TVer"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider Line"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-26T17:26:30.286597400+08:00" level=info msg="Start initial provider Discord"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-26T17:26:30.286597400+08:00" level=info msg="Start initial provider TVB"
time="2025-07-26T17:26:30.284573000+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-26T17:26:30.287719300+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-26T17:26:30.492841400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.494340100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.494340100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.495465500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.495465500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.496638000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.496638000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.496638000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.498283200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.498283200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.498785800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.498785800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.498785800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500175600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500175600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500175600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500175600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500175600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500175600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500677800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500677800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500677800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500677800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500677800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.500677800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509188600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509188600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509188600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509188600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509188600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509188600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.509693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.510210300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.510210300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.510210300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.510210300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.510721900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.510721900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.510721900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.510721900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:30.510721900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:31.558076600+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-26T17:26:31.573649900+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-26T17:26:31.583888500+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-07-26T17:26:31.594635900+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-26T17:26:31.611570700+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-26T17:26:31.649507600+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-26T17:26:31.651376900+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-07-26T17:26:31.685749800+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-26T17:26:31.688179800+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-26T17:26:31.709822600+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-07-26T17:26:31.721048100+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-26T17:26:31.725410400+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-26T17:26:31.727721600+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-26T17:26:31.730638800+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-07-26T17:26:31.731924100+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-26T17:26:31.797173900+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-26T17:26:31.845198300+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-26T17:26:31.852952500+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-26T17:26:31.862283800+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-26T17:26:31.869181700+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-26T17:26:31.885776900+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-26T17:26:31.925695600+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-26T17:26:31.928879300+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-26T17:26:31.955172800+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-26T17:26:31.958922100+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-26T17:26:31.964498600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:26:32.015206000+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-26T17:26:32.133311100+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-26T17:26:32.337582600+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-26T17:26:32.356936300+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-26T17:26:32.504078800+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-26T17:26:32.542583600+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-26T17:26:32.561004600+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-26T17:26:32.570460700+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-26T17:26:32.575073900+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-26T17:26:32.604512500+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-26T17:26:32.610138800+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-26T17:26:32.618993500+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-26T17:26:32.627117200+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-26T17:26:32.634250900+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-26T17:26:32.644313300+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-26T17:26:32.648466500+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-26T17:26:32.654041200+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-26T17:26:32.723177900+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-26T17:26:32.766791800+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-26T17:26:32.786493400+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-26T17:26:32.788338800+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-26T17:26:33.268349900+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-26T17:26:33.450342800+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-26T17:26:34.025299000+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-26T17:26:34.030057100+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-26T17:26:34.030057100+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-26T17:26:34.030057100+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-26T17:26:34.030057100+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-26T17:26:35.336954200+08:00" level=info msg="[TCP] 127.0.0.1:60079 --> kv801.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:26:35.346722400+08:00" level=info msg="[TCP] 127.0.0.1:60081 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-26T17:26:37.978581200+08:00" level=info msg="[TCP] 127.0.0.1:60109 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:26:38.087909600+08:00" level=info msg="[TCP] 127.0.0.1:60112 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:26:38.959916600+08:00" level=info msg="[TCP] 127.0.0.1:60116 --> kv801.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:26:42.077665300+08:00" level=info msg="[TCP] 127.0.0.1:60125 --> kv801.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:26:43.985114900+08:00" level=info msg="[TCP] 127.0.0.1:60132 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:26:44.087287000+08:00" level=info msg="[TCP] 127.0.0.1:60135 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:26:44.510339700+08:00" level=info msg="[TCP] 127.0.0.1:60138 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 using GLOBAL"
time="2025-07-26T17:26:44.615472500+08:00" level=info msg="[TCP] 127.0.0.1:60141 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 using GLOBAL"
time="2025-07-26T17:26:44.723511400+08:00" level=info msg="[TCP] 127.0.0.1:60144 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 using GLOBAL"
time="2025-07-26T17:26:44.828163100+08:00" level=info msg="[TCP] 127.0.0.1:60147 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 using GLOBAL"
time="2025-07-26T17:26:45.205237600+08:00" level=info msg="[TCP] 127.0.0.1:60150 --> kv801.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:26:45.321128200+08:00" level=info msg="[TCP] 127.0.0.1:60153 --> geo.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:26:51.707824400+08:00" level=info msg="[TCP] 127.0.0.1:60166 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:26:53.097538400+08:00" level=info msg="[TCP] 127.0.0.1:60171 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:26:54.925406000+08:00" level=info msg="[TCP] 127.0.0.1:60177 --> windows.msn.com:443 using GLOBAL"
time="2025-07-26T17:26:54.994495600+08:00" level=info msg="[TCP] 127.0.0.1:60179 --> www.msn.com:443 using GLOBAL"
time="2025-07-26T17:26:55.602229500+08:00" level=info msg="[TCP] 127.0.0.1:60182 --> windows.msn.com:443 using GLOBAL"
time="2025-07-26T17:26:56.631023200+08:00" level=info msg="[TCP] 127.0.0.1:60185 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:26:56.866429700+08:00" level=info msg="[TCP] 127.0.0.1:60187 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:26:57.145717500+08:00" level=info msg="[TCP] 127.0.0.1:60189 --> srtb.msn.com:443 using GLOBAL"
time="2025-07-26T17:26:58.076631500+08:00" level=info msg="[TCP] 127.0.0.1:60195 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:26:58.097856400+08:00" level=info msg="[TCP] 127.0.0.1:60197 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:26:58.104040500+08:00" level=info msg="[TCP] 127.0.0.1:60201 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:26:58.110856400+08:00" level=info msg="[TCP] 127.0.0.1:60199 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-26T17:26:58.406183600+08:00" level=info msg="[TCP] 127.0.0.1:60205 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:26:58.406183600+08:00" level=info msg="[TCP] 127.0.0.1:60208 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-26T17:26:58.407344500+08:00" level=info msg="[TCP] 127.0.0.1:60211 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-26T17:26:58.407894100+08:00" level=info msg="[TCP] 127.0.0.1:60204 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:26:58.407894100+08:00" level=info msg="[TCP] 127.0.0.1:60213 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-26T17:26:58.411268600+08:00" level=info msg="[TCP] 127.0.0.1:60207 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:26:58.411268600+08:00" level=info msg="[TCP] 127.0.0.1:60212 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-26T17:26:58.412426600+08:00" level=info msg="[TCP] 127.0.0.1:60209 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-26T17:26:58.413130400+08:00" level=info msg="[TCP] 127.0.0.1:60206 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:26:58.985668900+08:00" level=info msg="[TCP] 127.0.0.1:60223 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:27:01.037291600+08:00" level=info msg="[TCP] 127.0.0.1:60227 --> srtb.msn.com:443 using GLOBAL"
time="2025-07-26T17:27:09.984467900+08:00" level=info msg="[TCP] 127.0.0.1:60242 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:27:10.639216700+08:00" level=info msg="[TCP] 127.0.0.1:60247 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T17:27:10.743329300+08:00" level=info msg="[TCP] 127.0.0.1:60251 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T17:27:16.927491600+08:00" level=info msg="[TCP] 127.0.0.1:60265 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:27:19.074933200+08:00" level=info msg="[TCP] 127.0.0.1:60267 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:27:19.180793900+08:00" level=info msg="[TCP] 127.0.0.1:60270 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:27:25.982128700+08:00" level=info msg="[TCP] 127.0.0.1:60281 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-26T17:27:26.088998500+08:00" level=info msg="[TCP] 127.0.0.1:60284 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-26T17:27:26.870099600+08:00" level=info msg="[TCP] 127.0.0.1:60290 --> bam.nr-data.net:443 using GLOBAL"
time="2025-07-26T17:27:27.978077800+08:00" level=info msg="[TCP] 127.0.0.1:60294 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:27:28.087132000+08:00" level=info msg="[TCP] 127.0.0.1:60297 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:27:39.288237000+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-26T17:27:39.288744400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-26T17:27:39.288744400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-26T17:27:39.289255400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-26T17:27:39.289807600+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-26T17:27:39.289807600+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-26T17:27:39.291346800+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-26T17:27:39.292364100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-26T17:27:39.297482700+08:00" level=warning msg="[TUN] default interface changed by monitor,  => 以太网"
time="2025-07-26T17:27:39.509911300+08:00" level=info msg="[TUN] Tun adapter listening at: Meta([********/30],[fdfe:dcba:9876::1/126]), mtu: 9000, auto route: true, auto redir: false, ip stack: Mixed"
time="2025-07-26T17:27:39.513807900+08:00" level=info msg="Start initial provider Line"
time="2025-07-26T17:27:39.513807900+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-26T17:27:39.514808500+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-26T17:27:39.514808500+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-26T17:27:39.514808500+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-26T17:27:39.514808500+08:00" level=info msg="Start initial provider apple"
time="2025-07-26T17:27:39.515809500+08:00" level=info msg="Start initial provider telegram"
time="2025-07-26T17:27:39.515809500+08:00" level=info msg="Start initial provider Discord"
time="2025-07-26T17:27:39.515809500+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-26T17:27:39.515809500+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-26T17:27:39.515809500+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-26T17:27:39.515809500+08:00" level=info msg="Start initial provider TVB"
time="2025-07-26T17:27:39.516569600+08:00" level=info msg="Start initial provider NowE"
time="2025-07-26T17:27:39.517073500+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-26T17:27:39.519580800+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-26T17:27:39.521743400+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-26T17:27:39.521743400+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-26T17:27:39.524759800+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-26T17:27:39.525263500+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-26T17:27:39.525263500+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-26T17:27:39.525263500+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-26T17:27:39.526269800+08:00" level=info msg="Start initial provider google"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-26T17:27:39.525263500+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider Disney"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider TVer"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider Lan"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider BBC"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-26T17:27:39.527270900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-26T17:27:39.525263500+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-26T17:27:39.829416500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.829416500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.829416500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.829919600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.829919600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.829919600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.829919600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.832907600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.832907600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.832907600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.832907600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.832907600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.833410300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.833410300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.833410300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.833410300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.833410300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.833410300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.838927600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.839447400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.839447400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.839980500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.839980500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.839980500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.839980500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.840491300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.840491300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.840491300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.841006400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.841006400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.841006400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.841518100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.841518100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.841518100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.841518100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.841518100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.842030900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.842030900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.842030900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.842030900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.858075300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.858075300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.858075300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.858588000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.858588000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.858588000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.859098800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.859098800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.859608400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:39.884107600+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-26T17:27:39.884107600+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-26T17:27:39.885525500+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-26T17:27:39.887219800+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-26T17:27:39.888192000+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-26T17:27:39.888192000+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-26T17:27:39.888192000+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-26T17:27:39.888192000+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-26T17:27:39.889039100+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-26T17:27:39.889039100+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-26T17:27:39.890141200+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-26T17:27:39.895903500+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-26T17:27:39.895903500+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-26T17:27:39.896916600+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-26T17:27:39.896916600+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-26T17:27:39.899376800+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-26T17:27:39.900971100+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-26T17:27:39.902044700+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-26T17:27:39.903739600+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-26T17:27:39.903739600+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-26T17:27:39.903739600+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-26T17:27:39.905249900+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-26T17:27:39.907490900+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-26T17:27:39.908122300+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-26T17:27:39.908122300+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-26T17:27:39.908824400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-26T17:27:39.908824400+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-26T17:27:39.916761100+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-26T17:27:39.916761100+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-26T17:27:39.918711900+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-26T17:27:39.918711900+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-26T17:27:39.920888300+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-26T17:27:39.921390500+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-26T17:27:39.923129800+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-26T17:27:39.925446000+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-26T17:27:40.465927600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:40.507521900+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-26T17:27:40.508664800+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-26T17:27:40.518921800+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/users/sign_in\": EOF"
time="2025-07-26T17:27:40.603027100+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-26T17:27:40.627032000+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-26T17:27:40.647852500+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/users/sign_in\": net/http: HTTP/1.x transport connection broken: unexpected EOF"
time="2025-07-26T17:27:40.793590900+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-26T17:27:40.814245800+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-26T17:27:40.864395400+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-26T17:27:40.893224200+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-26T17:27:40.923889700+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-26T17:27:41.132959600+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-26T17:27:41.213524700+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-26T17:27:49.555236000+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-26T17:27:49.560173000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-26T17:27:49.560173000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-26T17:27:49.560675600+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-26T17:27:49.560675600+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-26T17:27:49.792432500+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:51090 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-26T17:27:50.446580200+08:00" level=info msg="[TCP] [fdfe:dcba:9876::1]:60617 --> [2409:8c54:871:1004::18]:443 using GLOBAL"
time="2025-07-26T17:27:51.092454300+08:00" level=info msg="[TCP] ********:60632 --> download.pytorch.org:443 using GLOBAL"
time="2025-07-26T17:27:51.307872900+08:00" level=info msg="[TCP] ********:60635 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:27:51.531069100+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-26T17:27:51.531580700+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-26T17:27:51.531580700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-26T17:27:51.532175700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-26T17:27:51.532175700+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-26T17:27:51.532175700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-26T17:27:51.534240300+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-26T17:27:51.535269500+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Lan"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider google"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider TVer"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider apple"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider telegram"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider Disney"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider BBC"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider NowE"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Discord"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider Line"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider TVB"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-26T17:27:51.618846800+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-26T17:27:51.619363500+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-26T17:27:51.815396500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.815396500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.817374500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.817374500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.817374500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.817877000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.819007700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.819007700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.821409600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.821409600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.821409600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.821409600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.821911800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.821911800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.822418200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.822418200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.822418200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.822418200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.822418200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.822996600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.822996600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.829315600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.829315600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.829315600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.829819800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.829819800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.829819800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.829819800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.829819800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.829819800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.830335500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.830335500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.830335500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.830335500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.830848900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.830848900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.830848900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.831361300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.831361300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.831361300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.831873200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.831873200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.831873200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.832386000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.832386000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.832386000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.851346800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.851346800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.851346800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:51.866649800+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-26T17:27:51.868154000+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-26T17:27:51.869406900+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-26T17:27:51.870727900+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-26T17:27:51.870727900+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-26T17:27:51.873813400+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-26T17:27:51.874900000+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-26T17:27:51.875717500+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-26T17:27:51.875717500+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-26T17:27:51.875717500+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-26T17:27:51.878536300+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-26T17:27:51.878536300+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-26T17:27:51.882425000+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-26T17:27:51.885695200+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-26T17:27:51.886784800+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-26T17:27:51.886784800+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-26T17:27:51.888059800+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-26T17:27:51.888059800+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-26T17:27:51.888059800+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-26T17:27:51.890164600+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-26T17:27:51.890774800+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-26T17:27:51.892682200+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-26T17:27:51.892682200+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-26T17:27:51.892682200+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-26T17:27:51.893184800+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-26T17:27:51.893184800+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-26T17:27:51.893184800+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-26T17:27:51.893184800+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-26T17:27:51.897411600+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-26T17:27:51.897411600+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-26T17:27:51.897411600+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-26T17:27:51.897411600+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-26T17:27:51.897915000+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-26T17:27:51.898874700+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-26T17:27:51.898874700+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-26T17:27:51.899785700+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-26T17:27:51.900288400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-26T17:27:51.900288400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-26T17:27:51.901816600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-26T17:27:51.907328000+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-26T17:27:53.494237900+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-26T17:27:53.797687500+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-26T17:27:53.863865100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-26T17:27:53.915874600+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/users/sign_in\": EOF"
time="2025-07-26T17:27:53.978331500+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-26T17:27:54.199298200+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-26T17:27:54.212928800+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-26T17:27:54.400083200+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-26T17:27:54.807396400+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-26T17:27:55.590916600+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-26T17:27:55.594167500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-26T17:27:55.594167500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-26T17:27:55.594167500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-26T17:27:55.594167500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-26T17:47:13.173492500+08:00" level=info msg="[TCP] 127.0.0.1:62208 --> b.6sc.co:443 using GLOBAL"
time="2025-07-26T17:47:15.987339100+08:00" level=info msg="[TCP] 127.0.0.1:62214 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:47:17.710641500+08:00" level=info msg="[TCP] 127.0.0.1:62218 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:47:18.944008100+08:00" level=info msg="[TCP] 127.0.0.1:62222 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:47:20.699081100+08:00" level=info msg="[TCP] 127.0.0.1:62224 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:47:25.190211600+08:00" level=info msg="[TCP] 127.0.0.1:62231 --> b.6sc.co:443 using GLOBAL"
time="2025-07-26T17:47:29.987477900+08:00" level=info msg="[TCP] 127.0.0.1:62237 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-26T17:47:36.129937600+08:00" level=info msg="[TCP] 127.0.0.1:62243 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-26T17:47:43.839386500+08:00" level=info msg="[TCP] 127.0.0.1:62252 --> c.contentsquare.net:443 using GLOBAL"
time="2025-07-26T17:47:43.992325400+08:00" level=info msg="[TCP] 127.0.0.1:62254 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T17:47:44.703064800+08:00" level=info msg="[TCP] 127.0.0.1:62256 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T17:47:44.849820700+08:00" level=info msg="[TCP] 127.0.0.1:62258 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-26T17:47:50.518981500+08:00" level=info msg="[TCP] 127.0.0.1:62264 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:47:51.039331800+08:00" level=info msg="[TCP] 127.0.0.1:62266 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-26T17:48:10.893553400+08:00" level=info msg="[TCP] 127.0.0.1:62288 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T17:48:35.324520600+08:00" level=info msg="[TCP] 127.0.0.1:62312 --> app-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:48:43.996018400+08:00" level=info msg="[TCP] 127.0.0.1:62322 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:48:45.986686700+08:00" level=info msg="[TCP] 127.0.0.1:62325 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-26T17:48:47.605889000+08:00" level=info msg="[TCP] 127.0.0.1:62330 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:48:52.159253900+08:00" level=info msg="[TCP] 127.0.0.1:62335 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-26T17:48:56.008463600+08:00" level=info msg="[TCP] 127.0.0.1:62346 --> api.github.com:443 using GLOBAL"
time="2025-07-26T17:48:56.130348900+08:00" level=info msg="[TCP] 127.0.0.1:62348 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-26T17:48:56.410886100+08:00" level=info msg="[TCP] 127.0.0.1:62351 --> api.github.com:443 using GLOBAL"
time="2025-07-26T17:48:56.414317900+08:00" level=info msg="[TCP] 127.0.0.1:62350 --> api.github.com:443 using GLOBAL"
time="2025-07-26T17:48:56.854464600+08:00" level=info msg="[TCP] 127.0.0.1:62354 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:48:56.997373800+08:00" level=info msg="[TCP] 127.0.0.1:62356 --> education.github.com:443 using GLOBAL"
time="2025-07-26T17:48:58.188019000+08:00" level=info msg="[TCP] 127.0.0.1:62359 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-26T17:48:58.317291400+08:00" level=info msg="[TCP] 127.0.0.1:62362 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-26T17:48:58.623858800+08:00" level=info msg="[TCP] 127.0.0.1:62365 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-26T17:48:58.629749300+08:00" level=info msg="[TCP] 127.0.0.1:62367 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-26T17:48:58.630306900+08:00" level=info msg="[TCP] 127.0.0.1:62368 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-26T17:48:58.631739100+08:00" level=info msg="[TCP] 127.0.0.1:62366 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-26T17:48:58.633019200+08:00" level=info msg="[TCP] 127.0.0.1:62364 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-26T17:48:59.937763100+08:00" level=info msg="[TCP] 127.0.0.1:62374 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:49:01.072615500+08:00" level=info msg="[TCP] 127.0.0.1:62377 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:49:08.031914300+08:00" level=info msg="[TCP] 127.0.0.1:62386 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:49:09.007846800+08:00" level=info msg="[TCP] 127.0.0.1:62388 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:49:20.811162000+08:00" level=info msg="[TCP] 127.0.0.1:62398 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:49:27.554696200+08:00" level=info msg="[TCP] 127.0.0.1:62406 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-26T17:49:27.850512800+08:00" level=info msg="[TCP] 127.0.0.1:62408 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-26T17:49:34.078862700+08:00" level=info msg="[TCP] 127.0.0.1:62422 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-26T17:49:34.907931700+08:00" level=info msg="[TCP] 127.0.0.1:62426 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:49:35.259509300+08:00" level=info msg="[TCP] 127.0.0.1:62428 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-26T17:49:37.764145900+08:00" level=info msg="[TCP] 127.0.0.1:62431 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:49:42.439198700+08:00" level=info msg="[TCP] 127.0.0.1:62436 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:49:45.134969700+08:00" level=info msg="[TCP] 127.0.0.1:62442 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:49:48.102129900+08:00" level=info msg="[TCP] 127.0.0.1:62446 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:49:48.614792300+08:00" level=info msg="[TCP] 127.0.0.1:62448 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:50:33.664347300+08:00" level=info msg="[TCP] 127.0.0.1:62486 --> privacyportal.onetrust.com:443 using GLOBAL"
time="2025-07-26T17:50:44.193607300+08:00" level=info msg="[TCP] 127.0.0.1:62494 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:51:20.918172100+08:00" level=info msg="[TCP] 127.0.0.1:62527 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:51:41.004023000+08:00" level=info msg="[TCP] 127.0.0.1:62545 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T17:52:25.056593700+08:00" level=info msg="[TCP] 127.0.0.1:62579 --> b.6sc.co:443 using GLOBAL"
time="2025-07-26T17:52:29.529498900+08:00" level=info msg="[TCP] 127.0.0.1:62584 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-26T17:52:43.990973700+08:00" level=info msg="[TCP] 127.0.0.1:62598 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:53:07.492135000+08:00" level=info msg="[TCP] 127.0.0.1:62631 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:53:09.238436300+08:00" level=info msg="[TCP] 127.0.0.1:62635 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:53:10.072264200+08:00" level=info msg="[TCP] 127.0.0.1:62637 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:53:10.548700700+08:00" level=info msg="[TCP] 127.0.0.1:62639 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:53:11.230430900+08:00" level=info msg="[TCP] 127.0.0.1:62643 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:53:12.149460100+08:00" level=info msg="[TCP] 127.0.0.1:62645 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:53:16.421119000+08:00" level=info msg="[TCP] 127.0.0.1:62658 --> config.teams.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:53:16.955420000+08:00" level=info msg="[TCP] 127.0.0.1:62661 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-26T17:53:16.956806900+08:00" level=info msg="[TCP] 127.0.0.1:62662 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-26T17:53:17.944116700+08:00" level=info msg="[TCP] 127.0.0.1:62666 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-26T17:53:19.080104200+08:00" level=info msg="[TCP] 127.0.0.1:62668 --> b.6sc.co:443 using GLOBAL"
time="2025-07-26T17:53:22.090856400+08:00" level=info msg="[TCP] 127.0.0.1:62674 --> storage.live.com:443 using GLOBAL"
time="2025-07-26T17:53:22.551658300+08:00" level=info msg="[TCP] 127.0.0.1:62676 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-26T17:53:23.095452000+08:00" level=info msg="[TCP] 127.0.0.1:62679 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-26T17:53:23.981019400+08:00" level=info msg="[TCP] 127.0.0.1:62682 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:53:31.373901300+08:00" level=info msg="[TCP] 127.0.0.1:62690 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:53:34.330314900+08:00" level=info msg="[TCP] 127.0.0.1:62696 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:53:37.659498600+08:00" level=info msg="[TCP] 127.0.0.1:62700 --> storage.live.com:443 using GLOBAL"
time="2025-07-26T17:53:38.677015700+08:00" level=info msg="[TCP] 127.0.0.1:62704 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-26T17:53:39.152078300+08:00" level=info msg="[TCP] 127.0.0.1:62706 --> onedriveclucproddm20014.blob.core.windows.net:443 using GLOBAL"
time="2025-07-26T17:53:39.905398800+08:00" level=info msg="[TCP] 127.0.0.1:62708 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-26T17:53:43.985522400+08:00" level=info msg="[TCP] 127.0.0.1:62714 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T17:53:45.333642800+08:00" level=info msg="[TCP] 127.0.0.1:62718 --> onedriveclucproddm20014.blob.core.windows.net:443 using GLOBAL"
time="2025-07-26T17:53:51.299355700+08:00" level=info msg="[TCP] 127.0.0.1:62724 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:53:51.453331800+08:00" level=info msg="[TCP] 127.0.0.1:62726 --> westeurope-5.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-26T17:53:52.975819000+08:00" level=info msg="[TCP] 127.0.0.1:62728 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-26T17:53:54.634413400+08:00" level=info msg="[TCP] 127.0.0.1:62734 --> westeurope-5.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-26T17:53:56.426366600+08:00" level=info msg="[TCP] 127.0.0.1:62737 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:22.377781700+08:00" level=info msg="[TCP] 127.0.0.1:62758 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-07-26T17:54:29.726642700+08:00" level=info msg="[TCP] 127.0.0.1:62765 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:30.926143600+08:00" level=info msg="[TCP] 127.0.0.1:62768 --> activity.windows.com:443 using GLOBAL"
time="2025-07-26T17:54:31.900303100+08:00" level=info msg="[TCP] 127.0.0.1:62770 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:31.961594500+08:00" level=info msg="[TCP] 127.0.0.1:62772 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:32.001211500+08:00" level=info msg="[TCP] 127.0.0.1:62774 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:34.003006300+08:00" level=info msg="[TCP] 127.0.0.1:62778 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-26T17:54:35.946590800+08:00" level=info msg="[TCP] 127.0.0.1:62783 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-07-26T17:54:38.193411100+08:00" level=info msg="[TCP] 127.0.0.1:62786 --> activity.windows.com:443 using GLOBAL"
time="2025-07-26T17:54:40.341617900+08:00" level=info msg="[TCP] 127.0.0.1:62790 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:40.352349900+08:00" level=info msg="[TCP] 127.0.0.1:62792 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:40.357806200+08:00" level=info msg="[TCP] 127.0.0.1:62794 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:41.307274000+08:00" level=info msg="[TCP] 127.0.0.1:62796 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:41.339240400+08:00" level=info msg="[TCP] 127.0.0.1:62798 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:41.444130900+08:00" level=info msg="[TCP] 127.0.0.1:62800 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:41.483491900+08:00" level=info msg="[TCP] 127.0.0.1:62802 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:41.529422100+08:00" level=info msg="[TCP] 127.0.0.1:62804 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:42.128469700+08:00" level=info msg="[TCP] 127.0.0.1:62807 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-07-26T17:54:44.017246300+08:00" level=info msg="[TCP] 127.0.0.1:62810 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:54:44.101152000+08:00" level=info msg="[TCP] 127.0.0.1:62812 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:44.107029400+08:00" level=info msg="[TCP] 127.0.0.1:62814 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:46.629478300+08:00" level=info msg="[TCP] 127.0.0.1:62818 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:54:50.356589600+08:00" level=info msg="[TCP] 127.0.0.1:62822 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-07-26T17:54:59.646647900+08:00" level=info msg="[TCP] 127.0.0.1:62831 --> static.nvidiagrid.net:443 using GLOBAL"
time="2025-07-26T17:55:06.644333800+08:00" level=info msg="[TCP] 127.0.0.1:62838 --> activity.windows.com:443 using GLOBAL"
time="2025-07-26T17:55:13.826743900+08:00" level=info msg="[TCP] 127.0.0.1:62847 --> activity.windows.com:443 using GLOBAL"
time="2025-07-26T17:55:29.046501200+08:00" level=info msg="[TCP] 127.0.0.1:62861 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:29.576277100+08:00" level=info msg="[TCP] 127.0.0.1:62863 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:31.867362200+08:00" level=info msg="[TCP] 127.0.0.1:62867 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:31.902064600+08:00" level=info msg="[TCP] 127.0.0.1:62869 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:31.942337100+08:00" level=info msg="[TCP] 127.0.0.1:62871 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:38.868272700+08:00" level=info msg="[TCP] 127.0.0.1:62877 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-07-26T17:55:39.662299200+08:00" level=info msg="[TCP] 127.0.0.1:62879 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:40.264881900+08:00" level=info msg="[TCP] 127.0.0.1:62883 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:40.273209100+08:00" level=info msg="[TCP] 127.0.0.1:62885 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:40.288563900+08:00" level=info msg="[TCP] 127.0.0.1:62889 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:40.291961300+08:00" level=info msg="[TCP] 127.0.0.1:62887 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:40.954507600+08:00" level=info msg="[TCP] 127.0.0.1:62891 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:41.031430200+08:00" level=info msg="[TCP] 127.0.0.1:62893 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:41.194170300+08:00" level=info msg="[TCP] 127.0.0.1:62895 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:41.199730800+08:00" level=info msg="[TCP] 127.0.0.1:62897 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:41.313059700+08:00" level=info msg="[TCP] 127.0.0.1:62899 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:41.350003700+08:00" level=info msg="[TCP] 127.0.0.1:62901 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:43.963867700+08:00" level=info msg="[TCP] 127.0.0.1:62905 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:55:43.987654200+08:00" level=info msg="[TCP] 127.0.0.1:62907 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T17:56:01.086599400+08:00" level=info msg="[TCP] 127.0.0.1:62922 --> b.6sc.co:443 using GLOBAL"
time="2025-07-26T17:56:03.291412300+08:00" level=info msg="[TCP] 127.0.0.1:62925 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-26T17:56:24.733451400+08:00" level=info msg="[TCP] 127.0.0.1:62947 --> c.contentsquare.net:443 using GLOBAL"
time="2025-07-26T17:56:31.434299700+08:00" level=info msg="[TCP] 127.0.0.1:62957 --> ctldl.windowsupdate.com:80 using GLOBAL"
time="2025-07-26T17:56:41.046298800+08:00" level=info msg="[TCP] 127.0.0.1:62968 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-26T17:56:43.994011700+08:00" level=info msg="[TCP] 127.0.0.1:62971 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:56:53.121110700+08:00" level=info msg="[TCP] 127.0.0.1:62983 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:57:07.269272900+08:00" level=info msg="[TCP] 127.0.0.1:62995 --> b.6sc.co:443 using GLOBAL"
time="2025-07-26T17:57:09.230528000+08:00" level=info msg="[TCP] 127.0.0.1:63001 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:57:12.113400400+08:00" level=info msg="[TCP] 127.0.0.1:63005 --> c.contentsquare.net:443 using GLOBAL"
time="2025-07-26T17:57:15.800083800+08:00" level=info msg="[TCP] 127.0.0.1:63009 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-26T17:57:22.920843100+08:00" level=info msg="[TCP] 127.0.0.1:63015 --> cn.bing.com:443 using GLOBAL"
time="2025-07-26T17:57:33.392842700+08:00" level=info msg="[TCP] 127.0.0.1:63029 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:57:41.651777100+08:00" level=info msg="[TCP] 127.0.0.1:63036 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-26T17:57:43.993257200+08:00" level=info msg="[TCP] 127.0.0.1:63039 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-26T17:57:59.130196700+08:00" level=info msg="[TCP] 127.0.0.1:63055 --> ntp.msn.com:443 using GLOBAL"
time="2025-07-26T17:57:59.913198600+08:00" level=info msg="[TCP] 127.0.0.1:63058 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:57:59.915174900+08:00" level=info msg="[TCP] 127.0.0.1:63070 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:57:59.916476100+08:00" level=info msg="[TCP] 127.0.0.1:63059 --> c.msn.com:443 using GLOBAL"
time="2025-07-26T17:57:59.916476100+08:00" level=info msg="[TCP] 127.0.0.1:63066 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:57:59.916476100+08:00" level=info msg="[TCP] 127.0.0.1:63062 --> c.bing.com:443 using GLOBAL"
time="2025-07-26T17:57:59.917159700+08:00" level=info msg="[TCP] 127.0.0.1:63072 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-07-26T17:57:59.918503400+08:00" level=info msg="[TCP] 127.0.0.1:63064 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:57:59.919006600+08:00" level=info msg="[TCP] 127.0.0.1:63067 --> api.msn.com:443 using GLOBAL"
time="2025-07-26T17:58:00.004677200+08:00" level=info msg="[TCP] 127.0.0.1:63074 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-26T17:58:00.006343400+08:00" level=info msg="[TCP] 127.0.0.1:63076 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:58:00.190940900+08:00" level=info msg="[TCP] 127.0.0.1:63085 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-26T17:58:00.192289500+08:00" level=info msg="[TCP] 127.0.0.1:63082 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-26T17:58:00.192289500+08:00" level=info msg="[TCP] 127.0.0.1:63080 --> r.bing.com:443 using GLOBAL"
time="2025-07-26T17:58:00.195640200+08:00" level=info msg="[TCP] 127.0.0.1:63079 --> r.bing.com:443 using GLOBAL"
time="2025-07-26T17:58:00.496493800+08:00" level=info msg="[TCP] 127.0.0.1:63087 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:58:01.370429300+08:00" level=info msg="[TCP] 127.0.0.1:63089 --> assets.msn.com:443 using GLOBAL"
time="2025-07-26T17:58:01.920987000+08:00" level=info msg="[TCP] 127.0.0.1:63091 --> www.bing.com:443 using GLOBAL"
time="2025-07-26T17:58:03.701074400+08:00" level=info msg="[TCP] 127.0.0.1:63095 --> r.bing.com:443 using GLOBAL"
time="2025-07-26T17:58:03.702835600+08:00" level=info msg="[TCP] 127.0.0.1:63100 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:58:03.705789600+08:00" level=info msg="[TCP] 127.0.0.1:63099 --> th.bing.com:443 using GLOBAL"
time="2025-07-26T17:58:03.707939100+08:00" level=info msg="[TCP] 127.0.0.1:63096 --> r.bing.com:443 using GLOBAL"
time="2025-07-26T17:58:04.826067700+08:00" level=info msg="[TCP] 127.0.0.1:63103 --> storage.live.com:443 using GLOBAL"
time="2025-07-26T17:58:06.064892400+08:00" level=info msg="[TCP] 127.0.0.1:63106 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-07-26T17:58:06.863134600+08:00" level=info msg="[TCP] 127.0.0.1:63109 --> login.live.com:443 using GLOBAL"
time="2025-07-26T17:58:40.373923600+08:00" level=info msg="[TCP] 127.0.0.1:63136 --> alive.github.com:443 using GLOBAL"
time="2025-07-26T17:58:43.991145400+08:00" level=info msg="[TCP] 127.0.0.1:63142 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-26T17:58:45.452103200+08:00" level=info msg="[TCP] 127.0.0.1:63145 --> alive.github.com:443 using GLOBAL"
time="2025-07-26T17:58:46.614646500+08:00" level=info msg="[TCP] 127.0.0.1:63148 --> alive.github.com:443 using GLOBAL"
time="2025-07-26T17:58:48.668032400+08:00" level=info msg="[TCP] 127.0.0.1:63151 --> alive.github.com:443 using GLOBAL"
time="2025-07-26T17:58:49.631048000+08:00" level=info msg="[TCP] 127.0.0.1:63154 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:49.634766200+08:00" level=info msg="[TCP] 127.0.0.1:63155 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:49.774137300+08:00" level=info msg="[TCP] 127.0.0.1:63158 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:49.831585300+08:00" level=info msg="[TCP] 127.0.0.1:63160 --> login.live.com:443 using GLOBAL"
time="2025-07-26T17:58:49.835742300+08:00" level=info msg="[TCP] 127.0.0.1:63162 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:50.974944500+08:00" level=info msg="[TCP] 127.0.0.1:63165 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:50.976637300+08:00" level=info msg="[TCP] 127.0.0.1:63164 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:51.153620800+08:00" level=info msg="[TCP] 127.0.0.1:63168 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:51.327958500+08:00" level=info msg="[TCP] 127.0.0.1:63170 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:51.520852500+08:00" level=info msg="[TCP] 127.0.0.1:63172 --> alive.github.com:443 using GLOBAL"
time="2025-07-26T17:58:51.757034300+08:00" level=info msg="[TCP] 127.0.0.1:63176 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:51.758365800+08:00" level=info msg="[TCP] 127.0.0.1:63175 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:51.928764800+08:00" level=info msg="[TCP] 127.0.0.1:63179 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:51.995083000+08:00" level=info msg="[TCP] 127.0.0.1:63181 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:54.433830100+08:00" level=info msg="[TCP] 127.0.0.1:63184 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-26T17:58:56.977616400+08:00" level=info msg="[TCP] 127.0.0.1:63189 --> login.live.com:443 using GLOBAL"
time="2025-07-26T17:58:57.136001300+08:00" level=info msg="[TCP] 127.0.0.1:63192 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:57.140073500+08:00" level=info msg="[TCP] 127.0.0.1:63191 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:57.313457200+08:00" level=info msg="[TCP] 127.0.0.1:63195 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:58:57.343098400+08:00" level=info msg="[TCP] 127.0.0.1:63197 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:59:00.744565600+08:00" level=info msg="[TCP] 127.0.0.1:63202 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-26T17:59:03.249862600+08:00" level=info msg="[TCP] 127.0.0.1:63209 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-26T17:59:27.501923500+08:00" level=info msg="[TCP] 127.0.0.1:63231 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:59:27.504807200+08:00" level=info msg="[TCP] 127.0.0.1:63232 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:59:27.675977300+08:00" level=info msg="[TCP] 127.0.0.1:63235 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:59:27.692883400+08:00" level=info msg="[TCP] 127.0.0.1:63237 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:59:28.754271000+08:00" level=info msg="[TCP] 127.0.0.1:63241 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:59:28.759136500+08:00" level=info msg="[TCP] 127.0.0.1:63242 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:59:28.899866400+08:00" level=info msg="[TCP] 127.0.0.1:63245 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:59:29.597193300+08:00" level=info msg="[TCP] 127.0.0.1:63247 --> zhuanlan.zhihu.com:443 using GLOBAL"
time="2025-07-26T17:59:34.993479400+08:00" level=info msg="[TCP] 127.0.0.1:63253 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-26T18:19:34.065621900+08:00" level=info msg="[TCP] 127.0.0.1:65048 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-26T18:19:35.271310100+08:00" level=info msg="[TCP] 127.0.0.1:65050 --> default.exp-tas.com:443 using GLOBAL"
