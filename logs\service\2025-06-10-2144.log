Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-10T21:44:50.117842300+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-10T21:44:50.127059300+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-10T21:44:50.127059300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-10T21:44:50.127570300+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-10T21:44:50.148375400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-10T21:44:50.149376500+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-10T21:44:50.397602100+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-10T21:44:50.398603900+08:00" level=info msg="Load GeoSite rule: private"
time="2025-06-10T21:44:50.420622600+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-10T21:44:50.422624600+08:00" level=info msg="Initial configuration complete, total time: 299ms"
time="2025-06-10T21:44:50.423625600+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-06-10T21:44:50.424626300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-10T21:44:50.425627200+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-06-10T21:44:50.425627200+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-06-10T21:44:50.425627200+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider NowE"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Lan"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider apple"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider TVB"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider google"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider TVer"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Line"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Discord"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Disney"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider telegram"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider BBC"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-10T21:44:50.430632300+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-10T21:44:55.433512800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.433512800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.433512800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.433602200+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-10T21:44:55.433602200+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-10T21:44:55.433602200+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-10T21:44:55.434112700+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-10T21:44:55.434620000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-10T21:44:55.435136600+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-10T21:44:55.440264500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-10T21:44:55.440264500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-10T21:44:55.440264500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-10T21:44:55.440264500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-10T21:44:59.863769200+08:00" level=info msg="[TCP] 127.0.0.1:60343 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-10T21:45:00.182362500+08:00" level=info msg="[TCP] 127.0.0.1:60353 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-10T21:45:00.391965700+08:00" level=info msg="[TCP] 127.0.0.1:60360 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-06-10T21:45:00.864288100+08:00" level=info msg="[TCP] 127.0.0.1:60368 --> res.wx.qq.com:443 using GLOBAL"
time="2025-06-10T21:45:01.174956600+08:00" level=info msg="[TCP] 127.0.0.1:60399 --> res.wx.qq.com:443 using GLOBAL"
time="2025-06-10T21:45:01.238278500+08:00" level=info msg="[TCP] 127.0.0.1:60401 --> wx.qlogo.cn:443 using GLOBAL"
time="2025-06-10T21:45:01.435923200+08:00" level=info msg="[TCP] 127.0.0.1:60404 --> dldir1v6.qq.com:443 using GLOBAL"
time="2025-06-10T21:45:05.254398600+08:00" level=info msg="[TCP] 127.0.0.1:60519 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-06-10T21:45:07.573715200+08:00" level=info msg="[TCP] 127.0.0.1:60527 --> servicewechat.com:443 using GLOBAL"
time="2025-06-10T21:45:12.437511200+08:00" level=error msg="🇸🇬新加坡01 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-10T21:45:12.437511200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-10T21:45:12.449538600+08:00" level=error msg="🇯🇵日本03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-10T21:45:12.449538600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-10T21:46:46.970091700+08:00" level=info msg="[TCP] 127.0.0.1:61355 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T21:46:47.229310300+08:00" level=info msg="[TCP] 127.0.0.1:61358 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T21:46:47.229310300+08:00" level=info msg="[TCP] 127.0.0.1:61357 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T21:46:47.274650100+08:00" level=info msg="[TCP] 127.0.0.1:61364 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T21:46:47.274650100+08:00" level=info msg="[TCP] 127.0.0.1:61363 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T21:46:47.276244700+08:00" level=info msg="[TCP] 127.0.0.1:61361 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T21:46:47.276244700+08:00" level=info msg="[TCP] 127.0.0.1:61362 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T21:46:48.927733100+08:00" level=info msg="[TCP] 127.0.0.1:61370 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-10T21:46:49.059231300+08:00" level=info msg="[TCP] 127.0.0.1:61373 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-10T21:46:50.136063800+08:00" level=info msg="[TCP] 127.0.0.1:61375 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T21:46:52.960854800+08:00" level=info msg="[TCP] 127.0.0.1:61379 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-10T21:46:53.443569500+08:00" level=info msg="[TCP] 127.0.0.1:61381 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T21:46:55.438525900+08:00" level=info msg="[TCP] 127.0.0.1:61385 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T21:47:01.098950700+08:00" level=info msg="[TCP] 127.0.0.1:61391 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-10T21:47:15.603185700+08:00" level=info msg="[TCP] 127.0.0.1:61411 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:15.873431300+08:00" level=info msg="[TCP] 127.0.0.1:61413 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:16.056678700+08:00" level=info msg="[TCP] 127.0.0.1:61415 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.396490700+08:00" level=info msg="[TCP] 127.0.0.1:61420 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.399515300+08:00" level=info msg="[TCP] 127.0.0.1:61426 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.399515300+08:00" level=info msg="[TCP] 127.0.0.1:61434 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.399515300+08:00" level=info msg="[TCP] 127.0.0.1:61432 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.400294000+08:00" level=info msg="[TCP] 127.0.0.1:61419 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.400294000+08:00" level=info msg="[TCP] 127.0.0.1:61427 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.400960900+08:00" level=info msg="[TCP] 127.0.0.1:61422 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.400960900+08:00" level=info msg="[TCP] 127.0.0.1:61418 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.400960900+08:00" level=info msg="[TCP] 127.0.0.1:61423 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.402428000+08:00" level=info msg="[TCP] 127.0.0.1:61433 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.403120400+08:00" level=info msg="[TCP] 127.0.0.1:61428 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.407231900+08:00" level=info msg="[TCP] 127.0.0.1:61424 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.409798200+08:00" level=info msg="[TCP] 127.0.0.1:61425 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.418146300+08:00" level=info msg="[TCP] 127.0.0.1:61429 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.457295100+08:00" level=info msg="[TCP] 127.0.0.1:61458 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.458363400+08:00" level=info msg="[TCP] 127.0.0.1:61454 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.458363400+08:00" level=info msg="[TCP] 127.0.0.1:61456 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.459373300+08:00" level=info msg="[TCP] 127.0.0.1:61453 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.460280200+08:00" level=info msg="[TCP] 127.0.0.1:61452 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.460280200+08:00" level=info msg="[TCP] 127.0.0.1:61459 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.466765200+08:00" level=info msg="[TCP] 127.0.0.1:61431 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.477135000+08:00" level=info msg="[TCP] 127.0.0.1:61421 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.478866400+08:00" level=info msg="[TCP] 127.0.0.1:61430 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.547988700+08:00" level=info msg="[TCP] 127.0.0.1:61469 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.568399200+08:00" level=info msg="[TCP] 127.0.0.1:61457 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.568902700+08:00" level=info msg="[TCP] 127.0.0.1:61455 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.796202400+08:00" level=info msg="[TCP] 127.0.0.1:61474 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:17.810507700+08:00" level=info msg="[TCP] 127.0.0.1:61472 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:18.017857200+08:00" level=info msg="[TCP] 127.0.0.1:61477 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:18.393116500+08:00" level=info msg="[TCP] 127.0.0.1:61482 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:18.403780700+08:00" level=info msg="[TCP] 127.0.0.1:61484 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:18.456006800+08:00" level=info msg="[TCP] 127.0.0.1:61486 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:47:18.799520400+08:00" level=info msg="[TCP] 127.0.0.1:61488 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:19.016284700+08:00" level=info msg="[TCP] 127.0.0.1:61491 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:19.043260500+08:00" level=info msg="[TCP] 127.0.0.1:61493 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:19.245998800+08:00" level=info msg="[TCP] 127.0.0.1:61496 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T21:47:20.285080300+08:00" level=info msg="[TCP] 127.0.0.1:61500 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:20.481424900+08:00" level=info msg="[TCP] 127.0.0.1:61503 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:20.483674000+08:00" level=info msg="[TCP] 127.0.0.1:61505 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:21.703224900+08:00" level=info msg="[TCP] 127.0.0.1:61508 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:22.505848400+08:00" level=info msg="[TCP] 127.0.0.1:61510 --> go.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:47:22.995311100+08:00" level=info msg="[TCP] 127.0.0.1:61513 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:24.863828300+08:00" level=info msg="[TCP] 127.0.0.1:61516 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:30.839010100+08:00" level=info msg="[TCP] 127.0.0.1:61523 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:33.922836800+08:00" level=info msg="[TCP] 127.0.0.1:61527 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-10T21:47:34.638854800+08:00" level=info msg="[TCP] 127.0.0.1:61530 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:35.048804200+08:00" level=info msg="[TCP] 127.0.0.1:61529 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:35.832345200+08:00" level=info msg="[TCP] 127.0.0.1:61534 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:47:35.979408400+08:00" level=info msg="[TCP] 127.0.0.1:61536 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:36.326522500+08:00" level=info msg="[TCP] 127.0.0.1:61538 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:37.236430800+08:00" level=info msg="[TCP] 127.0.0.1:61541 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:40.331249800+08:00" level=info msg="[TCP] 127.0.0.1:61547 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:40.550592400+08:00" level=info msg="[TCP] 127.0.0.1:61545 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:40.610508400+08:00" level=info msg="[TCP] 127.0.0.1:61549 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:47:42.626346100+08:00" level=info msg="[TCP] 127.0.0.1:61555 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:42.818687100+08:00" level=info msg="[TCP] 127.0.0.1:61553 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:43.665926900+08:00" level=info msg="[TCP] 127.0.0.1:61557 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:43.677900000+08:00" level=info msg="[TCP] 127.0.0.1:61559 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:43.772541600+08:00" level=info msg="[TCP] 127.0.0.1:61561 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:43.917427900+08:00" level=info msg="[TCP] 127.0.0.1:61564 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:47:44.371531800+08:00" level=info msg="[TCP] 127.0.0.1:61566 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:44.662841900+08:00" level=info msg="[TCP] 127.0.0.1:61568 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:44.844676700+08:00" level=info msg="[TCP] 127.0.0.1:61570 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:45.081681000+08:00" level=info msg="[TCP] 127.0.0.1:61572 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:47:49.263894600+08:00" level=info msg="[TCP] 127.0.0.1:61577 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T21:47:52.004897100+08:00" level=info msg="[TCP] 127.0.0.1:61580 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:00.061954700+08:00" level=info msg="[TCP] 127.0.0.1:61588 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:00.342589500+08:00" level=info msg="[TCP] 127.0.0.1:61590 --> ecs.office.com:443 using GLOBAL"
time="2025-06-10T21:48:00.930453900+08:00" level=info msg="[TCP] 127.0.0.1:61593 --> g.live.com:443 using GLOBAL"
time="2025-06-10T21:48:01.039924500+08:00" level=info msg="[TCP] 127.0.0.1:61596 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:01.551687300+08:00" level=info msg="[TCP] 127.0.0.1:61600 --> oneclient.sfx.ms:443 using GLOBAL"
time="2025-06-10T21:48:02.023581700+08:00" level=info msg="[TCP] 127.0.0.1:61603 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:02.346043000+08:00" level=info msg="[TCP] 127.0.0.1:61605 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:48:02.374051600+08:00" level=info msg="[TCP] 127.0.0.1:61598 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:02.928162400+08:00" level=info msg="[TCP] 127.0.0.1:61607 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:48:03.806075500+08:00" level=info msg="[TCP] 127.0.0.1:61611 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:03.885193100+08:00" level=info msg="[TCP] 127.0.0.1:61609 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:05.091186400+08:00" level=info msg="[TCP] 127.0.0.1:61617 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:48:05.270433600+08:00" level=info msg="[TCP] 127.0.0.1:61615 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:48:06.396456000+08:00" level=info msg="[TCP] 127.0.0.1:61619 --> login.live.com:443 using GLOBAL"
time="2025-06-10T21:48:10.040897800+08:00" level=info msg="[TCP] 127.0.0.1:61623 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:16.286729900+08:00" level=info msg="[TCP] 127.0.0.1:61630 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:16.450903100+08:00" level=info msg="[TCP] 127.0.0.1:61632 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:18.776856200+08:00" level=info msg="[TCP] 127.0.0.1:61635 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:19.076618200+08:00" level=info msg="[TCP] 127.0.0.1:61638 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:19.114830400+08:00" level=info msg="[TCP] 127.0.0.1:61640 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:20.249742900+08:00" level=info msg="[TCP] 127.0.0.1:61644 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:20.297620100+08:00" level=info msg="[TCP] 127.0.0.1:61646 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:21.078704100+08:00" level=info msg="[TCP] 127.0.0.1:61651 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:22.038209100+08:00" level=info msg="[TCP] 127.0.0.1:61657 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:22.041757800+08:00" level=info msg="[TCP] 127.0.0.1:61659 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:22.170939200+08:00" level=info msg="[TCP] 127.0.0.1:61662 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:22.487114600+08:00" level=info msg="[TCP] 127.0.0.1:61664 --> go.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:48:23.083831000+08:00" level=info msg="[TCP] 127.0.0.1:61667 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:23.085692600+08:00" level=info msg="[TCP] 127.0.0.1:61666 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:23.087876600+08:00" level=info msg="[TCP] 127.0.0.1:61670 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:23.100114600+08:00" level=info msg="[TCP] 127.0.0.1:61672 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:23.171745300+08:00" level=info msg="[TCP] 127.0.0.1:61675 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:24.105285200+08:00" level=info msg="[TCP] 127.0.0.1:61679 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:24.123638300+08:00" level=info msg="[TCP] 127.0.0.1:61677 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:25.049867800+08:00" level=info msg="[TCP] 127.0.0.1:61681 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:25.570714900+08:00" level=info msg="[TCP] 127.0.0.1:61684 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:25.575109600+08:00" level=info msg="[TCP] 127.0.0.1:61686 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:26.134535100+08:00" level=info msg="[TCP] 127.0.0.1:61688 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:26.210297000+08:00" level=info msg="[TCP] 127.0.0.1:61691 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:27.472196500+08:00" level=info msg="[TCP] 127.0.0.1:61693 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:27.753071500+08:00" level=info msg="[TCP] 127.0.0.1:61695 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:29.037696300+08:00" level=info msg="[TCP] 127.0.0.1:61698 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:29.568995200+08:00" level=info msg="[TCP] 127.0.0.1:61701 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:33.929872700+08:00" level=info msg="[TCP] 127.0.0.1:61705 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:33.930942700+08:00" level=info msg="[TCP] 127.0.0.1:61706 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:35.275544300+08:00" level=info msg="[TCP] 127.0.0.1:61710 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:42.413921700+08:00" level=info msg="[TCP] 127.0.0.1:61717 --> activity.windows.com:443 using GLOBAL"
time="2025-06-10T21:48:48.144878400+08:00" level=info msg="[TCP] 127.0.0.1:61725 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:48.177810500+08:00" level=info msg="[TCP] 127.0.0.1:61723 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:49.532118500+08:00" level=info msg="[TCP] 127.0.0.1:61728 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:49.538052600+08:00" level=info msg="[TCP] 127.0.0.1:61730 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:50.589814000+08:00" level=info msg="[TCP] 127.0.0.1:61733 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:50.781345300+08:00" level=info msg="[TCP] 127.0.0.1:61735 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:51.395409700+08:00" level=info msg="[TCP] 127.0.0.1:61737 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:55.033605900+08:00" level=info msg="[TCP] 127.0.0.1:61741 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:55.040192300+08:00" level=info msg="[TCP] 127.0.0.1:61743 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:57.461827500+08:00" level=info msg="[TCP] 127.0.0.1:61747 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:57.984350600+08:00" level=info msg="[TCP] 127.0.0.1:61749 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:58.350006200+08:00" level=info msg="[TCP] 127.0.0.1:61751 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:58.432135600+08:00" level=info msg="[TCP] 127.0.0.1:61753 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:58.865311200+08:00" level=info msg="[TCP] 127.0.0.1:61756 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:59.352397600+08:00" level=info msg="[TCP] 127.0.0.1:61758 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:59.459988800+08:00" level=info msg="[TCP] 127.0.0.1:61760 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:48:59.871325600+08:00" level=info msg="[TCP] 127.0.0.1:61763 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:02.635380800+08:00" level=info msg="[TCP] 127.0.0.1:61766 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:02.737561900+08:00" level=info msg="[TCP] 127.0.0.1:61769 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:05.048996100+08:00" level=info msg="[TCP] 127.0.0.1:61775 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:09.471617900+08:00" level=info msg="[TCP] 127.0.0.1:61780 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:14.073174400+08:00" level=info msg="[TCP] 127.0.0.1:61785 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:49:16.277498500+08:00" level=info msg="[TCP] 127.0.0.1:61788 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:17.900949600+08:00" level=info msg="[TCP] 127.0.0.1:61792 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:32.448106400+08:00" level=info msg="[TCP] 127.0.0.1:61803 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:32.452772100+08:00" level=info msg="[TCP] 127.0.0.1:61804 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:32.723192500+08:00" level=info msg="[TCP] 127.0.0.1:61807 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:33.449926300+08:00" level=info msg="[TCP] 127.0.0.1:61812 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:33.506212000+08:00" level=info msg="[TCP] 127.0.0.1:61810 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:33.508193700+08:00" level=info msg="[TCP] 127.0.0.1:61814 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:35.256461400+08:00" level=info msg="[TCP] 127.0.0.1:61819 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:35.763711200+08:00" level=info msg="[TCP] 127.0.0.1:61821 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:35.839816000+08:00" level=info msg="[TCP] 127.0.0.1:61816 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:46.905318200+08:00" level=info msg="[TCP] 127.0.0.1:61830 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:49:47.379352600+08:00" level=info msg="[TCP] 127.0.0.1:61837 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:47.384721600+08:00" level=info msg="[TCP] 127.0.0.1:61834 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:47.388043300+08:00" level=info msg="[TCP] 127.0.0.1:61835 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:47.400763200+08:00" level=info msg="[TCP] 127.0.0.1:61836 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:47.408914000+08:00" level=info msg="[TCP] 127.0.0.1:61833 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:48.377302600+08:00" level=info msg="[TCP] 127.0.0.1:61844 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:48.385012500+08:00" level=info msg="[TCP] 127.0.0.1:61846 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:48.641682600+08:00" level=info msg="[TCP] 127.0.0.1:61848 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:49.459872200+08:00" level=info msg="[TCP] 127.0.0.1:61850 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:49.946993800+08:00" level=info msg="[TCP] 127.0.0.1:61852 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:49.954874700+08:00" level=info msg="[TCP] 127.0.0.1:61854 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:51.076115600+08:00" level=info msg="[TCP] 127.0.0.1:61857 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:51.094136100+08:00" level=info msg="[TCP] 127.0.0.1:61859 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:52.085753900+08:00" level=info msg="[TCP] 127.0.0.1:61862 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:58.636268100+08:00" level=info msg="[TCP] 127.0.0.1:61868 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:59.468649800+08:00" level=info msg="[TCP] 127.0.0.1:61869 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:59.481444000+08:00" level=info msg="[TCP] 127.0.0.1:61875 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:49:59.587563500+08:00" level=info msg="[TCP] 127.0.0.1:61873 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:00.487140000+08:00" level=info msg="[TCP] 127.0.0.1:61876 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:01.049723300+08:00" level=info msg="[TCP] 127.0.0.1:61884 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:01.239224800+08:00" level=info msg="[TCP] 127.0.0.1:61888 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:01.912905200+08:00" level=info msg="[TCP] 127.0.0.1:61891 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:03.105848200+08:00" level=info msg="[TCP] 127.0.0.1:61897 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:50:05.773654200+08:00" level=info msg="[TCP] 127.0.0.1:61902 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:06.884960000+08:00" level=info msg="[TCP] 127.0.0.1:61905 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:06.935328200+08:00" level=info msg="[TCP] 127.0.0.1:61907 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:07.938493400+08:00" level=info msg="[TCP] 127.0.0.1:61910 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:08.613049200+08:00" level=info msg="[TCP] 127.0.0.1:61914 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:10.721736200+08:00" level=info msg="[TCP] 127.0.0.1:61917 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:11.939764000+08:00" level=info msg="[TCP] 127.0.0.1:61920 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:12.917476700+08:00" level=info msg="[TCP] 127.0.0.1:61923 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:13.285835200+08:00" level=info msg="[TCP] 127.0.0.1:61925 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:15.016061900+08:00" level=info msg="[TCP] 127.0.0.1:61929 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:16.320975100+08:00" level=info msg="[TCP] 127.0.0.1:61932 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:18.595296800+08:00" level=info msg="[TCP] 127.0.0.1:61936 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:20.000445900+08:00" level=info msg="[TCP] 127.0.0.1:61938 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:50:20.768695900+08:00" level=info msg="[TCP] 127.0.0.1:61941 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:50:22.525295400+08:00" level=info msg="[TCP] 127.0.0.1:61945 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:50:23.102798100+08:00" level=info msg="[TCP] 127.0.0.1:61947 --> cp601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:50:24.138806500+08:00" level=info msg="[TCP] 127.0.0.1:61952 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T21:50:24.540680900+08:00" level=info msg="[TCP] 127.0.0.1:61950 --> au.download.windowsupdate.com:80 using GLOBAL"
time="2025-06-10T21:50:25.502189100+08:00" level=info msg="[TCP] 127.0.0.1:61958 --> au.download.windowsupdate.com:80 using GLOBAL"
time="2025-06-10T21:50:29.625286000+08:00" level=info msg="[TCP] 127.0.0.1:61965 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T21:51:08.273113900+08:00" level=info msg="[TCP] 127.0.0.1:61994 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T21:51:14.734162500+08:00" level=info msg="[TCP] 127.0.0.1:62001 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T21:51:14.780508600+08:00" level=info msg="[TCP] 127.0.0.1:62000 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T21:51:16.582829100+08:00" level=info msg="[TCP] 127.0.0.1:62006 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:16.715477000+08:00" level=info msg="[TCP] 127.0.0.1:62008 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:21.726041300+08:00" level=info msg="[TCP] 127.0.0.1:62013 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:21.727336500+08:00" level=info msg="[TCP] 127.0.0.1:62014 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:28.148158300+08:00" level=info msg="[TCP] 127.0.0.1:62020 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:29.355558300+08:00" level=info msg="[TCP] 127.0.0.1:62024 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:34.552917300+08:00" level=info msg="[TCP] 127.0.0.1:62030 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:34.619689600+08:00" level=info msg="[TCP] 127.0.0.1:62034 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:34.982285900+08:00" level=info msg="[TCP] 127.0.0.1:62032 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:36.884991900+08:00" level=info msg="[TCP] 127.0.0.1:62037 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:39.160521500+08:00" level=info msg="[TCP] 127.0.0.1:62040 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:42.216791400+08:00" level=info msg="[TCP] 127.0.0.1:62044 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:42.596786200+08:00" level=info msg="[TCP] 127.0.0.1:62047 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:46.025215400+08:00" level=info msg="[TCP] 127.0.0.1:62051 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:50.090380500+08:00" level=info msg="[TCP] 127.0.0.1:62056 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:52.518752900+08:00" level=info msg="[TCP] 127.0.0.1:62059 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:51:59.808885900+08:00" level=info msg="[TCP] 127.0.0.1:62066 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:04.071300600+08:00" level=info msg="[TCP] 127.0.0.1:62074 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:04.145203000+08:00" level=info msg="[TCP] 127.0.0.1:62072 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:06.036380600+08:00" level=info msg="[TCP] 127.0.0.1:62077 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:14.885784700+08:00" level=info msg="[TCP] 127.0.0.1:62085 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:14.985089500+08:00" level=info msg="[TCP] 127.0.0.1:62087 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:15.176163900+08:00" level=info msg="[TCP] 127.0.0.1:62088 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:15.769432600+08:00" level=info msg="[TCP] 127.0.0.1:62093 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:15.785639400+08:00" level=info msg="[TCP] 127.0.0.1:62092 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:16.274672500+08:00" level=info msg="[TCP] 127.0.0.1:62096 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:16.913442200+08:00" level=info msg="[TCP] 127.0.0.1:62099 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:19.194703800+08:00" level=info msg="[TCP] 127.0.0.1:62102 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:24.858481100+08:00" level=info msg="[TCP] 127.0.0.1:62108 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:26.408334400+08:00" level=info msg="[TCP] 127.0.0.1:62111 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:27.108475500+08:00" level=info msg="[TCP] 127.0.0.1:62113 --> go.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:52:30.327245600+08:00" level=info msg="[TCP] 127.0.0.1:62117 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T21:52:30.330236000+08:00" level=info msg="[TCP] 127.0.0.1:62119 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T21:52:35.772533700+08:00" level=info msg="[TCP] 127.0.0.1:62125 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:52:46.348938600+08:00" level=info msg="[TCP] 127.0.0.1:62134 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T21:52:48.409677000+08:00" level=info msg="[TCP] 127.0.0.1:62137 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:52:53.116239300+08:00" level=info msg="[TCP] 127.0.0.1:62144 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:53:14.793238500+08:00" level=info msg="[TCP] 127.0.0.1:62160 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:53:14.851184900+08:00" level=info msg="[TCP] 127.0.0.1:62162 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:53:14.866648700+08:00" level=info msg="[TCP] 127.0.0.1:62164 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:53:16.103278600+08:00" level=info msg="[TCP] 127.0.0.1:62166 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:53:16.284329700+08:00" level=info msg="[TCP] 127.0.0.1:62168 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:53:19.796353700+08:00" level=info msg="[TCP] 127.0.0.1:62173 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:53:19.796353700+08:00" level=info msg="[TCP] 127.0.0.1:62174 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:53:23.388280100+08:00" level=info msg="[TCP] 127.0.0.1:62180 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:53:27.842245200+08:00" level=info msg="[TCP] 127.0.0.1:62184 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:53:44.855412500+08:00" level=info msg="[TCP] 127.0.0.1:62198 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:53:46.357274500+08:00" level=info msg="[TCP] 127.0.0.1:62201 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T21:53:46.360398800+08:00" level=info msg="[TCP] 127.0.0.1:62203 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T21:53:46.361418000+08:00" level=info msg="[TCP] 127.0.0.1:62200 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T21:53:55.885606100+08:00" level=info msg="[TCP] 127.0.0.1:62213 --> login.live.com:443 using GLOBAL"
time="2025-06-10T21:53:56.683635800+08:00" level=info msg="[TCP] 127.0.0.1:62216 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-10T21:53:56.712895000+08:00" level=info msg="[TCP] 127.0.0.1:62218 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:53:56.995155400+08:00" level=info msg="[TCP] 127.0.0.1:62220 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-06-10T21:53:57.387697100+08:00" level=info msg="[TCP] 127.0.0.1:62222 --> update.code.visualstudio.com:443 using GLOBAL"
time="2025-06-10T21:53:57.771394000+08:00" level=info msg="[TCP] 127.0.0.1:62224 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-10T21:53:57.878601800+08:00" level=info msg="[TCP] 127.0.0.1:62226 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-10T21:53:59.185100700+08:00" level=info msg="[TCP] 127.0.0.1:62229 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-06-10T21:53:59.204506800+08:00" level=info msg="[TCP] 127.0.0.1:62231 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:53:59.234044100+08:00" level=info msg="[TCP] 127.0.0.1:62233 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-06-10T21:53:59.540084100+08:00" level=info msg="[TCP] 127.0.0.1:62235 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-06-10T21:53:59.543695500+08:00" level=info msg="[TCP] 127.0.0.1:62237 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-06-10T21:53:59.548599700+08:00" level=info msg="[TCP] 127.0.0.1:62236 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-06-10T21:53:59.600316200+08:00" level=info msg="[TCP] 127.0.0.1:62241 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:54:00.982611400+08:00" level=info msg="[TCP] 127.0.0.1:62244 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:54:02.744033000+08:00" level=info msg="[TCP] 127.0.0.1:62249 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:54:02.763842200+08:00" level=info msg="[TCP] 127.0.0.1:62251 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-10T21:54:02.765240700+08:00" level=info msg="[TCP] 127.0.0.1:62257 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-10T21:54:02.766500500+08:00" level=info msg="[TCP] 127.0.0.1:62253 --> c.msn.cn:443 using GLOBAL"
time="2025-06-10T21:54:02.772142400+08:00" level=info msg="[TCP] 127.0.0.1:62254 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-10T21:54:02.850607500+08:00" level=info msg="[TCP] 127.0.0.1:62259 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-10T21:54:03.032001700+08:00" level=info msg="[TCP] 127.0.0.1:62261 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-10T21:54:03.075968500+08:00" level=info msg="[TCP] 127.0.0.1:62263 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-10T21:54:03.076485300+08:00" level=info msg="[TCP] 127.0.0.1:62264 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-10T21:54:03.152488800+08:00" level=info msg="[TCP] 127.0.0.1:62268 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-10T21:54:03.153278700+08:00" level=info msg="[TCP] 127.0.0.1:62267 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-10T21:54:03.350617100+08:00" level=info msg="[TCP] 127.0.0.1:62271 --> c.bing.com:443 using GLOBAL"
time="2025-06-10T21:54:03.849621800+08:00" level=info msg="[TCP] 127.0.0.1:62273 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-10T21:54:04.457969000+08:00" level=info msg="[TCP] 127.0.0.1:62275 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T21:54:04.457969000+08:00" level=info msg="[TCP] 127.0.0.1:62276 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T21:54:05.809604700+08:00" level=info msg="[TCP] 127.0.0.1:62281 --> ntp.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:06.610633400+08:00" level=info msg="[TCP] 127.0.0.1:62291 --> th.bing.com:443 using GLOBAL"
time="2025-06-10T21:54:06.610633400+08:00" level=info msg="[TCP] 127.0.0.1:62295 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-06-10T21:54:06.612138900+08:00" level=info msg="[TCP] 127.0.0.1:62286 --> c.bing.com:443 using GLOBAL"
time="2025-06-10T21:54:06.612138900+08:00" level=info msg="[TCP] 127.0.0.1:62289 --> api.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:06.613718000+08:00" level=info msg="[TCP] 127.0.0.1:62283 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T21:54:06.615182100+08:00" level=info msg="[TCP] 127.0.0.1:62284 --> c.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:06.617222500+08:00" level=info msg="[TCP] 127.0.0.1:62293 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-10T21:54:06.652879800+08:00" level=info msg="[TCP] 127.0.0.1:62297 --> assets.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:06.695587700+08:00" level=info msg="[TCP] 127.0.0.1:62299 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:06.712049500+08:00" level=info msg="[TCP] 127.0.0.1:62301 --> assets.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:06.816882000+08:00" level=info msg="[TCP] 127.0.0.1:62305 --> th.bing.com:443 using GLOBAL"
time="2025-06-10T21:54:06.818978600+08:00" level=info msg="[TCP] 127.0.0.1:62303 --> c.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:06.867717100+08:00" level=info msg="[TCP] 127.0.0.1:62307 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-10T21:54:06.934379200+08:00" level=info msg="[TCP] 127.0.0.1:62309 --> assets.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:06.971449900+08:00" level=info msg="[TCP] 127.0.0.1:62311 --> assets.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:07.323725400+08:00" level=info msg="[TCP] 127.0.0.1:62313 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:54:07.326753600+08:00" level=info msg="[TCP] 127.0.0.1:62315 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:54:07.652658100+08:00" level=info msg="[TCP] 127.0.0.1:62317 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:54:07.720058000+08:00" level=info msg="[TCP] 127.0.0.1:62319 --> assets.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:07.744152900+08:00" level=info msg="[TCP] 127.0.0.1:62324 --> r.msftstatic.com:443 using GLOBAL"
time="2025-06-10T21:54:07.746111100+08:00" level=info msg="[TCP] 127.0.0.1:62321 --> r.bing.com:443 using GLOBAL"
time="2025-06-10T21:54:07.747586600+08:00" level=info msg="[TCP] 127.0.0.1:62327 --> r.msftstatic.com:443 using GLOBAL"
time="2025-06-10T21:54:07.750191100+08:00" level=info msg="[TCP] 127.0.0.1:62322 --> r.bing.com:443 using GLOBAL"
time="2025-06-10T21:54:07.765620900+08:00" level=info msg="[TCP] 127.0.0.1:62329 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T21:54:08.550317600+08:00" level=info msg="[TCP] 127.0.0.1:62332 --> assets.msn.com:443 using GLOBAL"
time="2025-06-10T21:54:17.174915200+08:00" level=info msg="[TCP] 127.0.0.1:62367 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:55:50.218181700+08:00" level=info msg="[TCP] 127.0.0.1:62532 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:56:16.276243100+08:00" level=info msg="[TCP] 127.0.0.1:62597 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:56:20.470468300+08:00" level=info msg="[TCP] 127.0.0.1:62602 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:56:21.500730100+08:00" level=info msg="[TCP] 127.0.0.1:62604 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:14.882574500+08:00" level=info msg="[TCP] 127.0.0.1:62645 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:14.987684200+08:00" level=info msg="[TCP] 127.0.0.1:62648 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:14.992873200+08:00" level=info msg="[TCP] 127.0.0.1:62647 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:15.764513300+08:00" level=info msg="[TCP] 127.0.0.1:62651 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:15.768051600+08:00" level=info msg="[TCP] 127.0.0.1:62652 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:16.280509600+08:00" level=info msg="[TCP] 127.0.0.1:62655 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:18.599320400+08:00" level=info msg="[TCP] 127.0.0.1:62659 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:19.671968800+08:00" level=info msg="[TCP] 127.0.0.1:62662 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:20.472005400+08:00" level=info msg="[TCP] 127.0.0.1:62668 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:22.761708300+08:00" level=info msg="[TCP] 127.0.0.1:62677 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:57:24.873381100+08:00" level=info msg="[TCP] 127.0.0.1:62682 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:58:16.275478300+08:00" level=info msg="[TCP] 127.0.0.1:62725 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:58:20.493006400+08:00" level=info msg="[TCP] 127.0.0.1:62730 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:58:28.199527200+08:00" level=info msg="[TCP] 127.0.0.1:62737 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:59:16.280330300+08:00" level=info msg="[TCP] 127.0.0.1:62775 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:59:20.510018500+08:00" level=info msg="[TCP] 127.0.0.1:62779 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T21:59:21.885223600+08:00" level=info msg="[TCP] 127.0.0.1:62783 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:16.285731200+08:00" level=info msg="[TCP] 127.0.0.1:62826 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:20.506999500+08:00" level=info msg="[TCP] 127.0.0.1:62830 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:24.034913300+08:00" level=info msg="[TCP] 127.0.0.1:62834 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:50.214498200+08:00" level=info msg="[TCP] 127.0.0.1:62855 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:50.270620800+08:00" level=info msg="[TCP] 127.0.0.1:62857 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:51.341639800+08:00" level=info msg="[TCP] 127.0.0.1:62859 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:52.296105900+08:00" level=info msg="[TCP] 127.0.0.1:62862 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:53.172168100+08:00" level=info msg="[TCP] 127.0.0.1:62865 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:54.169631100+08:00" level=info msg="[TCP] 127.0.0.1:62867 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:55.420287200+08:00" level=info msg="[TCP] 127.0.0.1:62869 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:00:56.198736500+08:00" level=info msg="[TCP] 127.0.0.1:62873 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:09.382651900+08:00" level=info msg="[TCP] 127.0.0.1:62883 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:12.074408100+08:00" level=info msg="[TCP] 127.0.0.1:62887 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:13.012743500+08:00" level=info msg="[TCP] 127.0.0.1:62893 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:13.032307900+08:00" level=info msg="[TCP] 127.0.0.1:62896 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:13.032307900+08:00" level=info msg="[TCP] 127.0.0.1:62890 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:13.049663900+08:00" level=info msg="[TCP] 127.0.0.1:62898 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:14.024026200+08:00" level=info msg="[TCP] 127.0.0.1:62901 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:14.047126100+08:00" level=info msg="[TCP] 127.0.0.1:62892 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:15.049914100+08:00" level=info msg="[TCP] 127.0.0.1:62903 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:16.051012700+08:00" level=info msg="[TCP] 127.0.0.1:62908 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:16.345247400+08:00" level=info msg="[TCP] 127.0.0.1:62910 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:16.560333500+08:00" level=info msg="[TCP] 127.0.0.1:62912 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:26.341135300+08:00" level=info msg="[TCP] 127.0.0.1:62924 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:26.357299200+08:00" level=info msg="[TCP] 127.0.0.1:62922 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:27.776019600+08:00" level=info msg="[TCP] 127.0.0.1:62926 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:29.169836400+08:00" level=info msg="[TCP] 127.0.0.1:62930 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:29.486018000+08:00" level=info msg="[TCP] 127.0.0.1:62932 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:30.165384700+08:00" level=info msg="[TCP] 127.0.0.1:62934 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:30.166434200+08:00" level=info msg="[TCP] 127.0.0.1:62935 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:31.257746500+08:00" level=info msg="[TCP] 127.0.0.1:62945 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:31.400532100+08:00" level=info msg="[TCP] 127.0.0.1:62943 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:33.250934500+08:00" level=info msg="[TCP] 127.0.0.1:62952 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:33.254041500+08:00" level=info msg="[TCP] 127.0.0.1:62954 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:36.685212600+08:00" level=info msg="[TCP] 127.0.0.1:62958 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:37.973657400+08:00" level=info msg="[TCP] 127.0.0.1:62962 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:38.214177400+08:00" level=info msg="[TCP] 127.0.0.1:62964 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:40.312725900+08:00" level=info msg="[TCP] 127.0.0.1:62967 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:42.286744500+08:00" level=info msg="[TCP] 127.0.0.1:62970 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:46.386115900+08:00" level=info msg="[TCP] 127.0.0.1:62975 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:48.275854500+08:00" level=info msg="[TCP] 127.0.0.1:62978 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:01:49.920760400+08:00" level=info msg="[TCP] 127.0.0.1:62982 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:01:52.201243000+08:00" level=info msg="[TCP] 127.0.0.1:62985 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:01.042515900+08:00" level=info msg="[TCP] 127.0.0.1:62992 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:14.892263200+08:00" level=info msg="[TCP] 127.0.0.1:63006 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:14.984802300+08:00" level=info msg="[TCP] 127.0.0.1:63008 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:15.025390500+08:00" level=info msg="[TCP] 127.0.0.1:63009 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:15.762217600+08:00" level=info msg="[TCP] 127.0.0.1:63013 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:16.272243900+08:00" level=info msg="[TCP] 127.0.0.1:63016 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:17.673896800+08:00" level=info msg="[TCP] 127.0.0.1:63020 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:18.786028700+08:00" level=info msg="[TCP] 127.0.0.1:63012 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:20.921158300+08:00" level=info msg="[TCP] 127.0.0.1:63026 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:02:23.592239500+08:00" level=info msg="[TCP] 127.0.0.1:63024 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:24.168101600+08:00" level=info msg="[TCP] 127.0.0.1:63030 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:24.878656200+08:00" level=info msg="[TCP] 127.0.0.1:63032 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:27.844620100+08:00" level=info msg="[TCP] 127.0.0.1:63036 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:30.970384900+08:00" level=info msg="[TCP] 127.0.0.1:63040 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:02:32.374095200+08:00" level=info msg="[TCP] 127.0.0.1:63044 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:32.377003100+08:00" level=info msg="[TCP] 127.0.0.1:63045 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:34.991214800+08:00" level=info msg="[TCP] 127.0.0.1:63050 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:02:37.469780400+08:00" level=info msg="[TCP] 127.0.0.1:63053 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:40.164531700+08:00" level=info msg="[TCP] 127.0.0.1:63058 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:02:40.751684100+08:00" level=info msg="[TCP] 127.0.0.1:63063 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:02:40.754016200+08:00" level=info msg="[TCP] 127.0.0.1:63061 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:02:47.956828000+08:00" level=info msg="[TCP] 127.0.0.1:63070 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:02:50.951800800+08:00" level=info msg="[TCP] 127.0.0.1:63074 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:50.996848200+08:00" level=info msg="[TCP] 127.0.0.1:63076 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:51.006772200+08:00" level=info msg="[TCP] 127.0.0.1:63078 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:51.849984400+08:00" level=info msg="[TCP] 127.0.0.1:63080 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:53.584051100+08:00" level=info msg="[TCP] 127.0.0.1:63083 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:55.956455800+08:00" level=info msg="[TCP] 127.0.0.1:63087 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:02:55.956455800+08:00" level=info msg="[TCP] 127.0.0.1:63088 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:05.520559200+08:00" level=info msg="[TCP] 127.0.0.1:63097 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:03:16.276962100+08:00" level=info msg="[TCP] 127.0.0.1:63106 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:17.267738400+08:00" level=info msg="[TCP] 127.0.0.1:63109 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:30.318103200+08:00" level=info msg="[TCP] 127.0.0.1:63121 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:03:38.735296500+08:00" level=info msg="[TCP] 127.0.0.1:63128 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:38.790574700+08:00" level=info msg="[TCP] 127.0.0.1:63130 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:39.901495200+08:00" level=info msg="[TCP] 127.0.0.1:63133 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:41.116813500+08:00" level=info msg="[TCP] 127.0.0.1:63135 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-10T22:03:42.542261300+08:00" level=info msg="[TCP] 127.0.0.1:63139 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-10T22:03:42.905646100+08:00" level=info msg="[TCP] 127.0.0.1:63153 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.905646100+08:00" level=info msg="[TCP] 127.0.0.1:63145 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.906159200+08:00" level=info msg="[TCP] 127.0.0.1:63143 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.908690800+08:00" level=info msg="[TCP] 127.0.0.1:63152 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.908690800+08:00" level=info msg="[TCP] 127.0.0.1:63155 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.909217900+08:00" level=info msg="[TCP] 127.0.0.1:63144 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.909217900+08:00" level=info msg="[TCP] 127.0.0.1:63154 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.910394000+08:00" level=info msg="[TCP] 127.0.0.1:63147 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.910394000+08:00" level=info msg="[TCP] 127.0.0.1:63151 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.946174500+08:00" level=info msg="[TCP] 127.0.0.1:63171 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.957165800+08:00" level=info msg="[TCP] 127.0.0.1:63174 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.962088900+08:00" level=info msg="[TCP] 127.0.0.1:63179 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.963658000+08:00" level=info msg="[TCP] 127.0.0.1:63178 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.965250500+08:00" level=info msg="[TCP] 127.0.0.1:63177 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.977287700+08:00" level=info msg="[TCP] 127.0.0.1:63149 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:42.998118800+08:00" level=info msg="[TCP] 127.0.0.1:63173 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:43.047918000+08:00" level=info msg="[TCP] 127.0.0.1:63156 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:43.051996400+08:00" level=info msg="[TCP] 127.0.0.1:63146 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:43.127353400+08:00" level=info msg="[TCP] 127.0.0.1:63141 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:43.144137500+08:00" level=info msg="[TCP] 127.0.0.1:63148 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:43.145616900+08:00" level=info msg="[TCP] 127.0.0.1:63150 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:43.408971400+08:00" level=info msg="[TCP] 127.0.0.1:63187 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:43.411686100+08:00" level=info msg="[TCP] 127.0.0.1:63188 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:43.662282000+08:00" level=info msg="[TCP] 127.0.0.1:63192 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:03:43.695970400+08:00" level=info msg="[TCP] 127.0.0.1:63193 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:03:43.854395500+08:00" level=info msg="[TCP] 127.0.0.1:63197 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:44.416410400+08:00" level=info msg="[TCP] 127.0.0.1:63203 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:44.840238100+08:00" level=info msg="[TCP] 127.0.0.1:63207 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:47.075279000+08:00" level=info msg="[TCP] 127.0.0.1:63213 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:47.549097600+08:00" level=info msg="[TCP] 127.0.0.1:63216 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:51.096334400+08:00" level=info msg="[TCP] 127.0.0.1:63221 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:51.398332500+08:00" level=info msg="[TCP] 127.0.0.1:63224 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:03:51.427493000+08:00" level=info msg="[TCP] 127.0.0.1:63228 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:03:52.428518500+08:00" level=info msg="[TCP] 127.0.0.1:63226 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:03:52.921011900+08:00" level=info msg="[TCP] 127.0.0.1:63230 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:03:54.207176300+08:00" level=info msg="[TCP] 127.0.0.1:63233 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:04:03.691540200+08:00" level=info msg="[TCP] 127.0.0.1:63243 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:04:05.347919100+08:00" level=info msg="[TCP] 127.0.0.1:63246 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:04:05.539455500+08:00" level=info msg="[TCP] 127.0.0.1:63248 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:04:07.186632300+08:00" level=info msg="[TCP] 127.0.0.1:63254 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:04:07.768638200+08:00" level=info msg="[TCP] 127.0.0.1:63252 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:04:08.623619900+08:00" level=info msg="[TCP] 127.0.0.1:63256 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:04:11.175185400+08:00" level=info msg="[TCP] 127.0.0.1:63260 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:04:19.883317500+08:00" level=info msg="[TCP] 127.0.0.1:63268 --> cdn.jsdelivr.net:443 using GLOBAL"
time="2025-06-10T22:04:20.229319300+08:00" level=info msg="[TCP] 127.0.0.1:63270 --> www.google.com:443 using GLOBAL"
time="2025-06-10T22:04:22.469322300+08:00" level=info msg="[TCP] 127.0.0.1:63274 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:04:22.476501600+08:00" level=info msg="[TCP] 127.0.0.1:63275 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:04:24.412269800+08:00" level=info msg="[TCP] 127.0.0.1:63279 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-10T22:04:24.649575800+08:00" level=info msg="[TCP] 127.0.0.1:63282 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:04:24.654616800+08:00" level=info msg="[TCP] 127.0.0.1:63284 --> cdn7.xkw.com:443 using GLOBAL"
time="2025-06-10T22:04:26.112161500+08:00" level=info msg="[TCP] 127.0.0.1:63286 --> static.zxxk.com:443 using GLOBAL"
time="2025-06-10T22:04:41.185816800+08:00" level=info msg="[TCP] 127.0.0.1:63313 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:05:41.183955500+08:00" level=info msg="[TCP] 127.0.0.1:63379 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:06:38.708912400+08:00" level=info msg="[TCP] 127.0.0.1:63435 --> activity.windows.com:443 using GLOBAL"
time="2025-06-10T22:06:41.603843600+08:00" level=info msg="[TCP] 127.0.0.1:63438 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:06:42.741009300+08:00" level=info msg="[TCP] 127.0.0.1:63441 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:06:53.137261900+08:00" level=info msg="[TCP] 127.0.0.1:63450 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:06:55.514783100+08:00" level=info msg="[TCP] 127.0.0.1:63454 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:06:55.535488000+08:00" level=info msg="[TCP] 127.0.0.1:63453 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:06:57.376566200+08:00" level=info msg="[TCP] 127.0.0.1:63458 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:06:59.944661800+08:00" level=info msg="[TCP] 127.0.0.1:63463 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:07:00.057219000+08:00" level=info msg="[TCP] 127.0.0.1:63465 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:07:01.462289900+08:00" level=info msg="[TCP] 127.0.0.1:63467 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:07:40.607755000+08:00" level=info msg="[TCP] 127.0.0.1:63495 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:00.053640100+08:00" level=info msg="[TCP] 127.0.0.1:63515 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:01.742620700+08:00" level=info msg="[TCP] 127.0.0.1:63518 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:25.822194500+08:00" level=info msg="[TCP] 127.0.0.1:63536 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:08:29.597435500+08:00" level=info msg="[TCP] 127.0.0.1:63541 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:29.682054600+08:00" level=info msg="[TCP] 127.0.0.1:63543 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:30.569187700+08:00" level=info msg="[TCP] 127.0.0.1:63546 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:37.518817900+08:00" level=info msg="[TCP] 127.0.0.1:63553 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:37.567322000+08:00" level=info msg="[TCP] 127.0.0.1:63555 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:37.670301700+08:00" level=info msg="[TCP] 127.0.0.1:63557 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:37.670301700+08:00" level=info msg="[TCP] 127.0.0.1:63558 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:38.487580400+08:00" level=info msg="[TCP] 127.0.0.1:63561 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:39.140290400+08:00" level=info msg="[TCP] 127.0.0.1:63565 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:39.140290400+08:00" level=info msg="[TCP] 127.0.0.1:63564 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:39.491207700+08:00" level=info msg="[TCP] 127.0.0.1:63569 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:39.685367300+08:00" level=info msg="[TCP] 127.0.0.1:63571 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:40.445010500+08:00" level=info msg="[TCP] 127.0.0.1:63573 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:40.600563600+08:00" level=info msg="[TCP] 127.0.0.1:63575 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:42.505824200+08:00" level=info msg="[TCP] 127.0.0.1:63579 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:47.554521700+08:00" level=info msg="[TCP] 127.0.0.1:63586 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:08:53.249819100+08:00" level=info msg="[TCP] 127.0.0.1:63592 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:08:56.255038300+08:00" level=info msg="[TCP] 127.0.0.1:63596 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:09:04.703943100+08:00" level=info msg="[TCP] 127.0.0.1:63605 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:09:04.705378200+08:00" level=info msg="[TCP] 127.0.0.1:63604 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:09:40.610346300+08:00" level=info msg="[TCP] 127.0.0.1:63632 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:09:53.637384200+08:00" level=info msg="[TCP] 127.0.0.1:63642 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:10:01.815275100+08:00" level=info msg="[TCP] 127.0.0.1:63656 --> login.live.com:443 using GLOBAL"
time="2025-06-10T22:10:12.055232000+08:00" level=info msg="[TCP] 127.0.0.1:63665 --> login.live.com:443 using GLOBAL"
time="2025-06-10T22:10:21.756740200+08:00" level=info msg="[TCP] 127.0.0.1:63673 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:10:24.017847800+08:00" level=info msg="[TCP] 127.0.0.1:63678 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:10:24.027658600+08:00" level=info msg="[TCP] 127.0.0.1:63677 --> bizapi.alipan.com:443 using GLOBAL"
time="2025-06-10T22:10:24.168022500+08:00" level=info msg="[TCP] 127.0.0.1:63680 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:10:26.012988600+08:00" level=info msg="[TCP] 127.0.0.1:63686 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:10:30.461371400+08:00" level=info msg="[TCP] 127.0.0.1:63690 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:10:32.128448700+08:00" level=info msg="[TCP] 127.0.0.1:63696 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:10:32.270801600+08:00" level=info msg="[TCP] 127.0.0.1:63693 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:10:38.043819800+08:00" level=info msg="[TCP] 127.0.0.1:63702 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:10:39.591064400+08:00" level=info msg="[TCP] 127.0.0.1:63704 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:10:40.627397400+08:00" level=info msg="[TCP] 127.0.0.1:63707 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:10:48.441815800+08:00" level=info msg="[TCP] 127.0.0.1:63714 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:10:49.238885000+08:00" level=info msg="[TCP] 127.0.0.1:63716 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:10:52.043694100+08:00" level=info msg="[TCP] 127.0.0.1:63720 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:11:14.688394300+08:00" level=info msg="[TCP] 127.0.0.1:63738 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:11:14.691901800+08:00" level=info msg="[TCP] 127.0.0.1:63739 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:11:40.601815900+08:00" level=info msg="[TCP] 127.0.0.1:63758 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:11:57.758533200+08:00" level=info msg="[TCP] 127.0.0.1:63774 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:11:58.240253600+08:00" level=info msg="[TCP] 127.0.0.1:63776 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:11:58.717385500+08:00" level=info msg="[TCP] 127.0.0.1:63772 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:12:12.412544300+08:00" level=info msg="[TCP] 127.0.0.1:63790 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:12:23.890004200+08:00" level=info msg="[TCP] 127.0.0.1:63801 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:12:27.216741300+08:00" level=info msg="[TCP] 127.0.0.1:63808 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:12:27.218445700+08:00" level=info msg="[TCP] 127.0.0.1:63806 --> bizapi.alipan.com:443 using GLOBAL"
time="2025-06-10T22:12:29.398540900+08:00" level=info msg="[TCP] 127.0.0.1:63810 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:12:32.454506900+08:00" level=info msg="[TCP] 127.0.0.1:63815 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:12:37.031695700+08:00" level=info msg="[TCP] 127.0.0.1:63819 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:12:41.182359900+08:00" level=info msg="[TCP] 127.0.0.1:63826 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:12:41.202672400+08:00" level=info msg="[TCP] 127.0.0.1:63824 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:12:41.282304700+08:00" level=info msg="[TCP] 127.0.0.1:63825 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:12:51.977128700+08:00" level=info msg="[TCP] 127.0.0.1:63839 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:12:51.984069600+08:00" level=info msg="[TCP] 127.0.0.1:63841 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:12:54.905525800+08:00" level=info msg="[TCP] 127.0.0.1:63848 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:38.307600200+08:00" level=info msg="[TCP] 127.0.0.1:63944 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:38.308111500+08:00" level=info msg="[TCP] 127.0.0.1:63945 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:39.176599700+08:00" level=info msg="[TCP] 127.0.0.1:63950 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:39.234799600+08:00" level=info msg="[TCP] 127.0.0.1:63946 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:40.186592900+08:00" level=info msg="[TCP] 127.0.0.1:63951 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:42.820899500+08:00" level=info msg="[TCP] 127.0.0.1:63956 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:43.780272100+08:00" level=info msg="[TCP] 127.0.0.1:63961 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:43.802662000+08:00" level=info msg="[TCP] 127.0.0.1:63963 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:44.321982100+08:00" level=info msg="[TCP] 127.0.0.1:63965 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:45.116287500+08:00" level=info msg="[TCP] 127.0.0.1:63967 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:13:45.606702100+08:00" level=info msg="[TCP] 127.0.0.1:63970 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:47.557203700+08:00" level=info msg="[TCP] 127.0.0.1:63973 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:13:53.657086700+08:00" level=info msg="[TCP] 127.0.0.1:63981 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:13:54.539897100+08:00" level=info msg="[TCP] 127.0.0.1:63979 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:14:05.007299600+08:00" level=info msg="[TCP] 127.0.0.1:63992 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:14:05.009626400+08:00" level=info msg="[TCP] 127.0.0.1:63993 --> bizapi.alipan.com:443 using GLOBAL"
time="2025-06-10T22:14:05.010129000+08:00" level=info msg="[TCP] 127.0.0.1:63995 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:14:33.699719400+08:00" level=info msg="[TCP] 127.0.0.1:64023 --> bizapi.alipan.com:443 using GLOBAL"
time="2025-06-10T22:14:33.756720600+08:00" level=info msg="[TCP] 127.0.0.1:64025 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:14:33.811279200+08:00" level=info msg="[TCP] 127.0.0.1:64027 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:14:40.600266600+08:00" level=info msg="[TCP] 127.0.0.1:64036 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:14:41.521206200+08:00" level=info msg="[TCP] 127.0.0.1:64034 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:14:42.654964600+08:00" level=info msg="[TCP] 127.0.0.1:64042 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:14:44.109283100+08:00" level=info msg="[TCP] 127.0.0.1:64046 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:14:46.357485100+08:00" level=info msg="[TCP] 127.0.0.1:64049 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:14:51.853698800+08:00" level=info msg="[TCP] 127.0.0.1:64056 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:14:53.083252200+08:00" level=info msg="[TCP] 127.0.0.1:64060 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:14:53.600938100+08:00" level=info msg="[TCP] 127.0.0.1:64062 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:15:02.854638000+08:00" level=info msg="[TCP] 127.0.0.1:64073 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:15:15.288634600+08:00" level=info msg="[TCP] 127.0.0.1:64084 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:15:40.614062000+08:00" level=info msg="[TCP] 127.0.0.1:64104 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:15:44.454893000+08:00" level=info msg="[TCP] 127.0.0.1:64108 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:15:47.929626300+08:00" level=info msg="[TCP] 127.0.0.1:64113 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:14.708309400+08:00" level=info msg="[TCP] 127.0.0.1:64134 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:16:15.081672900+08:00" level=info msg="[TCP] 127.0.0.1:64133 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:16:27.074978300+08:00" level=info msg="[TCP] 127.0.0.1:64149 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:27.181923400+08:00" level=info msg="[TCP] 127.0.0.1:64147 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:29.193525600+08:00" level=info msg="[TCP] 127.0.0.1:64156 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:30.362860700+08:00" level=info msg="[TCP] 127.0.0.1:64162 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:31.246925400+08:00" level=info msg="[TCP] 127.0.0.1:64164 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:32.404568400+08:00" level=info msg="[TCP] 127.0.0.1:64166 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:32.455201500+08:00" level=info msg="[TCP] 127.0.0.1:64169 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:33.414053800+08:00" level=info msg="[TCP] 127.0.0.1:64172 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:35.421734700+08:00" level=info msg="[TCP] 127.0.0.1:64175 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:37.890188100+08:00" level=info msg="[TCP] 127.0.0.1:64178 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:40.647556200+08:00" level=info msg="[TCP] 127.0.0.1:64182 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:16:40.742141400+08:00" level=info msg="[TCP] 127.0.0.1:64184 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:16:48.553886700+08:00" level=info msg="[TCP] 127.0.0.1:64194 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:16:49.082835800+08:00" level=info msg="[TCP] 127.0.0.1:64192 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:17:07.883549600+08:00" level=info msg="[TCP] 127.0.0.1:64209 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:17:18.140854800+08:00" level=info msg="[TCP] 127.0.0.1:64218 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:18.148977700+08:00" level=info msg="[TCP] 127.0.0.1:64220 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:19.613646700+08:00" level=info msg="[TCP] 127.0.0.1:64223 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:23.115433900+08:00" level=info msg="[TCP] 127.0.0.1:64227 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:23.115936500+08:00" level=info msg="[TCP] 127.0.0.1:64228 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:25.241907400+08:00" level=info msg="[TCP] 127.0.0.1:64233 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:17:25.751838200+08:00" level=info msg="[TCP] 127.0.0.1:64237 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:25.919743900+08:00" level=info msg="[TCP] 127.0.0.1:64235 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:27.884048100+08:00" level=info msg="[TCP] 127.0.0.1:64248 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:29.492808500+08:00" level=info msg="[TCP] 127.0.0.1:64252 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:29.696360100+08:00" level=info msg="[TCP] 127.0.0.1:64254 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:31.494545600+08:00" level=info msg="[TCP] 127.0.0.1:64257 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:32.673170700+08:00" level=info msg="[TCP] 127.0.0.1:64260 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:38.850603100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64264 --> api2.cursor.sh:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:17:40.598156900+08:00" level=info msg="[TCP] 127.0.0.1:64271 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:44.821019000+08:00" level=info msg="[TCP] 127.0.0.1:64275 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:17:56.153879200+08:00" level=info msg="[TCP] 127.0.0.1:64285 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:17:56.201066800+08:00" level=info msg="[TCP] 127.0.0.1:64289 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:17:56.547039800+08:00" level=info msg="[TCP] 127.0.0.1:64291 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:17:58.734227200+08:00" level=info msg="[TCP] 127.0.0.1:64287 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:18:01.798689000+08:00" level=info msg="[TCP] 127.0.0.1:64298 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:01.802861600+08:00" level=info msg="[TCP] 127.0.0.1:64299 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:04.294677800+08:00" level=info msg="[TCP] 127.0.0.1:64305 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:05.631841000+08:00" level=info msg="[TCP] 127.0.0.1:64307 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:14.897278600+08:00" level=info msg="[TCP] 127.0.0.1:64319 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:14.940187500+08:00" level=info msg="[TCP] 127.0.0.1:64321 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:15.120376200+08:00" level=info msg="[TCP] 127.0.0.1:64317 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:17.921373000+08:00" level=info msg="[TCP] 127.0.0.1:64325 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:19.404490400+08:00" level=info msg="[TCP] 127.0.0.1:64329 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:18:19.873171500+08:00" level=info msg="[TCP] 127.0.0.1:64335 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:20.226475000+08:00" level=info msg="[TCP] 127.0.0.1:64331 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:20.839115100+08:00" level=info msg="[TCP] 127.0.0.1:64332 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:22.961272000+08:00" level=info msg="[TCP] 127.0.0.1:64339 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:18:38.173626100+08:00" level=info msg="[TCP] 127.0.0.1:64357 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:38.174130500+08:00" level=info msg="[TCP] 127.0.0.1:64358 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:38.224623500+08:00" level=info msg="[TCP] 127.0.0.1:64356 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:39.173476100+08:00" level=info msg="[TCP] 127.0.0.1:64363 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:39.214968400+08:00" level=info msg="[TCP] 127.0.0.1:64364 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:40.817997800+08:00" level=info msg="[TCP] 127.0.0.1:64370 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:41.214583700+08:00" level=info msg="[TCP] 127.0.0.1:64372 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:42.297777800+08:00" level=info msg="[TCP] 127.0.0.1:64374 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:18:53.127767800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64395 --> api2.cursor.sh:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:19:41.789267500+08:00" level=info msg="[TCP] 127.0.0.1:64455 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:19:46.294520900+08:00" level=info msg="[TCP] 127.0.0.1:64460 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:19:46.294520900+08:00" level=info msg="[TCP] 127.0.0.1:64462 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:19:46.377896900+08:00" level=info msg="[TCP] 127.0.0.1:64463 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:19:46.956581300+08:00" level=info msg="[TCP] 127.0.0.1:64466 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:19:47.916405300+08:00" level=info msg="[TCP] 127.0.0.1:64469 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:19:50.067832000+08:00" level=info msg="[TCP] 127.0.0.1:64471 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:19:54.014590500+08:00" level=info msg="[TCP] 127.0.0.1:64477 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:20:06.138155700+08:00" level=info msg="[TCP] 127.0.0.1:64489 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:20:40.605306500+08:00" level=info msg="[TCP] 127.0.0.1:64514 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:20:45.197417900+08:00" level=info msg="[TCP] 127.0.0.1:64520 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:20:47.913819700+08:00" level=info msg="[TCP] 127.0.0.1:64524 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:20:51.743987200+08:00" level=info msg="[TCP] 127.0.0.1:64528 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:21:14.762867400+08:00" level=info msg="[TCP] 127.0.0.1:64545 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:21:14.974962000+08:00" level=info msg="[TCP] 127.0.0.1:64546 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:21:16.455493200+08:00" level=info msg="[TCP] 127.0.0.1:64550 --> westus-0.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-06-10T22:21:31.910541700+08:00" level=info msg="[TCP] 127.0.0.1:64562 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:21:32.113027000+08:00" level=info msg="[TCP] 127.0.0.1:64563 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:21:40.622390600+08:00" level=info msg="[TCP] 127.0.0.1:64573 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:21:48.056811900+08:00" level=info msg="[TCP] 127.0.0.1:64580 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:21:51.101535300+08:00" level=info msg="[TCP] 127.0.0.1:64583 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:21:52.515134900+08:00" level=info msg="[TCP] 127.0.0.1:64587 --> westus-0.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-06-10T22:21:52.641294700+08:00" level=info msg="[TCP] 127.0.0.1:64589 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:21:55.090497200+08:00" level=info msg="[TCP] 127.0.0.1:64592 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:22:20.960409800+08:00" level=info msg="[TCP] 127.0.0.1:64623 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:22:40.603154800+08:00" level=info msg="[TCP] 127.0.0.1:64645 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:07.522200500+08:00" level=info msg="[TCP] 127.0.0.1:64667 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:09.056248800+08:00" level=info msg="[TCP] 127.0.0.1:64669 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:33.762029400+08:00" level=info msg="[TCP] 127.0.0.1:64688 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:33.808377800+08:00" level=info msg="[TCP] 127.0.0.1:64690 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:34.306588900+08:00" level=info msg="[TCP] 127.0.0.1:64693 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:23:35.905133500+08:00" level=info msg="[TCP] 127.0.0.1:64695 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:37.562331000+08:00" level=info msg="[TCP] 127.0.0.1:64699 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:37.708385400+08:00" level=info msg="[TCP] 127.0.0.1:64701 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:37.716857100+08:00" level=info msg="[TCP] 127.0.0.1:64702 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:39.152379700+08:00" level=info msg="[TCP] 127.0.0.1:64705 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:39.226440300+08:00" level=info msg="[TCP] 127.0.0.1:64706 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:39.994696400+08:00" level=info msg="[TCP] 127.0.0.1:64710 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:40.598841800+08:00" level=info msg="[TCP] 127.0.0.1:64713 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:41.009509000+08:00" level=info msg="[TCP] 127.0.0.1:64715 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:42.006286300+08:00" level=info msg="[TCP] 127.0.0.1:64717 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:42.666427500+08:00" level=info msg="[TCP] 127.0.0.1:64719 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:43.041122600+08:00" level=info msg="[TCP] 127.0.0.1:64722 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:43.846155800+08:00" level=info msg="[TCP] 127.0.0.1:64725 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:44.161717600+08:00" level=info msg="[TCP] 127.0.0.1:64727 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:45.355288200+08:00" level=info msg="[TCP] 127.0.0.1:64729 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:47.891310200+08:00" level=info msg="[TCP] 127.0.0.1:64733 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:48.184490200+08:00" level=info msg="[TCP] 127.0.0.1:64735 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:49.863919200+08:00" level=info msg="[TCP] 127.0.0.1:64739 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:51.843683300+08:00" level=info msg="[TCP] 127.0.0.1:64742 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:52.122904600+08:00" level=info msg="[TCP] 127.0.0.1:64744 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:54.150571200+08:00" level=info msg="[TCP] 127.0.0.1:64747 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:23:54.662043000+08:00" level=info msg="[TCP] 127.0.0.1:64749 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:23:56.732205500+08:00" level=info msg="[TCP] 127.0.0.1:64753 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:01.513672100+08:00" level=info msg="[TCP] 127.0.0.1:64760 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:01.553444300+08:00" level=info msg="[TCP] 127.0.0.1:64762 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:01.558570400+08:00" level=info msg="[TCP] 127.0.0.1:64763 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:01.558570400+08:00" level=info msg="[TCP] 127.0.0.1:64766 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:01.605889200+08:00" level=info msg="[TCP] 127.0.0.1:64768 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:02.553832500+08:00" level=info msg="[TCP] 127.0.0.1:64770 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:02.613098600+08:00" level=info msg="[TCP] 127.0.0.1:64772 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:03.657807400+08:00" level=info msg="[TCP] 127.0.0.1:64774 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:27.398285800+08:00" level=info msg="[TCP] 127.0.0.1:64792 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:24:36.378253600+08:00" level=info msg="[TCP] 127.0.0.1:64913 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:24:36.383190700+08:00" level=info msg="[TCP] 127.0.0.1:64911 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:24:39.475131600+08:00" level=info msg="[TCP] 127.0.0.1:64921 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:40.458043400+08:00" level=info msg="[TCP] 127.0.0.1:64924 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:40.606427700+08:00" level=info msg="[TCP] 127.0.0.1:64926 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:41.558340200+08:00" level=info msg="[TCP] 127.0.0.1:64931 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:41.600520000+08:00" level=info msg="[TCP] 127.0.0.1:64933 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:41.943093500+08:00" level=info msg="[TCP] 127.0.0.1:64935 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:42.670947600+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-10T22:24:42.670947600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-10T22:24:42.674014900+08:00" level=error msg="🇮🇳印度-下载专享[0.5x] ipv6 only failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-10T22:24:42.674014900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-10T22:24:42.681598300+08:00" level=error msg="🇰🇷韩国02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-10T22:24:42.681598300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-10T22:24:43.124200400+08:00" level=info msg="[TCP] 127.0.0.1:64937 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:44.052367400+08:00" level=info msg="[TCP] 127.0.0.1:64941 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:44.130025000+08:00" level=info msg="[TCP] 127.0.0.1:64943 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:46.905793100+08:00" level=info msg="[TCP] 127.0.0.1:64946 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:47.931632300+08:00" level=info msg="[TCP] 127.0.0.1:64949 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:24:54.215282200+08:00" level=info msg="[TCP] 127.0.0.1:64955 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:24:54.384981600+08:00" level=info msg="[TCP] 127.0.0.1:64957 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:25:40.041147300+08:00" level=info msg="[TCP] 127.0.0.1:64992 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:25:40.596553400+08:00" level=info msg="[TCP] 127.0.0.1:64994 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:25:40.912548400+08:00" level=info msg="[TCP] 127.0.0.1:64996 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:26:14.759863900+08:00" level=info msg="[TCP] 127.0.0.1:65025 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:26:14.761827400+08:00" level=info msg="[TCP] 127.0.0.1:65024 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:26:40.594428600+08:00" level=info msg="[TCP] 127.0.0.1:65046 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:26:44.896728000+08:00" level=info msg="[TCP] 127.0.0.1:65052 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:26:44.900120300+08:00" level=info msg="[TCP] 127.0.0.1:65051 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:26:49.636280200+08:00" level=info msg="[TCP] 127.0.0.1:65059 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:26:51.364102100+08:00" level=info msg="[TCP] 127.0.0.1:65062 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:27:01.352618700+08:00" level=info msg="[TCP] 127.0.0.1:65072 --> westus-0.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-06-10T22:27:02.034129200+08:00" level=info msg="[TCP] 127.0.0.1:65074 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:27:02.039205400+08:00" level=info msg="[TCP] 127.0.0.1:65076 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:27:03.636053700+08:00" level=info msg="[TCP] 127.0.0.1:65080 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:27:06.984660400+08:00" level=info msg="[TCP] 127.0.0.1:65084 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:27:06.995360100+08:00" level=info msg="[TCP] 127.0.0.1:65085 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:27:41.171236800+08:00" level=info msg="[TCP] 127.0.0.1:65129 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:37.558748100+08:00" level=info msg="[TCP] 127.0.0.1:65173 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:37.656051700+08:00" level=info msg="[TCP] 127.0.0.1:65176 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:37.658409100+08:00" level=info msg="[TCP] 127.0.0.1:65175 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:39.124021300+08:00" level=info msg="[TCP] 127.0.0.1:65180 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:39.126699900+08:00" level=info msg="[TCP] 127.0.0.1:65179 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:40.353903000+08:00" level=info msg="[TCP] 127.0.0.1:65184 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:40.596881000+08:00" level=info msg="[TCP] 127.0.0.1:65187 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:44.261743400+08:00" level=info msg="[TCP] 127.0.0.1:65191 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:48.197748200+08:00" level=info msg="[TCP] 127.0.0.1:65203 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:48.240410700+08:00" level=info msg="[TCP] 127.0.0.1:65205 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:28:49.260466200+08:00" level=info msg="[TCP] 127.0.0.1:65208 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:29:40.599013400+08:00" level=info msg="[TCP] 127.0.0.1:65250 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:29:47.433975300+08:00" level=info msg="[TCP] 127.0.0.1:65257 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:29:48.234983400+08:00" level=info msg="[TCP] 127.0.0.1:65260 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:29:50.659815300+08:00" level=info msg="[TCP] 127.0.0.1:65264 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:30:33.007481600+08:00" level=info msg="[TCP] 127.0.0.1:65314 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:30:40.604389900+08:00" level=info msg="[TCP] 127.0.0.1:65320 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:30:41.771018200+08:00" level=info msg="[TCP] 127.0.0.1:65323 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:30:51.275917600+08:00" level=info msg="[TCP] 127.0.0.1:65332 --> array615.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:30:51.507609900+08:00" level=info msg="[TCP] 127.0.0.1:65334 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:30:52.365128200+08:00" level=info msg="[TCP] 127.0.0.1:65336 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:30:54.572371400+08:00" level=info msg="[TCP] 127.0.0.1:65340 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:30:54.879865800+08:00" level=info msg="[TCP] 127.0.0.1:65342 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:30:55.909922900+08:00" level=info msg="[TCP] 127.0.0.1:65344 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:30:57.959411400+08:00" level=info msg="[TCP] 127.0.0.1:65348 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:31:14.853532300+08:00" level=info msg="[TCP] 127.0.0.1:65366 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:31:14.855378200+08:00" level=info msg="[TCP] 127.0.0.1:65365 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:31:14.855378200+08:00" level=info msg="[TCP] 127.0.0.1:65364 --> g.ceipmsn.com:80 using GLOBAL"
time="2025-06-10T22:31:15.611314100+08:00" level=info msg="[TCP] 127.0.0.1:65371 --> go.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:31:16.232225600+08:00" level=info msg="[TCP] 127.0.0.1:65373 --> bingwallpaper.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:31:16.895349200+08:00" level=info msg="[TCP] 127.0.0.1:65375 --> g.ceipmsn.com:80 using GLOBAL"
time="2025-06-10T22:31:18.776885300+08:00" level=info msg="[TCP] 127.0.0.1:65379 --> bingwallpaper.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:31:19.338621200+08:00" level=info msg="[TCP] 127.0.0.1:65381 --> g.ceipmsn.com:80 using GLOBAL"
time="2025-06-10T22:31:20.296664400+08:00" level=info msg="[TCP] 127.0.0.1:65383 --> bingwallpaper.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:31:23.555457700+08:00" level=info msg="[TCP] 127.0.0.1:65388 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:24.548574200+08:00" level=info msg="[TCP] 127.0.0.1:65391 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:25.143290800+08:00" level=info msg="[TCP] 127.0.0.1:65393 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:25.616270700+08:00" level=info msg="[TCP] 127.0.0.1:65395 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:29.200954700+08:00" level=info msg="[TCP] 127.0.0.1:65399 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:29.243185400+08:00" level=info msg="[TCP] 127.0.0.1:65401 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:30.225075500+08:00" level=info msg="[TCP] 127.0.0.1:65404 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:30.433165100+08:00" level=info msg="[TCP] 127.0.0.1:65407 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:31.391740100+08:00" level=info msg="[TCP] 127.0.0.1:65409 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:32.058633400+08:00" level=info msg="[TCP] 127.0.0.1:65411 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:32.987688500+08:00" level=info msg="[TCP] 127.0.0.1:65414 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:33.843477800+08:00" level=info msg="[TCP] 127.0.0.1:65417 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:33.994006200+08:00" level=info msg="[TCP] 127.0.0.1:65419 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:35.880648800+08:00" level=info msg="[TCP] 127.0.0.1:65422 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:38.155547300+08:00" level=info msg="[TCP] 127.0.0.1:65425 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:40.140581500+08:00" level=info msg="[TCP] 127.0.0.1:65429 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:40.606456000+08:00" level=info msg="[TCP] 127.0.0.1:65431 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:44.866001200+08:00" level=info msg="[TCP] 127.0.0.1:65436 --> westus-0.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-06-10T22:31:47.061258300+08:00" level=info msg="[TCP] 127.0.0.1:65439 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:49.120966400+08:00" level=info msg="[TCP] 127.0.0.1:65443 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:52.024403800+08:00" level=info msg="[TCP] 127.0.0.1:65447 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:52.071832300+08:00" level=info msg="[TCP] 127.0.0.1:65449 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:52.072343800+08:00" level=info msg="[TCP] 127.0.0.1:65450 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:52.082396400+08:00" level=info msg="[TCP] 127.0.0.1:65453 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:52.124887900+08:00" level=info msg="[TCP] 127.0.0.1:65455 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:53.078384100+08:00" level=info msg="[TCP] 127.0.0.1:65457 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:54.051064300+08:00" level=info msg="[TCP] 127.0.0.1:65460 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:31:54.258155100+08:00" level=info msg="[TCP] 127.0.0.1:65462 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:32:07.913909100+08:00" level=info msg="[TCP] 127.0.0.1:65474 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:32:08.906626400+08:00" level=info msg="[TCP] 127.0.0.1:65476 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:32:09.970405500+08:00" level=info msg="[TCP] 127.0.0.1:65483 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:32:10.438790900+08:00" level=info msg="[TCP] 127.0.0.1:65486 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:32:16.968763100+08:00" level=info msg="[TCP] 127.0.0.1:65492 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:32:17.983715600+08:00" level=info msg="[TCP] 127.0.0.1:65494 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:32:19.347797800+08:00" level=info msg="[TCP] 127.0.0.1:65498 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:32:21.088086900+08:00" level=info msg="[TCP] 127.0.0.1:65500 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:32:24.348115200+08:00" level=info msg="[TCP] 127.0.0.1:65505 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:32:40.603441300+08:00" level=info msg="[TCP] 127.0.0.1:65518 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:32:52.578810600+08:00" level=info msg="[TCP] 127.0.0.1:65527 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:32:53.075364800+08:00" level=info msg="[TCP] 127.0.0.1:65530 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:32:54.470866100+08:00" level=info msg="[TCP] 127.0.0.1:65532 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:32:54.684907900+08:00" level=info msg="[TCP] 127.0.0.1:49152 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:33:02.682368300+08:00" level=info msg="[TCP] 127.0.0.1:49159 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:37.561187500+08:00" level=info msg="[TCP] 127.0.0.1:49184 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:37.660463300+08:00" level=info msg="[TCP] 127.0.0.1:49186 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:37.662369600+08:00" level=info msg="[TCP] 127.0.0.1:49187 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:37.926966800+08:00" level=info msg="[TCP] 127.0.0.1:49191 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:40.598194200+08:00" level=info msg="[TCP] 127.0.0.1:49194 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:40.823278000+08:00" level=info msg="[TCP] 127.0.0.1:49197 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-10T22:33:42.202379000+08:00" level=info msg="[TCP] 127.0.0.1:49199 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:42.978719600+08:00" level=info msg="[TCP] 127.0.0.1:49201 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:33:44.491084000+08:00" level=info msg="[TCP] 127.0.0.1:49206 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:48.197487700+08:00" level=info msg="[TCP] 127.0.0.1:49210 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:51.711530400+08:00" level=info msg="[TCP] 127.0.0.1:49214 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:53.415339300+08:00" level=info msg="[TCP] 127.0.0.1:49218 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:54.107130600+08:00" level=info msg="[TCP] 127.0.0.1:49220 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:54.733702500+08:00" level=info msg="[TCP] 127.0.0.1:49222 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:54.762844500+08:00" level=info msg="[TCP] 127.0.0.1:49224 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:54.772411300+08:00" level=info msg="[TCP] 127.0.0.1:49226 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:33:55.720565400+08:00" level=info msg="[TCP] 127.0.0.1:49229 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:59.171821500+08:00" level=info msg="[TCP] 127.0.0.1:49234 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:33:59.171821500+08:00" level=info msg="[TCP] 127.0.0.1:49235 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:34:41.172015100+08:00" level=info msg="[TCP] 127.0.0.1:49298 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:34:57.330496600+08:00" level=info msg="[TCP] 127.0.0.1:49314 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:35:01.294345800+08:00" level=info msg="[TCP] 127.0.0.1:49319 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:35:02.110517200+08:00" level=info msg="[TCP] 127.0.0.1:49323 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:35:02.149735500+08:00" level=info msg="[TCP] 127.0.0.1:49322 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:35:08.343202500+08:00" level=info msg="[TCP] 127.0.0.1:49331 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:35:15.137799300+08:00" level=info msg="[TCP] 127.0.0.1:49339 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:35:17.853249500+08:00" level=info msg="[TCP] 127.0.0.1:49342 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:35:27.195011000+08:00" level=info msg="[TCP] 127.0.0.1:49352 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:35:27.787928800+08:00" level=info msg="[TCP] 127.0.0.1:49354 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:35:40.598752700+08:00" level=info msg="[TCP] 127.0.0.1:49364 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:35:41.612886500+08:00" level=info msg="[TCP] 127.0.0.1:49367 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:35:41.962353300+08:00" level=info msg="[TCP] 127.0.0.1:49369 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:35:51.306916700+08:00" level=info msg="[TCP] 127.0.0.1:49378 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:35:53.198011900+08:00" level=info msg="[TCP] 127.0.0.1:49380 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:35:54.886150800+08:00" level=info msg="[TCP] 127.0.0.1:49384 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:36:02.143512900+08:00" level=info msg="[TCP] 127.0.0.1:49391 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:02.271425300+08:00" level=info msg="[TCP] 127.0.0.1:49393 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:03.163845700+08:00" level=info msg="[TCP] 127.0.0.1:49396 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:06.269569200+08:00" level=info msg="[TCP] 127.0.0.1:49399 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:08.387192600+08:00" level=info msg="[TCP] 127.0.0.1:49403 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:09.056625200+08:00" level=info msg="[TCP] 127.0.0.1:49406 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:09.423007700+08:00" level=info msg="[TCP] 127.0.0.1:49409 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:14.099052200+08:00" level=info msg="[TCP] 127.0.0.1:49415 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:14.865959600+08:00" level=info msg="[TCP] 127.0.0.1:49419 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:36:14.981754700+08:00" level=info msg="[TCP] 127.0.0.1:49418 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:36:16.550755600+08:00" level=info msg="[TCP] 127.0.0.1:49413 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:31.926959900+08:00" level=info msg="[TCP] 127.0.0.1:49434 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:36:32.770376200+08:00" level=info msg="[TCP] 127.0.0.1:49436 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:41.773781800+08:00" level=info msg="[TCP] 127.0.0.1:49447 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:42.366145900+08:00" level=info msg="[TCP] 127.0.0.1:49450 --> g.live.com:443 using GLOBAL"
time="2025-06-10T22:36:48.845182000+08:00" level=info msg="[TCP] 127.0.0.1:49456 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:52.174768200+08:00" level=info msg="[TCP] 127.0.0.1:49462 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:57.528863900+08:00" level=info msg="[TCP] 127.0.0.1:49469 --> oneclient.sfx.ms:443 using GLOBAL"
time="2025-06-10T22:36:59.594758200+08:00" level=info msg="[TCP] 127.0.0.1:49472 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:36:59.607821600+08:00" level=info msg="[TCP] 127.0.0.1:49476 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:37:00.610995600+08:00" level=info msg="[TCP] 127.0.0.1:49474 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:37:00.692738200+08:00" level=info msg="[TCP] 127.0.0.1:49479 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:37:04.557519600+08:00" level=info msg="[TCP] 127.0.0.1:49485 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:37:04.558024800+08:00" level=info msg="[TCP] 127.0.0.1:49484 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:37:09.102754900+08:00" level=info msg="[TCP] 127.0.0.1:49491 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:37:14.294150400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49493 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:37:41.185897400+08:00" level=info msg="[TCP] 127.0.0.1:49541 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:38:50.177855100+08:00" level=info msg="[TCP] 127.0.0.1:49672 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:38:50.203976900+08:00" level=info msg="[TCP] 127.0.0.1:49671 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:38:50.203976900+08:00" level=info msg="[TCP] 127.0.0.1:49675 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:38:50.267198600+08:00" level=info msg="[TCP] 127.0.0.1:49673 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:38:51.487061600+08:00" level=info msg="[TCP] 127.0.0.1:49674 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:38:58.583990800+08:00" level=info msg="[TCP] 127.0.0.1:49687 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:39:01.061595100+08:00" level=info msg="[TCP] 127.0.0.1:49692 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:07.402864200+08:00" level=info msg="[TCP] 127.0.0.1:49698 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:15.041957600+08:00" level=info msg="[TCP] 127.0.0.1:49706 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:39:22.323417000+08:00" level=info msg="[TCP] 127.0.0.1:49712 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:39:40.899502200+08:00" level=info msg="[TCP] 127.0.0.1:49726 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:49.881912500+08:00" level=info msg="[TCP] 127.0.0.1:49736 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:50.141701200+08:00" level=info msg="[TCP] 127.0.0.1:49734 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:51.419966900+08:00" level=info msg="[TCP] 127.0.0.1:49740 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:52.654419200+08:00" level=info msg="[TCP] 127.0.0.1:49742 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:39:52.826116500+08:00" level=info msg="[TCP] 127.0.0.1:49746 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:53.982146400+08:00" level=info msg="[TCP] 127.0.0.1:49749 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:54.944631600+08:00" level=info msg="[TCP] 127.0.0.1:49754 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:55.874963100+08:00" level=info msg="[TCP] 127.0.0.1:49744 --> io.xkw.com:443 using GLOBAL"
time="2025-06-10T22:39:56.300591700+08:00" level=info msg="[TCP] 127.0.0.1:49758 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:56.314747000+08:00" level=info msg="[TCP] 127.0.0.1:49756 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:39:57.892821800+08:00" level=info msg="[TCP] 127.0.0.1:49752 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:39:59.172976200+08:00" level=info msg="[TCP] 127.0.0.1:49762 --> io.xkw.com:443 using GLOBAL"
time="2025-06-10T22:39:59.332625700+08:00" level=info msg="[TCP] 127.0.0.1:49764 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:00.086442900+08:00" level=info msg="[TCP] 127.0.0.1:49769 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:01.114005700+08:00" level=info msg="[TCP] 127.0.0.1:49771 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:01.123219700+08:00" level=info msg="[TCP] 127.0.0.1:49767 --> io.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:01.538509600+08:00" level=info msg="[TCP] 127.0.0.1:49780 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:02.937506400+08:00" level=info msg="[TCP] 127.0.0.1:49784 --> ops.gx.nvidia.com:443 using GLOBAL"
time="2025-06-10T22:40:03.497927500+08:00" level=info msg="[TCP] 127.0.0.1:49789 --> static.zxxk.com:443 using GLOBAL"
time="2025-06-10T22:40:03.528739400+08:00" level=info msg="[TCP] 127.0.0.1:49793 --> cdn7.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:03.562095100+08:00" level=info msg="[TCP] 127.0.0.1:49801 --> hmcdn.baidu.com:443 using GLOBAL"
time="2025-06-10T22:40:03.585942400+08:00" level=info msg="[TCP] 127.0.0.1:49787 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:03.801301100+08:00" level=info msg="[TCP] 127.0.0.1:49796 --> geetest.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:03.886748900+08:00" level=info msg="[TCP] 127.0.0.1:49788 --> cdn5.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:03.886748900+08:00" level=info msg="[TCP] 127.0.0.1:49795 --> cdn6.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:03.991611000+08:00" level=info msg="[TCP] 127.0.0.1:49805 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:04.006136100+08:00" level=info msg="[TCP] 127.0.0.1:49803 --> static.zxxk.com:443 using GLOBAL"
time="2025-06-10T22:40:04.936563100+08:00" level=info msg="[TCP] 127.0.0.1:49807 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:05.598801100+08:00" level=info msg="[TCP] 127.0.0.1:49811 --> geetest.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:05.984353300+08:00" level=info msg="[TCP] 127.0.0.1:49815 --> hmcdn.baidu.com:443 using GLOBAL"
time="2025-06-10T22:40:06.090329800+08:00" level=info msg="[TCP] 127.0.0.1:49817 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:06.618832400+08:00" level=info msg="[TCP] 127.0.0.1:49813 --> io.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:07.597451900+08:00" level=info msg="[TCP] 127.0.0.1:49826 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:07.644628300+08:00" level=info msg="[TCP] 127.0.0.1:49828 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:07.648676300+08:00" level=info msg="[TCP] 127.0.0.1:49821 --> hmcdn.baidu.com:443 using GLOBAL"
time="2025-06-10T22:40:08.026758700+08:00" level=info msg="[TCP] 127.0.0.1:49832 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-10T22:40:08.048853400+08:00" level=info msg="[TCP] 127.0.0.1:49834 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:08.446079500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49797 --> zz.bdstatic.com:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:40:08.663976500+08:00" level=info msg="[TCP] 127.0.0.1:49830 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:08.816354900+08:00" level=info msg="[TCP] 127.0.0.1:49837 --> zz.bdstatic.com:443 using GLOBAL"
time="2025-06-10T22:40:09.027248000+08:00" level=info msg="[TCP] 127.0.0.1:49839 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:09.910900500+08:00" level=info msg="[TCP] 127.0.0.1:49842 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:10.103214900+08:00" level=info msg="[TCP] 127.0.0.1:49843 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:10.577449500+08:00" level=info msg="[TCP] 127.0.0.1:49846 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-10T22:40:10.970158500+08:00" level=info msg="[TCP] 127.0.0.1:49848 --> sp0.baidu.com:443 using GLOBAL"
time="2025-06-10T22:40:11.459950700+08:00" level=info msg="[TCP] 127.0.0.1:49851 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-10T22:40:11.827717800+08:00" level=info msg="[TCP] 127.0.0.1:49854 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-10T22:40:11.958981600+08:00" level=info msg="[TCP] 127.0.0.1:49856 --> p.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:12.330450600+08:00" level=info msg="[TCP] 127.0.0.1:49858 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:12.611436000+08:00" level=info msg="[TCP] 127.0.0.1:49861 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:13.625404400+08:00" level=info msg="[TCP] 127.0.0.1:49862 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:13.653018000+08:00" level=info msg="[TCP] 127.0.0.1:49866 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:15.979566700+08:00" level=info msg="[TCP] 127.0.0.1:49870 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:16.248810300+08:00" level=info msg="[TCP] 127.0.0.1:49872 --> sso.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:16.572016600+08:00" level=info msg="[TCP] 127.0.0.1:49874 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-10T22:40:20.749575900+08:00" level=info msg="[TCP] 127.0.0.1:49879 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:40:21.502327800+08:00" level=info msg="[TCP] 127.0.0.1:49882 --> hmcdn.baidu.com:443 using GLOBAL"
time="2025-06-10T22:40:21.839814200+08:00" level=info msg="[TCP] 127.0.0.1:49883 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:40:24.023185800+08:00" level=info msg="[TCP] 127.0.0.1:49887 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:40:26.916217000+08:00" level=info msg="[TCP] 127.0.0.1:49895 --> sso.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:27.307080300+08:00" level=info msg="[TCP] 127.0.0.1:49889 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:40:27.855270500+08:00" level=info msg="[TCP] 127.0.0.1:49898 --> bizapi.alipan.com:443 using GLOBAL"
time="2025-06-10T22:40:28.792295000+08:00" level=info msg="[TCP] 127.0.0.1:49901 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:40:29.436906100+08:00" level=info msg="[TCP] 127.0.0.1:49903 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:40:30.156163100+08:00" level=info msg="[TCP] 127.0.0.1:49906 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:40:30.449260000+08:00" level=info msg="[TCP] 127.0.0.1:49908 --> cdn7.xkw.com:443 using GLOBAL"
time="2025-06-10T22:40:32.608667100+08:00" level=info msg="[TCP] 127.0.0.1:49912 --> static.zxxk.com:443 using GLOBAL"
time="2025-06-10T22:40:38.510080400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49915 --> onedriveclucproddm20038.blob.core.windows.net:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:40:40.024477200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49918 --> xpaywalletcdn-prod.azureedge.net:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:40:40.671556100+08:00" level=info msg="[TCP] 127.0.0.1:49929 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:41.172931200+08:00" level=info msg="[TCP] 127.0.0.1:49927 --> xpaywalletcdn-prod.azureedge.net:443 using GLOBAL"
time="2025-06-10T22:40:43.316791500+08:00" level=info msg="[TCP] 127.0.0.1:49933 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:40:43.814457000+08:00" level=info msg="[TCP] 127.0.0.1:49935 --> sso.xkw.com:443 using GLOBAL"
time="2025-06-10T22:41:06.188220200+08:00" level=info msg="[TCP] 127.0.0.1:49957 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:41:06.240248800+08:00" level=info msg="[TCP] 127.0.0.1:49954 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:41:07.186225200+08:00" level=info msg="[TCP] 127.0.0.1:49953 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:41:08.930103200+08:00" level=info msg="[TCP] 127.0.0.1:49963 --> imzujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:41:11.132607900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49958 --> zujuan.xkw.com:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:41:12.205570600+08:00" level=info msg="[TCP] 127.0.0.1:49967 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:41:14.911888100+08:00" level=info msg="[TCP] 127.0.0.1:49983 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:41:16.228374600+08:00" level=info msg="[TCP] 127.0.0.1:49981 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:41:19.848483100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49984 --> services.bingapis.com:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:41:42.196152600+08:00" level=info msg="[TCP] 127.0.0.1:50008 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:41:42.639299300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50004 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:41:45.764190200+08:00" level=info msg="[TCP] 127.0.0.1:50012 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:42:50.243393100+08:00" level=info msg="[TCP] 127.0.0.1:50144 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:43:50.184021300+08:00" level=info msg="[TCP] 127.0.0.1:50342 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:43:50.197074100+08:00" level=info msg="[TCP] 127.0.0.1:50340 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:43:50.271680700+08:00" level=info msg="[TCP] 127.0.0.1:50341 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:43:51.409676400+08:00" level=info msg="[TCP] 127.0.0.1:50343 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:43:55.127497800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50339 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:44:21.181399400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50380 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:44:23.474787200+08:00" level=info msg="[TCP] 127.0.0.1:50388 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:44:26.220452000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50385 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T22:44:34.963188600+08:00" level=info msg="[TCP] 127.0.0.1:50398 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:44:40.596422100+08:00" level=info msg="[TCP] 127.0.0.1:50404 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:44:42.861171000+08:00" level=info msg="[TCP] 127.0.0.1:50407 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:44:46.913859200+08:00" level=info msg="[TCP] 127.0.0.1:50412 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:44:47.361493200+08:00" level=info msg="[TCP] 127.0.0.1:50414 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:44:47.394991900+08:00" level=info msg="[TCP] 127.0.0.1:50416 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:44:47.650351700+08:00" level=info msg="[TCP] 127.0.0.1:50418 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:44:48.884273500+08:00" level=info msg="[TCP] 127.0.0.1:50423 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:44:55.374633700+08:00" level=info msg="[TCP] 127.0.0.1:50429 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:45:14.636662600+08:00" level=info msg="[TCP] 127.0.0.1:50449 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:14.786985500+08:00" level=info msg="[TCP] 127.0.0.1:50451 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:18.492794100+08:00" level=info msg="[TCP] 127.0.0.1:50456 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:21.017572400+08:00" level=info msg="[TCP] 127.0.0.1:50459 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:22.028579700+08:00" level=info msg="[TCP] 127.0.0.1:50462 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:22.652424600+08:00" level=info msg="[TCP] 127.0.0.1:50465 --> g.alicdn.com:443 using GLOBAL"
time="2025-06-10T22:45:24.588335200+08:00" level=info msg="[TCP] 127.0.0.1:50469 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:25.029229400+08:00" level=info msg="[TCP] 127.0.0.1:50471 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:27.311535900+08:00" level=info msg="[TCP] 127.0.0.1:50475 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:29.987805300+08:00" level=info msg="[TCP] 127.0.0.1:50478 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:32.925570700+08:00" level=info msg="[TCP] 127.0.0.1:50482 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:36.711430600+08:00" level=info msg="[TCP] 127.0.0.1:50487 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:36.722769900+08:00" level=info msg="[TCP] 127.0.0.1:50489 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:36.754207400+08:00" level=info msg="[TCP] 127.0.0.1:50491 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:36.754207400+08:00" level=info msg="[TCP] 127.0.0.1:50492 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:36.767302100+08:00" level=info msg="[TCP] 127.0.0.1:50495 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:36.824304700+08:00" level=info msg="[TCP] 127.0.0.1:50497 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:37.680928600+08:00" level=info msg="[TCP] 127.0.0.1:50500 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:37.772496900+08:00" level=info msg="[TCP] 127.0.0.1:50502 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:39.840544000+08:00" level=info msg="[TCP] 127.0.0.1:50505 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:40.606975800+08:00" level=info msg="[TCP] 127.0.0.1:50508 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:45:55.438939700+08:00" level=info msg="[TCP] 127.0.0.1:50520 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:46:06.326602000+08:00" level=info msg="[TCP] 127.0.0.1:50530 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:07.214726500+08:00" level=info msg="[TCP] 127.0.0.1:50533 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:08.411168700+08:00" level=info msg="[TCP] 127.0.0.1:50540 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:09.406621300+08:00" level=info msg="[TCP] 127.0.0.1:50546 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:10.465540000+08:00" level=info msg="[TCP] 127.0.0.1:50549 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:11.382326100+08:00" level=info msg="[TCP] 127.0.0.1:50552 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:11.469055200+08:00" level=info msg="[TCP] 127.0.0.1:50554 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:14.673003000+08:00" level=info msg="[TCP] 127.0.0.1:50558 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:14.902304900+08:00" level=info msg="[TCP] 127.0.0.1:50560 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:46:14.907240400+08:00" level=info msg="[TCP] 127.0.0.1:50561 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:46:15.835934000+08:00" level=info msg="[TCP] 127.0.0.1:50565 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:46:18.184249800+08:00" level=info msg="[TCP] 127.0.0.1:50568 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:20.531903500+08:00" level=info msg="[TCP] 127.0.0.1:50572 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:21.533771300+08:00" level=info msg="[TCP] 127.0.0.1:50574 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:22.411326900+08:00" level=info msg="[TCP] 127.0.0.1:50577 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:22.602944700+08:00" level=info msg="[TCP] 127.0.0.1:50579 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:46:22.606262200+08:00" level=info msg="[TCP] 127.0.0.1:50581 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:46:31.505596000+08:00" level=info msg="[TCP] 127.0.0.1:50592 --> g.alicdn.com:443 using GLOBAL"
time="2025-06-10T22:46:31.505596000+08:00" level=info msg="[TCP] 127.0.0.1:50590 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:46:32.489443800+08:00" level=info msg="[TCP] 127.0.0.1:50595 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:35.799415900+08:00" level=info msg="[TCP] 127.0.0.1:50599 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:37.973764600+08:00" level=info msg="[TCP] 127.0.0.1:50603 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:40.597530900+08:00" level=info msg="[TCP] 127.0.0.1:50606 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:40.938328100+08:00" level=info msg="[TCP] 127.0.0.1:50608 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:41.536766200+08:00" level=info msg="[TCP] 127.0.0.1:50611 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:41.688033900+08:00" level=info msg="[TCP] 127.0.0.1:50613 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:43.899486700+08:00" level=info msg="[TCP] 127.0.0.1:50617 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:43.904694300+08:00" level=info msg="[TCP] 127.0.0.1:50619 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:45.788487000+08:00" level=info msg="[TCP] 127.0.0.1:50622 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:49.643472800+08:00" level=info msg="[TCP] 127.0.0.1:50628 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:51.868304200+08:00" level=info msg="[TCP] 127.0.0.1:50631 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:52.971358400+08:00" level=info msg="[TCP] 127.0.0.1:50634 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:46:53.786484100+08:00" level=info msg="[TCP] 127.0.0.1:50637 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:56.145481700+08:00" level=info msg="[TCP] 127.0.0.1:50640 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:56.160812800+08:00" level=info msg="[TCP] 127.0.0.1:50643 --> westus-0.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-06-10T22:46:57.211968600+08:00" level=info msg="[TCP] 127.0.0.1:50645 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:57.219647100+08:00" level=info msg="[TCP] 127.0.0.1:50647 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:46:58.177784500+08:00" level=info msg="[TCP] 127.0.0.1:50652 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:47:02.160995500+08:00" level=info msg="[TCP] 127.0.0.1:50657 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:47:02.163677100+08:00" level=info msg="[TCP] 127.0.0.1:50658 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:47:08.472978000+08:00" level=info msg="[TCP] 127.0.0.1:50666 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:47:09.766388200+08:00" level=info msg="[TCP] 127.0.0.1:50668 --> download.clashverge.dev:443 using GLOBAL"
time="2025-06-10T22:47:09.902111100+08:00" level=info msg="[TCP] 127.0.0.1:50670 --> cdn5.xkw.com:443 using GLOBAL"
time="2025-06-10T22:47:09.971557000+08:00" level=info msg="[TCP] 127.0.0.1:50672 --> io.xkw.com:443 using GLOBAL"
time="2025-06-10T22:47:41.174199700+08:00" level=info msg="[TCP] 127.0.0.1:50730 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:48:50.214132700+08:00" level=info msg="[TCP] 127.0.0.1:50867 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:48:50.214132700+08:00" level=info msg="[TCP] 127.0.0.1:50863 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:48:50.216637800+08:00" level=info msg="[TCP] 127.0.0.1:50866 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:48:50.218004900+08:00" level=info msg="[TCP] 127.0.0.1:50865 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:48:50.218004900+08:00" level=info msg="[TCP] 127.0.0.1:50864 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:49:01.119653800+08:00" level=info msg="[TCP] 127.0.0.1:50882 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:49:05.061488200+08:00" level=info msg="[TCP] 127.0.0.1:50886 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:49:06.977429300+08:00" level=info msg="[TCP] 127.0.0.1:50890 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:49:28.776484100+08:00" level=info msg="[TCP] 127.0.0.1:50906 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:49:28.777549600+08:00" level=info msg="[TCP] 127.0.0.1:50909 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:49:28.777549600+08:00" level=info msg="[TCP] 127.0.0.1:50907 --> g.alicdn.com:443 using GLOBAL"
time="2025-06-10T22:49:28.783732700+08:00" level=info msg="[TCP] 127.0.0.1:50910 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:49:29.142279800+08:00" level=info msg="[TCP] 127.0.0.1:50914 --> g.alicdn.com:443 using GLOBAL"
time="2025-06-10T22:49:41.616218300+08:00" level=info msg="[TCP] 127.0.0.1:50924 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:49:42.058085200+08:00" level=info msg="[TCP] 127.0.0.1:50926 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:49:43.612708000+08:00" level=info msg="[TCP] 127.0.0.1:50930 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:49:55.675666700+08:00" level=info msg="[TCP] 127.0.0.1:50942 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:50:05.070277000+08:00" level=info msg="[TCP] 127.0.0.1:50952 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:50:09.101076200+08:00" level=info msg="[TCP] 127.0.0.1:50956 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:50:19.863742400+08:00" level=info msg="[TCP] 127.0.0.1:50970 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:50:19.863742400+08:00" level=info msg="[TCP] 127.0.0.1:50967 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:50:19.865977600+08:00" level=info msg="[TCP] 127.0.0.1:50968 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:50:20.410565000+08:00" level=info msg="[TCP] 127.0.0.1:50973 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:50:40.595683000+08:00" level=info msg="[TCP] 127.0.0.1:50990 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:50:41.601410100+08:00" level=info msg="[TCP] 127.0.0.1:50992 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:50:55.731909400+08:00" level=info msg="[TCP] 127.0.0.1:51004 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:51:05.077403800+08:00" level=info msg="[TCP] 127.0.0.1:51013 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:51:08.406279700+08:00" level=info msg="[TCP] 127.0.0.1:51017 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:51:14.913839400+08:00" level=info msg="[TCP] 127.0.0.1:51023 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:51:14.955742200+08:00" level=info msg="[TCP] 127.0.0.1:51024 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-10T22:51:16.441242000+08:00" level=info msg="[TCP] 127.0.0.1:51028 --> www.bing.com:443 using GLOBAL"
time="2025-06-10T22:51:25.163583800+08:00" level=info msg="[TCP] 127.0.0.1:51035 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:51:28.337140300+08:00" level=info msg="[TCP] 127.0.0.1:51043 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:51:30.748463400+08:00" level=info msg="[TCP] 127.0.0.1:51040 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:51:40.607392600+08:00" level=info msg="[TCP] 127.0.0.1:51053 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:51:42.073635500+08:00" level=info msg="[TCP] 127.0.0.1:51056 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:52:06.612857900+08:00" level=info msg="[TCP] 127.0.0.1:51077 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:52:06.998793400+08:00" level=info msg="[TCP] 127.0.0.1:51079 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:52:07.719730700+08:00" level=info msg="[TCP] 127.0.0.1:51075 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:52:40.603018800+08:00" level=info msg="[TCP] 127.0.0.1:51103 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:52:53.428302000+08:00" level=info msg="[TCP] 127.0.0.1:51114 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:52:55.572002800+08:00" level=info msg="[TCP] 127.0.0.1:51117 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:52:58.893392100+08:00" level=info msg="[TCP] 127.0.0.1:51123 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:01.790623300+08:00" level=info msg="[TCP] 127.0.0.1:51121 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:11.944880900+08:00" level=info msg="[TCP] 127.0.0.1:51132 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:12.605234000+08:00" level=info msg="[TCP] 127.0.0.1:51138 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:53:12.733765000+08:00" level=info msg="[TCP] 127.0.0.1:51137 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:53:12.938296000+08:00" level=info msg="[TCP] 127.0.0.1:51141 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:53:26.463351200+08:00" level=info msg="[TCP] 127.0.0.1:51152 --> westus-0.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-06-10T22:53:27.182053300+08:00" level=info msg="[TCP] 127.0.0.1:51155 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:27.252069800+08:00" level=info msg="[TCP] 127.0.0.1:51161 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:28.225848400+08:00" level=info msg="[TCP] 127.0.0.1:51158 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:28.260700400+08:00" level=info msg="[TCP] 127.0.0.1:51154 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:28.263404200+08:00" level=info msg="[TCP] 127.0.0.1:51163 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:31.202974000+08:00" level=info msg="[TCP] 127.0.0.1:51167 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:37.662892500+08:00" level=info msg="[TCP] 127.0.0.1:51176 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:37.834666700+08:00" level=info msg="[TCP] 127.0.0.1:51175 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:40.600186900+08:00" level=info msg="[TCP] 127.0.0.1:51181 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:40.622823100+08:00" level=info msg="[TCP] 127.0.0.1:51173 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:42.640753100+08:00" level=info msg="[TCP] 127.0.0.1:51184 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:46.749792500+08:00" level=info msg="[TCP] 127.0.0.1:51191 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:46.913755100+08:00" level=info msg="[TCP] 127.0.0.1:51189 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:49.200229500+08:00" level=info msg="[TCP] 127.0.0.1:51199 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:50.194077500+08:00" level=info msg="[TCP] 127.0.0.1:51203 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:51.326531700+08:00" level=info msg="[TCP] 127.0.0.1:51206 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:52.315159800+08:00" level=info msg="[TCP] 127.0.0.1:51209 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:53.142292100+08:00" level=info msg="[TCP] 127.0.0.1:51211 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:55.755010800+08:00" level=info msg="[TCP] 127.0.0.1:51215 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:53:58.512988800+08:00" level=info msg="[TCP] 127.0.0.1:51219 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:01.644699000+08:00" level=info msg="[TCP] 127.0.0.1:51224 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:02.775031500+08:00" level=info msg="[TCP] 127.0.0.1:51226 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:03.679413400+08:00" level=info msg="[TCP] 127.0.0.1:51230 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:05.763736200+08:00" level=info msg="[TCP] 127.0.0.1:51233 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:11.396762200+08:00" level=info msg="[TCP] 127.0.0.1:51238 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:18.617086800+08:00" level=info msg="[TCP] 127.0.0.1:51245 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:20.586013900+08:00" level=info msg="[TCP] 127.0.0.1:51251 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:54:20.594958700+08:00" level=info msg="[TCP] 127.0.0.1:51249 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:54:20.621775100+08:00" level=info msg="[TCP] 127.0.0.1:51252 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:54:20.812528400+08:00" level=info msg="[TCP] 127.0.0.1:51255 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:54:27.438850100+08:00" level=info msg="[TCP] 127.0.0.1:51262 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:54:31.357431600+08:00" level=info msg="[TCP] 127.0.0.1:51267 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:31.410478800+08:00" level=info msg="[TCP] 127.0.0.1:51269 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:31.414424400+08:00" level=info msg="[TCP] 127.0.0.1:51271 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:34.814026100+08:00" level=info msg="[TCP] 127.0.0.1:51274 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:35.528077200+08:00" level=info msg="[TCP] 127.0.0.1:51279 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:36.036570700+08:00" level=info msg="[TCP] 127.0.0.1:51277 --> array617.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:54:36.333894000+08:00" level=info msg="[TCP] 127.0.0.1:51283 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:36.381774500+08:00" level=info msg="[TCP] 127.0.0.1:51282 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:38.110653900+08:00" level=info msg="[TCP] 127.0.0.1:51287 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:54:40.606210500+08:00" level=info msg="[TCP] 127.0.0.1:51291 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:44.825420500+08:00" level=info msg="[TCP] 127.0.0.1:51299 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:54:45.500999500+08:00" level=info msg="[TCP] 127.0.0.1:51297 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:54:45.658964200+08:00" level=info msg="[TCP] 127.0.0.1:51295 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-10T22:55:12.940662700+08:00" level=info msg="[TCP] 127.0.0.1:51320 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:55:15.635327000+08:00" level=info msg="[TCP] 127.0.0.1:51326 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:55:22.086049700+08:00" level=info msg="[TCP] 127.0.0.1:51331 --> www.google.com:443 using GLOBAL"
time="2025-06-10T22:55:24.190918300+08:00" level=info msg="[TCP] 127.0.0.1:51335 --> t0.gstatic.com:443 using GLOBAL"
time="2025-06-10T22:55:28.224310200+08:00" level=info msg="[TCP] 127.0.0.1:51351 --> cdn7.xkw.com:443 using GLOBAL"
time="2025-06-10T22:55:28.225700800+08:00" level=info msg="[TCP] 127.0.0.1:51350 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:55:28.228899300+08:00" level=info msg="[TCP] 127.0.0.1:51357 --> cdn5.xkw.com:443 using GLOBAL"
time="2025-06-10T22:55:28.243012500+08:00" level=info msg="[TCP] 127.0.0.1:51349 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-06-10T22:55:28.447043400+08:00" level=info msg="[TCP] 127.0.0.1:51359 --> cdn6.xkw.com:443 using GLOBAL"
time="2025-06-10T22:55:28.487170600+08:00" level=info msg="[TCP] 127.0.0.1:51362 --> x1.c.lencr.org:80 using GLOBAL"
time="2025-06-10T22:55:28.523775800+08:00" level=info msg="[TCP] 127.0.0.1:51342 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-10T22:55:28.557183300+08:00" level=info msg="[TCP] 127.0.0.1:51341 --> t0.gstatic.com:443 using GLOBAL"
time="2025-06-10T22:55:28.652078200+08:00" level=info msg="[TCP] 127.0.0.1:51345 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:55:28.662558400+08:00" level=info msg="[TCP] 127.0.0.1:51347 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:55:28.826233100+08:00" level=info msg="[TCP] 127.0.0.1:51362 --> c.pki.goog:80 using GLOBAL"
time="2025-06-10T22:55:29.191098600+08:00" level=info msg="[TCP] 127.0.0.1:51365 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:55:29.333184000+08:00" level=info msg="[TCP] 127.0.0.1:51367 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-10T22:55:29.936150000+08:00" level=info msg="[TCP] 127.0.0.1:51369 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-10T22:55:30.874438900+08:00" level=info msg="[TCP] 127.0.0.1:51371 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-10T22:55:31.244045700+08:00" level=info msg="[TCP] 127.0.0.1:51353 --> static.zxxk.com:443 using GLOBAL"
time="2025-06-10T22:55:31.535768400+08:00" level=info msg="[TCP] 127.0.0.1:51374 --> static.zxxk.com:443 using GLOBAL"
time="2025-06-10T22:55:40.604745300+08:00" level=info msg="[TCP] 127.0.0.1:51396 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:56:40.649658400+08:00" level=info msg="[TCP] 127.0.0.1:51471 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:23.290538600+08:00" level=info msg="[TCP] 127.0.0.1:51503 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:25.333639000+08:00" level=info msg="[TCP] 127.0.0.1:51506 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:40.604623300+08:00" level=info msg="[TCP] 127.0.0.1:51521 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:40.710485200+08:00" level=info msg="[TCP] 127.0.0.1:51523 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:40.718282300+08:00" level=info msg="[TCP] 127.0.0.1:51524 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:40.916220000+08:00" level=info msg="[TCP] 127.0.0.1:51527 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:40.920061100+08:00" level=info msg="[TCP] 127.0.0.1:51528 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:41.866435400+08:00" level=info msg="[TCP] 127.0.0.1:51532 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:43.605319400+08:00" level=info msg="[TCP] 127.0.0.1:51535 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:44.543822300+08:00" level=info msg="[TCP] 127.0.0.1:51538 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:44.644056900+08:00" level=info msg="[TCP] 127.0.0.1:51540 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:48.278518100+08:00" level=info msg="[TCP] 127.0.0.1:51546 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:48.282288000+08:00" level=info msg="[TCP] 127.0.0.1:51545 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:51.185048600+08:00" level=info msg="[TCP] 127.0.0.1:51551 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:52.157464800+08:00" level=info msg="[TCP] 127.0.0.1:51553 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:54.224527200+08:00" level=info msg="[TCP] 127.0.0.1:51557 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:54.333155300+08:00" level=info msg="[TCP] 127.0.0.1:51559 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:55.744987200+08:00" level=info msg="[TCP] 127.0.0.1:51562 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:56.207686900+08:00" level=info msg="[TCP] 127.0.0.1:51568 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:56.748746200+08:00" level=info msg="[TCP] 127.0.0.1:51563 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:57.727331800+08:00" level=info msg="[TCP] 127.0.0.1:51570 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:58.744565500+08:00" level=info msg="[TCP] 127.0.0.1:51573 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:58.859158700+08:00" level=info msg="[TCP] 127.0.0.1:51575 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:57:59.880567900+08:00" level=info msg="[TCP] 127.0.0.1:51578 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:00.115711600+08:00" level=info msg="[TCP] 127.0.0.1:51580 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:00.117646100+08:00" level=info msg="[TCP] 127.0.0.1:51581 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:00.862885100+08:00" level=info msg="[TCP] 127.0.0.1:51585 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:03.189971200+08:00" level=info msg="[TCP] 127.0.0.1:51589 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:04.953553500+08:00" level=info msg="[TCP] 127.0.0.1:51592 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:07.556465300+08:00" level=info msg="[TCP] 127.0.0.1:51596 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:09.613930100+08:00" level=info msg="[TCP] 127.0.0.1:51600 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:11.577725500+08:00" level=info msg="[TCP] 127.0.0.1:51603 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:15.588816900+08:00" level=info msg="[TCP] 127.0.0.1:51608 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:25.785618900+08:00" level=info msg="[TCP] 127.0.0.1:51616 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:25.845492800+08:00" level=info msg="[TCP] 127.0.0.1:51618 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:25.852240800+08:00" level=info msg="[TCP] 127.0.0.1:51620 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:29.656173700+08:00" level=info msg="[TCP] 127.0.0.1:51625 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:29.900837400+08:00" level=info msg="[TCP] 127.0.0.1:51627 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:30.800392300+08:00" level=info msg="[TCP] 127.0.0.1:51630 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:30.800392300+08:00" level=info msg="[TCP] 127.0.0.1:51631 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:37.569006700+08:00" level=info msg="[TCP] 127.0.0.1:51639 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:37.665778600+08:00" level=info msg="[TCP] 127.0.0.1:51642 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:37.667668700+08:00" level=info msg="[TCP] 127.0.0.1:51641 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:39.134824500+08:00" level=info msg="[TCP] 127.0.0.1:51646 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:39.137445500+08:00" level=info msg="[TCP] 127.0.0.1:51647 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:40.611447800+08:00" level=info msg="[TCP] 127.0.0.1:51651 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:49.350176900+08:00" level=info msg="[TCP] 127.0.0.1:51665 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:49.473338600+08:00" level=info msg="[TCP] 127.0.0.1:51667 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:50.194689000+08:00" level=info msg="[TCP] 127.0.0.1:51669 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:50.337992700+08:00" level=info msg="[TCP] 127.0.0.1:51671 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:54.232265300+08:00" level=info msg="[TCP] 127.0.0.1:51676 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:55.196122100+08:00" level=info msg="[TCP] 127.0.0.1:51679 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:56.198901900+08:00" level=info msg="[TCP] 127.0.0.1:51681 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:57.115516500+08:00" level=info msg="[TCP] 127.0.0.1:51684 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:58:57.550393600+08:00" level=info msg="[TCP] 127.0.0.1:51686 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:00.734946500+08:00" level=info msg="[TCP] 127.0.0.1:51690 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:02.434363900+08:00" level=info msg="[TCP] 127.0.0.1:51693 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:06.152977700+08:00" level=info msg="[TCP] 127.0.0.1:51698 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:09.691669600+08:00" level=info msg="[TCP] 127.0.0.1:51702 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:13.772682800+08:00" level=info msg="[TCP] 127.0.0.1:51707 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:15.722323100+08:00" level=info msg="[TCP] 127.0.0.1:51710 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:18.098770700+08:00" level=info msg="[TCP] 127.0.0.1:51714 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:20.204632900+08:00" level=info msg="[TCP] 127.0.0.1:51717 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:23.407560700+08:00" level=info msg="[TCP] 127.0.0.1:51721 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:25.035238500+08:00" level=info msg="[TCP] 127.0.0.1:51727 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:26.645541100+08:00" level=info msg="[TCP] 127.0.0.1:51729 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:26.992651200+08:00" level=info msg="[TCP] 127.0.0.1:51731 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:28.899736200+08:00" level=info msg="[TCP] 127.0.0.1:51735 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:29.085992100+08:00" level=info msg="[TCP] 127.0.0.1:51737 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:30.100619400+08:00" level=info msg="[TCP] 127.0.0.1:51739 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:30.138084100+08:00" level=info msg="[TCP] 127.0.0.1:51742 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:34.180236900+08:00" level=info msg="[TCP] 127.0.0.1:51750 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:34.182001000+08:00" level=info msg="[TCP] 127.0.0.1:51749 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T22:59:41.182174000+08:00" level=info msg="[TCP] 127.0.0.1:51757 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:10.372303800+08:00" level=info msg="[TCP] 127.0.0.1:51798 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:29.069816600+08:00" level=info msg="[TCP] 127.0.0.1:51814 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:31.119410200+08:00" level=info msg="[TCP] 127.0.0.1:51817 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:32.955873000+08:00" level=info msg="[TCP] 127.0.0.1:51820 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:33.043367300+08:00" level=info msg="[TCP] 127.0.0.1:51822 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:35.296823500+08:00" level=info msg="[TCP] 127.0.0.1:51829 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:35.573312600+08:00" level=info msg="[TCP] 127.0.0.1:51832 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:36.967822100+08:00" level=info msg="[TCP] 127.0.0.1:51835 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:37.556171300+08:00" level=info msg="[TCP] 127.0.0.1:51837 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:37.963641700+08:00" level=info msg="[TCP] 127.0.0.1:51840 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:37.965650600+08:00" level=info msg="[TCP] 127.0.0.1:51842 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:40.600039900+08:00" level=info msg="[TCP] 127.0.0.1:51845 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:40.706211000+08:00" level=info msg="[TCP] 127.0.0.1:51847 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:42.688686900+08:00" level=info msg="[TCP] 127.0.0.1:51850 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:46.054133400+08:00" level=info msg="[TCP] 127.0.0.1:51855 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:00:48.498970700+08:00" level=info msg="[TCP] 127.0.0.1:51858 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:01:05.323107700+08:00" level=info msg="[TCP] 127.0.0.1:51873 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:01:11.975747100+08:00" level=info msg="[TCP] 127.0.0.1:51879 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:01:14.506443500+08:00" level=info msg="[TCP] 127.0.0.1:51883 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:01:21.582154000+08:00" level=info msg="[TCP] 127.0.0.1:51894 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:01:21.592190700+08:00" level=info msg="[TCP] 127.0.0.1:51896 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:01:22.538418700+08:00" level=info msg="[TCP] 127.0.0.1:51899 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:01:26.511627600+08:00" level=info msg="[TCP] 127.0.0.1:51904 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:01:26.512699100+08:00" level=info msg="[TCP] 127.0.0.1:51905 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:01:40.605708600+08:00" level=info msg="[TCP] 127.0.0.1:51917 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:01:47.327969100+08:00" level=info msg="[TCP] 127.0.0.1:51923 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:02:40.594305700+08:00" level=info msg="[TCP] 127.0.0.1:51980 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:02:41.437403800+08:00" level=info msg="[TCP] 127.0.0.1:51983 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:02:42.541064200+08:00" level=info msg="[TCP] 127.0.0.1:51986 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:02:43.744162900+08:00" level=info msg="[TCP] 127.0.0.1:51988 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:37.547608300+08:00" level=info msg="[TCP] 127.0.0.1:52030 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:37.561800200+08:00" level=info msg="[TCP] 127.0.0.1:52032 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:37.561800200+08:00" level=info msg="[TCP] 127.0.0.1:52033 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:37.669060200+08:00" level=info msg="[TCP] 127.0.0.1:52037 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:38.685580100+08:00" level=info msg="[TCP] 127.0.0.1:52036 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:39.125498400+08:00" level=info msg="[TCP] 127.0.0.1:52042 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:39.126897600+08:00" level=info msg="[TCP] 127.0.0.1:52043 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:39.126897600+08:00" level=info msg="[TCP] 127.0.0.1:52041 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:40.599539600+08:00" level=info msg="[TCP] 127.0.0.1:52048 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:40.659438900+08:00" level=info msg="[TCP] 127.0.0.1:52050 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:40.823897300+08:00" level=info msg="[TCP] 127.0.0.1:52052 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-10T23:03:42.029252300+08:00" level=info msg="[TCP] 127.0.0.1:52055 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:42.538108600+08:00" level=info msg="[TCP] 127.0.0.1:52057 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:43.601713300+08:00" level=info msg="[TCP] 127.0.0.1:52060 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:50.208834800+08:00" level=info msg="[TCP] 127.0.0.1:52066 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:52.891598500+08:00" level=info msg="[TCP] 127.0.0.1:52071 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:52.894055900+08:00" level=info msg="[TCP] 127.0.0.1:52070 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:52.947583500+08:00" level=info msg="[TCP] 127.0.0.1:52074 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:55.056079400+08:00" level=info msg="[TCP] 127.0.0.1:52078 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:57.196844900+08:00" level=info msg="[TCP] 127.0.0.1:52084 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:57.442944900+08:00" level=info msg="[TCP] 127.0.0.1:52086 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:58.671295600+08:00" level=info msg="[TCP] 127.0.0.1:52089 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:03:59.675356000+08:00" level=info msg="[TCP] 127.0.0.1:52091 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:00.501940800+08:00" level=info msg="[TCP] 127.0.0.1:52094 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:01.671890100+08:00" level=info msg="[TCP] 127.0.0.1:52098 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:03.799772400+08:00" level=info msg="[TCP] 127.0.0.1:52101 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:05.764054100+08:00" level=info msg="[TCP] 127.0.0.1:52104 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:07.524166200+08:00" level=info msg="[TCP] 127.0.0.1:52110 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:08.420377100+08:00" level=info msg="[TCP] 127.0.0.1:52112 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:10.392578000+08:00" level=info msg="[TCP] 127.0.0.1:52116 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:12.450728200+08:00" level=info msg="[TCP] 127.0.0.1:52119 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:12.604275000+08:00" level=info msg="[TCP] 127.0.0.1:52121 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:14.893235100+08:00" level=info msg="[TCP] 127.0.0.1:52125 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:16.965267100+08:00" level=info msg="[TCP] 127.0.0.1:52129 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:17.750890700+08:00" level=info msg="[TCP] 127.0.0.1:52131 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:17.756615500+08:00" level=info msg="[TCP] 127.0.0.1:52133 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:18.642472000+08:00" level=info msg="[TCP] 127.0.0.1:52137 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:20.293317000+08:00" level=info msg="[TCP] 127.0.0.1:52140 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:22.680882100+08:00" level=info msg="[TCP] 127.0.0.1:52145 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:22.688856100+08:00" level=info msg="[TCP] 127.0.0.1:52144 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:27.721054800+08:00" level=info msg="[TCP] 127.0.0.1:52151 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:04:40.610607600+08:00" level=info msg="[TCP] 127.0.0.1:52176 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:18.577468200+08:00" level=info msg="[TCP] 127.0.0.1:52207 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:20.624985800+08:00" level=info msg="[TCP] 127.0.0.1:52211 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:25.266631100+08:00" level=info msg="[TCP] 127.0.0.1:52217 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:25.358612500+08:00" level=info msg="[TCP] 127.0.0.1:52219 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:26.361050700+08:00" level=info msg="[TCP] 127.0.0.1:52222 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:26.723540700+08:00" level=info msg="[TCP] 127.0.0.1:52224 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:27.703443600+08:00" level=info msg="[TCP] 127.0.0.1:52226 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:28.709221200+08:00" level=info msg="[TCP] 127.0.0.1:52229 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:28.793849500+08:00" level=info msg="[TCP] 127.0.0.1:52231 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:30.198496900+08:00" level=info msg="[TCP] 127.0.0.1:52235 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:40.602000800+08:00" level=info msg="[TCP] 127.0.0.1:52244 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:05:59.734707500+08:00" level=info msg="[TCP] 127.0.0.1:52279 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:06:16.572470100+08:00" level=info msg="[TCP] 127.0.0.1:52294 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:06:37.626026700+08:00" level=info msg="[TCP] 127.0.0.1:52314 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:06:40.866742900+08:00" level=info msg="[TCP] 127.0.0.1:52319 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:06:57.688089500+08:00" level=info msg="[TCP] 127.0.0.1:52334 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:06:57.746919300+08:00" level=info msg="[TCP] 127.0.0.1:52336 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:06:58.823554300+08:00" level=info msg="[TCP] 127.0.0.1:52338 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:07:02.602562800+08:00" level=info msg="[TCP] 127.0.0.1:52343 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:07:02.705950500+08:00" level=info msg="[TCP] 127.0.0.1:52344 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:07:41.172797000+08:00" level=info msg="[TCP] 127.0.0.1:52372 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:08:38.215790300+08:00" level=info msg="[TCP] 127.0.0.1:52502 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:08:38.258301800+08:00" level=info msg="[TCP] 127.0.0.1:52504 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:08:39.228906200+08:00" level=info msg="[TCP] 127.0.0.1:52503 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:08:50.187396000+08:00" level=info msg="[TCP] 127.0.0.1:52519 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:08:50.189173100+08:00" level=info msg="[TCP] 127.0.0.1:52520 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:08:51.193096100+08:00" level=info msg="[TCP] 127.0.0.1:52521 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:08:51.196283900+08:00" level=info msg="[TCP] 127.0.0.1:52525 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:08:52.529608300+08:00" level=info msg="[TCP] 127.0.0.1:52531 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:09:50.220638600+08:00" level=info msg="[TCP] 127.0.0.1:52574 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:09:50.221735200+08:00" level=info msg="[TCP] 127.0.0.1:52575 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:10:50.180353200+08:00" level=info msg="[TCP] 127.0.0.1:52637 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:11:51.224672300+08:00" level=info msg="[TCP] 127.0.0.1:52681 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:12:25.506289300+08:00" level=info msg="[TCP] 127.0.0.1:52710 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:12:29.180197700+08:00" level=info msg="[TCP] 127.0.0.1:52714 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:12:30.336881000+08:00" level=info msg="[TCP] 127.0.0.1:52717 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:12:32.050217900+08:00" level=info msg="[TCP] 127.0.0.1:52720 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:12:40.610212000+08:00" level=info msg="[TCP] 127.0.0.1:52728 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:12.427710800+08:00" level=info msg="[TCP] 127.0.0.1:52752 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:12.557093200+08:00" level=info msg="[TCP] 127.0.0.1:52754 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:16.159941500+08:00" level=info msg="[TCP] 127.0.0.1:52759 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:19.123989200+08:00" level=info msg="[TCP] 127.0.0.1:52768 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:23.726321900+08:00" level=info msg="[TCP] 127.0.0.1:52775 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:24.729556900+08:00" level=info msg="[TCP] 127.0.0.1:52780 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:24.945593300+08:00" level=info msg="[TCP] 127.0.0.1:52782 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:30.161065700+08:00" level=info msg="[TCP] 127.0.0.1:52787 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:34.655674200+08:00" level=info msg="[TCP] 127.0.0.1:52794 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:37.547783900+08:00" level=info msg="[TCP] 127.0.0.1:52798 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:37.563056300+08:00" level=info msg="[TCP] 127.0.0.1:52800 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:37.672155800+08:00" level=info msg="[TCP] 127.0.0.1:52802 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:37.674064700+08:00" level=info msg="[TCP] 127.0.0.1:52803 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:39.134957600+08:00" level=info msg="[TCP] 127.0.0.1:52807 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:39.137819200+08:00" level=info msg="[TCP] 127.0.0.1:52806 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:40.605519500+08:00" level=info msg="[TCP] 127.0.0.1:52812 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:41.872111000+08:00" level=info msg="[TCP] 127.0.0.1:52814 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:42.403860400+08:00" level=info msg="[TCP] 127.0.0.1:52817 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:45.779627300+08:00" level=info msg="[TCP] 127.0.0.1:52821 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:46.080557800+08:00" level=info msg="[TCP] 127.0.0.1:52825 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:47.667963900+08:00" level=info msg="[TCP] 127.0.0.1:52828 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:52.873454800+08:00" level=info msg="[TCP] 127.0.0.1:52834 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:53.727692400+08:00" level=info msg="[TCP] 127.0.0.1:52836 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:57.194526500+08:00" level=info msg="[TCP] 127.0.0.1:52843 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:13:59.008216300+08:00" level=info msg="[TCP] 127.0.0.1:52849 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:05.432713200+08:00" level=info msg="[TCP] 127.0.0.1:52856 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:08.720542400+08:00" level=info msg="[TCP] 127.0.0.1:52860 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:11.914173400+08:00" level=info msg="[TCP] 127.0.0.1:52864 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:11.967755300+08:00" level=info msg="[TCP] 127.0.0.1:52866 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:16.483379600+08:00" level=info msg="[TCP] 127.0.0.1:52871 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:16.884018400+08:00" level=info msg="[TCP] 127.0.0.1:52874 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:16.886285400+08:00" level=info msg="[TCP] 127.0.0.1:52875 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:30.458906300+08:00" level=info msg="[TCP] 127.0.0.1:52888 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:30.587663400+08:00" level=info msg="[TCP] 127.0.0.1:52890 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:35.158340000+08:00" level=info msg="[TCP] 127.0.0.1:52896 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:37.052914400+08:00" level=info msg="[TCP] 127.0.0.1:52899 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:37.991903400+08:00" level=info msg="[TCP] 127.0.0.1:52902 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:38.071131600+08:00" level=info msg="[TCP] 127.0.0.1:52904 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:40.081115800+08:00" level=info msg="[TCP] 127.0.0.1:52907 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:40.612510500+08:00" level=info msg="[TCP] 127.0.0.1:52909 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:42.066173300+08:00" level=info msg="[TCP] 127.0.0.1:52912 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:44.722678700+08:00" level=info msg="[TCP] 127.0.0.1:52916 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:49.936162700+08:00" level=info msg="[TCP] 127.0.0.1:52922 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:50.193241000+08:00" level=info msg="[TCP] 127.0.0.1:52924 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:52.143631400+08:00" level=info msg="[TCP] 127.0.0.1:52928 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:14:57.047785100+08:00" level=info msg="[TCP] 127.0.0.1:52934 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:03.988452800+08:00" level=info msg="[TCP] 127.0.0.1:52941 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:07.273282300+08:00" level=info msg="[TCP] 127.0.0.1:52948 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:07.877471900+08:00" level=info msg="[TCP] 127.0.0.1:52950 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:09.471557800+08:00" level=info msg="[TCP] 127.0.0.1:52953 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:10.131919200+08:00" level=info msg="[TCP] 127.0.0.1:52955 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:11.841707000+08:00" level=info msg="[TCP] 127.0.0.1:52961 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:13.733461000+08:00" level=info msg="[TCP] 127.0.0.1:52964 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:15.551915700+08:00" level=info msg="[TCP] 127.0.0.1:52972 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:15.872087200+08:00" level=info msg="[TCP] 127.0.0.1:52974 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:17.334800500+08:00" level=info msg="[TCP] 127.0.0.1:52978 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:17.431217300+08:00" level=info msg="[TCP] 127.0.0.1:52980 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:17.433038500+08:00" level=info msg="[TCP] 127.0.0.1:52982 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:18.463243600+08:00" level=info msg="[TCP] 127.0.0.1:52984 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:18.470111900+08:00" level=info msg="[TCP] 127.0.0.1:52986 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:22.349814000+08:00" level=info msg="[TCP] 127.0.0.1:52992 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:22.349814000+08:00" level=info msg="[TCP] 127.0.0.1:52991 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:37.567622400+08:00" level=info msg="[TCP] 127.0.0.1:53019 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:15:40.606422900+08:00" level=info msg="[TCP] 127.0.0.1:53026 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:19.785664200+08:00" level=info msg="[TCP] 127.0.0.1:53057 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:25.139602400+08:00" level=info msg="[TCP] 127.0.0.1:53064 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:26.987885900+08:00" level=info msg="[TCP] 127.0.0.1:53067 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:27.106622100+08:00" level=info msg="[TCP] 127.0.0.1:53070 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:31.606115600+08:00" level=info msg="[TCP] 127.0.0.1:53075 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:32.570154500+08:00" level=info msg="[TCP] 127.0.0.1:53078 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:33.571285400+08:00" level=info msg="[TCP] 127.0.0.1:53081 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:33.573041800+08:00" level=info msg="[TCP] 127.0.0.1:53083 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:35.509978500+08:00" level=info msg="[TCP] 127.0.0.1:53086 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:37.443036800+08:00" level=info msg="[TCP] 127.0.0.1:53089 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:40.605610200+08:00" level=info msg="[TCP] 127.0.0.1:53093 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:16:55.047000100+08:00" level=info msg="[TCP] 127.0.0.1:53106 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:17:00.261940900+08:00" level=info msg="[TCP] 127.0.0.1:53111 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:17:05.988464000+08:00" level=info msg="[TCP] 127.0.0.1:53115 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:17:12.807691900+08:00" level=info msg="[TCP] 127.0.0.1:53126 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:17:13.830486500+08:00" level=info msg="[TCP] 127.0.0.1:53128 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:17:14.320156400+08:00" level=info msg="[TCP] 127.0.0.1:53132 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:17:17.723111500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53130 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:17:18.461810000+08:00" level=info msg="[TCP] 127.0.0.1:53141 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:17:18.897950700+08:00" level=info msg="[TCP] 127.0.0.1:53138 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:17:20.702096800+08:00" level=info msg="[TCP] 127.0.0.1:53137 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:17:22.851584800+08:00" level=info msg="[TCP] 127.0.0.1:53146 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:17:40.884407400+08:00" level=info msg="[TCP] 127.0.0.1:53170 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:18:37.567783700+08:00" level=info msg="[TCP] 127.0.0.1:53222 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:18:37.672438800+08:00" level=info msg="[TCP] 127.0.0.1:53226 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:18:37.685979000+08:00" level=info msg="[TCP] 127.0.0.1:53227 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:18:38.572298400+08:00" level=info msg="[TCP] 127.0.0.1:53221 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:18:39.133487300+08:00" level=info msg="[TCP] 127.0.0.1:53230 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:18:40.602778000+08:00" level=info msg="[TCP] 127.0.0.1:53235 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:18:41.348185100+08:00" level=info msg="[TCP] 127.0.0.1:53231 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:18:44.999013100+08:00" level=info msg="[TCP] 127.0.0.1:53240 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:18:47.064983300+08:00" level=info msg="[TCP] 127.0.0.1:53248 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:19:30.633442500+08:00" level=info msg="[TCP] 127.0.0.1:53286 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:19:31.725604300+08:00" level=info msg="[TCP] 127.0.0.1:53289 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:19:40.704731100+08:00" level=info msg="[TCP] 127.0.0.1:53297 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:19:50.393057300+08:00" level=info msg="[TCP] 127.0.0.1:53305 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:20:30.942596000+08:00" level=info msg="[TCP] 127.0.0.1:53339 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:20:34.141040900+08:00" level=info msg="[TCP] 127.0.0.1:53343 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:20:40.869149600+08:00" level=info msg="[TCP] 127.0.0.1:53349 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:15.909121500+08:00" level=info msg="[TCP] 127.0.0.1:53376 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:17.017327300+08:00" level=info msg="[TCP] 127.0.0.1:53379 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:22.357587800+08:00" level=info msg="[TCP] 127.0.0.1:53386 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:22.892726300+08:00" level=info msg="[TCP] 127.0.0.1:53383 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:24.596741700+08:00" level=info msg="[TCP] 127.0.0.1:53390 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:25.459456500+08:00" level=info msg="[TCP] 127.0.0.1:53393 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:31.301668600+08:00" level=info msg="[TCP] 127.0.0.1:53398 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:31.951068500+08:00" level=info msg="[TCP] 127.0.0.1:53401 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:39.523916100+08:00" level=info msg="[TCP] 127.0.0.1:53412 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:39.528089700+08:00" level=info msg="[TCP] 127.0.0.1:53413 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:39.535980100+08:00" level=info msg="[TCP] 127.0.0.1:53416 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:39.657086900+08:00" level=info msg="[TCP] 127.0.0.1:53418 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:40.579484200+08:00" level=info msg="[TCP] 127.0.0.1:53407 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:40.597385300+08:00" level=info msg="[TCP] 127.0.0.1:53423 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:40.707020200+08:00" level=info msg="[TCP] 127.0.0.1:53410 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:40.731278200+08:00" level=info msg="[TCP] 127.0.0.1:53421 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:41.910081600+08:00" level=info msg="[TCP] 127.0.0.1:53427 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:41.979233600+08:00" level=info msg="[TCP] 127.0.0.1:53425 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:45.928832300+08:00" level=info msg="[TCP] 127.0.0.1:53432 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:48.021124700+08:00" level=info msg="[TCP] 127.0.0.1:53441 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:49.799187700+08:00" level=info msg="[TCP] 127.0.0.1:53435 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:21:50.968157000+08:00" level=info msg="[TCP] 127.0.0.1:53447 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:22:41.670278200+08:00" level=info msg="[TCP] 127.0.0.1:53485 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:22:50.210296200+08:00" level=info msg="[TCP] 127.0.0.1:53493 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:23:37.657818900+08:00" level=info msg="[TCP] 127.0.0.1:53534 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:23:37.884825800+08:00" level=info msg="[TCP] 127.0.0.1:53535 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:23:38.548958700+08:00" level=info msg="[TCP] 127.0.0.1:53530 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:23:38.563837300+08:00" level=info msg="[TCP] 127.0.0.1:53532 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:23:39.124863600+08:00" level=info msg="[TCP] 127.0.0.1:53541 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:23:40.256677800+08:00" level=info msg="[TCP] 127.0.0.1:53540 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:23:40.689837700+08:00" level=info msg="[TCP] 127.0.0.1:53544 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:23:44.270172400+08:00" level=info msg="[TCP] 127.0.0.1:53549 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:23:52.870009700+08:00" level=info msg="[TCP] 127.0.0.1:53556 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:23:59.971275300+08:00" level=info msg="[TCP] 127.0.0.1:53564 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:24:05.142996500+08:00" level=info msg="[TCP] 127.0.0.1:53573 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:24:27.562861500+08:00" level=info msg="[TCP] 127.0.0.1:53593 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:24:28.645998200+08:00" level=info msg="[TCP] 127.0.0.1:53595 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:24:31.285149500+08:00" level=info msg="[TCP] 127.0.0.1:53602 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:24:31.481581000+08:00" level=info msg="[TCP] 127.0.0.1:53604 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:24:34.480689400+08:00" level=info msg="[TCP] 127.0.0.1:53605 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:24:40.609224600+08:00" level=info msg="[TCP] 127.0.0.1:53618 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:24:41.710627500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53611 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:24:42.503825500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53614 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:24:50.311516500+08:00" level=info msg="[TCP] 127.0.0.1:53629 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:25:43.860334800+08:00" level=info msg="[TCP] 127.0.0.1:53687 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:26:41.923952700+08:00" level=info msg="[TCP] 127.0.0.1:53759 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:26:42.350605500+08:00" level=info msg="[TCP] 127.0.0.1:53763 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:26:44.792121200+08:00" level=info msg="[TCP] 127.0.0.1:53766 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:27:19.843900300+08:00" level=info msg="[TCP] 127.0.0.1:53794 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:27:20.641788600+08:00" level=info msg="[TCP] 127.0.0.1:53792 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:27:27.439035800+08:00" level=info msg="[TCP] 127.0.0.1:53803 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:27:27.496289600+08:00" level=info msg="[TCP] 127.0.0.1:53799 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:27:31.431690700+08:00" level=info msg="[TCP] 127.0.0.1:53808 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:27:33.651257700+08:00" level=info msg="[TCP] 127.0.0.1:53813 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:27:37.381765000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53811 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:27:37.977567600+08:00" level=info msg="[TCP] 127.0.0.1:53818 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:27:40.708799700+08:00" level=info msg="[TCP] 127.0.0.1:53822 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:27:46.395761600+08:00" level=info msg="[TCP] 127.0.0.1:53828 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:27:52.871568300+08:00" level=info msg="[TCP] 127.0.0.1:53835 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:16.103885100+08:00" level=info msg="[TCP] 127.0.0.1:53858 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:17.809952700+08:00" level=info msg="[TCP] 127.0.0.1:53863 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:19.293966300+08:00" level=info msg="[TCP] 127.0.0.1:53860 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:20.918559600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53856 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:28:20.982255300+08:00" level=info msg="[TCP] 127.0.0.1:53867 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:20.982255300+08:00" level=info msg="[TCP] 127.0.0.1:53868 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:35.208926700+08:00" level=info msg="[TCP] 127.0.0.1:53881 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:37.559777700+08:00" level=info msg="[TCP] 127.0.0.1:53884 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:37.664854800+08:00" level=info msg="[TCP] 127.0.0.1:53887 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:39.134770200+08:00" level=info msg="[TCP] 127.0.0.1:53891 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:40.269600500+08:00" level=info msg="[TCP] 127.0.0.1:53896 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:40.594692600+08:00" level=info msg="[TCP] 127.0.0.1:53898 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:42.612605300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53888 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:28:42.716586700+08:00" level=info msg="[TCP] 127.0.0.1:53902 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:28:44.082609600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53892 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:29:40.635094700+08:00" level=info msg="[TCP] 127.0.0.1:53951 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:29:50.206654300+08:00" level=info msg="[TCP] 127.0.0.1:53959 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:10.909616500+08:00" level=info msg="[TCP] 127.0.0.1:53977 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:15.221018300+08:00" level=info msg="[TCP] 127.0.0.1:53985 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:16.550515900+08:00" level=info msg="[TCP] 127.0.0.1:53982 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:18.114041500+08:00" level=info msg="[TCP] 127.0.0.1:53989 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:27.337232100+08:00" level=info msg="[TCP] 127.0.0.1:53997 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:31.343205000+08:00" level=info msg="[TCP] 127.0.0.1:54006 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:32.487020400+08:00" level=info msg="[TCP] 127.0.0.1:54010 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:36.126075200+08:00" level=info msg="[TCP] 127.0.0.1:54014 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:37.692314400+08:00" level=info msg="[TCP] 127.0.0.1:54017 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:40.617922600+08:00" level=info msg="[TCP] 127.0.0.1:54023 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:30:44.023521500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54020 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:31:41.175071100+08:00" level=info msg="[TCP] 127.0.0.1:54091 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:32:01.987237100+08:00" level=info msg="[TCP] 127.0.0.1:54110 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:32:08.996062000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54113 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:32:43.638526500+08:00" level=info msg="[TCP] 127.0.0.1:54144 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:01.800211200+08:00" level=info msg="[TCP] 127.0.0.1:54161 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:03.109344200+08:00" level=info msg="[TCP] 127.0.0.1:54164 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:37.535753700+08:00" level=info msg="[TCP] 127.0.0.1:54188 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:37.556061300+08:00" level=info msg="[TCP] 127.0.0.1:54190 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:37.669577700+08:00" level=info msg="[TCP] 127.0.0.1:54192 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:37.669577700+08:00" level=info msg="[TCP] 127.0.0.1:54193 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:39.130839600+08:00" level=info msg="[TCP] 127.0.0.1:54199 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:39.134661100+08:00" level=info msg="[TCP] 127.0.0.1:54198 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:40.343478900+08:00" level=info msg="[TCP] 127.0.0.1:54202 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:40.597589700+08:00" level=info msg="[TCP] 127.0.0.1:54204 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:40.837457400+08:00" level=info msg="[TCP] 127.0.0.1:54207 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-10T23:33:41.545497000+08:00" level=info msg="[TCP] 127.0.0.1:54209 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:52.883533100+08:00" level=info msg="[TCP] 127.0.0.1:54218 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:53.439909200+08:00" level=info msg="[TCP] 127.0.0.1:54221 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:53.691094400+08:00" level=info msg="[TCP] 127.0.0.1:54223 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:55.746831700+08:00" level=info msg="[TCP] 127.0.0.1:54226 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:56.762432300+08:00" level=info msg="[TCP] 127.0.0.1:54229 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:57.766564100+08:00" level=info msg="[TCP] 127.0.0.1:54232 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:33:58.256921000+08:00" level=info msg="[TCP] 127.0.0.1:54234 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:00.240272700+08:00" level=info msg="[TCP] 127.0.0.1:54238 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:05.115047400+08:00" level=info msg="[TCP] 127.0.0.1:54246 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:07.252535900+08:00" level=info msg="[TCP] 127.0.0.1:54249 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:09.809020600+08:00" level=info msg="[TCP] 127.0.0.1:54253 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:12.736901400+08:00" level=info msg="[TCP] 127.0.0.1:54257 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:13.952145100+08:00" level=info msg="[TCP] 127.0.0.1:54259 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:13.994687400+08:00" level=info msg="[TCP] 127.0.0.1:54261 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:13.994687400+08:00" level=info msg="[TCP] 127.0.0.1:54262 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:14.005712700+08:00" level=info msg="[TCP] 127.0.0.1:54265 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:14.079475700+08:00" level=info msg="[TCP] 127.0.0.1:54267 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:15.000584500+08:00" level=info msg="[TCP] 127.0.0.1:54270 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:16.413248400+08:00" level=info msg="[TCP] 127.0.0.1:54273 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:18.907658800+08:00" level=info msg="[TCP] 127.0.0.1:54277 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:40.612241000+08:00" level=info msg="[TCP] 127.0.0.1:54294 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:34:50.222041700+08:00" level=info msg="[TCP] 127.0.0.1:54302 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:35:13.623974400+08:00" level=info msg="[TCP] 127.0.0.1:54322 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:35:14.525672100+08:00" level=info msg="[TCP] 127.0.0.1:54324 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:35:15.620795400+08:00" level=info msg="[TCP] 127.0.0.1:54327 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:35:17.061935600+08:00" level=info msg="[TCP] 127.0.0.1:54334 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:35:40.600321800+08:00" level=info msg="[TCP] 127.0.0.1:54357 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:10.528147200+08:00" level=info msg="[TCP] 127.0.0.1:54385 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:10.543354600+08:00" level=info msg="[TCP] 127.0.0.1:54387 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:12.670190300+08:00" level=info msg="[TCP] 127.0.0.1:54390 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:15.453767500+08:00" level=info msg="[TCP] 127.0.0.1:54396 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:15.456417300+08:00" level=info msg="[TCP] 127.0.0.1:54397 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:26.409458800+08:00" level=info msg="[TCP] 127.0.0.1:54408 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:26.457495000+08:00" level=info msg="[TCP] 127.0.0.1:54410 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:27.954206500+08:00" level=info msg="[TCP] 127.0.0.1:54412 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:30.768027900+08:00" level=info msg="[TCP] 127.0.0.1:54416 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:31.915575400+08:00" level=info msg="[TCP] 127.0.0.1:54419 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:32.877459000+08:00" level=info msg="[TCP] 127.0.0.1:54422 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:32.917566100+08:00" level=info msg="[TCP] 127.0.0.1:54424 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:33.126648900+08:00" level=info msg="[TCP] 127.0.0.1:54426 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:36.283030700+08:00" level=info msg="[TCP] 127.0.0.1:54430 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:37.561734900+08:00" level=info msg="[TCP] 127.0.0.1:54433 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:38.340550400+08:00" level=info msg="[TCP] 127.0.0.1:54435 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:40.447606700+08:00" level=info msg="[TCP] 127.0.0.1:54439 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:40.599939500+08:00" level=info msg="[TCP] 127.0.0.1:54441 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:42.650934800+08:00" level=info msg="[TCP] 127.0.0.1:54444 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:43.329954300+08:00" level=info msg="[TCP] 127.0.0.1:54446 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:45.504286200+08:00" level=info msg="[TCP] 127.0.0.1:54452 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:48.877273300+08:00" level=info msg="[TCP] 127.0.0.1:54457 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:49.962065300+08:00" level=info msg="[TCP] 127.0.0.1:54460 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:50.801016500+08:00" level=info msg="[TCP] 127.0.0.1:54463 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:50.819050300+08:00" level=info msg="[TCP] 127.0.0.1:54465 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:53.119015000+08:00" level=info msg="[TCP] 127.0.0.1:54468 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:55.728021700+08:00" level=info msg="[TCP] 127.0.0.1:54472 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:36:55.741148800+08:00" level=info msg="[TCP] 127.0.0.1:54473 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:37:40.604979900+08:00" level=info msg="[TCP] 127.0.0.1:54506 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:38:37.567705700+08:00" level=info msg="[TCP] 127.0.0.1:54548 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:38:37.661178400+08:00" level=info msg="[TCP] 127.0.0.1:54550 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:38:37.664801900+08:00" level=info msg="[TCP] 127.0.0.1:54551 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:38:39.136962000+08:00" level=info msg="[TCP] 127.0.0.1:54556 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:38:39.136962000+08:00" level=info msg="[TCP] 127.0.0.1:54555 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:38:40.607545800+08:00" level=info msg="[TCP] 127.0.0.1:54560 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:38:41.262168100+08:00" level=info msg="[TCP] 127.0.0.1:54562 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:38:42.282247300+08:00" level=info msg="[TCP] 127.0.0.1:54565 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:39:40.599543300+08:00" level=info msg="[TCP] 127.0.0.1:54635 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:39:50.231216200+08:00" level=info msg="[TCP] 127.0.0.1:54643 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:40:40.607040800+08:00" level=info msg="[TCP] 127.0.0.1:54699 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:40:56.484029300+08:00" level=info msg="[TCP] 127.0.0.1:54712 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:41:41.191107700+08:00" level=info msg="[TCP] 127.0.0.1:54746 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:27.418681600+08:00" level=info msg="[TCP] 127.0.0.1:54784 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:28.490557800+08:00" level=info msg="[TCP] 127.0.0.1:54787 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:37.556929800+08:00" level=info msg="[TCP] 127.0.0.1:54797 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:40.610269200+08:00" level=info msg="[TCP] 127.0.0.1:54801 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:49.794400600+08:00" level=info msg="[TCP] 127.0.0.1:54809 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:49.952950600+08:00" level=info msg="[TCP] 127.0.0.1:54811 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:51.164970000+08:00" level=info msg="[TCP] 127.0.0.1:54813 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:51.839491900+08:00" level=info msg="[TCP] 127.0.0.1:54816 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:53.116082700+08:00" level=info msg="[TCP] 127.0.0.1:54819 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:54.119058700+08:00" level=info msg="[TCP] 127.0.0.1:54821 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:54.310059400+08:00" level=info msg="[TCP] 127.0.0.1:54823 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:42:57.840211300+08:00" level=info msg="[TCP] 127.0.0.1:54828 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:00.190847500+08:00" level=info msg="[TCP] 127.0.0.1:54833 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:03.388270500+08:00" level=info msg="[TCP] 127.0.0.1:54844 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:03.426752000+08:00" level=info msg="[TCP] 127.0.0.1:54846 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:03.427822200+08:00" level=info msg="[TCP] 127.0.0.1:54847 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:03.444453100+08:00" level=info msg="[TCP] 127.0.0.1:54850 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:03.553898600+08:00" level=info msg="[TCP] 127.0.0.1:54852 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:04.441171400+08:00" level=info msg="[TCP] 127.0.0.1:54855 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:04.740657300+08:00" level=info msg="[TCP] 127.0.0.1:54857 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:06.180868300+08:00" level=info msg="[TCP] 127.0.0.1:54860 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:16.124747900+08:00" level=info msg="[TCP] 127.0.0.1:54869 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:23.963870800+08:00" level=info msg="[TCP] 127.0.0.1:54882 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:24.863004100+08:00" level=info msg="[TCP] 127.0.0.1:54884 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:26.851945000+08:00" level=info msg="[TCP] 127.0.0.1:54893 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:35.184575300+08:00" level=info msg="[TCP] 127.0.0.1:54902 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:37.096946400+08:00" level=info msg="[TCP] 127.0.0.1:54906 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:37.533844600+08:00" level=info msg="[TCP] 127.0.0.1:54909 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:37.556455500+08:00" level=info msg="[TCP] 127.0.0.1:54911 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:37.659207800+08:00" level=info msg="[TCP] 127.0.0.1:54914 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:37.663359000+08:00" level=info msg="[TCP] 127.0.0.1:54913 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:39.127763200+08:00" level=info msg="[TCP] 127.0.0.1:54919 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:39.131237400+08:00" level=info msg="[TCP] 127.0.0.1:54918 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:40.181139700+08:00" level=info msg="[TCP] 127.0.0.1:54922 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:40.599125500+08:00" level=info msg="[TCP] 127.0.0.1:54925 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:41.171655900+08:00" level=info msg="[TCP] 127.0.0.1:54927 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:43:52.878737900+08:00" level=info msg="[TCP] 127.0.0.1:54937 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:44:21.617310300+08:00" level=info msg="[TCP] 127.0.0.1:54963 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:44:36.007395900+08:00" level=info msg="[TCP] 127.0.0.1:54975 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:44:36.089961100+08:00" level=info msg="[TCP] 127.0.0.1:54977 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:44:37.187249500+08:00" level=info msg="[TCP] 127.0.0.1:54980 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:44:37.964758900+08:00" level=info msg="[TCP] 127.0.0.1:54982 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:44:40.607499300+08:00" level=info msg="[TCP] 127.0.0.1:54986 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:44:40.931854700+08:00" level=info msg="[TCP] 127.0.0.1:54989 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:44:40.933975300+08:00" level=info msg="[TCP] 127.0.0.1:54988 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:44:51.177074700+08:00" level=info msg="[TCP] 127.0.0.1:55022 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:45:38.184668100+08:00" level=info msg="[TCP] 127.0.0.1:55098 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:45:41.171293900+08:00" level=info msg="[TCP] 127.0.0.1:55102 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:46:50.217609600+08:00" level=info msg="[TCP] 127.0.0.1:55238 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:47:40.609307900+08:00" level=info msg="[TCP] 127.0.0.1:55320 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:48:37.566189400+08:00" level=info msg="[TCP] 127.0.0.1:55466 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:48:37.568700000+08:00" level=info msg="[TCP] 127.0.0.1:55465 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:48:37.659813300+08:00" level=info msg="[TCP] 127.0.0.1:55470 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:48:37.661287400+08:00" level=info msg="[TCP] 127.0.0.1:55469 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:48:39.136780400+08:00" level=info msg="[TCP] 127.0.0.1:55476 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:48:39.138090100+08:00" level=info msg="[TCP] 127.0.0.1:55475 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:48:40.715746300+08:00" level=info msg="[TCP] 127.0.0.1:55480 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:48:42.186736400+08:00" level=info msg="[TCP] 127.0.0.1:55483 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:48:43.267987400+08:00" level=info msg="[TCP] 127.0.0.1:55486 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:48:52.554268300+08:00" level=info msg="[TCP] 127.0.0.1:55497 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:03.919446400+08:00" level=info msg="[TCP] 127.0.0.1:55507 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:19.014935300+08:00" level=info msg="[TCP] 127.0.0.1:55519 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:19.115959300+08:00" level=info msg="[TCP] 127.0.0.1:55521 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:23.502069800+08:00" level=info msg="[TCP] 127.0.0.1:55524 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:26.265089000+08:00" level=info msg="[TCP] 127.0.0.1:55530 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:39.025787900+08:00" level=info msg="[TCP] 127.0.0.1:55541 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:39.701662000+08:00" level=info msg="[TCP] 127.0.0.1:55540 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:39.849017500+08:00" level=info msg="[TCP] 127.0.0.1:55546 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:39.904944700+08:00" level=info msg="[TCP] 127.0.0.1:55549 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:40.611043000+08:00" level=info msg="[TCP] 127.0.0.1:55551 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:41.800559900+08:00" level=info msg="[TCP] 127.0.0.1:55544 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:43.966030800+08:00" level=info msg="[TCP] 127.0.0.1:55557 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:44.918459400+08:00" level=info msg="[TCP] 127.0.0.1:55560 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:45.989365600+08:00" level=info msg="[TCP] 127.0.0.1:55564 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:46.978460400+08:00" level=info msg="[TCP] 127.0.0.1:55566 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:48.504372000+08:00" level=info msg="[TCP] 127.0.0.1:55569 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:49:56.125605700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55573 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:50:28.947149600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55602 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:50:40.660653400+08:00" level=info msg="[TCP] 127.0.0.1:55616 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:50:44.735861800+08:00" level=info msg="[TCP] 127.0.0.1:55620 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:51:08.036566700+08:00" level=info msg="[TCP] 127.0.0.1:55643 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:51:12.891618200+08:00" level=info msg="[TCP] 127.0.0.1:55647 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:51:14.349570500+08:00" level=info msg="[TCP] 127.0.0.1:55651 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:51:15.586552800+08:00" level=info msg="[TCP] 127.0.0.1:55657 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:51:15.654007500+08:00" level=info msg="[TCP] 127.0.0.1:55659 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:51:20.540345600+08:00" level=info msg="[TCP] 127.0.0.1:55665 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:51:20.573949600+08:00" level=info msg="[TCP] 127.0.0.1:55664 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:51:28.212719000+08:00" level=info msg="[TCP] 127.0.0.1:55678 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:51:36.046647900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55682 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:51:42.504503800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55703 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:51:43.056587100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55705 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:51:43.113821300+08:00" level=info msg="[TCP] 127.0.0.1:55709 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:52:45.553715400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55758 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:53:40.109046500+08:00" level=info msg="[TCP] 127.0.0.1:55827 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:53:40.510726500+08:00" level=info msg="[TCP] 127.0.0.1:55829 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:53:40.614341300+08:00" level=info msg="[TCP] 127.0.0.1:55841 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:53:41.666833100+08:00" level=info msg="[TCP] 127.0.0.1:55834 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:53:42.485661900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55825 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:53:42.608799000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55830 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:53:44.075179500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55835 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-10T23:53:44.525229000+08:00" level=info msg="[TCP] 127.0.0.1:55847 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:53:45.816876400+08:00" level=info msg="[TCP] 127.0.0.1:55849 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:53:52.886800400+08:00" level=info msg="[TCP] 127.0.0.1:55856 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:54:18.733658600+08:00" level=info msg="[TCP] 127.0.0.1:55879 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:54:20.478581800+08:00" level=info msg="[TCP] 127.0.0.1:55882 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:54:37.558887600+08:00" level=info msg="[TCP] 127.0.0.1:55899 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:54:40.607801300+08:00" level=info msg="[TCP] 127.0.0.1:55903 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:54:52.195122000+08:00" level=info msg="[TCP] 127.0.0.1:55912 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:18.723199100+08:00" level=info msg="[TCP] 127.0.0.1:55935 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:20.462256100+08:00" level=info msg="[TCP] 127.0.0.1:55937 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:20.464546700+08:00" level=info msg="[TCP] 127.0.0.1:55939 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:20.559238900+08:00" level=info msg="[TCP] 127.0.0.1:55942 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:20.884459500+08:00" level=info msg="[TCP] 127.0.0.1:55944 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:20.978494300+08:00" level=info msg="[TCP] 127.0.0.1:55946 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:21.677516400+08:00" level=info msg="[TCP] 127.0.0.1:55949 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:21.983783700+08:00" level=info msg="[TCP] 127.0.0.1:55951 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:22.763697300+08:00" level=info msg="[TCP] 127.0.0.1:55953 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:23.882450200+08:00" level=info msg="[TCP] 127.0.0.1:55956 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:24.890179100+08:00" level=info msg="[TCP] 127.0.0.1:55960 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:27.229482000+08:00" level=info msg="[TCP] 127.0.0.1:55963 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:31.501575900+08:00" level=info msg="[TCP] 127.0.0.1:55968 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:37.822746700+08:00" level=info msg="[TCP] 127.0.0.1:55975 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:40.615709900+08:00" level=info msg="[TCP] 127.0.0.1:55979 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:40.942088000+08:00" level=info msg="[TCP] 127.0.0.1:55981 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:40.982362100+08:00" level=info msg="[TCP] 127.0.0.1:55983 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:40.982362100+08:00" level=info msg="[TCP] 127.0.0.1:55984 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:41.001139300+08:00" level=info msg="[TCP] 127.0.0.1:55987 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:41.097233300+08:00" level=info msg="[TCP] 127.0.0.1:55989 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:41.879407900+08:00" level=info msg="[TCP] 127.0.0.1:55992 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:41.995732000+08:00" level=info msg="[TCP] 127.0.0.1:55995 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:43.943070900+08:00" level=info msg="[TCP] 127.0.0.1:55999 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:56.604078800+08:00" level=info msg="[TCP] 127.0.0.1:56009 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:57.547377600+08:00" level=info msg="[TCP] 127.0.0.1:56016 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:57.922006700+08:00" level=info msg="[TCP] 127.0.0.1:56018 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:55:59.405490900+08:00" level=info msg="[TCP] 127.0.0.1:56028 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:00.223913700+08:00" level=info msg="[TCP] 127.0.0.1:56026 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:01.208762600+08:00" level=info msg="[TCP] 127.0.0.1:56035 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:01.234598100+08:00" level=info msg="[TCP] 127.0.0.1:56032 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:04.256887100+08:00" level=info msg="[TCP] 127.0.0.1:56041 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:06.340117800+08:00" level=info msg="[TCP] 127.0.0.1:56044 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:07.087256300+08:00" level=info msg="[TCP] 127.0.0.1:56046 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:08.677943200+08:00" level=info msg="[TCP] 127.0.0.1:56049 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:10.838426000+08:00" level=info msg="[TCP] 127.0.0.1:56053 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:12.936789000+08:00" level=info msg="[TCP] 127.0.0.1:56056 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:40.593284800+08:00" level=info msg="[TCP] 127.0.0.1:56079 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:43.497158700+08:00" level=info msg="[TCP] 127.0.0.1:56083 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:48.007629200+08:00" level=info msg="[TCP] 127.0.0.1:56089 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:50.356580700+08:00" level=info msg="[TCP] 127.0.0.1:56093 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:54.839802100+08:00" level=info msg="[TCP] 127.0.0.1:56101 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:55.000209000+08:00" level=info msg="[TCP] 127.0.0.1:56104 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:55.051229000+08:00" level=info msg="[TCP] 127.0.0.1:56106 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:57.130887700+08:00" level=info msg="[TCP] 127.0.0.1:56114 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:56:57.218248800+08:00" level=info msg="[TCP] 127.0.0.1:56116 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:57:00.173050000+08:00" level=info msg="[TCP] 127.0.0.1:56121 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:57:00.176747400+08:00" level=info msg="[TCP] 127.0.0.1:56120 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:57:38.213101900+08:00" level=info msg="[TCP] 127.0.0.1:56171 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:57:41.188296600+08:00" level=info msg="[TCP] 127.0.0.1:56176 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:58:50.210663300+08:00" level=info msg="[TCP] 127.0.0.1:56238 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:58:50.210663300+08:00" level=info msg="[TCP] 127.0.0.1:56236 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:58:50.212871700+08:00" level=info msg="[TCP] 127.0.0.1:56234 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:58:50.212871700+08:00" level=info msg="[TCP] 127.0.0.1:56235 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:58:50.214226500+08:00" level=info msg="[TCP] 127.0.0.1:56237 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:58:50.217339300+08:00" level=info msg="[TCP] 127.0.0.1:56233 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:58:51.244461800+08:00" level=info msg="[TCP] 127.0.0.1:56246 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:58:52.445769800+08:00" level=info msg="[TCP] 127.0.0.1:56249 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:59:39.219264700+08:00" level=info msg="[TCP] 127.0.0.1:56293 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:59:40.322633800+08:00" level=info msg="[TCP] 127.0.0.1:56296 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:59:40.599189600+08:00" level=info msg="[TCP] 127.0.0.1:56298 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-10T23:59:51.201825400+08:00" level=info msg="[TCP] 127.0.0.1:56306 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:13.195667600+08:00" level=info msg="[TCP] 127.0.0.1:56332 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:14.281484400+08:00" level=info msg="[TCP] 127.0.0.1:56335 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:14.662149500+08:00" level=info msg="[TCP] 127.0.0.1:56337 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:14.782206700+08:00" level=info msg="[TCP] 127.0.0.1:56339 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:15.694053900+08:00" level=info msg="[TCP] 127.0.0.1:56342 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:16.641472200+08:00" level=info msg="[TCP] 127.0.0.1:56344 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:16.838091600+08:00" level=info msg="[TCP] 127.0.0.1:56347 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:17.020004400+08:00" level=info msg="[TCP] 127.0.0.1:56349 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:17.969350900+08:00" level=info msg="[TCP] 127.0.0.1:56351 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:18.984309500+08:00" level=info msg="[TCP] 127.0.0.1:56354 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:20.805034200+08:00" level=info msg="[TCP] 127.0.0.1:56357 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:22.185631800+08:00" level=info msg="[TCP] 127.0.0.1:56363 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:27.447220000+08:00" level=info msg="[TCP] 127.0.0.1:56369 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:31.160072100+08:00" level=info msg="[TCP] 127.0.0.1:56374 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:37.559537400+08:00" level=info msg="[TCP] 127.0.0.1:56380 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:40.389737700+08:00" level=info msg="[TCP] 127.0.0.1:56384 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:40.598945800+08:00" level=info msg="[TCP] 127.0.0.1:56386 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:45.936425100+08:00" level=info msg="[TCP] 127.0.0.1:56391 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:48.804138100+08:00" level=info msg="[TCP] 127.0.0.1:56395 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:52.244854100+08:00" level=info msg="[TCP] 127.0.0.1:56402 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:54.324887500+08:00" level=info msg="[TCP] 127.0.0.1:56406 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:54.384524200+08:00" level=info msg="[TCP] 127.0.0.1:56408 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:54.459593000+08:00" level=info msg="[TCP] 127.0.0.1:56410 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:55.475925200+08:00" level=info msg="[TCP] 127.0.0.1:56413 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:00:55.994803800+08:00" level=info msg="[TCP] 127.0.0.1:56415 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:01:00.181803300+08:00" level=info msg="[TCP] 127.0.0.1:56420 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:01:00.183579600+08:00" level=info msg="[TCP] 127.0.0.1:56421 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:01:41.182126200+08:00" level=info msg="[TCP] 127.0.0.1:56470 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:02:37.143310300+08:00" level=info msg="[TCP] 127.0.0.1:56600 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:02:41.189622300+08:00" level=info msg="[TCP] 127.0.0.1:56606 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:38.226484700+08:00" level=info msg="[TCP] 127.0.0.1:56669 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:38.228040700+08:00" level=info msg="[TCP] 127.0.0.1:56665 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:39.182953900+08:00" level=info msg="[TCP] 127.0.0.1:56676 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:39.183986200+08:00" level=info msg="[TCP] 127.0.0.1:56675 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:39.221437700+08:00" level=info msg="[TCP] 127.0.0.1:56666 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:39.226054200+08:00" level=info msg="[TCP] 127.0.0.1:56668 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:39.227080700+08:00" level=info msg="[TCP] 127.0.0.1:56667 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:40.221232600+08:00" level=info msg="[TCP] 127.0.0.1:56680 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:40.851022900+08:00" level=info msg="[TCP] 127.0.0.1:56683 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-11T00:03:41.358294100+08:00" level=info msg="[TCP] 127.0.0.1:56685 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:48.775586500+08:00" level=info msg="[TCP] 127.0.0.1:56691 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:51.146885600+08:00" level=info msg="[TCP] 127.0.0.1:56695 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:52.616752300+08:00" level=info msg="[TCP] 127.0.0.1:56698 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:03:52.876042400+08:00" level=info msg="[TCP] 127.0.0.1:56700 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:04:16.013478800+08:00" level=info msg="[TCP] 127.0.0.1:56723 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:04:17.352639400+08:00" level=info msg="[TCP] 127.0.0.1:56727 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:04:40.611746500+08:00" level=info msg="[TCP] 127.0.0.1:56744 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:04:51.160632200+08:00" level=info msg="[TCP] 127.0.0.1:56754 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:04:51.207048800+08:00" level=info msg="[TCP] 127.0.0.1:56756 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:04:52.255133800+08:00" level=info msg="[TCP] 127.0.0.1:56758 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:05:40.610184300+08:00" level=info msg="[TCP] 127.0.0.1:56809 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:05:51.174735400+08:00" level=info msg="[TCP] 127.0.0.1:56819 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:07.282630400+08:00" level=info msg="[TCP] 127.0.0.1:56835 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:27.188894700+08:00" level=info msg="[TCP] 127.0.0.1:56857 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:37.576964700+08:00" level=info msg="[TCP] 127.0.0.1:56866 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:40.596940500+08:00" level=info msg="[TCP] 127.0.0.1:56870 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:51.174139700+08:00" level=info msg="[TCP] 127.0.0.1:56879 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:52.388512400+08:00" level=info msg="[TCP] 127.0.0.1:56882 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:56.941611400+08:00" level=info msg="[TCP] 127.0.0.1:56887 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:57.073655500+08:00" level=info msg="[TCP] 127.0.0.1:56889 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:57.968373100+08:00" level=info msg="[TCP] 127.0.0.1:56892 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:58.484118200+08:00" level=info msg="[TCP] 127.0.0.1:56895 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:06:59.429868700+08:00" level=info msg="[TCP] 127.0.0.1:56899 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:00.220685800+08:00" level=info msg="[TCP] 127.0.0.1:56901 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:05.805380300+08:00" level=info msg="[TCP] 127.0.0.1:56907 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:06.822148300+08:00" level=info msg="[TCP] 127.0.0.1:56910 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:08.316770000+08:00" level=info msg="[TCP] 127.0.0.1:56913 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:08.792849700+08:00" level=info msg="[TCP] 127.0.0.1:56915 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:10.715618100+08:00" level=info msg="[TCP] 127.0.0.1:56918 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:13.068041000+08:00" level=info msg="[TCP] 127.0.0.1:56922 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:16.874461300+08:00" level=info msg="[TCP] 127.0.0.1:56926 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:19.011186400+08:00" level=info msg="[TCP] 127.0.0.1:56930 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:19.068574400+08:00" level=info msg="[TCP] 127.0.0.1:56932 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:22.844640600+08:00" level=info msg="[TCP] 127.0.0.1:56938 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:24.888101900+08:00" level=info msg="[TCP] 127.0.0.1:56941 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:27.701447600+08:00" level=info msg="[TCP] 127.0.0.1:56945 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:29.671434800+08:00" level=info msg="[TCP] 127.0.0.1:56949 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:31.918029300+08:00" level=info msg="[TCP] 127.0.0.1:56952 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:34.145139200+08:00" level=info msg="[TCP] 127.0.0.1:56956 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:36.178861500+08:00" level=info msg="[TCP] 127.0.0.1:56959 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:40.605394800+08:00" level=info msg="[TCP] 127.0.0.1:56964 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:48.069663800+08:00" level=info msg="[TCP] 127.0.0.1:56973 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:07:53.551378400+08:00" level=info msg="[TCP] 127.0.0.1:56981 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:00.487158100+08:00" level=info msg="[TCP] 127.0.0.1:56992 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:00.553392400+08:00" level=info msg="[TCP] 127.0.0.1:56994 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:00.649042000+08:00" level=info msg="[TCP] 127.0.0.1:56996 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:02.100818400+08:00" level=info msg="[TCP] 127.0.0.1:57000 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:03.654417700+08:00" level=info msg="[TCP] 127.0.0.1:57003 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:05.144891300+08:00" level=info msg="[TCP] 127.0.0.1:57006 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:05.497692900+08:00" level=info msg="[TCP] 127.0.0.1:57008 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:05.504371300+08:00" level=info msg="[TCP] 127.0.0.1:57009 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:37.562050000+08:00" level=info msg="[TCP] 127.0.0.1:57052 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:37.658908500+08:00" level=info msg="[TCP] 127.0.0.1:57055 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:37.659989700+08:00" level=info msg="[TCP] 127.0.0.1:57054 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:39.128779900+08:00" level=info msg="[TCP] 127.0.0.1:57060 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:39.130730100+08:00" level=info msg="[TCP] 127.0.0.1:57059 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:40.607374800+08:00" level=info msg="[TCP] 127.0.0.1:57064 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:43.239616500+08:00" level=info msg="[TCP] 127.0.0.1:57070 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:08:48.935461500+08:00" level=info msg="[TCP] 127.0.0.1:57084 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:09:09.031233300+08:00" level=info msg="[TCP] 127.0.0.1:57102 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:09:10.236708600+08:00" level=info msg="[TCP] 127.0.0.1:57105 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:09:37.560473000+08:00" level=info msg="[TCP] 127.0.0.1:57128 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:09:40.604741100+08:00" level=info msg="[TCP] 127.0.0.1:57132 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:09:46.250764700+08:00" level=info msg="[TCP] 127.0.0.1:57138 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:09:46.297367900+08:00" level=info msg="[TCP] 127.0.0.1:57140 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:09:51.078966200+08:00" level=info msg="[TCP] 127.0.0.1:57144 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:09:51.214959500+08:00" level=info msg="[TCP] 127.0.0.1:57147 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:09:54.037613100+08:00" level=info msg="[TCP] 127.0.0.1:57150 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:10:01.065731500+08:00" level=info msg="[TCP] 127.0.0.1:57159 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:10:04.267862400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57162 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-11T00:10:07.013310300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57175 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-11T00:10:17.547381200+08:00" level=info msg="[TCP] 127.0.0.1:57187 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:10:33.853811900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57207 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-11T00:10:45.550569500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57220 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-11T00:10:46.305775000+08:00" level=info msg="[TCP] 127.0.0.1:57226 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:10:46.420493400+08:00" level=info msg="[TCP] 127.0.0.1:57224 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:10:47.291747600+08:00" level=info msg="[TCP] 127.0.0.1:57235 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:10:48.830194300+08:00" level=info msg="[TCP] 127.0.0.1:57238 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:10:48.831882600+08:00" level=info msg="[TCP] 127.0.0.1:57237 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:10:48.916449900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57228 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-11T00:10:49.415023200+08:00" level=info msg="[TCP] 127.0.0.1:57242 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:11:40.613869300+08:00" level=info msg="[TCP] 127.0.0.1:57293 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:12:09.553659500+08:00" level=info msg="[TCP] 127.0.0.1:57319 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:12:20.589317800+08:00" level=info msg="[TCP] 127.0.0.1:57329 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:12:26.420529000+08:00" level=info msg="[TCP] 127.0.0.1:57336 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:12:29.833861600+08:00" level=info msg="[TCP] 127.0.0.1:57341 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:12:31.271484800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57334 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-11T00:12:32.131935900+08:00" level=info msg="[TCP] 127.0.0.1:57344 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:12:34.957394300+08:00" level=info msg="[TCP] 127.0.0.1:57350 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:12:38.080259700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57347 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-11T00:12:41.860499600+08:00" level=info msg="[TCP] 127.0.0.1:57358 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:12:42.510570700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57354 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-11T00:12:43.557579100+08:00" level=info msg="[TCP] 127.0.0.1:57362 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:12:53.044101000+08:00" level=info msg="[TCP] 127.0.0.1:57374 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:02.320225400+08:00" level=info msg="[TCP] 127.0.0.1:57385 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:02.428345600+08:00" level=info msg="[TCP] 127.0.0.1:57387 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:02.433016100+08:00" level=info msg="[TCP] 127.0.0.1:57383 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:07.096002400+08:00" level=info msg="[TCP] 127.0.0.1:57390 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:07.261010800+08:00" level=info msg="[TCP] 127.0.0.1:57394 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:07.433960200+08:00" level=info msg="[TCP] 127.0.0.1:57395 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:08.477914500+08:00" level=info msg="[TCP] 127.0.0.1:57399 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:13.337262400+08:00" level=info msg="[TCP] 127.0.0.1:57404 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:37.574128000+08:00" level=info msg="[TCP] 127.0.0.1:57430 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:37.696344500+08:00" level=info msg="[TCP] 127.0.0.1:57435 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:37.756815800+08:00" level=info msg="[TCP] 127.0.0.1:57434 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:37.869803000+08:00" level=info msg="[TCP] 127.0.0.1:57432 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:39.133264100+08:00" level=info msg="[TCP] 127.0.0.1:57441 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:40.141934600+08:00" level=info msg="[TCP] 127.0.0.1:57440 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:41.528019800+08:00" level=info msg="[TCP] 127.0.0.1:57448 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:43.202332200+08:00" level=info msg="[TCP] 127.0.0.1:57451 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:43.822172300+08:00" level=info msg="[TCP] 127.0.0.1:57445 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:13:52.915885100+08:00" level=info msg="[TCP] 127.0.0.1:57459 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:14:10.411562500+08:00" level=info msg="[TCP] 127.0.0.1:57475 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:14:13.474264400+08:00" level=info msg="[TCP] 127.0.0.1:57480 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:14:24.536967100+08:00" level=info msg="[TCP] 127.0.0.1:57490 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:14:27.639262000+08:00" level=info msg="[TCP] 127.0.0.1:57494 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:14:40.632155900+08:00" level=info msg="[TCP] 127.0.0.1:57506 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:14:51.252796700+08:00" level=info msg="[TCP] 127.0.0.1:57516 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:15:10.609454300+08:00" level=info msg="[TCP] 127.0.0.1:57534 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:15:23.685103600+08:00" level=info msg="[TCP] 127.0.0.1:57545 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:15:37.566861600+08:00" level=info msg="[TCP] 127.0.0.1:57559 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:15:41.669956600+08:00" level=info msg="[TCP] 127.0.0.1:57563 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:11.427002400+08:00" level=info msg="[TCP] 127.0.0.1:57587 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:23.407018500+08:00" level=info msg="[TCP] 127.0.0.1:57599 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:36.446920300+08:00" level=info msg="[TCP] 127.0.0.1:57610 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:36.694800400+08:00" level=info msg="[TCP] 127.0.0.1:57612 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:37.479070500+08:00" level=info msg="[TCP] 127.0.0.1:57615 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:38.828903400+08:00" level=info msg="[TCP] 127.0.0.1:57619 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:39.569993900+08:00" level=info msg="[TCP] 127.0.0.1:57617 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:41.610588700+08:00" level=info msg="[TCP] 127.0.0.1:57623 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:44.104735700+08:00" level=info msg="[TCP] 127.0.0.1:57627 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:45.921541800+08:00" level=info msg="[TCP] 127.0.0.1:57630 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:47.886380400+08:00" level=info msg="[TCP] 127.0.0.1:57636 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:48.012428800+08:00" level=info msg="[TCP] 127.0.0.1:57633 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:50.191323800+08:00" level=info msg="[TCP] 127.0.0.1:57641 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:52.338336100+08:00" level=info msg="[TCP] 127.0.0.1:57645 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:56.699404800+08:00" level=info msg="[TCP] 127.0.0.1:57650 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:57.148028100+08:00" level=info msg="[TCP] 127.0.0.1:57652 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:16:59.891099000+08:00" level=info msg="[TCP] 127.0.0.1:57656 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:17:02.956673200+08:00" level=info msg="[TCP] 127.0.0.1:57660 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:17:23.395618400+08:00" level=info msg="[TCP] 127.0.0.1:57677 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:17:24.764869300+08:00" level=info msg="[TCP] 127.0.0.1:57681 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:17:25.446228900+08:00" level=info msg="[TCP] 127.0.0.1:57679 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:17:26.944751700+08:00" level=info msg="[TCP] 127.0.0.1:57686 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:17:28.440157700+08:00" level=info msg="[TCP] 127.0.0.1:57690 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:17:28.706437200+08:00" level=info msg="[TCP] 127.0.0.1:57691 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:17:31.143392900+08:00" level=info msg="[TCP] 127.0.0.1:57696 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:17:40.610466500+08:00" level=info msg="[TCP] 127.0.0.1:57704 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:18:38.177572500+08:00" level=info msg="[TCP] 127.0.0.1:57771 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:18:38.192412800+08:00" level=info msg="[TCP] 127.0.0.1:57770 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:18:38.396000100+08:00" level=info msg="[TCP] 127.0.0.1:57769 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:18:39.187391300+08:00" level=info msg="[TCP] 127.0.0.1:57772 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:18:39.201298300+08:00" level=info msg="[TCP] 127.0.0.1:57778 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:18:40.199544400+08:00" level=info msg="[TCP] 127.0.0.1:57779 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:18:41.178310500+08:00" level=info msg="[TCP] 127.0.0.1:57783 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:18:41.871283800+08:00" level=info msg="[TCP] 127.0.0.1:57785 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:18:44.250036600+08:00" level=info msg="[TCP] 127.0.0.1:57788 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:19:50.242616600+08:00" level=info msg="[TCP] 127.0.0.1:57879 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:10.091810100+08:00" level=info msg="[TCP] 127.0.0.1:57909 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:32.688148900+08:00" level=info msg="[TCP] 127.0.0.1:57940 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:33.787216200+08:00" level=info msg="[TCP] 127.0.0.1:57946 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:34.948012200+08:00" level=info msg="[TCP] 127.0.0.1:57949 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:37.636441200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57941 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-11T00:20:40.600536800+08:00" level=info msg="[TCP] 127.0.0.1:57957 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:40.989397000+08:00" level=info msg="[TCP] 127.0.0.1:57960 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:45.141038000+08:00" level=info msg="[TCP] 127.0.0.1:57966 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:47.135991400+08:00" level=info msg="[TCP] 127.0.0.1:57974 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:47.138659400+08:00" level=info msg="[TCP] 127.0.0.1:57973 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:48.163110100+08:00" level=info msg="[TCP] 127.0.0.1:57977 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:49.315094000+08:00" level=info msg="[TCP] 127.0.0.1:57981 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:52.785196800+08:00" level=info msg="[TCP] 127.0.0.1:57987 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:54.021189300+08:00" level=info msg="[TCP] 127.0.0.1:57991 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:54.728438200+08:00" level=info msg="[TCP] 127.0.0.1:57994 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:54.731265300+08:00" level=info msg="[TCP] 127.0.0.1:57993 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:55.761067600+08:00" level=info msg="[TCP] 127.0.0.1:57998 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:20:57.195343000+08:00" level=info msg="[TCP] 127.0.0.1:58002 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:17.939946700+08:00" level=info msg="[TCP] 127.0.0.1:58032 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:18.923338100+08:00" level=info msg="[TCP] 127.0.0.1:58036 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:20.476560500+08:00" level=info msg="[TCP] 127.0.0.1:58041 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:20.731991200+08:00" level=info msg="[TCP] 127.0.0.1:58043 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:21.801277100+08:00" level=info msg="[TCP] 127.0.0.1:58046 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:22.808739600+08:00" level=info msg="[TCP] 127.0.0.1:58049 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:22.817222200+08:00" level=info msg="[TCP] 127.0.0.1:58051 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:25.207556000+08:00" level=info msg="[TCP] 127.0.0.1:58057 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:25.773189200+08:00" level=info msg="[TCP] 127.0.0.1:58055 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:29.883402200+08:00" level=info msg="[TCP] 127.0.0.1:58066 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:31.225428900+08:00" level=info msg="[TCP] 127.0.0.1:58069 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:32.206129500+08:00" level=info msg="[TCP] 127.0.0.1:58072 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:34.422233000+08:00" level=info msg="[TCP] 127.0.0.1:58077 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:37.385220200+08:00" level=info msg="[TCP] 127.0.0.1:58082 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:37.560543400+08:00" level=info msg="[TCP] 127.0.0.1:58085 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:38.339954200+08:00" level=info msg="[TCP] 127.0.0.1:58088 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:38.590408300+08:00" level=info msg="[TCP] 127.0.0.1:58090 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:38.672599000+08:00" level=info msg="[TCP] 127.0.0.1:58092 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:40.601731900+08:00" level=info msg="[TCP] 127.0.0.1:58097 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:41.237457000+08:00" level=info msg="[TCP] 127.0.0.1:58099 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:41.908836700+08:00" level=info msg="[TCP] 127.0.0.1:58103 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:44.179189900+08:00" level=info msg="[TCP] 127.0.0.1:58108 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:21:44.180271800+08:00" level=info msg="[TCP] 127.0.0.1:58107 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:22:41.180310300+08:00" level=info msg="[TCP] 127.0.0.1:58174 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:04.400031400+08:00" level=info msg="[TCP] 127.0.0.1:58197 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:09.861431000+08:00" level=info msg="[TCP] 127.0.0.1:58202 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:10.132878300+08:00" level=info msg="[TCP] 127.0.0.1:58204 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:10.184958900+08:00" level=info msg="[TCP] 127.0.0.1:58206 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:11.089784000+08:00" level=info msg="[TCP] 127.0.0.1:58209 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:12.411779200+08:00" level=info msg="[TCP] 127.0.0.1:58212 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:13.375997700+08:00" level=info msg="[TCP] 127.0.0.1:58215 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:14.349419300+08:00" level=info msg="[TCP] 127.0.0.1:58217 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:14.387146200+08:00" level=info msg="[TCP] 127.0.0.1:58219 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:16.364703300+08:00" level=info msg="[TCP] 127.0.0.1:58223 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:17.430687000+08:00" level=info msg="[TCP] 127.0.0.1:58225 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:29.571324200+08:00" level=info msg="[TCP] 127.0.0.1:58306 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:29.659157800+08:00" level=info msg="[TCP] 127.0.0.1:58308 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:29.839884400+08:00" level=info msg="[TCP] 127.0.0.1:58310 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:34.532438200+08:00" level=info msg="[TCP] 127.0.0.1:58316 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:34.534462800+08:00" level=info msg="[TCP] 127.0.0.1:58315 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:37.535657100+08:00" level=info msg="[TCP] 127.0.0.1:58321 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:37.564939200+08:00" level=info msg="[TCP] 127.0.0.1:58323 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:37.660960900+08:00" level=info msg="[TCP] 127.0.0.1:58327 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:37.663376300+08:00" level=info msg="[TCP] 127.0.0.1:58326 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:39.133309500+08:00" level=info msg="[TCP] 127.0.0.1:58330 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:39.137123700+08:00" level=info msg="[TCP] 127.0.0.1:58331 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:40.613747500+08:00" level=info msg="[TCP] 127.0.0.1:58336 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:23:52.875584100+08:00" level=info msg="[TCP] 127.0.0.1:58356 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:24:37.570234400+08:00" level=info msg="[TCP] 127.0.0.1:58395 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:24:40.601710800+08:00" level=info msg="[TCP] 127.0.0.1:58399 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:25:10.100603500+08:00" level=info msg="[TCP] 127.0.0.1:58433 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:25:40.601589100+08:00" level=info msg="[TCP] 127.0.0.1:58461 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:26:40.613342900+08:00" level=info msg="[TCP] 127.0.0.1:58510 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:27:40.603340800+08:00" level=info msg="[TCP] 127.0.0.1:58560 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:28:37.558086300+08:00" level=info msg="[TCP] 127.0.0.1:58602 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:28:37.672671500+08:00" level=info msg="[TCP] 127.0.0.1:58605 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:28:37.675035200+08:00" level=info msg="[TCP] 127.0.0.1:58604 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:28:39.134351200+08:00" level=info msg="[TCP] 127.0.0.1:58611 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:28:39.136350300+08:00" level=info msg="[TCP] 127.0.0.1:58610 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:28:40.603130200+08:00" level=info msg="[TCP] 127.0.0.1:58614 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:28:43.731274600+08:00" level=info msg="[TCP] 127.0.0.1:58618 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:28:44.799642600+08:00" level=info msg="[TCP] 127.0.0.1:58621 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:29:41.615861300+08:00" level=info msg="[TCP] 127.0.0.1:58671 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:30:10.106764600+08:00" level=info msg="[TCP] 127.0.0.1:58694 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:30:40.594241300+08:00" level=info msg="[TCP] 127.0.0.1:58717 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:31:40.605914300+08:00" level=info msg="[TCP] 127.0.0.1:58772 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:32:40.595788300+08:00" level=info msg="[TCP] 127.0.0.1:58816 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:33:37.550412300+08:00" level=info msg="[TCP] 127.0.0.1:58858 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:33:37.561004500+08:00" level=info msg="[TCP] 127.0.0.1:58860 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:33:37.665282000+08:00" level=info msg="[TCP] 127.0.0.1:58863 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:33:37.667233500+08:00" level=info msg="[TCP] 127.0.0.1:58862 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:33:39.129503000+08:00" level=info msg="[TCP] 127.0.0.1:58867 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:33:39.130975500+08:00" level=info msg="[TCP] 127.0.0.1:58868 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:33:40.609698300+08:00" level=info msg="[TCP] 127.0.0.1:58872 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:33:40.864617100+08:00" level=info msg="[TCP] 127.0.0.1:58874 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-11T00:33:43.324312300+08:00" level=info msg="[TCP] 127.0.0.1:58878 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:33:44.503076500+08:00" level=info msg="[TCP] 127.0.0.1:58880 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:33:52.873128700+08:00" level=info msg="[TCP] 127.0.0.1:58888 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:34:03.716238000+08:00" level=info msg="[TCP] 127.0.0.1:58898 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:34:40.599937600+08:00" level=info msg="[TCP] 127.0.0.1:58924 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:35:10.121313800+08:00" level=info msg="[TCP] 127.0.0.1:58949 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:35:40.605168100+08:00" level=info msg="[TCP] 127.0.0.1:58979 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:36:40.608909000+08:00" level=info msg="[TCP] 127.0.0.1:59025 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:37:40.601006000+08:00" level=info msg="[TCP] 127.0.0.1:59070 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-11T00:38:39.011391400+08:00" level=warning msg="Mihomo shutting down"
