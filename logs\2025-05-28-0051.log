2025-05-28 00:51:34 INFO - try to run core in service mode
2025-05-28 00:51:34 INFO - start service: {"core_type": "verge-mihomo-alpha", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-05-28-0051.log"}
2025-05-28 00:51:34 INFO - Initializing hotkeys, global hotkey enabled: true
2025-05-28 00:51:34 INFO - No hotkeys configured
2025-05-28 00:51:34 INFO - Starting to create window
2025-05-28 00:51:34 INFO - Creating new window
2025-05-28 00:51:35 INFO - Window created successfully, attempting to show
2025-05-28 00:51:35 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:51:35 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:56:15 INFO - Starting to create window
2025-05-28 00:56:15 INFO - Found existing window, trying to show it
2025-05-28 00:56:15 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:56:15 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:56:28 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:56:28 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 01:36:47 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 01:36:47 INFO - Successfully registered hotkey Control+Q for quit
