2025-07-18 18:50:12 INFO - try to run core in service mode
2025-07-18 18:50:12 INFO - start service: {"core_type": "verge-mihomo-alpha", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-18-1850.log", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml"}
2025-07-18 18:50:12 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-18 18:50:12 INFO - No hotkeys configured
2025-07-18 18:50:12 INFO - Starting to create window
2025-07-18 18:50:12 INFO - Creating new window
2025-07-18 18:50:13 INFO - Window created successfully, attempting to show
2025-07-18 18:50:13 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:50:13 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:50:23 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:50:23 INFO - Successfully registered hotkey Control+Q for quit
