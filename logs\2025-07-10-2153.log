2025-07-10 21:53:33 INFO - try to run core in service mode
2025-07-10 21:53:33 INFO - start service: {"config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-10-2153.log", "core_type": "verge-mihomo-alpha", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml"}
2025-07-10 21:53:33 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-10 21:53:33 INFO - No hotkeys configured
2025-07-10 21:53:33 INFO - Starting to create window
2025-07-10 21:53:33 INFO - Creating new window
2025-07-10 21:53:34 INFO - Window created successfully, attempting to show
2025-07-10 21:53:34 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-10 21:53:34 INFO - Successfully registered hotkey Control+Q for quit
2025-07-10 21:53:34 INFO - running timer task `R3SfPp0qApId`
