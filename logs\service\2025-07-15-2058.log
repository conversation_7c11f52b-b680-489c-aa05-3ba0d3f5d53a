Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-15T20:58:20.390937600+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-15T20:58:20.400772700+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-15T20:58:20.400772700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-15T20:58:20.401794400+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-15T20:58:20.426146800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-15T20:58:20.426657400+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-15T20:58:20.743113700+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-15T20:58:20.743113700+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-15T20:58:20.762872500+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-15T20:58:20.765489300+08:00" level=info msg="Initial configuration complete, total time: 367ms"
time="2025-07-15T20:58:20.767023000+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-15T20:58:20.767532800+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-15T20:58:20.768568600+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-15T20:58:20.768568600+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-15T20:58:20.768568600+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider telegram"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Line"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider BBC"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Discord"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider TVB"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider TVer"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Disney"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider google"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider NowE"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Lan"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider apple"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-15T20:58:20.774691800+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-15T20:58:22.392515500+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-15T20:58:22.393030600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-15T20:58:22.393030600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-15T20:58:22.393539100+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-15T20:58:22.393539100+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-15T20:58:22.394046600+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-15T20:58:22.395662800+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-15T20:58:25.777352300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777352300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777352300+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-15T20:58:25.777352300+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-15T20:58:25.777954900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777954900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777954900+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-15T20:58:25.777954900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777954900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777954900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777954900+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-15T20:58:25.777954900+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-15T20:58:25.777954900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777954900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777954900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777954900+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-15T20:58:25.777954900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.777954900+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-15T20:58:25.777954900+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-15T20:58:25.777954900+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-15T20:58:25.777954900+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-15T20:58:25.777954900+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-15T20:58:25.778465200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778465200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778465200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778465200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778465200+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-15T20:58:25.778465200+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-15T20:58:25.778465200+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-15T20:58:25.778465200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778465200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778465200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778465200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778465200+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-15T20:58:25.778465200+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-15T20:58:25.778465200+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-15T20:58:25.778465200+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-15T20:58:25.778465200+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-15T20:58:25.779478900+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-15T20:58:25.778976200+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-15T20:58:25.779487900+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-15T20:58:25.783074500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-15T20:58:25.783074500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-15T20:58:25.783074500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-15T20:58:25.783074500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Discord"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider NowE"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Disney"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Line"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider apple"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider BBC"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider TVer"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider google"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider telegram"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider Lan"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider TVB"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-15T20:58:25.785122400+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-15T20:58:30.785555000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.785555000+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786572500+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-15T20:58:30.787088400+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.787088400+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.787088400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-15T20:58:30.787088400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-15T20:58:30.787088400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-15T20:58:30.786576400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:30.787088400+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-15T20:58:30.787088400+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-15T20:58:30.787088400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-15T20:58:30.787088400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-15T20:58:30.786066400+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-15T20:58:30.789136200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-15T20:58:30.789136200+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-15T20:58:30.789136200+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-15T20:58:30.789649400+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-15T20:58:31.129451200+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-15T20:58:31.129962900+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-15T20:58:31.129962900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-15T20:58:31.130476600+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-15T20:58:31.130986000+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-15T20:58:31.130986000+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-15T20:58:31.132005800+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider TVB"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider TVer"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Lan"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Discord"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider google"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider NowE"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider BBC"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Line"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider apple"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider telegram"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Disney"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-15T20:58:31.134050300+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-15T20:58:36.135574700+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-15T20:58:36.136091100+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-15T20:58:36.135581400+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.136091100+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-15T20:58:36.136091100+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-15T20:58:36.136091100+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-15T20:58:36.135071400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.134909900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:58:36.136091100+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-15T20:58:36.136091100+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-15T20:58:36.140800800+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-15T20:58:36.140800800+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-15T20:58:36.140800800+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-15T20:58:36.140800800+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-15T20:59:17.638148000+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-15T20:59:17.638660600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-15T20:59:17.638660600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-15T20:59:17.639188600+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-15T20:59:17.639188600+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-15T20:59:17.639743900+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-15T20:59:17.640768600+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-15T20:59:17.642806400+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider BBC"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Disney"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider TVB"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider NowE"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider google"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider TVer"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Line"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider telegram"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Lan"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider apple"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider Discord"
time="2025-07-15T20:59:17.643318600+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.643907900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:22.644908100+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-15T20:59:22.643907900+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-15T20:59:22.645909200+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-15T20:59:22.649495100+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-15T20:59:22.649495100+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-15T20:59:22.649495100+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-15T20:59:22.649495100+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-15T20:59:29.172877300+08:00" level=error msg="🇹🇷土耳其01 [2x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-15T20:59:29.172877300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-15T20:59:32.091059200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56450 --> marketplace.cursorapi.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T20:59:32.146295700+08:00" level=info msg="[TCP] 127.0.0.1:56469 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T20:59:35.297781200+08:00" level=info msg="[TCP] 127.0.0.1:56478 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T20:59:37.164171300+08:00" level=info msg="[TCP] 127.0.0.1:56487 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T20:59:38.942700900+08:00" level=info msg="[TCP] 127.0.0.1:56493 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T20:59:39.860490200+08:00" level=info msg="[TCP] 127.0.0.1:56499 --> api.steampowered.com:443 using GLOBAL"
time="2025-07-15T20:59:42.466716200+08:00" level=info msg="[TCP] 127.0.0.1:56507 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T20:59:43.967261000+08:00" level=info msg="[TCP] 127.0.0.1:56515 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-15T20:59:44.809725700+08:00" level=info msg="[TCP] 127.0.0.1:56520 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T20:59:45.055259200+08:00" level=info msg="[TCP] 127.0.0.1:56523 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T20:59:46.653469000+08:00" level=info msg="[TCP] 127.0.0.1:56530 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T20:59:58.168518600+08:00" level=info msg="[TCP] 127.0.0.1:56584 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T20:59:58.399011100+08:00" level=info msg="[TCP] 127.0.0.1:56587 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-15T21:00:02.184855100+08:00" level=info msg="[TCP] 127.0.0.1:56611 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T21:00:18.178811100+08:00" level=info msg="[TCP] 127.0.0.1:56686 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-15T21:00:38.436552100+08:00" level=info msg="[TCP] 127.0.0.1:56748 --> ms-python.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:00:38.440430100+08:00" level=info msg="[TCP] 127.0.0.1:56751 --> w88975.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:00:38.440963800+08:00" level=info msg="[TCP] 127.0.0.1:56754 --> augment.gallerycdn.azure.cn:443 using GLOBAL"
time="2025-07-15T21:00:38.503177100+08:00" level=info msg="[TCP] 127.0.0.1:56757 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:00:38.819656800+08:00" level=info msg="[TCP] 127.0.0.1:56760 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:00:38.819656800+08:00" level=info msg="[TCP] 127.0.0.1:56761 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:00:38.820758800+08:00" level=info msg="[TCP] 127.0.0.1:56764 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:00:38.820758800+08:00" level=info msg="[TCP] 127.0.0.1:56762 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:00:38.822160100+08:00" level=info msg="[TCP] 127.0.0.1:56763 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:00:40.297358700+08:00" level=info msg="[TCP] 127.0.0.1:56779 --> redis.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:00:41.434827600+08:00" level=info msg="[TCP] 127.0.0.1:56788 --> augment.gallerycdn.azure.cn:443 using GLOBAL"
time="2025-07-15T21:00:42.768038300+08:00" level=info msg="[TCP] 127.0.0.1:56791 --> augment-assets.com:443 using GLOBAL"
time="2025-07-15T21:00:44.585031600+08:00" level=info msg="[TCP] 127.0.0.1:56796 --> w88975.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:00:46.509476300+08:00" level=info msg="[TCP] 127.0.0.1:56803 --> redis.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:00:50.733567900+08:00" level=info msg="[TCP] 127.0.0.1:56815 --> w88975.gallery.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:00:51.555758600+08:00" level=info msg="[TCP] 127.0.0.1:56818 --> w88975.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:01:01.163043900+08:00" level=info msg="[TCP] 127.0.0.1:56850 --> www.vscode-unpkg.net:443 using GLOBAL"
time="2025-07-15T21:01:01.504395100+08:00" level=info msg="[TCP] 127.0.0.1:56856 --> augment.gallery.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:01:01.517420500+08:00" level=info msg="[TCP] 127.0.0.1:56859 --> augment.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:01:01.638117400+08:00" level=info msg="[TCP] 127.0.0.1:56862 --> augment.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:01:02.510759000+08:00" level=info msg="[TCP] 127.0.0.1:56869 --> augment.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:01:03.026727200+08:00" level=info msg="[TCP] 127.0.0.1:56872 --> augment.gallerycdn.vsassets.io:443 using GLOBAL"
time="2025-07-15T21:01:21.177197400+08:00" level=info msg="[TCP] 127.0.0.1:56904 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T21:01:25.546954900+08:00" level=info msg="[TCP] 127.0.0.1:56912 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T21:01:44.671317400+08:00" level=info msg="[TCP] 127.0.0.1:56937 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T21:01:45.174966100+08:00" level=info msg="[TCP] 127.0.0.1:56941 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T21:01:47.033022100+08:00" level=info msg="[TCP] 127.0.0.1:56945 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T21:01:47.463282400+08:00" level=info msg="[TCP] 127.0.0.1:56950 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-15T21:01:50.652893200+08:00" level=info msg="[TCP] 127.0.0.1:56956 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-15T21:01:51.058697500+08:00" level=info msg="[TCP] 127.0.0.1:56959 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T21:01:53.184610800+08:00" level=info msg="[TCP] 127.0.0.1:56971 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-15T21:01:53.666469500+08:00" level=info msg="[TCP] 127.0.0.1:56976 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-15T21:01:54.090873000+08:00" level=info msg="[TCP] 127.0.0.1:56981 --> update.code.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:01:55.185964800+08:00" level=info msg="[TCP] 127.0.0.1:56987 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-15T21:01:55.378836100+08:00" level=info msg="[TCP] 127.0.0.1:56990 --> news.baidu.com:443 using GLOBAL"
time="2025-07-15T21:01:55.382303500+08:00" level=info msg="[TCP] 127.0.0.1:56991 --> news.baidu.com:443 using GLOBAL"
time="2025-07-15T21:01:55.424053200+08:00" level=info msg="[TCP] 127.0.0.1:56996 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:01:55.425129700+08:00" level=info msg="[TCP] 127.0.0.1:56997 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-15T21:01:56.486082700+08:00" level=info msg="[TCP] 127.0.0.1:57004 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:01:56.737354000+08:00" level=info msg="[TCP] 127.0.0.1:57007 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-15T21:01:56.790358100+08:00" level=info msg="[TCP] 127.0.0.1:57013 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:01:56.790358100+08:00" level=info msg="[TCP] 127.0.0.1:57012 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:01:56.791372300+08:00" level=info msg="[TCP] 127.0.0.1:57014 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:01:56.791372300+08:00" level=info msg="[TCP] 127.0.0.1:57011 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:01:56.791879600+08:00" level=info msg="[TCP] 127.0.0.1:57010 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:01:56.919748600+08:00" level=info msg="[TCP] 127.0.0.1:57025 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-15T21:01:57.158152500+08:00" level=info msg="[TCP] 127.0.0.1:57028 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:01:57.382674600+08:00" level=info msg="[TCP] 127.0.0.1:57032 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:01:57.477859700+08:00" level=info msg="[TCP] 127.0.0.1:57036 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:01:57.478860600+08:00" level=info msg="[TCP] 127.0.0.1:57038 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:01:57.478860600+08:00" level=info msg="[TCP] 127.0.0.1:57037 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:01:57.624967000+08:00" level=info msg="[TCP] 127.0.0.1:57045 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-15T21:01:57.748480400+08:00" level=info msg="[TCP] 127.0.0.1:57048 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:01:57.771249400+08:00" level=info msg="[TCP] 127.0.0.1:57051 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T21:01:58.012251500+08:00" level=info msg="[TCP] 127.0.0.1:57054 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:01:58.323926100+08:00" level=info msg="[TCP] 127.0.0.1:57057 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T21:01:58.472876100+08:00" level=info msg="[TCP] 127.0.0.1:57060 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T21:01:59.230235300+08:00" level=info msg="[TCP] 127.0.0.1:57063 --> internal-api-lark-api.feishu.cn:443 using GLOBAL"
time="2025-07-15T21:02:00.896798200+08:00" level=info msg="[TCP] 127.0.0.1:57074 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:02.199999900+08:00" level=info msg="[TCP] 127.0.0.1:57081 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-15T21:02:03.333006900+08:00" level=info msg="[TCP] 127.0.0.1:57085 --> news.baidu.com:443 using GLOBAL"
time="2025-07-15T21:02:04.837966800+08:00" level=info msg="[TCP] 127.0.0.1:57089 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T21:02:05.130070700+08:00" level=info msg="[TCP] 127.0.0.1:57092 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:06.856393500+08:00" level=info msg="[TCP] 127.0.0.1:57097 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:06.856393500+08:00" level=info msg="[TCP] 127.0.0.1:57099 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:06.856393500+08:00" level=info msg="[TCP] 127.0.0.1:57098 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:06.862648300+08:00" level=info msg="[TCP] 127.0.0.1:57100 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:06.987737700+08:00" level=info msg="[TCP] 127.0.0.1:57109 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:07.139707800+08:00" level=info msg="[TCP] 127.0.0.1:57112 --> api.segment.io:443 using GLOBAL"
time="2025-07-15T21:02:07.433936100+08:00" level=info msg="[TCP] 127.0.0.1:57115 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:07.488916800+08:00" level=info msg="[TCP] 127.0.0.1:57118 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:07.604130700+08:00" level=info msg="[TCP] 127.0.0.1:57121 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:07.738549800+08:00" level=info msg="[TCP] 127.0.0.1:57124 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T21:02:09.976071200+08:00" level=info msg="[TCP] 127.0.0.1:57129 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-15T21:02:10.028343400+08:00" level=info msg="[TCP] 127.0.0.1:57132 --> d8.api.augmentcode.com:443 using GLOBAL"
time="2025-07-15T21:02:11.504611200+08:00" level=info msg="[TCP] 127.0.0.1:57138 --> news.baidu.com:443 using GLOBAL"
time="2025-07-15T21:02:12.303960700+08:00" level=info msg="[TCP] 127.0.0.1:57149 --> news.baidu.com:443 using GLOBAL"
time="2025-07-15T21:02:17.057568000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57144 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:17.057648000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57151 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:17.057648000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57163 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:17.057648000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57158 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:17.057648000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57165 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:17.057648000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57150 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:17.057648000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57166 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:17.057648000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57157 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:17.057648000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57156 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:17.137158600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57153 --> api.segment.io:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:20.290043000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57161 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57177 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57178 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57175 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57174 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57188 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57179 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57189 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57176 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57187 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57186 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.058358800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57190 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:22.250387300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57180 --> api.segment.io:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57200 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57210 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57222 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57204 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57202 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57212 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57201 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57214 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57213 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57203 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57211 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.058611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57217 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:27.483082300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57205 --> api.segment.io:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058680200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57230 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57238 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57232 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57236 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57229 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57227 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57231 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57239 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57228 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57233 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57235 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:32.058856300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57237 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:37.059005100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57250 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:37.059005100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57257 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:37.059005100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57258 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:37.059005100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57249 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:37.059005100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57253 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:37.059005100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57256 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:37.059005100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57247 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:37.059005100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57248 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:37.059005100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57254 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:37.114937500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57246 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:42.059247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57266 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:42.059247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57269 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:42.059247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57268 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:42.059247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57267 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:42.059247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57280 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:42.059247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57275 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:42.059247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57281 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:42.059247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57279 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:42.059247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57276 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:42.059247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57270 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:47.059499500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57297 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:47.059499500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57289 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:47.059499500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57290 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:47.059499500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57291 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:47.059499500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57293 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:47.059499500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57296 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:47.059499500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57287 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:47.059499500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57288 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:52.059591600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57309 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:52.059591600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57308 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:52.059591600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57305 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:52.059591600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57307 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:52.059591600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57304 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:52.059591600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57303 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:57.060028400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57323 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:57.060028400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57318 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:57.060028400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57330 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:57.060028400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57317 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:57.060028400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57324 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:02:57.060028400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57316 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:02.060070600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57337 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:02.060070600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57335 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:02.060070600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57336 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:07.060616400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57370 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:07.060616400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57368 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:07.060616400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57369 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:12.060646000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57380 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:12.060646000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57379 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:12.060646000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57385 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:12.060646000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57386 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:12.060646000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57381 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:17.061308200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57394 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:17.061308200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57395 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:22.062029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57408 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:27.062607900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57428 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:27.062607900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57416 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:32.062789000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57433 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:37.063397100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57455 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:37.063397100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57452 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:37.063397100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57454 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:42.064066000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57471 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:42.064066000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57469 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:42.064066000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57467 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:42.064066000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57468 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:42.064066000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57472 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:47.064191500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57498 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:03:47.064191500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57497 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:01.753076500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57593 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:01.753076500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57599 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:06.753962600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57617 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:11.754012700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57630 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:11.754012700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57628 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:11.754012700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57629 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:16.754285000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57642 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:16.754285000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57638 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:16.754285000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57639 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:16.754285000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57640 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:16.754285000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57641 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:21.754438600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57651 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:21.754438600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57650 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:26.754497200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57663 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:31.754525300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57670 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:31.754525300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57669 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:36.755009800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57678 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:46.755871700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57695 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:46.755871700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57696 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:46.755871700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57694 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:51.756744200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57708 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:51.756744200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57707 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:51.756744200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57703 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:51.756744200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57704 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:51.756744200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57705 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:56.757318500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57717 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:04:56.757318500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57716 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:01.757969700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57733 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:06.758883600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57745 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:22.350737300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57765 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:22.350737300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57766 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:22.350737300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57764 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:22.350737300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57775 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:22.350737300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57776 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:27.351810900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57789 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:27.351810900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57783 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:27.351810900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57782 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:27.351810900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57784 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:27.351810900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57781 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:27.351810900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57785 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:32.351955600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57794 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:37.352853000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57806 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:57.867536300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57827 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:57.867636600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57835 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:57.867636600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57834 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:57.867636600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57833 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:57.867636600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57832 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:05:57.867636600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57840 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:02.867750600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57847 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:02.867750600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57851 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:02.867750600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57846 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:02.867750600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57850 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:02.867750600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57849 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:02.867750600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57848 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:31.788954500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57884 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:31.789064300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57891 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:31.789064300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57890 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:31.789064300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57894 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:31.789064300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57892 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:31.789064300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57893 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:36.789509200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57902 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:36.789509200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57904 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:36.789509200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57905 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:36.789509200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57907 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:36.789509200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57906 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:06:36.789509200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57903 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:01.789722400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57938 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:06.796798500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57949 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:06.796798500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57957 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:06.796798500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57961 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:06.796798500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57959 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:06.796798500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57960 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:06.796798500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57958 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:11.797298300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57968 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:11.797298300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57972 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:11.797298300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57971 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:11.797298300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57970 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:11.797298300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57969 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:16.798034600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57984 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:26.798648800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58001 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:31.798785700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58006 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:41.800204600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58030 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:41.800204600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58028 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:41.800204600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58026 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:41.800204600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58029 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:41.800204600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58027 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:46.800341500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58040 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:46.800341500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58043 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:46.800341500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58039 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:46.800341500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58042 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:07:46.800341500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58041 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:01.788215100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58094 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:06.925720100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58118 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:17.406193300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58136 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:17.406193300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58138 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:17.406193300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58137 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:17.406193300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58144 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:17.406193300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58143 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:22.407088600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58154 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:22.407088600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58153 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:22.407088600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58152 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:22.407088600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58155 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:22.407088600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58156 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:27.407290600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58167 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:32.407622800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58172 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:53.103869100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58240 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:53.103869100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58241 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:53.103869100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58243 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:53.103869100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58242 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:53.103869100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58244 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:58.136275100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58270 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:58.136374600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58274 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:58.136374600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58275 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:58.136374600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58272 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:58.136374600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58273 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:58.136374600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58296 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:08:58.136374600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58298 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:03.136508800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58308 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:28.615589600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58381 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:28.615589600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58386 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:28.615589600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58387 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:28.615589600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58389 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:28.615589600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58388 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:28.615589600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58392 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:33.616114400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58404 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:33.616114400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58400 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:33.616114400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58399 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:33.616114400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58403 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:33.616114400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58401 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:09:33.616114400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58402 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:01.787153300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58443 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:01.787153300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58457 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:01.787153300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58461 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:01.787153300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58459 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:01.787153300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58458 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:01.787153300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58460 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:06.787341000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58486 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:06.787341000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58483 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:06.787341000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58482 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:06.787341000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58481 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:06.787341000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58484 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:06.787341000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58485 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:31.788006300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58584 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:36.794449300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58616 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:36.794449300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58628 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:36.794449300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58627 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:36.794449300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58626 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:36.794449300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58641 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:36.794449300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58624 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:36.794449300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58625 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:41.795018300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58650 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:41.795018300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58646 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:41.795018300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58648 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:41.795018300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58647 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:10:41.795018300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58649 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:01.788945600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58740 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:06.796351100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58774 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:12.330114600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58798 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:12.330114600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58799 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:12.330215400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58804 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:12.330114600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58797 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:12.330215400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58805 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:17.330348900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58815 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:17.330348900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58818 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:17.330348900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58814 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:17.330348900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58816 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:17.330348900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58817 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:31.791019700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58839 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:36.799311400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58849 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:47.878425900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58867 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:47.878425900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58866 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:47.878425900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58868 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:47.878684700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58869 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:47.878684700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58874 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:52.879701900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58887 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:52.879701900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58884 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:52.879701900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58885 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:52.879701900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58886 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:52.879701900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58883 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:11:57.880240800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58904 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:02.880377500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58911 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:21.896915700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58944 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:21.896915700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58955 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:21.896915700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58956 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:21.896915700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58957 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:21.896915700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58954 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:21.896915700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58953 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:26.897108600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58965 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:26.897108600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58968 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:26.897108600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58964 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:26.897108600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58967 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:26.897108600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58966 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:31.897613700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58977 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:36.898113300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58986 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:57.427947500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59055 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:57.427947500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59056 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:57.427947500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59057 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:57.427947500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59062 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:57.427947500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59063 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:12:57.427947500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59086 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:02.428971000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59097 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:02.428971000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59092 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:02.428971000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59094 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:02.428971000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59093 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:02.428971000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59096 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:02.428971000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59095 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:31.796840400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59252 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:31.796945700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59264 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:31.796945700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59263 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:31.796945700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59267 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:31.796945700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59262 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:31.796945700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59261 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:36.797037600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59288 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:36.797037600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59287 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:36.797555300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59286 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:36.797555300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59285 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:36.797555300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59283 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:13:36.797555300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59284 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:01.788294500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59401 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:01.788380500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59406 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:06.789418600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59417 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:06.789418600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59418 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:06.789418600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59416 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:06.789418600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59420 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:06.789418600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59419 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:06.789418600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59415 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:11.790397300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59436 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:11.790397300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59435 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:11.790397300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59433 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:11.790397300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59434 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:11.790397300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59432 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:31.790644900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59501 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:36.796596200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59518 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:42.401317500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59545 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:42.401413800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59542 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:42.401317500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59541 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:42.401317500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59544 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:42.401413800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59543 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:47.424249900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59569 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:47.424249900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59568 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:47.424249900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59565 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:47.424249900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59567 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:14:47.424249900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59566 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:01.800533200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59611 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:06.806691300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59629 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:17.915598900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59669 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:17.915707000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59674 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:17.915707000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59677 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:17.915707000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59676 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:17.915707000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59675 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:22.915734800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59700 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:22.915734800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59701 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:22.915734800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59702 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:22.915734800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59704 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:22.915734800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59703 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:27.916233100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59741 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:32.916283400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59750 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:37.916853000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59792 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:53.460042000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59836 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:53.460042000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59835 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:53.460151500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59834 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:53.460151500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59838 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:53.460151500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59837 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:58.461233800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59878 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:58.461233800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59877 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:58.461233800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59874 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:58.461233800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59876 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:58.461233800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59896 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:15:58.461233800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59875 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:03.461257100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59907 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:28.988798100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59972 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:28.988798100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59971 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:28.988798100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59977 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:28.988798100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59978 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:28.988798100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59979 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:28.988798100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59982 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:33.988821100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59991 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:33.988821100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59996 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:33.988821100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59993 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:33.988821100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59992 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:33.988821100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59995 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:16:33.988821100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59994 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:01.790871700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60043 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:01.790999800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60054 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:01.790999800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60053 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:01.790999800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60057 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:01.790999800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60055 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:01.790999800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60056 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:06.791480800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60069 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:06.791480800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60066 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:06.791480800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60064 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:06.791480800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60065 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:06.791480800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60067 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:06.791480800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60068 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:21.923017300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60086 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:31.790131700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60098 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:36.796169100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60107 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:36.796169100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60115 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:36.796169100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60116 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:36.796169100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60117 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:36.796169100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60114 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:36.796169100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60113 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:41.796228000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60130 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:41.796228000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60129 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:41.796228000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60127 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:41.796228000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60128 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:17:41.796228000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60126 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:01.790275700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60152 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:06.796759900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60165 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:12.320696100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60178 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:12.320696100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60184 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:12.320696100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60183 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:12.320696100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60185 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:12.320696100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60186 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:17.321084200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60199 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:17.321084200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60197 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:17.321084200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60198 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:17.321084200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60196 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:17.321084200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60195 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:31.791563800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60215 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:36.797232200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60223 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:47.847826700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60244 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:47.847826700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60243 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:47.847826700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60250 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:47.847826700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60251 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:47.847826700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60249 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:52.849625200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60289 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:52.849742500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60290 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:52.849625200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60288 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:52.849625200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60291 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:52.849625200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60287 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:57.849722800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60317 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:18:57.849722800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60316 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:02.850197600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60323 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:23.425925000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60355 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:23.425925000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60356 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:23.425925000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60353 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:23.426039900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60354 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:23.426039900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60352 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:28.448385900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60370 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:28.448467300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60367 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:28.448467300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60369 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:28.448467300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60377 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:28.448467300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60366 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:28.448467300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60368 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:33.449476400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60383 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:58.959192600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60418 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:58.959192600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60417 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:58.959311400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60419 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:58.959192600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60416 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:58.959311400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60424 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:19:58.959311400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60431 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:03.959582400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60444 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:03.959582400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60441 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:03.959582400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60442 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:03.959582400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60439 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:03.959582400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60443 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:03.959582400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60440 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:31.795521800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60476 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:31.796522600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60483 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:31.796522600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60484 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:31.796522600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60487 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:31.796522600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60485 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:31.796522600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60486 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:36.796790300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60500 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:36.796790300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60497 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:36.796790300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60499 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:36.796790300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60494 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:36.796790300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60496 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:36.796790300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60495 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:20:41.798447500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60508 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:01.825233000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60537 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:06.831441900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60552 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:06.831441900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60560 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:06.831441900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60561 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:06.831441900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60559 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:06.831441900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60562 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:06.831441900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60558 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:08.693956200+08:00" level=info msg="[TCP] 127.0.0.1:60592 --> news.baidu.com:443 using GLOBAL"
time="2025-07-15T21:21:11.352976200+08:00" level=info msg="[TCP] 127.0.0.1:60597 --> news.baidu.com:443 using GLOBAL"
time="2025-07-15T21:21:11.832119600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60588 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:11.832119600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60587 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:11.832119600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60590 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:11.832119600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60591 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:11.832119600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60589 --> d8.api.augmentcode.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:14.966232700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60596 --> augment.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-15T21:21:14.966232700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60600 --> augment.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-15T23:26:51.155527200+08:00" level=info msg="[TCP] 127.0.0.1:64467 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:26:54.814277900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64459 --> marketplace.cursorapi.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T23:26:56.919848100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64470 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T23:26:57.262062900+08:00" level=info msg="[TCP] 127.0.0.1:64696 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:26:58.325063400+08:00" level=info msg="[TCP] 127.0.0.1:64705 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:26:58.458204100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64662 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T23:26:58.511448500+08:00" level=info msg="[TCP] 127.0.0.1:64708 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T23:26:58.625526000+08:00" level=info msg="[TCP] 127.0.0.1:64711 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:26:59.815307100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64684 --> marketplace.cursorapi.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T23:26:59.874269200+08:00" level=info msg="[TCP] 127.0.0.1:64717 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:27:00.054447900+08:00" level=info msg="[TCP] 127.0.0.1:64720 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-15T23:27:01.921070500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64690 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T23:27:02.482409800+08:00" level=info msg="[TCP] 127.0.0.1:64726 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:27:03.127677900+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-15T23:27:03.127677900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-15T23:27:03.128291700+08:00" level=error msg="🇯🇵日本03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-15T23:27:03.128291700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-15T23:27:03.128291700+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-15T23:27:03.128291700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-15T23:27:03.143538500+08:00" level=error msg="🇯🇵日本01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-15T23:27:03.143538500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-15T23:27:03.143538500+08:00" level=error msg="🇯🇵日本04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-15T23:27:03.143538500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-15T23:27:03.484029900+08:00" level=info msg="[TCP] 127.0.0.1:64730 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:27:07.404996200+08:00" level=info msg="[TCP] 127.0.0.1:64736 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-15T23:27:09.708515300+08:00" level=info msg="[TCP] 127.0.0.1:64740 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:27:10.691280500+08:00" level=info msg="[TCP] 127.0.0.1:64746 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T23:27:10.728760200+08:00" level=info msg="[TCP] 127.0.0.1:64749 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:27:11.421914400+08:00" level=info msg="[TCP] 127.0.0.1:64752 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T23:27:12.122044800+08:00" level=info msg="[TCP] 127.0.0.1:64755 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:27:21.179469900+08:00" level=info msg="[TCP] 127.0.0.1:64769 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:27:21.492931300+08:00" level=info msg="[TCP] 127.0.0.1:64774 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:27:21.494019300+08:00" level=info msg="[TCP] 127.0.0.1:64773 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:27:21.663802800+08:00" level=info msg="[TCP] 127.0.0.1:64779 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:27:21.935565700+08:00" level=info msg="[TCP] 127.0.0.1:64782 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:27:22.172849500+08:00" level=info msg="[TCP] 127.0.0.1:64785 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:27:29.184053400+08:00" level=info msg="[TCP] 127.0.0.1:64793 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:27:29.492516400+08:00" level=info msg="[TCP] 127.0.0.1:64796 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:27:35.045117600+08:00" level=info msg="[TCP] 127.0.0.1:64822 --> x1.c.lencr.org:80 using GLOBAL"
time="2025-07-15T23:27:35.659334500+08:00" level=info msg="[TCP] 127.0.0.1:64822 --> c.pki.goog:80 using GLOBAL"
time="2025-07-15T23:27:36.723118200+08:00" level=info msg="[TCP] 127.0.0.1:64822 --> ctldl.windowsupdate.com:80 using GLOBAL"
time="2025-07-15T23:27:53.566780900+08:00" level=info msg="[TCP] 127.0.0.1:64890 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T23:27:57.890543500+08:00" level=info msg="[TCP] 127.0.0.1:64907 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:28:06.389207200+08:00" level=info msg="[TCP] 127.0.0.1:64921 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:28:06.757513500+08:00" level=info msg="[TCP] 127.0.0.1:64924 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:28:07.216501700+08:00" level=info msg="[TCP] 127.0.0.1:64928 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:28:13.539963200+08:00" level=info msg="[TCP] 127.0.0.1:64939 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T23:28:20.873121600+08:00" level=info msg="[TCP] 127.0.0.1:64948 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T23:28:21.168105200+08:00" level=info msg="[TCP] 127.0.0.1:64951 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:28:21.577374100+08:00" level=info msg="[TCP] 127.0.0.1:64954 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:28:22.461509600+08:00" level=info msg="[TCP] 127.0.0.1:64958 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:28:22.783199900+08:00" level=info msg="[TCP] 127.0.0.1:64961 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:28:23.029574000+08:00" level=info msg="[TCP] 127.0.0.1:64964 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:28:23.701978500+08:00" level=info msg="[TCP] 127.0.0.1:64968 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:28:30.176533200+08:00" level=info msg="[TCP] 127.0.0.1:64977 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:28:30.177588300+08:00" level=info msg="[TCP] 127.0.0.1:64983 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:28:30.181741100+08:00" level=info msg="[TCP] 127.0.0.1:64980 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:28:30.713706300+08:00" level=info msg="[TCP] 127.0.0.1:64987 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:28:31.622391400+08:00" level=info msg="[TCP] 127.0.0.1:64991 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:28:31.760847500+08:00" level=info msg="[TCP] 127.0.0.1:64994 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-15T23:28:34.876897900+08:00" level=info msg="[TCP] 127.0.0.1:65001 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:28:37.512371500+08:00" level=info msg="[TCP] 127.0.0.1:65006 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:28:52.173840000+08:00" level=info msg="[TCP] 127.0.0.1:65065 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:28:53.626155400+08:00" level=info msg="[TCP] 127.0.0.1:65071 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T23:28:56.617651100+08:00" level=info msg="[TCP] 127.0.0.1:65079 --> blacklist.tampermonkey.net:443 using GLOBAL"
time="2025-07-15T23:28:57.281744400+08:00" level=info msg="[TCP] 127.0.0.1:65083 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:29:20.418712200+08:00" level=info msg="[TCP] 127.0.0.1:65112 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:29:21.171659400+08:00" level=info msg="[TCP] 127.0.0.1:65119 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:29:21.171659400+08:00" level=info msg="[TCP] 127.0.0.1:65116 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:29:21.370300600+08:00" level=info msg="[TCP] 127.0.0.1:65124 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:29:21.419808400+08:00" level=info msg="[TCP] 127.0.0.1:65127 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:29:21.484793300+08:00" level=info msg="[TCP] 127.0.0.1:65130 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:29:23.784048700+08:00" level=info msg="[TCP] 127.0.0.1:65134 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T23:29:31.017189200+08:00" level=info msg="[TCP] 127.0.0.1:65144 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T23:29:32.462446800+08:00" level=info msg="[TCP] 127.0.0.1:65149 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T23:29:34.564488700+08:00" level=info msg="[TCP] 127.0.0.1:65156 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:29:41.009053200+08:00" level=info msg="[TCP] 127.0.0.1:65165 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-15T23:29:45.168423000+08:00" level=info msg="[TCP] 127.0.0.1:65171 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:29:45.852378900+08:00" level=info msg="[TCP] 127.0.0.1:65175 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:29:48.170583300+08:00" level=info msg="[TCP] 127.0.0.1:65179 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-15T23:29:53.682954300+08:00" level=info msg="[TCP] 127.0.0.1:65190 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T23:29:54.563830200+08:00" level=info msg="[TCP] 127.0.0.1:65195 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:29:54.687740300+08:00" level=info msg="[TCP] 127.0.0.1:65199 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:29:55.755652500+08:00" level=info msg="[TCP] 127.0.0.1:65202 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:30:07.447977900+08:00" level=info msg="[TCP] 127.0.0.1:65223 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:30:12.923600600+08:00" level=info msg="[TCP] 127.0.0.1:65234 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:30:13.256082900+08:00" level=info msg="[TCP] 127.0.0.1:65237 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T23:30:13.263724300+08:00" level=info msg="[TCP] 127.0.0.1:65240 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:30:21.172458100+08:00" level=info msg="[TCP] 127.0.0.1:65257 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:30:21.174929900+08:00" level=info msg="[TCP] 127.0.0.1:65260 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:30:21.475106600+08:00" level=info msg="[TCP] 127.0.0.1:65263 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:30:21.607521400+08:00" level=info msg="[TCP] 127.0.0.1:65266 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:30:23.636260200+08:00" level=info msg="[TCP] 127.0.0.1:65270 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:30:41.942809000+08:00" level=info msg="[TCP] 127.0.0.1:65291 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:30:42.784962900+08:00" level=info msg="[TCP] 127.0.0.1:65295 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:30:50.127226100+08:00" level=info msg="[TCP] 127.0.0.1:65303 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:30:57.285319000+08:00" level=info msg="[TCP] 127.0.0.1:65318 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:30:57.637975100+08:00" level=info msg="[TCP] 127.0.0.1:65322 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:30:57.888878800+08:00" level=info msg="[TCP] 127.0.0.1:65325 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:30:58.596230500+08:00" level=info msg="[TCP] 127.0.0.1:65329 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:30:58.806838000+08:00" level=info msg="[TCP] 127.0.0.1:65332 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:31:07.178169200+08:00" level=info msg="[TCP] 127.0.0.1:65349 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-15T23:31:09.437238200+08:00" level=info msg="[TCP] 127.0.0.1:65353 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-15T23:31:15.932555200+08:00" level=info msg="[TCP] 127.0.0.1:65363 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:31:19.129884300+08:00" level=info msg="[TCP] 127.0.0.1:65377 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T23:31:21.166214000+08:00" level=info msg="[TCP] 127.0.0.1:65382 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:31:24.216647500+08:00" level=info msg="[TCP] 127.0.0.1:65388 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:31:52.073451600+08:00" level=info msg="[TCP] 127.0.0.1:65417 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:31:54.329050500+08:00" level=info msg="[TCP] 127.0.0.1:65427 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:32:01.633856000+08:00" level=info msg="[TCP] 127.0.0.1:65449 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:32:08.970076000+08:00" level=info msg="[TCP] 127.0.0.1:65459 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:32:09.166533700+08:00" level=info msg="[TCP] 127.0.0.1:65462 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:32:16.148445100+08:00" level=info msg="[TCP] 127.0.0.1:65473 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-15T23:32:16.394942100+08:00" level=info msg="[TCP] 127.0.0.1:65476 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:32:17.094708300+08:00" level=info msg="[TCP] 127.0.0.1:65480 --> downloads.cursor.com:443 using GLOBAL"
time="2025-07-15T23:32:21.176889700+08:00" level=info msg="[TCP] 127.0.0.1:65488 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:32:21.179574900+08:00" level=info msg="[TCP] 127.0.0.1:65491 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:32:21.193300600+08:00" level=info msg="[TCP] 127.0.0.1:65495 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:32:21.494132800+08:00" level=info msg="[TCP] 127.0.0.1:65498 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:32:21.496551200+08:00" level=info msg="[TCP] 127.0.0.1:65499 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:32:21.811735300+08:00" level=info msg="[TCP] 127.0.0.1:65504 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:32:30.173811800+08:00" level=info msg="[TCP] 127.0.0.1:65513 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:32:53.857825300+08:00" level=info msg="[TCP] 127.0.0.1:49169 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T23:32:59.008686700+08:00" level=info msg="[TCP] 127.0.0.1:49180 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:33:07.071399500+08:00" level=info msg="[TCP] 127.0.0.1:49212 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-15T23:33:13.963978400+08:00" level=info msg="[TCP] 127.0.0.1:49234 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:33:14.259157000+08:00" level=info msg="[TCP] 127.0.0.1:49238 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:33:15.183990500+08:00" level=info msg="[TCP] 127.0.0.1:49244 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:33:18.925643300+08:00" level=info msg="[TCP] 127.0.0.1:49262 --> activity.windows.com:443 using GLOBAL"
time="2025-07-15T23:33:20.856751300+08:00" level=info msg="[TCP] 127.0.0.1:49277 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-15T23:33:20.891287000+08:00" level=info msg="[TCP] 127.0.0.1:49280 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-15T23:33:21.162415800+08:00" level=info msg="[TCP] 127.0.0.1:49286 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-15T23:33:21.163474300+08:00" level=info msg="[TCP] 127.0.0.1:49288 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-15T23:33:21.163474300+08:00" level=info msg="[TCP] 127.0.0.1:49289 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-15T23:33:21.163989100+08:00" level=info msg="[TCP] 127.0.0.1:49287 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-15T23:33:21.165642000+08:00" level=info msg="[TCP] 127.0.0.1:49285 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-15T23:33:21.423498300+08:00" level=info msg="[TCP] 127.0.0.1:49301 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-15T23:33:21.444423300+08:00" level=info msg="[TCP] 127.0.0.1:49305 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-15T23:33:21.591484900+08:00" level=info msg="[TCP] 127.0.0.1:49308 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-15T23:33:22.024272600+08:00" level=info msg="[TCP] 127.0.0.1:49313 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-15T23:33:22.095259400+08:00" level=info msg="[TCP] 127.0.0.1:49316 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-15T23:33:22.449248100+08:00" level=info msg="[TCP] 127.0.0.1:49320 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:33:22.819166200+08:00" level=info msg="[TCP] 127.0.0.1:49325 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:33:23.367392600+08:00" level=info msg="[TCP] 127.0.0.1:49330 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-15T23:33:23.913606600+08:00" level=info msg="[TCP] 127.0.0.1:49338 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-15T23:33:32.179551800+08:00" level=info msg="[TCP] 127.0.0.1:49373 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-15T23:33:38.854953400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49390 --> zhijuanwang.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-15T23:56:27.305419500+08:00" level=info msg="[TCP] 127.0.0.1:53186 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:56:34.652999200+08:00" level=info msg="[TCP] 127.0.0.1:53194 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:56:35.475494300+08:00" level=info msg="[TCP] 127.0.0.1:53201 --> letsencrypt.org:443 using GLOBAL"
time="2025-07-15T23:56:36.739099300+08:00" level=info msg="[TCP] 127.0.0.1:53207 --> outreach.abetterinternet.org:443 using GLOBAL"
time="2025-07-15T23:56:37.020200300+08:00" level=info msg="[TCP] 127.0.0.1:53211 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:56:38.605266000+08:00" level=info msg="[TCP] 127.0.0.1:53218 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-15T23:56:38.686673500+08:00" level=info msg="[TCP] 127.0.0.1:53221 --> d4twhgtvn0ff5.cloudfront.net:443 using GLOBAL"
time="2025-07-15T23:56:38.692109800+08:00" level=info msg="[TCP] 127.0.0.1:53224 --> pi.pardot.com:443 using GLOBAL"
time="2025-07-15T23:56:38.771783400+08:00" level=info msg="[TCP] 127.0.0.1:53227 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:56:43.275569800+08:00" level=info msg="[TCP] 127.0.0.1:53254 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:56:50.173296100+08:00" level=info msg="[TCP] 127.0.0.1:53281 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:56:50.175744900+08:00" level=info msg="[TCP] 127.0.0.1:53278 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:56:51.174978900+08:00" level=info msg="[TCP] 127.0.0.1:53288 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-15T23:56:55.217570900+08:00" level=info msg="[TCP] 127.0.0.1:53310 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T23:56:57.283214700+08:00" level=info msg="[TCP] 127.0.0.1:53319 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:57:01.454704700+08:00" level=info msg="[TCP] 127.0.0.1:53338 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:57:01.792006000+08:00" level=info msg="[TCP] 127.0.0.1:53342 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:57:04.460354000+08:00" level=info msg="[TCP] 127.0.0.1:53353 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:57:13.564783800+08:00" level=info msg="[TCP] 127.0.0.1:53374 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:57:21.183345000+08:00" level=info msg="[TCP] 127.0.0.1:53383 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:57:21.184687400+08:00" level=info msg="[TCP] 127.0.0.1:53386 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:57:21.498723500+08:00" level=info msg="[TCP] 127.0.0.1:53390 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:57:21.500415500+08:00" level=info msg="[TCP] 127.0.0.1:53391 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:57:21.501519900+08:00" level=info msg="[TCP] 127.0.0.1:53389 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:57:21.657209400+08:00" level=info msg="[TCP] 127.0.0.1:53398 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:57:24.656564400+08:00" level=info msg="[TCP] 127.0.0.1:53403 --> letsencrypt.org:443 using GLOBAL"
time="2025-07-15T23:57:25.675531300+08:00" level=info msg="[TCP] 127.0.0.1:53407 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-15T23:57:25.913212900+08:00" level=info msg="[TCP] 127.0.0.1:53410 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-15T23:57:55.269584400+08:00" level=info msg="[TCP] 127.0.0.1:53456 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T23:58:03.351419600+08:00" level=info msg="[TCP] 127.0.0.1:53471 --> certbot.eff.org:443 using GLOBAL"
time="2025-07-15T23:58:03.432459900+08:00" level=info msg="[TCP] 127.0.0.1:53474 --> certbot.eff.org:443 using GLOBAL"
time="2025-07-15T23:58:03.440370900+08:00" level=info msg="[TCP] 127.0.0.1:53477 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:58:06.709619100+08:00" level=info msg="[TCP] 127.0.0.1:53483 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:58:07.016606100+08:00" level=info msg="[TCP] 127.0.0.1:53486 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:58:08.247030400+08:00" level=info msg="[TCP] 127.0.0.1:53490 --> activity.windows.com:443 using GLOBAL"
time="2025-07-15T23:58:08.251460700+08:00" level=info msg="[TCP] 127.0.0.1:53493 --> activity.windows.com:443 using GLOBAL"
time="2025-07-15T23:58:10.806507600+08:00" level=info msg="[TCP] 127.0.0.1:53505 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:58:21.166339000+08:00" level=info msg="[TCP] 127.0.0.1:53518 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-15T23:58:55.325300600+08:00" level=info msg="[TCP] 127.0.0.1:53610 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T23:58:55.463850800+08:00" level=info msg="[TCP] 127.0.0.1:53613 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T23:58:57.887490500+08:00" level=info msg="[TCP] 127.0.0.1:53627 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:59:11.456314500+08:00" level=info msg="[TCP] 127.0.0.1:53690 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-15T23:59:11.691975900+08:00" level=info msg="[TCP] 127.0.0.1:53694 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:59:12.430312300+08:00" level=info msg="[TCP] 127.0.0.1:53700 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:59:17.260193400+08:00" level=info msg="[TCP] 127.0.0.1:53719 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:59:19.297803300+08:00" level=info msg="[TCP] 127.0.0.1:53731 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:59:19.743163700+08:00" level=info msg="[TCP] 127.0.0.1:53735 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:59:21.172272000+08:00" level=info msg="[TCP] 127.0.0.1:53741 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-15T23:59:22.168555100+08:00" level=info msg="[TCP] 127.0.0.1:53748 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-15T23:59:23.822450900+08:00" level=info msg="[TCP] 127.0.0.1:53754 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-15T23:59:27.791349100+08:00" level=info msg="[TCP] 127.0.0.1:53773 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:59:28.123462700+08:00" level=info msg="[TCP] 127.0.0.1:53777 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:59:30.430282600+08:00" level=info msg="[TCP] 127.0.0.1:53786 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:59:30.639157200+08:00" level=info msg="[TCP] 127.0.0.1:53790 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:59:30.843949700+08:00" level=info msg="[TCP] 127.0.0.1:53794 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:59:35.451617100+08:00" level=info msg="[TCP] 127.0.0.1:53817 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:59:37.271489700+08:00" level=info msg="[TCP] 127.0.0.1:53834 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:59:38.002479100+08:00" level=info msg="[TCP] 127.0.0.1:53842 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:59:38.007730300+08:00" level=info msg="[TCP] 127.0.0.1:53841 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-15T23:59:38.135935300+08:00" level=info msg="[TCP] 127.0.0.1:53847 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-15T23:59:38.587331300+08:00" level=info msg="[TCP] 127.0.0.1:53850 --> cn.bing.com:443 using GLOBAL"
time="2025-07-15T23:59:38.897897500+08:00" level=info msg="[TCP] 127.0.0.1:53856 --> cn.bing.com:443 using GLOBAL"
time="2025-07-15T23:59:39.295637000+08:00" level=info msg="[TCP] 127.0.0.1:53861 --> cn.bing.com:443 using GLOBAL"
time="2025-07-15T23:59:39.301189700+08:00" level=info msg="[TCP] 127.0.0.1:53864 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-15T23:59:39.893172500+08:00" level=info msg="[TCP] 127.0.0.1:53870 --> www.bing.com:443 using GLOBAL"
time="2025-07-15T23:59:42.749303200+08:00" level=info msg="[TCP] 127.0.0.1:53881 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-16T00:00:21.179708900+08:00" level=info msg="[TCP] 127.0.0.1:53957 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-16T00:00:21.179708900+08:00" level=info msg="[TCP] 127.0.0.1:53960 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-16T00:00:21.182219400+08:00" level=info msg="[TCP] 127.0.0.1:53963 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-16T00:00:21.495627300+08:00" level=info msg="[TCP] 127.0.0.1:53967 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-16T00:00:21.495627300+08:00" level=info msg="[TCP] 127.0.0.1:53968 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-16T00:00:21.496139500+08:00" level=info msg="[TCP] 127.0.0.1:53966 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-16T00:00:24.471753200+08:00" level=info msg="[TCP] 127.0.0.1:53977 --> acme-v02.api.letsencrypt.org:443 using GLOBAL"
time="2025-07-16T00:00:24.513381200+08:00" level=info msg="[TCP] 127.0.0.1:53980 --> acme-v02.api.letsencrypt.org:443 using GLOBAL"
time="2025-07-16T00:00:26.529911300+08:00" level=info msg="[TCP] 127.0.0.1:53985 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-16T00:00:29.889452300+08:00" level=info msg="[TCP] 127.0.0.1:53991 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-16T00:00:41.328010200+08:00" level=info msg="[TCP] 127.0.0.1:54008 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-16T00:00:55.232613700+08:00" level=info msg="[TCP] 127.0.0.1:54116 --> activity.windows.com:443 using GLOBAL"
time="2025-07-16T00:01:04.433162500+08:00" level=info msg="[TCP] 127.0.0.1:54157 --> ntp.msn.com:443 using GLOBAL"
time="2025-07-16T00:01:06.803794300+08:00" level=info msg="[TCP] 127.0.0.1:54167 --> assets.msn.com:443 using GLOBAL"
time="2025-07-16T00:01:07.073962000+08:00" level=info msg="[TCP] 127.0.0.1:54175 --> c.bing.com:443 using GLOBAL"
time="2025-07-16T00:01:07.073962000+08:00" level=info msg="[TCP] 127.0.0.1:54172 --> c.msn.com:443 using GLOBAL"
time="2025-07-16T00:01:07.075209700+08:00" level=info msg="[TCP] 127.0.0.1:54189 --> th.bing.com:443 using GLOBAL"
time="2025-07-16T00:01:07.075209700+08:00" level=info msg="[TCP] 127.0.0.1:54180 --> assets.msn.com:443 using GLOBAL"
time="2025-07-16T00:01:07.075209700+08:00" level=info msg="[TCP] 127.0.0.1:54181 --> api.msn.com:443 using GLOBAL"
time="2025-07-16T00:01:07.075209700+08:00" level=info msg="[TCP] 127.0.0.1:54171 --> www.bing.com:443 using GLOBAL"
time="2025-07-16T00:01:07.076153400+08:00" level=info msg="[TCP] 127.0.0.1:54191 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-07-16T00:01:07.080682300+08:00" level=info msg="[TCP] 127.0.0.1:54193 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-16T00:01:07.083701600+08:00" level=info msg="[TCP] 127.0.0.1:54186 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-16T00:01:07.133616500+08:00" level=info msg="[TCP] 127.0.0.1:54198 --> assets.msn.com:443 using GLOBAL"
time="2025-07-16T00:01:07.447962300+08:00" level=info msg="[TCP] 127.0.0.1:54202 --> assets.msn.com:443 using GLOBAL"
time="2025-07-16T00:01:07.464923100+08:00" level=info msg="[TCP] 127.0.0.1:54205 --> www.bing.com:443 using GLOBAL"
time="2025-07-16T00:01:07.466015900+08:00" level=info msg="[TCP] 127.0.0.1:54206 --> ts1.tc.mm.bing.net:443 using GLOBAL"
time="2025-07-16T00:01:07.769433800+08:00" level=info msg="[TCP] 127.0.0.1:54212 --> www.bing.com:443 using GLOBAL"
time="2025-07-16T00:01:08.674092600+08:00" level=info msg="[TCP] 127.0.0.1:54218 --> login.live.com:443 using GLOBAL"
time="2025-07-16T00:01:08.751712400+08:00" level=info msg="[TCP] 127.0.0.1:54223 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-16T00:01:08.751712400+08:00" level=info msg="[TCP] 127.0.0.1:54225 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-16T00:01:08.752289000+08:00" level=info msg="[TCP] 127.0.0.1:54224 --> cdnjs.cloudflare.com:443 using GLOBAL"
time="2025-07-16T00:01:08.757273600+08:00" level=info msg="[TCP] 127.0.0.1:54221 --> zhijuanwang.com:80 using GLOBAL"
time="2025-07-16T00:01:09.939672900+08:00" level=info msg="[TCP] 127.0.0.1:54236 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-16T00:01:10.444291700+08:00" level=info msg="[TCP] 127.0.0.1:54244 --> r.bing.com:443 using GLOBAL"
time="2025-07-16T00:01:10.444291700+08:00" level=info msg="[TCP] 127.0.0.1:54246 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-16T00:01:10.444291700+08:00" level=info msg="[TCP] 127.0.0.1:54251 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-16T00:01:10.444942000+08:00" level=info msg="[TCP] 127.0.0.1:54242 --> r.bing.com:443 using GLOBAL"
time="2025-07-16T00:01:13.472138700+08:00" level=info msg="[TCP] 127.0.0.1:54262 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-16T00:52:41.955661200+08:00" level=warning msg="Mihomo shutting down"
