Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-29T01:09:08.261503400+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-29T01:09:08.270198000+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-29T01:09:08.270198000+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-29T01:09:08.270708300+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-29T01:09:08.325402100+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-29T01:09:08.325914500+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-29T01:09:08.674920700+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-29T01:09:08.674920700+08:00" level=info msg="Load GeoSite rule: private"
time="2025-06-29T01:09:08.693747100+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-29T01:09:08.696813500+08:00" level=info msg="Initial configuration complete, total time: 429ms"
time="2025-06-29T01:09:08.696813500+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-06-29T01:09:08.697831600+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-29T01:09:08.698343300+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-06-29T01:09:08.698343300+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-06-29T01:09:08.698343300+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider BBC"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider google"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Disney"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider apple"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider TVB"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Lan"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider NowE"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider telegram"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Discord"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider TVer"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Line"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-29T01:09:08.698854700+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-29T01:09:08.926481000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.926481000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.927570300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.927570300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.927570300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.929234500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.929234500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.929234500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.929748200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.929748200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.929748200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.929748200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.930981400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.930981400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.930981400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.930981400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.932691300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.932691300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.933231500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.933231500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.933231500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.933231500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.933231500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.933744400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.934685100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.934685100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.934685100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.935281100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.935281100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.935281100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.935281100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.935281100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.935788300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.935788300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.935788300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.936494600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.941311300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.941311300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.941311300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.941311300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.941311300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.941311300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.941311300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.941824300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.941824300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.942396700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:08.946544100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:09.514174500+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-06-29T01:09:09.519755400+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-06-29T01:09:09.519755400+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-06-29T01:09:09.523270600+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-06-29T01:09:09.540877700+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-06-29T01:09:09.544358700+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-06-29T01:09:09.547011100+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-06-29T01:09:09.577516200+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-06-29T01:09:09.579169600+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-06-29T01:09:09.584464500+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-06-29T01:09:09.588501700+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-06-29T01:09:09.592007800+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-06-29T01:09:09.603899400+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-06-29T01:09:09.605686500+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-06-29T01:09:09.615502900+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-06-29T01:09:09.659920200+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-06-29T01:09:09.662855300+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-06-29T01:09:09.670956600+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-06-29T01:09:09.679271100+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-06-29T01:09:09.681015800+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-06-29T01:09:09.682545400+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-06-29T01:09:09.688143100+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-06-29T01:09:09.688650300+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-06-29T01:09:09.708387200+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-06-29T01:09:09.716479500+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-06-29T01:09:09.733830100+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-06-29T01:09:09.739236500+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-06-29T01:09:09.783285400+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-06-29T01:09:09.806665500+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-06-29T01:09:09.826433700+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-06-29T01:09:09.852581500+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-06-29T01:09:09.876102000+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-06-29T01:09:09.878269700+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-06-29T01:09:09.919731100+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-06-29T01:09:09.943363800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:09.945062600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:09.965609200+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-06-29T01:09:09.995593900+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-06-29T01:09:10.004256700+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-06-29T01:09:10.022660800+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-06-29T01:09:10.097293600+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-06-29T01:09:10.111611200+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-06-29T01:09:10.126394700+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-06-29T01:09:10.198403900+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-06-29T01:09:10.232878300+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-06-29T01:09:10.295480500+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-06-29T01:09:10.375598300+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-06-29T01:09:10.660090900+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-06-29T01:09:10.812891500+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-06-29T01:09:11.342896100+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-06-29T01:09:11.581329600+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-29T01:09:11.581856300+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-29T01:09:11.581856300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-29T01:09:11.582365800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-29T01:09:11.582365800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-29T01:09:11.582365800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-29T01:09:11.583904800+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-29T01:09:13.296366400+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-06-29T01:09:13.298026200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-29T01:09:13.298534800+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-29T01:09:13.298534800+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-29T01:09:13.298534800+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider TVer"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider google"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Lan"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider TVB"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Disney"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Line"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider telegram"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider apple"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider NowE"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider BBC"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Discord"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-29T01:09:13.300068400+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-29T01:09:13.511585200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.511585200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.514513200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.514513200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.514513200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.515346600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.515346600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.515954000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.515954000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.516950700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.516950700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.516950700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.516950700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.516950700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.517794700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.517794700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.518755300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.518755300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.518755300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.518755300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.518755300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.518755300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.519267900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.521462200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.521462200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.521462200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.521975300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.521975300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.521975300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.521975300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.521975300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.521975300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.521975300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.524671300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.524671300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.524671300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.524671300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.524671300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.524671300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.525186900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.525186900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.525186900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.525186900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:13.527955700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:14.132220400+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-06-29T01:09:14.139858600+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-06-29T01:09:14.206464800+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-06-29T01:09:14.245065100+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-06-29T01:09:14.257676300+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-06-29T01:09:14.257676300+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-06-29T01:09:14.269487600+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-06-29T01:09:14.269487600+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-06-29T01:09:14.279218500+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-06-29T01:09:14.281876500+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-06-29T01:09:14.293273900+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-06-29T01:09:14.327490400+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-06-29T01:09:14.338189000+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-06-29T01:09:14.343658800+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-06-29T01:09:14.375242000+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-06-29T01:09:14.380306800+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-06-29T01:09:14.383922500+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-06-29T01:09:14.392639800+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-06-29T01:09:14.481610100+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-06-29T01:09:14.482118600+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-06-29T01:09:14.522695700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:14.527156100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:14.527156100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:14.528897000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:14.533004100+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-06-29T01:09:14.569688100+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-06-29T01:09:14.576973900+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-06-29T01:09:14.581630200+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-06-29T01:09:14.636643100+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-06-29T01:09:14.658611900+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-06-29T01:09:14.659145500+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-06-29T01:09:14.691949900+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-06-29T01:09:14.697122000+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-06-29T01:09:14.711153000+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-06-29T01:09:14.718304800+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-06-29T01:09:14.727698100+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-06-29T01:09:14.735937700+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-06-29T01:09:14.749231100+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-06-29T01:09:14.860744100+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-06-29T01:09:14.893686300+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-06-29T01:09:14.942824100+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-06-29T01:09:14.987248600+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-06-29T01:09:14.991758800+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-06-29T01:09:15.047753900+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-06-29T01:09:15.093385100+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-06-29T01:09:15.165030100+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-06-29T01:09:15.220447200+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-06-29T01:09:15.220447200+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-06-29T01:09:15.230605900+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-06-29T01:09:15.438192700+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-06-29T01:09:15.529046400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:16.377447400+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-06-29T01:09:16.681251400+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-06-29T01:09:20.342157100+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-06-29T01:09:20.347246300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-29T01:09:20.347246300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-29T01:09:20.347246300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-29T01:09:20.347246300+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-29T01:09:20.661052500+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-29T01:09:20.661559000+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-29T01:09:20.661559000+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-29T01:09:20.662065300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-29T01:09:20.662065300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-29T01:09:20.662065300+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-29T01:09:20.663745300+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Disney"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Discord"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Line"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider apple"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider google"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider telegram"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider BBC"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider TVB"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Lan"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider NowE"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider TVer"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-29T01:09:20.665269000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-29T01:09:20.817091900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.817091900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.817091900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.818588800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.818588800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.819100800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.819851900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.819851900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.820404400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.821313900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.821313900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.821313900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.821313900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.823185300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.823185300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.823185300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.823185300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.823185300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.823185300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.823185300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.823688600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.823693700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826052400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826052400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826052400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826052400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826052400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826565900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826565900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826565900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826565900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826565900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826565900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826565900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.826565900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.827106200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.830805000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.830805000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.830805000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.830805000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.830805000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.831317500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.831317500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.831317500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:20.831317500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:21.467327800+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-06-29T01:09:21.536826800+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-06-29T01:09:21.564540300+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-06-29T01:09:21.568602500+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-06-29T01:09:21.582469600+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-06-29T01:09:21.591791800+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-06-29T01:09:21.597460200+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-06-29T01:09:21.611977100+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-06-29T01:09:21.631184900+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-06-29T01:09:21.649924800+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-06-29T01:09:21.671407600+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-06-29T01:09:21.681720400+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-06-29T01:09:21.719634700+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-06-29T01:09:21.755019100+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-06-29T01:09:21.756327300+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-06-29T01:09:21.788059300+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-06-29T01:09:21.791409800+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-06-29T01:09:21.792271200+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-06-29T01:09:21.813408000+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-06-29T01:09:21.820234900+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-06-29T01:09:21.825279500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:21.828708900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:21.828708900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:21.830825500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-29T01:09:21.848492200+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-06-29T01:09:21.852565700+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-06-29T01:09:21.902672200+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-06-29T01:09:21.919598400+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-06-29T01:09:21.946714100+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-06-29T01:09:21.946714100+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-06-29T01:09:21.992119000+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-06-29T01:09:21.992650700+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-06-29T01:09:22.099732200+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-06-29T01:09:22.123877500+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-06-29T01:09:22.146303200+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-06-29T01:09:22.225857100+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-06-29T01:09:22.286684000+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-06-29T01:09:22.541775600+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-06-29T01:09:22.595394800+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-06-29T01:09:22.700333200+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-06-29T01:09:22.708045300+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-06-29T01:09:22.740510700+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-06-29T01:09:22.814421400+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-06-29T01:09:22.879593600+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-06-29T01:09:23.104829600+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-06-29T01:09:23.141190300+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-06-29T01:09:23.495774200+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-06-29T01:09:23.515335400+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-06-29T01:09:23.560293000+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-06-29T01:09:23.682643600+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-06-29T01:09:24.603288400+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-06-29T01:09:24.645914100+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-06-29T01:09:24.944213800+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-06-29T01:09:24.951033200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-29T01:09:24.951033200+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-29T01:09:24.951033200+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-29T01:09:24.951535700+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-29T01:09:25.324986200+08:00" level=info msg="[TCP] 127.0.0.1:56280 --> 91.108.56.181:443 using GLOBAL"
time="2025-06-29T01:09:25.324986200+08:00" level=info msg="[TCP] 127.0.0.1:56277 --> 91.108.56.181:80 using GLOBAL"
time="2025-06-29T01:09:25.326394500+08:00" level=info msg="[TCP] 127.0.0.1:56274 --> 91.108.56.181:443 using GLOBAL"
time="2025-06-29T01:09:25.330334700+08:00" level=info msg="[TCP] 127.0.0.1:56276 --> 149.154.167.43:443 using GLOBAL"
time="2025-06-29T01:09:25.330334700+08:00" level=info msg="[TCP] 127.0.0.1:56281 --> 149.154.167.43:80 using GLOBAL"
time="2025-06-29T01:09:25.338768700+08:00" level=info msg="[TCP] 127.0.0.1:56284 --> 91.108.56.181:80 using GLOBAL"
time="2025-06-29T01:09:28.480010500+08:00" level=info msg="[TCP] 127.0.0.1:56320 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:09:39.*********+08:00" level=info msg="[TCP] 127.0.0.1:56338 --> download.clashverge.dev:443 using GLOBAL"
time="2025-06-29T01:09:39.*********+08:00" level=info msg="[TCP] 127.0.0.1:56336 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:09:45.*********+08:00" level=info msg="[TCP] 127.0.0.1:56347 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T01:09:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56355 --> accounts.google.com:443 using GLOBAL"
time="2025-06-29T01:09:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56357 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T01:09:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56364 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:09:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56373 --> beacons2.gvt2.com:443 using GLOBAL"
time="2025-06-29T01:09:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56367 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T01:09:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56368 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-29T01:09:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56369 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T01:09:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56376 --> play.google.com:443 using GLOBAL"
time="2025-06-29T01:09:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56377 --> myaccount.google.com:443 using GLOBAL"
time="2025-06-29T01:09:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56378 --> labs.google.com:443 using GLOBAL"
time="2025-06-29T01:09:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:56359 --> go.reebr.com:443 using GLOBAL"
time="2025-06-29T01:09:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:56361 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T01:09:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:56384 --> go.reebr.com:443 using GLOBAL"
time="2025-06-29T01:09:54.*********+08:00" level=info msg="[TCP] 127.0.0.1:56392 --> beacons2.gvt2.com:443 using GLOBAL"
time="2025-06-29T01:10:00.*********+08:00" level=info msg="[TCP] 127.0.0.1:56403 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-29T01:10:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:56398 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:03.*********+08:00" level=info msg="[TCP] 127.0.0.1:56409 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:06.613492600+08:00" level=info msg="[TCP] 127.0.0.1:56415 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-06-29T01:10:08.491477600+08:00" level=info msg="[TCP] 127.0.0.1:56423 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:09.546634600+08:00" level=info msg="[TCP] 127.0.0.1:56425 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:15.725315300+08:00" level=info msg="[TCP] 127.0.0.1:56439 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:18.501317900+08:00" level=info msg="[TCP] 127.0.0.1:56448 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:25.832449900+08:00" level=info msg="[TCP] 127.0.0.1:56458 --> mtalk.google.com:443 using GLOBAL"
time="2025-06-29T01:10:28.492380000+08:00" level=info msg="[TCP] 127.0.0.1:56463 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:38.503915900+08:00" level=info msg="[TCP] 127.0.0.1:56480 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:44.324379600+08:00" level=info msg="[TCP] 127.0.0.1:56487 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-06-29T01:10:46.721602500+08:00" level=info msg="[TCP] 127.0.0.1:56492 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:47.157954800+08:00" level=info msg="[TCP] 127.0.0.1:56494 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:48.311590500+08:00" level=info msg="[TCP] 127.0.0.1:56498 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T01:10:48.334732600+08:00" level=info msg="[TCP] 127.0.0.1:56500 --> x.com:443 using GLOBAL"
time="2025-06-29T01:10:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56502 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T01:10:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56504 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56506 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T01:10:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56508 --> accounts.google.com:443 using GLOBAL"
time="2025-06-29T01:10:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:56509 --> beacons.gcp.gvt2.com:443 using GLOBAL"
time="2025-06-29T01:10:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:56512 --> beacons.gcp.gvt2.com:443 using GLOBAL"
time="2025-06-29T01:10:52.*********+08:00" level=info msg="[TCP] 127.0.0.1:56519 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:53.*********+08:00" level=info msg="[TCP] 127.0.0.1:56521 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:10:58.*********+08:00" level=info msg="[TCP] 127.0.0.1:56528 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:11:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:56534 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:11:05.*********+08:00" level=info msg="[TCP] 127.0.0.1:56540 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-06-29T01:11:08.*********+08:00" level=info msg="[TCP] 127.0.0.1:56547 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:11:13.519469500+08:00" level=info msg="[TCP] 127.0.0.1:56555 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:11:18.507590600+08:00" level=info msg="[TCP] 127.0.0.1:56569 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:11:28.504361400+08:00" level=info msg="[TCP] 127.0.0.1:56581 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:11:33.535442300+08:00" level=info msg="[TCP] 127.0.0.1:56590 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:11:38.512333000+08:00" level=info msg="[TCP] 127.0.0.1:56597 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:11:47.409172300+08:00" level=info msg="[TCP] 127.0.0.1:56623 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:11:48.327458400+08:00" level=info msg="[TCP] 127.0.0.1:56627 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T01:11:48.498655900+08:00" level=info msg="[TCP] 127.0.0.1:56630 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:11:48.630494400+08:00" level=info msg="[TCP] 127.0.0.1:56633 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T01:11:49.310703100+08:00" level=info msg="[TCP] 127.0.0.1:56636 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T01:11:55.455249000+08:00" level=info msg="[TCP] 127.0.0.1:56655 --> www.jyeoo.com:443 using GLOBAL"
time="2025-06-29T01:11:58.514220500+08:00" level=info msg="[TCP] 127.0.0.1:56666 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:12:08.508431000+08:00" level=info msg="[TCP] 127.0.0.1:56696 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:12:18.503253000+08:00" level=info msg="[TCP] 127.0.0.1:56734 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:12:26.146931400+08:00" level=info msg="[TCP] 127.0.0.1:56756 --> mtalk.google.com:5228 using GLOBAL"
time="2025-06-29T01:12:26.669207500+08:00" level=info msg="[TCP] 127.0.0.1:56759 --> mtalk.google.com:5228 using GLOBAL"
time="2025-06-29T01:12:28.565767500+08:00" level=info msg="[TCP] 127.0.0.1:56764 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:12:38.515026100+08:00" level=info msg="[TCP] 127.0.0.1:56791 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:12:48.317514700+08:00" level=info msg="[TCP] 127.0.0.1:56821 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-29T01:12:48.521363700+08:00" level=info msg="[TCP] 127.0.0.1:56823 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:12:58.536274200+08:00" level=info msg="[TCP] 127.0.0.1:56835 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:13:00.628584500+08:00" level=info msg="[TCP] 127.0.0.1:56842 --> 91.108.56.181:443 using GLOBAL"
time="2025-06-29T01:13:00.636710000+08:00" level=info msg="[TCP] 127.0.0.1:56843 --> 91.108.56.181:80 using GLOBAL"
time="2025-06-29T01:13:07.711968400+08:00" level=info msg="[TCP] 127.0.0.1:56853 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-06-29T01:13:08.533901700+08:00" level=info msg="[TCP] 127.0.0.1:56858 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:13:18.534022300+08:00" level=info msg="[TCP] 127.0.0.1:56874 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:13:28.*********+08:00" level=info msg="[TCP] 127.0.0.1:56888 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:13:38.*********+08:00" level=info msg="[TCP] 127.0.0.1:56903 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:13:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56921 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-29T01:13:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56923 --> myaccount.google.com:443 using GLOBAL"
time="2025-06-29T01:13:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56925 --> waa-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-29T01:13:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56927 --> play.google.com:443 using GLOBAL"
time="2025-06-29T01:13:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56929 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:13:48.*********+08:00" level=info msg="[TCP] 127.0.0.1:56933 --> beacons.gcp.gvt2.com:443 using GLOBAL"
time="2025-06-29T01:13:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:56932 --> accounts.google.com:443 using GLOBAL"
time="2025-06-29T01:13:52.*********+08:00" level=info msg="[TCP] 127.0.0.1:56943 --> www.recaptcha.net:443 using GLOBAL"
time="2025-06-29T01:13:54.*********+08:00" level=info msg="[TCP] 127.0.0.1:56946 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-29T01:13:55.*********+08:00" level=info msg="[TCP] 127.0.0.1:56950 --> clients4.google.com:443 using GLOBAL"
time="2025-06-29T01:13:58.*********+08:00" level=info msg="[TCP] 127.0.0.1:56957 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:14:00.*********+08:00" level=info msg="[TCP] 127.0.0.1:56962 --> www.bing.com:443 using GLOBAL"
time="2025-06-29T01:14:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:56970 --> clients4.google.com:443 using GLOBAL"
time="2025-06-29T01:14:08.*********+08:00" level=info msg="[TCP] 127.0.0.1:56989 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-29T01:14:15.*********+08:00" level=info msg="[TCP] 127.0.0.1:57021 --> download.clashverge.dev:443 using GLOBAL"
time="2025-06-29T01:59:49.314894900+08:00" level=warning msg="Mihomo shutting down"
