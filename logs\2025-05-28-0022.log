2025-05-28 00:22:47 INFO - reinstall service
2025-05-28 00:22:49 INFO - install service susccess.
2025-05-28 00:22:50 INFO - try to run core in service mode
2025-05-28 00:22:50 INFO - start service: {"log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-05-28-0022.log", "core_type": "verge-mihomo", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo.exe", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml"}
2025-05-28 00:22:50 INFO - Initializing hotkeys, global hotkey enabled: true
2025-05-28 00:22:50 INFO - No hotkeys configured
2025-05-28 00:22:50 INFO - Starting to create window
2025-05-28 00:22:50 INFO - Creating new window
2025-05-28 00:22:50 INFO - Window created successfully, attempting to show
2025-05-28 00:22:51 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:22:51 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:22:55 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:22:55 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:23:25 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:23:25 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:23:55 INFO - changing core to verge-mihomo-alpha
2025-05-28 00:23:55 INFO - change core to `verge-mihomo-alpha`
2025-05-28 00:23:56 INFO - stop the core by service
2025-05-28 00:23:56 INFO - try to run core in service mode
2025-05-28 00:23:56 INFO - start service: {"core_type": "verge-mihomo-alpha", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-05-28-0023.log", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml"}
2025-05-28 00:23:56 INFO - core changed to verge-mihomo-alpha
2025-05-28 00:28:57 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:28:57 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:29:04 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:29:04 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:29:47 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:29:47 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:39:13 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:39:13 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:39:36 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-28 00:39:36 INFO - Successfully registered hotkey Control+Q for quit
2025-05-28 00:39:43 INFO - stop the core by service
2025-05-28 00:39:43 ERROR - Failed to unregister hotkey: HotKey { mods: Modifiers(CONTROL), key: KeyQ, id: 524323 }
