Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-10T21:53:33.983520700+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-10T21:53:33.993778400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-10T21:53:33.993778400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-10T21:53:33.994289900+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-10T21:53:34.020017100+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-10T21:53:34.020017100+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-10T21:53:34.272695500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-10T21:53:34.272695500+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-10T21:53:34.291712200+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-10T21:53:34.295716000+08:00" level=info msg="Initial configuration complete, total time: 306ms"
time="2025-07-10T21:53:34.295716000+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-10T21:53:34.296717100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-10T21:53:34.297717500+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-10T21:53:34.297717500+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-10T21:53:34.297717500+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider BBC"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider TVer"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider NowE"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider apple"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider Discord"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider Disney"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider google"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider Lan"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider Line"
time="2025-07-10T21:53:34.303723100+08:00" level=info msg="Start initial provider telegram"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-10T21:53:34.304723900+08:00" level=info msg="Start initial provider TVB"
time="2025-07-10T21:53:37.810685800+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-10T21:53:37.811211000+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-10T21:53:37.811211000+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-10T21:53:37.811721000+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-10T21:53:37.812229900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-10T21:53:37.812229900+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-10T21:53:37.813252100+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-10T21:53:39.305625600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.305625600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.305625600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.305625600+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-10T21:53:39.305625600+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-10T21:53:39.305625600+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-10T21:53:39.306434700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.306434700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.306434700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: i/o timeout"
time="2025-07-10T21:53:39.306434700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.306434700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.306434700+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-10T21:53:39.306434700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: i/o timeout"
time="2025-07-10T21:53:39.306434700+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-10T21:53:39.306434700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.306434700+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-10T21:53:39.306434700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.306434700+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-10T21:53:39.306434700+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-10T21:53:39.306434700+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-10T21:53:39.306434700+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-10T21:53:39.306434700+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-10T21:53:39.307104000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.307104000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.307104000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.307104000+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-10T21:53:39.307104000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.307104000+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-10T21:53:39.307104000+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-10T21:53:39.307104000+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: i/o timeout"
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-10T21:53:39.308623700+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308117600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.308623700+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-10T21:53:39.308117600+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-10T21:53:39.308623700+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-10T21:53:39.308623700+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-10T21:53:39.308623700+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-10T21:53:39.308623700+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-10T21:53:39.308623700+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-10T21:53:39.308623700+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-10T21:53:39.309132200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19822 connect error: connect failed: dial tcp ***************:19822: i/o timeout\ndial tcp **************:19822: i/o timeout\ndial tcp *************:19822: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:1]:19822: connectex: No connection could be made because the target machine actively refused it."
time="2025-07-10T21:53:39.309132200+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-10T21:53:39.312364300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-10T21:53:39.312364300+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-10T21:53:39.312364300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-10T21:53:39.312364300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider telegram"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider NowE"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider TVB"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Lan"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Line"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider TVer"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider BBC"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider google"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider apple"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Disney"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider Discord"
time="2025-07-10T21:53:39.313889100+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-10T21:53:44.315771400+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-10T21:53:44.315771400+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.315771400+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-10T21:53:44.315771400+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-10T21:53:44.315771400+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-10T21:53:44.315771400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-10T21:53:44.315771400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:44.315771400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-10T21:53:44.315771400+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-10T21:53:44.314770400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-10T21:53:44.315771400+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-10T21:53:44.317773200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-10T21:53:44.317773200+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-10T21:53:44.317773200+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-10T21:53:44.317773200+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-10T21:53:44.647225000+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-10T21:53:44.647737300+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-10T21:53:44.647737300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-10T21:53:44.648265300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-10T21:53:44.648265300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-10T21:53:44.648265300+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-10T21:53:44.649821000+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Disney"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider NowE"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Discord"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider TVB"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider apple"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider google"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Line"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider BBC"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Lan"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider telegram"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider TVer"
time="2025-07-10T21:53:44.651347600+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-10T21:53:49.651993400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652594800+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652607700+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-10T21:53:49.653631300+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-10T21:53:49.653631300+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-10T21:53:49.653119800+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.652090000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:49.653631300+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-10T21:53:49.653631300+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-10T21:53:49.660482700+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-10T21:53:49.660482700+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-10T21:53:49.660482700+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-10T21:53:49.660482700+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-10T21:53:54.688036000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56182 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:53:56.350516200+08:00" level=error msg="🇮🇳印度-下载专享[0.5x] ipv6 only failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-10T21:53:56.350564200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-10T21:53:59.689382100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56305 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:02.340267300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56312 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:04.696162500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56321 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:04.696189800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56359 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:09.696731900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56379 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:14.696970400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56416 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:17.340198100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56412 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:19.697635000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56430 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:19.697635000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56429 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:24.698144900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56438 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:24.698144900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56439 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:24.699657400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56435 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:24.699657400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56447 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:29.698284500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56460 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:29.698284500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56452 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:32.355176500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56456 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:34.698863000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56481 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:44.699373000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56505 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:44.699373000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56497 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:47.368355700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56502 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:49.699880100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56519 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:54.700250400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56533 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:54:58.120930600+08:00" level=info msg="[TCP] 127.0.0.1:56541 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T21:54:59.700451800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56549 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:03.372463500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56546 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:04.700555800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56577 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:08.519622300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56565 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:08.519622300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56564 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:08.519622300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56563 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:08.519622300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56566 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:08.519622300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56591 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:08.519622300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56586 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:09.339730700+08:00" level=info msg="[TCP] 127.0.0.1:56607 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T21:55:09.576372900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56574 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:09.576372900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56573 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:09.576474800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56587 --> cmp3-hkg1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:09.577001000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56576 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:09.577001000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56575 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:09.700798600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56615 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:10.625739900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56585 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:10.625739900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56588 --> ext1-maa2.steamserver.net:27038 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:13.853944800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56605 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:14.701069100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56636 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:19.701709000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56643 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:24.702148500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56656 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:24.702148500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56653 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:26.746467100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56652 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:29.702730200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56685 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:29.703843600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56664 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:31.881125700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56669 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:31.881215100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56670 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:31.881215100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56668 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:31.881215100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56667 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:31.881215100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56691 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:31.881215100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56681 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:32.707975200+08:00" level=info msg="[TCP] 127.0.0.1:56700 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T21:55:32.934190300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56676 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:32.934190300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56678 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:32.934190300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56677 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:32.934190300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56675 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:33.990463800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56682 --> ext2-maa2.steamserver.net:27031 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:33.990463800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56680 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:33.990463800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56683 --> cmp2-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:34.703793700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56705 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:34.703793700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56690 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:37.046053000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56697 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:39.703930800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56718 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:44.704134900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56729 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:49.704227100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56737 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:52.100830000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56734 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:54.704687500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56765 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:57.277854200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56746 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:57.277854200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56747 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:57.277854200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56749 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:57.277854200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56748 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:57.277854200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56762 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:57.277854200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56770 --> cmp1-sgp1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:58.107279800+08:00" level=info msg="[TCP] 127.0.0.1:56784 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T21:55:58.336692000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56756 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:58.336692000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56758 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:58.336692000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56755 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:58.336692000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56757 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:58.336798800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56763 --> cmp2-sgp1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:59.390494000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56761 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:59.394123500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56764 --> cmp1-hkg1.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:55:59.704831800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56794 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:02.462455800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56783 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:04.705240500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56810 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:09.705379600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56820 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:19.706568800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56838 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:19.706568800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56837 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:19.706568800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56831 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:21.482051200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56834 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:24.706893700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56851 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:24.706893700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56879 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:24.706893700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56839 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:24.706893700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56850 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:24.706893700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56848 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:24.710194100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56849 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:26.626681700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56874 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:26.626681700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56873 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:26.626681700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56871 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:26.626681700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56872 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:26.626818500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56888 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:26.626818500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56908 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:27.479694300+08:00" level=info msg="[TCP] 127.0.0.1:56919 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T21:56:27.687406700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56883 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:27.687494400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56884 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:27.687494400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56882 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:27.687494400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56881 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:27.687494400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56890 --> cmp3-hkg1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:27.687494400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56889 --> cmp2-sea1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:28.745599100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56887 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:29.707026900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56932 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:29.707026900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56898 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:29.707026900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56897 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:29.762780800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56901 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:29.762780800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56902 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:29.762780800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56903 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:29.762780800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56899 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:29.762780800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56900 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:31.855492900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56914 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:34.707125400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56937 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:39.707502700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56971 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:39.707502700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56963 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:49.818011200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56997 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:49.818011200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57029 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:59.819006700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57156 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:56:59.819006700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57123 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:02.825250200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57152 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:04.819792200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57195 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:07.971742500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57165 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:07.971742500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57166 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:07.971866000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57168 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:07.971742500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57167 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:07.971866000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57204 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:07.971866000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57201 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:08.844706300+08:00" level=info msg="[TCP] 127.0.0.1:57216 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T21:57:09.031107100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57176 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:09.031208400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57178 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:09.031208400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57177 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:09.031208400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57175 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:09.031208400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57202 --> cmp3-hkg1.steamserver.net:27021 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:09.820544000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57229 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:10.077982400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57200 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:10.078058900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57203 --> cmp1-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:13.183303000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57211 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:14.820586800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57257 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:19.820881000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57269 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:19.820881000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57267 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:24.820979900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57280 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:24.820979900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57283 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:24.823321300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57274 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:29.821565800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57293 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:29.821565800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57307 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:29.981638900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57292 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:34.821685600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57312 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:34.821685600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57328 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:39.822222700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57339 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:44.822651100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57351 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:49.823657400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57360 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:54.823733800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57369 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:57:59.824202100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57381 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:04.824238900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57389 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:09.825027200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57405 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:11.254058600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57396 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:13.345150900+08:00" level=info msg="[TCP] 127.0.0.1:57433 --> news.baidu.com:443 using GLOBAL"
time="2025-07-10T21:58:14.825157300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57444 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:14.825157300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57440 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:14.947678800+08:00" level=info msg="[TCP] 127.0.0.1:57449 --> news.baidu.com:443 using GLOBAL"
time="2025-07-10T21:58:16.380238400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57420 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:16.380238400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57419 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:16.380238400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57418 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:16.380238400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57417 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:16.380238400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57443 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:16.380238400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57436 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:17.147879600+08:00" level=info msg="[TCP] 127.0.0.1:57463 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T21:58:17.437628300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57426 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:17.437628300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57425 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:17.440553600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57427 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:17.440553600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57428 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:18.495627300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57435 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:18.495627300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57437 --> ext1-maa2.steamserver.net:27038 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:19.825249700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57468 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:19.825249700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57457 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:21.643228100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57458 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:24.825618200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57479 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:24.825618200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57483 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:29.825941600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57496 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:29.828137200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57488 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:34.826293600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57505 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:34.826293600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57502 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:39.826445900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57515 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:44.826671300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57524 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:49.826813900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57531 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:54.827682800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57544 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:58:59.828269500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57564 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:59:04.828695400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57575 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:59:09.829768200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57591 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:59:13.722114600+08:00" level=info msg="[TCP] 127.0.0.1:57601 --> news.baidu.com:443 using GLOBAL"
time="2025-07-10T21:59:14.830313900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57604 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:59:14.830313900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57603 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T21:59:14.943185400+08:00" level=info msg="[TCP] 127.0.0.1:57610 --> news.baidu.com:443 using GLOBAL"
time="2025-07-10T21:59:19.830805400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57613 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:24.590219700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58493 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:29.547422400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58507 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:34.679033400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58527 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:34.679149300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58528 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:34.679149300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58526 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:34.679149300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58529 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:34.679149300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58545 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:34.679149300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58557 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:34.679149300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58546 --> ext2-syd1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:35.478556600+08:00" level=info msg="[TCP] 127.0.0.1:58571 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T22:03:35.735033900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58535 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:35.735033900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58536 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:35.738752300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58537 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:35.743327400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58538 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:36.792941600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58544 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:03:36.797292800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58547 --> cmp2-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:00.845555400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59901 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:05.994097900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59925 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:05.994190800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59926 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:05.994190800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59948 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:05.994699400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59932 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:05.994699400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59927 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:05.994699400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59942 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:06.817718900+08:00" level=info msg="[TCP] 127.0.0.1:59969 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T22:09:07.049494500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59934 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:07.049494500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59936 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:07.049494500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59935 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:07.049649800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59944 --> cmp3-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:07.050162100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59937 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:08.103855900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59941 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:08.103855900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59943 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:09:11.190557400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59965 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:13.242655900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61391 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:18.216247700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61407 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:23.370020700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61422 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:23.370020700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61423 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:23.370020700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61475 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:23.372834400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61429 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:23.372834400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61428 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:23.372834400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61461 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:24.160676800+08:00" level=info msg="[TCP] 127.0.0.1:61497 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T22:15:24.427515000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61431 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:24.427515000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61432 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:24.427515000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61434 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:24.428110700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61433 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:24.428110700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61462 --> cmp3-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:25.474292400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61463 --> cmp2-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:15:25.474292400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61460 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:25.610739600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62643 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:30.756300500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62669 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:30.756300500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62668 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:30.756300500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62693 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:30.756300500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62667 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:30.756300500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62685 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:30.756300500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62687 --> ext2-syd1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:30.760903800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62674 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:31.602716300+08:00" level=info msg="[TCP] 127.0.0.1:62717 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T22:25:31.759691300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62680 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:31.759691300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62681 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:31.760318100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62683 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:31.760318100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62682 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:31.760318100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62684 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:32.815541400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62686 --> cmp1-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:25:35.955638400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62710 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:38.960793600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63882 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:43.937312200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63901 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:49.083462400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63912 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:49.083462400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63913 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:49.083462400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63911 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:49.083462400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63910 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:49.083462400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63925 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:49.083462400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63929 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:49.876844500+08:00" level=info msg="[TCP] 127.0.0.1:63936 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T22:34:50.137964400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63918 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:50.137964400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63919 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:50.215086700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63921 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:50.215086700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63920 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:50.215186700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63924 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:50.215186700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63926 --> cmp2-hkg1.steamserver.net:27021 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:34:51.258217700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63927 --> ext1-maa2.steamserver.net:27038 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:53.286068800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65256 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:58.442263900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65266 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:58.442263900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65265 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:58.442263900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65264 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:58.442263900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65271 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:58.442263900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65280 --> cmp1-tyo3.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:58.442770300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65279 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:58.442866500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65282 --> cmp1-tyo3.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:59.257378600+08:00" level=info msg="[TCP] 127.0.0.1:65292 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T22:45:59.495755200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65275 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:59.495755200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65276 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:59.495755200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65274 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:59.495755200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65278 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:59.495755200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65281 --> cmp3-hkg1.steamserver.net:27021 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:45:59.496572900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65277 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:46:03.635773800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65291 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:51:59.632085900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49459 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:04.778548800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49477 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:04.778548800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49478 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:04.778548800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49480 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:04.778683900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49479 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:04.778683900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49492 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:04.778683900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49496 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:04.778683900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49493 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:05.592435700+08:00" level=info msg="[TCP] 127.0.0.1:49509 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T22:52:05.833895200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49489 --> cmp1-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:05.833895200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49488 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:05.833895200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49487 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:05.834014600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49494 --> cmp3-hkg1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:05.838549700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49490 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:06.896253200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49495 --> ext2-maa2.steamserver.net:27031 error: dns resolve failed: couldn't find ip"
time="2025-07-10T22:52:09.980569200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49508 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:03:56.000735400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51387 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:01.142994400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51412 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:01.142994400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51413 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:01.142994400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51414 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:01.142994400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51415 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:01.142994400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51433 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:01.142994400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51429 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:01.142994400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51428 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:01.947673300+08:00" level=info msg="[TCP] 127.0.0.1:51452 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T23:04:02.198347700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51425 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:02.198458500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51426 --> cmp1-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:02.198458500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51424 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:02.202180900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51427 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:03.255650700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51430 --> cmp1-hkg1.steamserver.net:27023 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:04:06.355998600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51447 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:28.325099300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54925 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:33.370733500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54958 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:38.503569400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54990 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:38.503569400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54991 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:38.503569400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55010 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:38.503569400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55020 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:38.507571700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54997 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:38.507571700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54996 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:38.507571700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55011 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:39.302621500+08:00" level=info msg="[TCP] 127.0.0.1:55041 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T23:24:39.553589000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55004 --> cmp1-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:39.553589000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55003 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:39.553589000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55002 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:39.554269200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55005 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:39.554269200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55012 --> cmp1-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:24:40.614488100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55013 --> cmp2-tyo3.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:35.701793600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57451 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:40.833103300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57467 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:40.833103300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57466 --> cmp1-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:40.833103300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57468 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:40.833103300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57484 --> cmp1-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:40.834292100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57481 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:40.837901700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57473 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:41.657491500+08:00" level=info msg="[TCP] 127.0.0.1:57502 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T23:38:41.840117400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57475 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:41.840117400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57474 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:41.844792800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57476 --> ext2-maa2.steamserver.net:27031 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:41.844843500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57477 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:42.845151300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57482 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:42.845233000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57480 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:42.845233000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57483 --> cmp2-hkg1.steamserver.net:27024 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:38:46.084835200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57497 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:44.041084700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58939 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:49.121632700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58954 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:54.295504500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58964 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:54.295504500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58963 --> cmp1-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:54.295622100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58981 --> cmp1-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:54.296136000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58970 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:54.296136000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58965 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:54.296136000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58976 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:55.123462000+08:00" level=info msg="[TCP] 127.0.0.1:59000 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-10T23:56:55.345558300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58972 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:55.345558300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58973 --> ext2-maa2.steamserver.net:27031 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:55.345558300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58971 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:55.348720300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58974 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:56.396720000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58975 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:56.396787600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58978 --> cmp2-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-10T23:56:56.400294900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58977 --> cmp1-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:08:55.512686500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59786 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:00.666162900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59796 --> ext2-syd1.steamserver.net:27033 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:00.666162900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59795 --> cmp1-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:00.666162900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59813 --> cmp1-tyo3.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:00.671475100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59801 --> cmp2-ord1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:00.671475100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59802 --> ext1-syd1.steamserver.net:27034 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:00.671475100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59809 --> cmp2-ord1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:01.511787600+08:00" level=info msg="[TCP] 127.0.0.1:59827 --> test.steampowered.com:80 using GLOBAL"
time="2025-07-11T00:09:01.718193500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59803 --> cmp3-hkg1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:01.718312700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59804 --> cmp1-ord1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:01.722186000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59805 --> ext2-maa2.steamserver.net:27031 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:01.722186000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59806 --> cmp2-sea1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:02.773496800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59808 --> cmp2-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:02.777621300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59810 --> cmp1-sgp1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:02.786118100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59811 --> cmp2-sgp1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:09:05.863474000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59825 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-07-11T00:23:40.152047600+08:00" level=warning msg="Mihomo shutting down"
