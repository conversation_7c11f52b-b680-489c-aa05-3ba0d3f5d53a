Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-05-31T19:35:23.775256200+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-31T19:35:23.783957200+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-31T19:35:23.783957200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-31T19:35:23.786116900+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-05-31T19:35:23.804394500+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-31T19:35:23.804394500+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-05-31T19:35:24.048616600+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-31T19:35:24.048616600+08:00" level=info msg="Load GeoSite rule: private"
time="2025-05-31T19:35:24.063630700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-31T19:35:24.065632600+08:00" level=info msg="Initial configuration complete, total time: 284ms"
time="2025-05-31T19:35:24.065632600+08:00" level=info msg="RESTful API listening at: 12*******:9097"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Mixed(http+socks) proxy listening at: 12*******:7897"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider TVer"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Disney"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Discord"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider NowE"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider BBC"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider telegram"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider google"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider apple"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider TVB"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Line"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider Lan"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-31T19:35:24.067634700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-31T19:35:29.068465500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.068465500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.068465500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.068465500+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-05-31T19:35:29.068465500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.068465500+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-05-31T19:35:29.068465500+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-05-31T19:35:29.068465500+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.070084000+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-05-31T19:35:29.070084000+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-05-31T19:35:29.070084000+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-05-31T19:35:29.069574600+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-05-31T19:35:29.069065600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:35:29.069065600+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-05-31T19:35:29.070084000+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-05-31T19:35:29.071357400+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-31T19:35:29.071357400+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-31T19:35:29.071357400+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-31T19:35:29.071357400+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-31T19:35:29.406478300+08:00" level=info msg="[TCP] 12*******:63608 --> download.clashverge.dev:443 using GLOBAL"
time="2025-05-31T19:35:29.688055700+08:00" level=info msg="[TCP] 12*******:63616 --> github.com:443 using GLOBAL"
time="2025-05-31T19:35:30.260992200+08:00" level=info msg="[TCP] 12*******:63642 --> objects.githubusercontent.com:443 using GLOBAL"
time="2025-05-31T19:35:32.326612800+08:00" level=info msg="[TCP] 12*******:63655 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:35:36.930350200+08:00" level=info msg="[TCP] 12*******:63660 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:35:50.673609200+08:00" level=info msg="[TCP] 12*******:63672 --> test.steampowered.com:80 using GLOBAL"
time="2025-05-31T19:35:50.765643700+08:00" level=info msg="[TCP] 12*******:63676 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:35:50.771296600+08:00" level=info msg="[TCP] 12*******:63675 --> ipv6check-http.steamserver.net:80 using GLOBAL"
time="2025-05-31T19:35:51.355886600+08:00" level=info msg="[TCP] 12*******:63680 --> cmp3-hkg1.steamserver.net:27024 using GLOBAL"
time="2025-05-31T19:35:51.357321800+08:00" level=info msg="[TCP] 12*******:63681 --> cmp1-hkg1.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:35:51.359061300+08:00" level=info msg="[TCP] 12*******:63686 --> cmp1-sgp1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:35:51.363948400+08:00" level=info msg="[TCP] 12*******:63682 --> cmp1-hkg1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:35:51.547190400+08:00" level=info msg="[TCP] 12*******:63690 --> e6.o.lencr.org:80 using GLOBAL"
time="2025-05-31T19:35:51.720049800+08:00" level=info msg="[TCP] 12*******:63690 --> e5.o.lencr.org:80 using GLOBAL"
time="2025-05-31T19:35:52.016266000+08:00" level=info msg="[TCP] 12*******:63696 --> cmp1-sgp1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:35:52.020474600+08:00" level=info msg="[TCP] 12*******:63695 --> cmp2-sgp1.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:35:52.138235200+08:00" level=info msg="[TCP] 12*******:63699 --> cmp1-tyo3.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:35:52.491674000+08:00" level=info msg="[TCP] 12*******:63701 --> cmp1-tyo3.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:35:52.497923900+08:00" level=info msg="[TCP] 12*******:63690 --> e5.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:35:52.645872000+08:00" level=info msg="[TCP] 12*******:63704 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:35:53.506968900+08:00" level=info msg="[TCP] 12*******:63708 --> cmp2-lax1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:35:53.509782000+08:00" level=info msg="[TCP] 12*******:63707 --> cmp1-tyo3.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:35:53.515060800+08:00" level=info msg="[TCP] 12*******:63711 --> cmp1-ord1.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:35:53.522336600+08:00" level=info msg="[TCP] 12*******:63713 --> ext2-maa2.steamserver.net:27030 using GLOBAL"
time="2025-05-31T19:35:53.882880000+08:00" level=info msg="[TCP] 12*******:63690 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-05-31T19:35:54.056963300+08:00" level=info msg="[TCP] 12*******:63690 --> e6.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:35:54.592983600+08:00" level=info msg="[TCP] 12*******:63718 --> cmp1-hkg1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:35:56.733567700+08:00" level=info msg="[TCP] 12*******:63731 --> shared.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:35:56.903629900+08:00" level=info msg="[TCP] 12*******:63733 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:35:57.128320200+08:00" level=info msg="[TCP] 12*******:63735 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:35:57.230695700+08:00" level=info msg="[TCP] 12*******:63738 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:35:57.260321000+08:00" level=info msg="[TCP] 12*******:63741 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:35:57.261875600+08:00" level=info msg="[TCP] 12*******:63740 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:35:57.871378900+08:00" level=info msg="[TCP] 12*******:63744 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:35:58.401834600+08:00" level=info msg="[TCP] 12*******:63746 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:35:58.699695900+08:00" level=info msg="[TCP] 12*******:63749 --> steamcommunity-a.akamaihd.net:443 using GLOBAL"
time="2025-05-31T19:35:59.180090200+08:00" level=info msg="[TCP] 12*******:63751 --> cdn.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:35:59.416880200+08:00" level=info msg="[TCP] 12*******:63754 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:35:59.718572700+08:00" level=info msg="[TCP] 12*******:63757 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:35:59.719830400+08:00" level=info msg="[TCP] 12*******:63763 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:35:59.720920700+08:00" level=info msg="[TCP] 12*******:63762 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:35:59.724271000+08:00" level=info msg="[TCP] 12*******:63758 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:35:59.725358600+08:00" level=info msg="[TCP] 12*******:63756 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:36:00.824830600+08:00" level=info msg="[TCP] 12*******:63767 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:36:04.387554400+08:00" level=info msg="[TCP] 12*******:63775 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:36:04.655190000+08:00" level=info msg="[TCP] 12*******:63771 --> shared.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:36:04.665653800+08:00" level=info msg="[TCP] 12*******:63773 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:36:05.178585700+08:00" level=info msg="[TCP] 12*******:63777 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:36:07.605992000+08:00" level=info msg="[TCP] 12*******:63781 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:36:08.173653900+08:00" level=info msg="[TCP] 12*******:63783 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:36:09.537763100+08:00" level=info msg="[TCP] 12*******:63796 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:36:09.892504300+08:00" level=info msg="[TCP] 12*******:63798 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:36:20.538234400+08:00" level=info msg="[TCP] 12*******:63814 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:36:20.598652000+08:00" level=info msg="[TCP] 12*******:63816 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:36:23.860075600+08:00" level=info msg="[TCP] 12*******:63823 --> cmp1-sgp1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:36:23.861543800+08:00" level=info msg="[TCP] 12*******:63824 --> cmp1-tyo3.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:36:23.862047000+08:00" level=info msg="[TCP] 12*******:63821 --> cmp1-sgp1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:36:23.863571300+08:00" level=info msg="[TCP] 12*******:63822 --> cmp2-sgp1.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:36:23.885585400+08:00" level=info msg="[TCP] 12*******:63830 --> download.clashverge.dev:443 using GLOBAL"
time="2025-05-31T19:36:24.020580300+08:00" level=info msg="[TCP] 12*******:63832 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:36:24.292553700+08:00" level=info msg="[TCP] 12*******:63834 --> cmp1-tyo3.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:36:24.357174400+08:00" level=info msg="[TCP] 12*******:63836 --> cmp2-lax1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:36:24.422277000+08:00" level=info msg="[TCP] 12*******:63838 --> cmp2-iad1.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:36:24.661310600+08:00" level=info msg="[TCP] 12*******:63841 --> ext2-maa2.steamserver.net:27024 using GLOBAL"
time="2025-05-31T19:36:24.926299600+08:00" level=info msg="[TCP] 12*******:63690 --> e6.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:36:24.950124100+08:00" level=info msg="[TCP] 12*******:63844 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:36:25.466361500+08:00" level=info msg="[TCP] 12*******:63856 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-31T19:36:25.735520500+08:00" level=info msg="[TCP] 12*******:63690 --> e5.o.lencr.org:80 using GLOBAL"
time="2025-05-31T19:36:34.748444400+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63867 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:36:35.351477400+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63874 --> cmp1-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:36:36.218699700+08:00" level=info msg="[TCP] 12*******:63888 --> test.steampowered.com:80 using GLOBAL"
time="2025-05-31T19:36:40.507878100+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63882 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:36:45.591646000+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63895 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:36:50.776040000+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63904 --> cmp1-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:36:50.776040000+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63905 --> cmp2-lax1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:36:50.781050000+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63910 --> cmp1-dfw2.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:36:50.784671900+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63911 --> ext1-syd1.steamserver.net:27035 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:36:51.894226600+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63913 --> cmp1-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:36:52.649752500+08:00" level=info msg="[TCP] 12*******:63920 --> test.steampowered.com:80 using GLOBAL"
time="2025-05-31T19:37:04.161367600+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63941 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:09.294833200+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63952 --> cmp2-lax1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:09.294833200+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63950 --> cmp1-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:09.294833200+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63951 --> cmp1-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:09.294833200+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63953 --> cmp1-dfw2.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:09.294833200+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63960 --> cmp1-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:10.122174200+08:00" level=info msg="[TCP] 12*******:63970 --> test.steampowered.com:80 using GLOBAL"
time="2025-05-31T19:37:10.358422100+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63958 --> ext1-maa2.steamserver.net:27038 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:14.442492200+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63969 --> ipv6check-http.steamserver.net:80 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:27.509596300+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:63994 --> api.steampowered.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:32.567929900+08:00" level=info msg="[TCP] 12*******:64027 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:37:32.636769300+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:64013 --> cmp1-hkg1.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:32.636769300+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:64015 --> cmp2-lax1.steamserver.net:27018 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:32.636769300+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:64014 --> cmp1-tyo3.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:32.636769300+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:64012 --> cmp1-hkg1.steamserver.net:27020 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:33.696632400+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:64022 --> ext2-maa2.steamserver.net:27022 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:33.696632400+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:64023 --> cmp1-dfw2.steamserver.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:34.813620600+08:00" level=warning msg="[TCP] dial GLOBAL 12*******:64024 --> cmp2-sgp1.steamserver.net:27019 error: dns resolve failed: couldn't find ip"
time="2025-05-31T19:37:35.108313300+08:00" level=info msg="[TCP] 12*******:64034 --> ipv6check-http.steamserver.net:80 using GLOBAL"
time="2025-05-31T19:37:35.617846900+08:00" level=info msg="[TCP] 12*******:64036 --> test.steampowered.com:80 using GLOBAL"
time="2025-05-31T19:37:40.477150700+08:00" level=info msg="[TCP] 12*******:64043 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:37:40.640691000+08:00" level=info msg="[TCP] 12*******:64045 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:37:41.069305800+08:00" level=info msg="[TCP] 12*******:64048 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:37:41.678701000+08:00" level=info msg="[TCP] 12*******:64059 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:37:41.678701000+08:00" level=info msg="[TCP] 12*******:64053 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:37:41.679204200+08:00" level=info msg="[TCP] 12*******:64057 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:37:41.682029500+08:00" level=info msg="[TCP] 12*******:64050 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:37:41.683205700+08:00" level=info msg="[TCP] 12*******:64054 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:37:41.686023700+08:00" level=info msg="[TCP] 12*******:64052 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:37:43.027149300+08:00" level=info msg="[TCP] 12*******:64063 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:37:43.075389300+08:00" level=info msg="[TCP] 12*******:64065 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:37:50.789772800+08:00" level=info msg="[TCP] 12*******:64072 --> client-update.queniuqe.com:443 using GLOBAL"
time="2025-05-31T19:37:50.933185900+08:00" level=info msg="[TCP] 12*******:64074 --> avatars.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:37:51.593263900+08:00" level=info msg="[TCP] 12*******:64076 --> media.st.dl.eccdnx.com:443 using GLOBAL"
time="2025-05-31T19:37:52.337190600+08:00" level=info msg="[TCP] 12*******:64079 --> ocsp.comodoca.com:80 using GLOBAL"
time="2025-05-31T19:37:52.571765000+08:00" level=info msg="[TCP] 12*******:64079 --> ocsp.usertrust.com:80 using GLOBAL"
time="2025-05-31T19:37:52.805163800+08:00" level=info msg="[TCP] 12*******:64079 --> ocsp.sectigo.com:80 using GLOBAL"
time="2025-05-31T19:37:53.285295400+08:00" level=info msg="[TCP] 12*******:64084 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:37:55.921321200+08:00" level=info msg="[TCP] 12*******:64098 --> x1.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:37:56.172613500+08:00" level=info msg="[TCP] 12*******:64098 --> ctldl.windowsupdate.com:80 using GLOBAL"
time="2025-05-31T19:37:56.465701800+08:00" level=info msg="[TCP] 12*******:64098 --> ctldl.windowsupdate.com:80 using GLOBAL"
time="2025-05-31T19:37:56.525143100+08:00" level=info msg="[TCP] 12*******:64104 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:37:56.526307000+08:00" level=info msg="[TCP] 12*******:64102 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:37:56.774823400+08:00" level=info msg="[TCP] 12*******:64109 --> cmp2-sgp1.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:37:56.775347800+08:00" level=info msg="[TCP] 12*******:64108 --> cmp1-hkg1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:37:56.779230000+08:00" level=info msg="[TCP] 12*******:64107 --> cmp1-hkg1.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:37:56.780253800+08:00" level=info msg="[TCP] 12*******:64110 --> cmp1-tyo3.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:37:57.398773700+08:00" level=info msg="[TCP] 12*******:64116 --> ext1-maa2.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:37:57.403559000+08:00" level=info msg="[TCP] 12*******:64115 --> cmp2-lax1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:37:57.898322500+08:00" level=info msg="[TCP] 12*******:64119 --> ext1-syd1.steamserver.net:27030 using GLOBAL"
time="2025-05-31T19:37:58.706469200+08:00" level=info msg="[TCP] 12*******:64122 --> cmp1-hkg1.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:37:59.572041400+08:00" level=info msg="[TCP] 12*******:64125 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:38:00.355333200+08:00" level=info msg="[TCP] 12*******:64127 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:00.546431900+08:00" level=info msg="[TCP] 12*******:64129 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:38:00.559519100+08:00" level=info msg="[TCP] 12*******:64131 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:38:00.732965800+08:00" level=info msg="[TCP] 12*******:64133 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:19.435138200+08:00" level=info msg="[TCP] 12*******:64151 --> ipv6check-http.steamserver.net:80 using GLOBAL"
time="2025-05-31T19:38:19.805132100+08:00" level=info msg="[TCP] 12*******:64148 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:38:20.534654200+08:00" level=info msg="[TCP] 12*******:64154 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:38:21.002323400+08:00" level=info msg="[TCP] 12*******:64156 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:22.265243400+08:00" level=info msg="[TCP] 12*******:64158 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:38:22.681613300+08:00" level=info msg="[TCP] 12*******:64161 --> cmp1-hkg1.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:38:22.681613300+08:00" level=info msg="[TCP] 12*******:64164 --> cmp1-sgp1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:38:22.681613300+08:00" level=info msg="[TCP] 12*******:64162 --> cmp3-hkg1.steamserver.net:27023 using GLOBAL"
time="2025-05-31T19:38:22.685161800+08:00" level=info msg="[TCP] 12*******:64163 --> cmp3-hkg1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:38:22.989911800+08:00" level=info msg="[TCP] 12*******:64169 --> cmp2-sgp1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:38:22.993347200+08:00" level=info msg="[TCP] 12*******:64170 --> cmp2-sgp1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:38:23.119010000+08:00" level=info msg="[TCP] 12*******:64174 --> cmp2-tyo3.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:38:23.119010000+08:00" level=info msg="[TCP] 12*******:64173 --> cmp2-tyo3.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:38:23.440072700+08:00" level=info msg="[TCP] 12*******:64079 --> e6.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:38:24.493295500+08:00" level=info msg="[TCP] 12*******:64179 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:38:24.734741900+08:00" level=info msg="[TCP] 12*******:64181 --> cmp2-tyo3.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:38:24.739542000+08:00" level=info msg="[TCP] 12*******:64182 --> cmp2-lax1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:38:24.743186600+08:00" level=info msg="[TCP] 12*******:64185 --> cmp1-sea1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:38:24.748065200+08:00" level=info msg="[TCP] 12*******:64187 --> cmp2-dfw2.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:38:25.304176700+08:00" level=info msg="[TCP] 12*******:64079 --> e5.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:38:26.604682400+08:00" level=info msg="[TCP] 12*******:64203 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:27.207446600+08:00" level=info msg="[TCP] 12*******:64205 --> cmp3-hkg1.steamserver.net:27023 using GLOBAL"
time="2025-05-31T19:38:28.849578400+08:00" level=info msg="[TCP] 12*******:64208 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:38:35.072801800+08:00" level=info msg="[TCP] 12*******:64214 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:38:36.025970100+08:00" level=info msg="[TCP] 12*******:64217 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:38:36.858144100+08:00" level=info msg="[TCP] 12*******:64219 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:38:37.408081300+08:00" level=info msg="[TCP] 12*******:64221 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:37.812008300+08:00" level=info msg="[TCP] 12*******:64224 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:38:38.052866400+08:00" level=info msg="[TCP] 12*******:64226 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:38.055838900+08:00" level=info msg="[TCP] 12*******:64228 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:38.460510700+08:00" level=info msg="[TCP] 12*******:64231 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:38:38.523479800+08:00" level=info msg="[TCP] 12*******:64233 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:38.534759900+08:00" level=info msg="[TCP] 12*******:64236 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:38:38.538315900+08:00" level=info msg="[TCP] 12*******:64235 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:38.550334100+08:00" level=info msg="[TCP] 12*******:64239 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:38:38.551385900+08:00" level=info msg="[TCP] 12*******:64241 --> clan.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:38:38.551385900+08:00" level=info msg="[TCP] 12*******:64242 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:38:38.803887800+08:00" level=info msg="[TCP] 12*******:64245 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:38.878731400+08:00" level=info msg="[TCP] 12*******:64247 --> clan.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:38:39.768444200+08:00" level=info msg="[TCP] 12*******:64249 --> www.youtube.com:443 using GLOBAL"
time="2025-05-31T19:38:47.153597900+08:00" level=info msg="[TCP] 12*******:64256 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:47.167058200+08:00" level=info msg="[TCP] 12*******:64258 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:47.873972900+08:00" level=info msg="[TCP] 12*******:64261 --> cache6-hkg1.steamcontent.com:443 using GLOBAL"
time="2025-05-31T19:38:48.248779300+08:00" level=info msg="[TCP] 12*******:64079 --> e5.o.lencr.org:80 using GLOBAL"
time="2025-05-31T19:38:53.428006400+08:00" level=info msg="[TCP] 12*******:64267 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:53.433085300+08:00" level=info msg="[TCP] 12*******:64269 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:53.940588300+08:00" level=info msg="[TCP] 12*******:64272 --> community.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:38:53.940588300+08:00" level=info msg="[TCP] 12*******:64273 --> store.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:38:54.202476500+08:00" level=info msg="[TCP] 12*******:64276 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:38:55.227301400+08:00" level=info msg="[TCP] 12*******:64288 --> www.youtube.com:443 using GLOBAL"
time="2025-05-31T19:39:02.651417100+08:00" level=info msg="[TCP] 12*******:64295 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:39:07.405252700+08:00" level=info msg="[TCP] 12*******:64301 --> test.steampowered.com:80 using GLOBAL"
time="2025-05-31T19:39:07.423159700+08:00" level=info msg="[TCP] 12*******:64304 --> ipv6check-http.steamserver.net:80 using GLOBAL"
time="2025-05-31T19:39:07.446176800+08:00" level=info msg="[TCP] 12*******:64306 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:39:08.259030600+08:00" level=info msg="[TCP] 12*******:64312 --> cmp1-hkg1.steamserver.net:27021 using GLOBAL"
time="2025-05-31T19:39:08.259030600+08:00" level=info msg="[TCP] 12*******:64311 --> cmp3-hkg1.steamserver.net:27024 using GLOBAL"
time="2025-05-31T19:39:08.262610100+08:00" level=info msg="[TCP] 12*******:64316 --> cmp2-tyo3.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:39:08.265381600+08:00" level=info msg="[TCP] 12*******:64313 --> cmp2-tyo3.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:39:08.581304800+08:00" level=info msg="[TCP] 12*******:64321 --> cmp2-tyo3.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:39:08.586753000+08:00" level=info msg="[TCP] 12*******:64323 --> cmp2-hkg1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:39:08.768285800+08:00" level=info msg="[TCP] 12*******:64325 --> e5.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:39:09.565231700+08:00" level=info msg="[TCP] 12*******:64328 --> cmp1-sgp1.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:39:09.568657500+08:00" level=info msg="[TCP] 12*******:64330 --> cmp2-sgp1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:39:09.616231400+08:00" level=info msg="[TCP] 12*******:64333 --> cmp1-lax1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:39:09.617608700+08:00" level=info msg="[TCP] 12*******:64332 --> cmp2-sgp1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:39:10.122915900+08:00" level=info msg="[TCP] 12*******:64325 --> e6.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:39:10.918771400+08:00" level=info msg="[TCP] 12*******:64338 --> cmp1-dfw2.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:39:10.919274400+08:00" level=info msg="[TCP] 12*******:64337 --> cmp1-dfw2.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:39:12.267320500+08:00" level=info msg="[TCP] 12*******:64343 --> cmp3-hkg1.steamserver.net:27024 using GLOBAL"
time="2025-05-31T19:39:16.450808500+08:00" level=info msg="[TCP] 12*******:64347 --> shared.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:39:16.737383500+08:00" level=info msg="[TCP] 12*******:64349 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:39:17.316130600+08:00" level=info msg="[TCP] 12*******:64352 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:39:17.379753700+08:00" level=info msg="[TCP] 12*******:64355 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:39:17.381379400+08:00" level=info msg="[TCP] 12*******:64354 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:39:17.460373700+08:00" level=info msg="[TCP] 12*******:64358 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:39:17.608001100+08:00" level=info msg="[TCP] 12*******:64360 --> cache6-hkg1.steamcontent.com:443 using GLOBAL"
time="2025-05-31T19:39:18.115361000+08:00" level=info msg="[TCP] 12*******:64364 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:39:18.431668100+08:00" level=info msg="[TCP] 12*******:64366 --> steamcommunity-a.akamaihd.net:443 using GLOBAL"
time="2025-05-31T19:39:18.601468800+08:00" level=info msg="[TCP] 12*******:64368 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:39:18.941309500+08:00" level=info msg="[TCP] 12*******:64370 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:39:19.275525900+08:00" level=info msg="[TCP] 12*******:64372 --> cdn.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:39:27.053540000+08:00" level=info msg="[TCP] 12*******:64391 --> ipv6check-http.steamserver.net:80 using GLOBAL"
time="2025-05-31T19:39:27.480300100+08:00" level=info msg="[TCP] 12*******:64389 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:39:32.586493500+08:00" level=info msg="[TCP] 12*******:64505 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:39:33.056638000+08:00" level=info msg="[TCP] 12*******:64507 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:39:33.371877600+08:00" level=info msg="[TCP] 12*******:64512 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:39:33.371877600+08:00" level=info msg="[TCP] 12*******:64510 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:39:33.373376600+08:00" level=info msg="[TCP] 12*******:64513 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:39:33.377679200+08:00" level=info msg="[TCP] 12*******:64511 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:39:34.802414600+08:00" level=info msg="[TCP] 12*******:64525 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:39:35.844549800+08:00" level=info msg="[TCP] 12*******:64528 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:39:38.049070000+08:00" level=info msg="[TCP] 12*******:64531 --> shared.fastly.steamstatic.com:443 using GLOBAL"
time="2025-05-31T19:39:38.308701600+08:00" level=info msg="[TCP] 12*******:64533 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:39:38.872459200+08:00" level=info msg="[TCP] 12*******:64537 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:39:39.029274500+08:00" level=error msg="🇯🇵日本03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-31T19:39:39.029274500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-31T19:39:39.040904200+08:00" level=info msg="[TCP] 12*******:64542 --> cmp2-tyo3.steamserver.net:27019 using GLOBAL"
time="2025-05-31T19:39:39.040904200+08:00" level=info msg="[TCP] 12*******:64539 --> cmp1-hkg1.steamserver.net:27024 using GLOBAL"
time="2025-05-31T19:39:39.042673500+08:00" level=info msg="[TCP] 12*******:64540 --> cmp3-hkg1.steamserver.net:27025 using GLOBAL"
time="2025-05-31T19:39:39.046194500+08:00" level=info msg="[TCP] 12*******:64541 --> cmp2-tyo3.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:39:39.154803300+08:00" level=info msg="[TCP] 12*******:64547 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:39:39.326819800+08:00" level=info msg="[TCP] 12*******:64550 --> cmp2-tyo3.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:39:39.376972200+08:00" level=info msg="[TCP] 12*******:64552 --> cmp1-hkg1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:39:39.504084300+08:00" level=info msg="[TCP] 12*******:64554 --> cmp1-sgp1.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:39:39.520626900+08:00" level=info msg="[TCP] 12*******:64555 --> cmp1-sgp1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:39:39.724365000+08:00" level=info msg="[TCP] 12*******:64558 --> cmp1-sgp1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:39:39.889230300+08:00" level=info msg="[TCP] 12*******:64561 --> cmp2-sea1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:39:39.890374600+08:00" level=info msg="[TCP] 12*******:64560 --> cmp1-lax1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:39:40.387456400+08:00" level=info msg="[TCP] 12*******:64325 --> e5.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:39:40.586917200+08:00" level=info msg="[TCP] 12*******:64565 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:39:41.275847800+08:00" level=info msg="[TCP] 12*******:64567 --> cmp1-hkg1.steamserver.net:27024 using GLOBAL"
time="2025-05-31T19:39:46.559816800+08:00" level=info msg="[TCP] 12*******:64573 --> activity.windows.com:443 using GLOBAL"
time="2025-05-31T19:39:47.673826600+08:00" level=info msg="[TCP] 12*******:64576 --> cache7-hkg1.steamcontent.com:443 using GLOBAL"
time="2025-05-31T19:39:47.882479300+08:00" level=info msg="[TCP] 12*******:64325 --> e6.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:39:57.830453300+08:00" level=info msg="[TCP] 12*******:64598 --> ipv6check-http.steamserver.net:80 using GLOBAL"
time="2025-05-31T19:39:58.221609500+08:00" level=info msg="[TCP] 12*******:64596 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:40:01.115030600+08:00" level=info msg="[TCP] 12*******:64602 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:40:01.142022100+08:00" level=info msg="[TCP] 12*******:64604 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:40:04.610976000+08:00" level=info msg="[TCP] 12*******:64716 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:40:06.467095900+08:00" level=info msg="[TCP] 12*******:64719 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:40:06.676770900+08:00" level=info msg="[TCP] 12*******:64721 --> update.googleapis.com:443 using GLOBAL"
time="2025-05-31T19:40:09.655039000+08:00" level=info msg="[TCP] 12*******:64729 --> steamcommunity.com:443 using GLOBAL"
time="2025-05-31T19:40:09.727055800+08:00" level=info msg="[TCP] 12*******:64731 --> api.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:40:11.248820200+08:00" level=info msg="[TCP] 12*******:64735 --> cmp1-hkg1.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:40:11.251906000+08:00" level=info msg="[TCP] 12*******:64734 --> cmp3-hkg1.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:40:11.253432100+08:00" level=info msg="[TCP] 12*******:64739 --> cmp2-tyo3.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:40:11.259132000+08:00" level=info msg="[TCP] 12*******:64736 --> cmp2-tyo3.steamserver.net:27020 using GLOBAL"
time="2025-05-31T19:40:12.249260200+08:00" level=info msg="[TCP] 12*******:64743 --> cmp2-tyo3.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:40:12.250438900+08:00" level=info msg="[TCP] 12*******:64748 --> cmp1-sgp1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:40:12.253346600+08:00" level=info msg="[TCP] 12*******:64744 --> cmp3-hkg1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:40:12.254349200+08:00" level=info msg="[TCP] 12*******:64745 --> cmp2-sgp1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:40:13.230347900+08:00" level=info msg="[TCP] 12*******:64752 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:40:13.231392700+08:00" level=info msg="[TCP] 12*******:64753 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:40:13.258304300+08:00" level=info msg="[TCP] 12*******:64756 --> cmp2-sgp1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:40:13.258304300+08:00" level=info msg="[TCP] 12*******:64761 --> cmp1-sea1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:40:13.263004900+08:00" level=info msg="[TCP] 12*******:64757 --> cmp2-lax1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:40:13.263004900+08:00" level=info msg="[TCP] 12*******:64758 --> cmp1-lax1.steamserver.net:27018 using GLOBAL"
time="2025-05-31T19:40:13.494008700+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-31T19:40:13.494008700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-31T19:40:13.494008700+08:00" level=error msg="🇰🇷韩国02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-31T19:40:13.494008700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-31T19:40:14.382108500+08:00" level=info msg="[TCP] 12*******:64765 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-31T19:40:14.383427700+08:00" level=info msg="[TCP] 12*******:64764 --> cmp2-lax1.steamserver.net:443 using GLOBAL"
time="2025-05-31T19:40:19.333902400+08:00" level=info msg="[TCP] 12*******:64773 --> cache8-hkg1.steamcontent.com:443 using GLOBAL"
time="2025-05-31T19:40:20.108600700+08:00" level=info msg="[TCP] 12*******:64325 --> e6.c.lencr.org:80 using GLOBAL"
time="2025-05-31T19:40:34.514651200+08:00" level=info msg="[TCP] 12*******:64821 --> ipv6check-http.steamserver.net:80 using GLOBAL"
time="2025-05-31T19:40:34.856369600+08:00" level=info msg="[TCP] 12*******:64817 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-31T19:40:34.859009600+08:00" level=info msg="[TCP] 12*******:64818 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-31T19:40:35.716820800+08:00" level=info msg="[TCP] 12*******:64824 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:40:36.015205600+08:00" level=info msg="[TCP] 12*******:64827 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:40:36.658693300+08:00" level=info msg="[TCP] 12*******:64830 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:40:37.962821400+08:00" level=info msg="[TCP] 12*******:64833 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:40:38.275891100+08:00" level=info msg="[TCP] 12*******:64835 --> store.steampowered.com:443 using GLOBAL"
time="2025-05-31T19:40:38.964609400+08:00" level=info msg="[TCP] 12*******:64838 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-31T19:40:38.970707900+08:00" level=info msg="[TCP] 12*******:64837 --> unpm-upaas.uc.cn:443 using GLOBAL"
