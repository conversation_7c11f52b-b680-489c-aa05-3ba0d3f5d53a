Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-05-30T20:28:00.199338400+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-30T20:28:00.228609300+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-30T20:28:00.228609300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-30T20:28:00.229635100+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-05-30T20:28:00.252652800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-30T20:28:00.252652800+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-05-30T20:28:00.512454700+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-30T20:28:00.512454700+08:00" level=info msg="Load GeoSite rule: private"
time="2025-05-30T20:28:00.527468200+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-30T20:28:00.530471600+08:00" level=info msg="Initial configuration complete, total time: 305ms"
time="2025-05-30T20:28:00.531472400+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-05-30T20:28:00.532473100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-30T20:28:00.532473100+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-05-30T20:28:00.532473100+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-05-30T20:28:00.532473100+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider TVB"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Lan"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider google"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Line"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider TVer"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider apple"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider telegram"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Discord"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider NowE"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Disney"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider BBC"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-30T20:28:00.549488800+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-30T20:28:03.131205500+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-30T20:28:03.132220000+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-30T20:28:03.132220000+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-30T20:28:03.132727500+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-30T20:28:03.132727500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-30T20:28:03.132727500+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-30T20:28:03.133768300+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-05-30T20:28:05.552314500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552411100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552411100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552411100+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-05-30T20:28:05.552411100+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-05-30T20:28:05.552411100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552411100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552411100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552411100+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-05-30T20:28:05.552411100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552411100+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-05-30T20:28:05.552411100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552411100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552411100+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-05-30T20:28:05.552411100+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-05-30T20:28:05.552411100+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-05-30T20:28:05.552411100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552411100+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-05-30T20:28:05.552411100+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-05-30T20:28:05.552411100+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.552925400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-05-30T20:28:05.552925400+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-05-30T20:28:05.553431100+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-05-30T20:28:05.556319800+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-30T20:28:05.556319800+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-30T20:28:05.556319800+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-30T20:28:05.556822200+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider apple"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Lan"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider TVB"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider telegram"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Line"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider NowE"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider TVer"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Discord"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Disney"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider BBC"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider google"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-30T20:28:05.558354900+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-05-30T20:28:10.559424900+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.559433300+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-05-30T20:28:10.559433300+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.559433300+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-05-30T20:28:10.559433300+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.559433300+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-05-30T20:28:10.559433300+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.559433300+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-05-30T20:28:10.559433300+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:10.559433300+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-05-30T20:28:10.559433300+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-05-30T20:28:10.558554600+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-05-30T20:28:10.561465000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-30T20:28:10.561465000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-30T20:28:10.561465000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-30T20:28:10.561465000+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-30T20:28:10.862649600+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-30T20:28:10.863159700+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-30T20:28:10.863159700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-30T20:28:10.863671400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-30T20:28:10.863671400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-30T20:28:10.863671400+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-30T20:28:10.864695200+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-05-30T20:28:10.866224900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Discord"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider BBC"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider telegram"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider TVB"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider TVer"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Lan"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider NowE"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Line"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Disney"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider google"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider apple"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-30T20:28:10.866734600+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-30T20:28:15.599297000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50422 --> ecs.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.784643400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50431 --> download.clashverge.dev:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-05-30T20:28:15.869223100+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-05-30T20:28:15.867646600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:15.868711700+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-05-30T20:28:15.869223100+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-05-30T20:28:15.873873100+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-30T20:28:15.873873100+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-30T20:28:15.873873100+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-30T20:28:15.873873100+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-30T20:28:22.059745900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50569 --> self.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:22.979484500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50583 --> cp501.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:23.148754700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50584 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:23.570035200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50587 --> access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:27.507321000+08:00" level=info msg="[TCP] 127.0.0.1:50615 --> userver-upaas.uc.cn:443 using GLOBAL"
time="2025-05-30T20:28:28.149720600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50595 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:28.571167700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50600 --> access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:29.326619800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50605 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:30.603531400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50610 --> ecs.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:30.993783700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50611 --> cp501.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:31.649652700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50612 --> static.edge.microsoftapp.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:31.711786200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50613 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:31.711786200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50620 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:31.711786200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50627 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:32.038519900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50614 --> blacklist.tampermonkey.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:32.518213600+08:00" level=info msg="[TCP] 127.0.0.1:50654 --> update.pan.baidu.com:443 using GLOBAL"
time="2025-05-30T20:28:33.175240200+08:00" level=info msg="[TCP] 127.0.0.1:50657 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-05-30T20:28:33.791158000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50622 --> self.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:34.794982900+08:00" level=info msg="[TCP] 127.0.0.1:50671 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-05-30T20:28:35.789916600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50630 --> download.clashverge.dev:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:36.651110200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50636 --> static.edge.microsoftapp.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:36.711896400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50645 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:36.711896400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50646 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:36.711896400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50647 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:36.734926900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50648 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:37.038898800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50649 --> blacklist.tampermonkey.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:39.003399100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50663 --> cp501.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:39.549799500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50669 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:39.549864600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50690 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:40.791076000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50674 --> github.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:41.712465300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50706 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:41.735286600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50684 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:44.084124000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50691 --> self.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:44.335846300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50696 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:44.550164200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50726 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:44.550164200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50703 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:44.550164200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50702 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:45.606942300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50704 --> ecs.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:46.712793100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50711 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:46.712793100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50733 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:47.016199000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50712 --> cp501.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:47.030733500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50717 --> kv601.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:47.503219700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50718 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:47.503278400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50719 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:47.503787100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50720 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:49.550351500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50731 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:51.713613700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50738 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:52.501313500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50740 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:52.503882000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50750 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:52.503882000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50749 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:52.504595400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50751 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:54.304718800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50757 --> self.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:54.550701700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50764 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:54.550701700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50779 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:55.026630800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50763 --> cp501.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:56.720236000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50766 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:58.538734000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50772 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:58.538805100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50773 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:58.539365600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50778 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:59.550774300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50785 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:28:59.550774300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50784 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:00.083083400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50787 --> storeedgefd.dsx.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:00.614167900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50792 --> ecs.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:01.721350400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50794 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:03.037913300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50800 --> cp501.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:03.539320900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50806 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:03.539320900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50805 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:03.540336800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50807 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:04.328084200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50813 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:05.384844700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50818 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:06.473791500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50824 --> self.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:08.539855600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50841 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:08.539855600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50842 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:09.896802000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50835 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:09.896802000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50854 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:10.201236400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50843 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:10.385465600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50844 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:10.385465600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50852 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:11.047609400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50850 --> cp501.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:13.453504200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50857 --> settings-win.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:13.540139100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50867 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:13.540139100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50866 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:14.897294800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50873 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:14.897294800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50872 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:15.202628900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50875 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:15.385619900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50884 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:15.631473100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50885 --> ecs.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:16.672412800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50887 --> self.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:16.783020100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50892 --> static.nvidiagrid.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:17.777468100+08:00" level=info msg="[TCP] 127.0.0.1:50918 --> 127.0.0.1:33331 using GLOBAL"
time="2025-05-30T20:29:19.015776800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50898 --> storeedgefd.dsx.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:19.053313500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50899 --> cp501.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:19.332948200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50901 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:19.897582200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50910 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:19.897582200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50936 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:22.569964800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50913 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:22.793445000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50920 --> download.clashverge.dev:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:23.560970800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50928 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:23.560970800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50923 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:23.561058800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50924 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:24.025823300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50930 --> activity.windows.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:24.898107400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50941 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:24.898107400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50942 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:27.062713300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51053 --> cp501.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:27.150851100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51054 --> self.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:27.793788900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51056 --> github.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:28.562303100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51064 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:28.562303100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51062 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:28.562303100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51063 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:28.940595500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51069 --> access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:29.031531900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51070 --> activity.windows.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:33.941481100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51099 --> access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:34.339265000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51104 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:29:54.346199300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51225 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-30T20:30:09.359312100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51248 --> g.live.com:443 error: dns resolve failed: couldn't find ip"
