2025-07-13 23:03:43 INFO - try to run core in service mode
2025-07-13 23:03:43 INFO - start service: {"config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-13-2303.log", "core_type": "verge-mihomo-alpha"}
2025-07-13 23:03:43 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-13 23:03:43 INFO - No hotkeys configured
2025-07-13 23:03:43 INFO - Starting to create window
2025-07-13 23:03:43 INFO - Creating new window
2025-07-13 23:03:44 INFO - Window created successfully, attempting to show
2025-07-13 23:03:44 INFO - running timer task `R3SfPp0qApId`
2025-07-13 23:03:44 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-13 23:03:44 INFO - Successfully registered hotkey Control+Q for quit
2025-07-13 23:03:47 ERROR - timer task update error: failed to fetch remote profile with status 403 Forbidden
