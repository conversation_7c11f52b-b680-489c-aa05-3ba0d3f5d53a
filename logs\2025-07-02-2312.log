2025-07-02 23:12:37 INFO - try to run core in service mode
2025-07-02 23:12:37 INFO - start service: {"config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-02-2312.log", "core_type": "verge-mihomo-alpha", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe"}
2025-07-02 23:12:37 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-02 23:12:37 INFO - No hotkeys configured
2025-07-02 23:12:37 INFO - Starting to create window
2025-07-02 23:12:37 INFO - Creating new window
2025-07-02 23:12:37 INFO - Window created successfully, attempting to show
2025-07-02 23:12:37 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-02 23:12:37 INFO - Successfully registered hotkey Control+Q for quit
2025-07-02 23:12:38 INFO - running timer task `R3SfPp0qApId`
2025-07-03 01:15:39 INFO - Starting to create window
2025-07-03 01:15:39 INFO - Found existing window, trying to show it
2025-07-03 01:15:39 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-03 01:15:39 INFO - Successfully registered hotkey Control+Q for quit
