Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-05-28T00:23:56.372344200+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-28T00:23:56.380034400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-28T00:23:56.380034400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-28T00:23:56.381054500+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-05-28T00:23:56.390928500+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-28T00:23:56.391438100+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-05-28T00:23:56.574585100+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-28T00:23:56.574585100+08:00" level=info msg="Load GeoSite rule: private"
time="2025-05-28T00:23:56.585594800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-28T00:23:56.588598300+08:00" level=info msg="Initial configuration complete, total time: 210ms"
time="2025-05-28T00:23:56.588598300+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-05-28T00:23:56.589599000+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-28T00:23:56.589599000+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-05-28T00:23:56.589599000+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-05-28T00:23:56.589599000+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-05-28T00:23:56.589599000+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider apple"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Lan"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Disney"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider NowE"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider TVer"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider google"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Line"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Discord"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider TVB"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider telegram"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider BBC"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-28T00:23:56.590599400+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-28T00:24:01.812252500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.812252500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.813278700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.813278700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.813278700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.813278700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.814814800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.814814800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.814814800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.814814800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.816490900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.816490900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.816490900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.816490900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.816490900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.816490900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.816490900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.817000700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.817000700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.817000700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.817000700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.817000700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.817000700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822014400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822014400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822014400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822014400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822014400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822014400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822014400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822531500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822531500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822531500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822531500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822531500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822531500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822531500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.822531500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.823045800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.823045800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.823045800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.823045800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.823045800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.823045800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.823045800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.830720700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.830720700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.830720700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:01.830720700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:02.438643600+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-05-28T00:24:02.444054900+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-05-28T00:24:02.470101300+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-05-28T00:24:02.473987100+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-05-28T00:24:02.475374400+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-05-28T00:24:02.479218900+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-05-28T00:24:02.479218900+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-05-28T00:24:02.479860300+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-05-28T00:24:02.503052800+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-05-28T00:24:02.511817200+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-05-28T00:24:02.550468300+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-05-28T00:24:02.565496800+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-05-28T00:24:02.565496800+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-05-28T00:24:02.570204000+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-05-28T00:24:02.581392500+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-05-28T00:24:02.584092200+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-05-28T00:24:02.591058400+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-05-28T00:24:02.609436300+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-05-28T00:24:02.610868400+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-05-28T00:24:02.614637000+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-05-28T00:24:02.625911400+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-05-28T00:24:02.626448700+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-05-28T00:24:02.627151900+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-05-28T00:24:02.637237600+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-05-28T00:24:02.642246800+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-05-28T00:24:02.676743100+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-05-28T00:24:02.694369000+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-05-28T00:24:02.696069400+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-05-28T00:24:02.700641400+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-05-28T00:24:02.706810900+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-05-28T00:24:02.712989400+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-05-28T00:24:02.719673500+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-05-28T00:24:02.735337300+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-05-28T00:24:02.741304100+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-05-28T00:24:02.747504700+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-05-28T00:24:02.753290700+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-05-28T00:24:02.761459900+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-05-28T00:24:02.803067900+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-05-28T00:24:02.803067900+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-05-28T00:24:02.823916100+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-05-28T00:24:02.983294200+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-05-28T00:24:02.989918100+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-05-28T00:24:03.005282400+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-05-28T00:24:03.051360700+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-05-28T00:24:03.223476300+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-28T00:24:03.223985400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-28T00:24:03.223985400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-28T00:24:03.224996400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-28T00:24:03.225004200+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-28T00:24:03.225004200+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-28T00:24:03.226026300+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-05-28T00:24:04.024027100+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-05-28T00:24:04.326280400+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-05-28T00:24:04.391238000+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-05-28T00:24:06.590679700+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": net/http: TLS handshake timeout"
time="2025-05-28T00:24:06.591801700+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": net/http: TLS handshake timeout"
time="2025-05-28T00:24:06.594440500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-28T00:24:06.594440500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-28T00:24:06.594440500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-28T00:24:06.594440500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider TVB"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Line"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Lan"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider TVer"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider telegram"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Discord"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider NowE"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider google"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider apple"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider BBC"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Disney"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-28T00:24:06.595974000+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-28T00:24:11.649360200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.649360200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.650381800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.650888700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.652006200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.652006200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.652006200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.652804300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.652804300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.653752300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.653752300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.653752300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.653752300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.655581200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.655581200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.768255700+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-05-28T00:24:11.768255700+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-05-28T00:24:11.783267200+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-05-28T00:24:11.847755300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.847755300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.847755300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.848695900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.848695900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:11.850486800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.392227700+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-05-28T00:24:12.464821900+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-05-28T00:24:12.512366300+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-05-28T00:24:12.543531900+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-05-28T00:24:12.566896300+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-05-28T00:24:12.579772600+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-05-28T00:24:12.598644900+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-05-28T00:24:12.602107200+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-05-28T00:24:12.663222500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.663222500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.665169000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.665169000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.666792300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.668952600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.670120600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.670120600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.670120600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.670120600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.671393100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.671393100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.782887000+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-05-28T00:24:12.805309600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.805309600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.805309600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.806670800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.806670800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.806670800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.808123700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:12.816652000+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-05-28T00:24:12.822015600+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-05-28T00:24:12.885404900+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-05-28T00:24:12.941972900+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-05-28T00:24:12.946299000+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-05-28T00:24:12.992397100+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-05-28T00:24:13.015535000+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-05-28T00:24:13.084169600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:13.084169600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:13.085605700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:13.085605700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:13.085605700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:13.085605700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:13.085605700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:13.086631300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:13.236004500+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-05-28T00:24:13.297078800+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-05-28T00:24:13.518063100+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-05-28T00:24:13.541735600+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-05-28T00:24:13.823846100+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-05-28T00:24:13.841333400+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-05-28T00:24:13.863669900+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-05-28T00:24:13.930049900+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-05-28T00:24:13.962754000+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-05-28T00:24:14.101911400+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-05-28T00:24:14.108062000+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-05-28T00:24:14.115325800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:14.190485900+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-05-28T00:24:14.684194200+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-05-28T00:24:14.687339900+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-05-28T00:24:14.693564100+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-05-28T00:24:14.728180900+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-05-28T00:24:14.757824700+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-05-28T00:24:14.778770600+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-05-28T00:24:14.785159000+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-05-28T00:24:14.796393700+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-05-28T00:24:14.828817400+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-05-28T00:24:14.847671700+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-05-28T00:24:14.863306900+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-05-28T00:24:15.099806200+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-05-28T00:24:15.365946700+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-05-28T00:24:15.446774900+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-05-28T00:24:15.619439800+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-05-28T00:24:16.553492700+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-05-28T00:24:18.832919600+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-05-28T00:24:26.597369900+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/users/sign_in\": context deadline exceeded"
time="2025-05-28T00:24:26.601935700+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-28T00:24:26.601935700+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-28T00:24:26.601935700+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-28T00:24:26.601935700+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-28T00:24:31.572967300+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-28T00:24:31.573473300+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-28T00:24:31.573473300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-28T00:24:31.573983000+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-28T00:24:31.573983000+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-28T00:24:31.573983000+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-28T00:24:31.575003800+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-05-28T00:24:31.576528300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Lan"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider NowE"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider BBC"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider google"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Disney"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Discord"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider telegram"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider TVer"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider TVB"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider apple"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Line"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-28T00:24:31.577040000+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-28T00:24:35.617490900+08:00" level=error msg="🇰🇷韩国01 [5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-05-28T00:24:35.617490900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-28T00:24:36.629971400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.632640500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.632640500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.633714400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.633714400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.634599700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.634599700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.634599700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.636410700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.636410700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.636410700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.638358100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.638358100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.664768400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.666971200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.666971200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.667550800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.667550800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:36.667550800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.278757300+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-05-28T00:24:37.294637600+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-05-28T00:24:37.321316300+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-05-28T00:24:37.331622100+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-05-28T00:24:37.331622100+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-05-28T00:24:37.356192700+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-05-28T00:24:37.361982500+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-05-28T00:24:37.421433800+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-05-28T00:24:37.430321900+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-05-28T00:24:37.573022200+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-05-28T00:24:37.650910700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.650910700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.652809700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.652809700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.661449500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.664185900+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-05-28T00:24:37.729741200+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-05-28T00:24:37.759826100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.759826100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.759826100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.760385100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.762903200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.762903200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.762903200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.762903200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.762903200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.764964100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:37.808915500+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-05-28T00:24:37.817107100+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-05-28T00:24:37.857555000+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-05-28T00:24:38.123668100+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-05-28T00:24:38.421652200+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-05-28T00:24:38.558197500+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-05-28T00:24:38.567485400+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-05-28T00:24:38.573975300+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-05-28T00:24:38.777260400+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-05-28T00:24:38.777909900+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-05-28T00:24:38.792586400+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-05-28T00:24:38.881925600+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-05-28T00:24:38.905892900+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-05-28T00:24:39.143470200+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-05-28T00:24:39.289025400+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-05-28T00:24:39.461199900+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-05-28T00:24:39.467042700+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-05-28T00:24:39.470098000+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-05-28T00:24:39.478509600+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-05-28T00:24:39.573197800+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-05-28T00:24:39.669022900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:39.669534400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:39.669534400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:39.671361300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:39.671361300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[流量：49.61 GB/600 GB]"
time="2025-05-28T00:24:40.402599500+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-05-28T00:24:40.405182900+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-05-28T00:24:41.238685700+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-05-28T00:24:41.477098300+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-05-28T00:24:41.577047100+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": net/http: TLS handshake timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": net/http: TLS handshake timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": net/http: TLS handshake timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": net/http: TLS handshake timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="[TCP] dial 贤者云xianzheyun.top (match Match/) mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp *************:19812: i/o timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="[TCP] dial 贤者云xianzheyun.top (match Match/) mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp *************:19812: i/o timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="[TCP] dial 贤者云xianzheyun.top (match Match/) mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp *************:19812: i/o timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="[TCP] dial 贤者云xianzheyun.top (match Match/) mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp *************:19812: i/o timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="[TCP] dial 贤者云xianzheyun.top (match Match/) mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp *************:19812: i/o timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="[TCP] dial 贤者云xianzheyun.top (match Match/) mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp *************:19812: i/o timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="[TCP] dial 贤者云xianzheyun.top (match Match/) mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp *************:19812: i/o timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="[TCP] dial 贤者云xianzheyun.top (match Match/) mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp *************:19812: i/o timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": net/http: TLS handshake timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="[TCP] dial 贤者云xianzheyun.top (match Match/) mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp *************:19812: i/o timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="because 自动选择 failed multiple times, active health check"
time="2025-05-28T00:24:41.577816000+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-05-28T00:24:41.577816000+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-05-28T00:24:41.577816000+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": net/http: TLS handshake timeout"
time="2025-05-28T00:24:41.577816000+08:00" level=warning msg="[TCP] dial 贤者云xianzheyun.top (match Match/) mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp *************:19812: i/o timeout"
time="2025-05-28T00:24:41.704196500+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-05-28T00:24:41.738829100+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-05-28T00:24:47.914164600+08:00" level=warning msg="because 自动选择 failed multiple times, active health check"
time="2025-05-28T00:24:51.578921000+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/users/sign_in\": context deadline exceeded"
time="2025-05-28T00:24:51.587751000+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-28T00:24:51.587751000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-28T00:24:51.587751000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-28T00:24:51.587751000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-28T00:24:54.757431800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 match Match using 贤者云xianzheyun.top[您的ISP：中国辽宁省大连市【移动】 ]"
time="2025-05-28T00:24:58.304136100+08:00" level=error msg="🇰🇷韩国02 [2x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-05-28T00:24:58.304136100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-28T00:24:59.703460700+08:00" level=info msg="[TCP] 127.0.0.1:63154 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-28T00:25:01.688524900+08:00" level=error msg="🇰🇷韩国02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-28T00:25:01.688524900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-28T00:25:01.688524900+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-05-28T00:25:01.688524900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-05-28T00:25:07.074898300+08:00" level=info msg="[TCP] 127.0.0.1:63171 --> clashcn.com:443 using GLOBAL"
time="2025-05-28T00:25:07.567564600+08:00" level=info msg="[TCP] 127.0.0.1:63173 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-28T00:25:08.785210800+08:00" level=info msg="[TCP] 127.0.0.1:62963 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-05-28T00:25:08.928350800+08:00" level=info msg="[TCP] 127.0.0.1:63177 --> cn.bing.com:443 using GLOBAL"
time="2025-05-28T00:25:09.107929500+08:00" level=info msg="[TCP] 127.0.0.1:63179 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:25:11.042683900+08:00" level=info msg="[TCP] 127.0.0.1:63182 --> th.bing.com:443 using GLOBAL"
time="2025-05-28T00:25:11.113341100+08:00" level=info msg="[TCP] 127.0.0.1:63184 --> th.bing.com:443 using GLOBAL"
time="2025-05-28T00:25:13.928989400+08:00" level=info msg="[TCP] 127.0.0.1:63190 --> c.msn.cn:443 using GLOBAL"
time="2025-05-28T00:25:13.928989400+08:00" level=info msg="[TCP] 127.0.0.1:63191 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-05-28T00:25:13.932519300+08:00" level=info msg="[TCP] 127.0.0.1:63188 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-28T00:25:13.970046700+08:00" level=info msg="[TCP] 127.0.0.1:63194 --> assets.msn.cn:443 using GLOBAL"
time="2025-05-28T00:25:13.979158400+08:00" level=info msg="[TCP] 127.0.0.1:63196 --> assets.msn.cn:443 using GLOBAL"
time="2025-05-28T00:25:14.232836700+08:00" level=info msg="[TCP] 127.0.0.1:63198 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-28T00:25:14.235911100+08:00" level=info msg="[TCP] 127.0.0.1:63199 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-28T00:25:14.275202900+08:00" level=info msg="[TCP] 127.0.0.1:63202 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-28T00:25:14.276365900+08:00" level=info msg="[TCP] 127.0.0.1:63203 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-05-28T00:25:15.535251000+08:00" level=info msg="[TCP] 127.0.0.1:63209 --> web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:25:15.541113900+08:00" level=info msg="[TCP] 127.0.0.1:63208 --> web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:25:16.178495900+08:00" level=info msg="[TCP] 127.0.0.1:63212 --> j.clarity.ms:443 using GLOBAL"
time="2025-05-28T00:25:16.787235200+08:00" level=info msg="[TCP] 127.0.0.1:63214 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:25:16.845909300+08:00" level=info msg="[TCP] 127.0.0.1:63216 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:25:16.897964800+08:00" level=info msg="[TCP] 127.0.0.1:63218 --> ntp.msn.com:443 using GLOBAL"
time="2025-05-28T00:25:17.893121600+08:00" level=info msg="[TCP] 127.0.0.1:63221 --> assets.msn.com:443 using GLOBAL"
time="2025-05-28T00:25:18.029908300+08:00" level=info msg="[TCP] 127.0.0.1:63223 --> api.msn.com:443 using GLOBAL"
time="2025-05-28T00:25:18.071009200+08:00" level=info msg="[TCP] 127.0.0.1:63225 --> www.bing.com:443 using GLOBAL"
time="2025-05-28T00:25:18.071009200+08:00" level=info msg="[TCP] 127.0.0.1:63231 --> th.bing.com:443 using GLOBAL"
time="2025-05-28T00:25:18.072621500+08:00" level=info msg="[TCP] 127.0.0.1:63235 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-05-28T00:25:18.075160300+08:00" level=info msg="[TCP] 127.0.0.1:63229 --> c.bing.com:443 using GLOBAL"
time="2025-05-28T00:25:18.076436400+08:00" level=info msg="[TCP] 127.0.0.1:63232 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-05-28T00:25:18.080141900+08:00" level=info msg="[TCP] 127.0.0.1:63226 --> c.msn.com:443 using GLOBAL"
time="2025-05-28T00:25:18.672690500+08:00" level=info msg="[TCP] 127.0.0.1:63240 --> telegram.me:443 using GLOBAL"
time="2025-05-28T00:25:18.675233600+08:00" level=info msg="[TCP] 127.0.0.1:63238 --> t.me:443 using GLOBAL"
time="2025-05-28T00:25:18.966144400+08:00" level=info msg="[TCP] 127.0.0.1:63242 --> assets.msn.com:443 using GLOBAL"
time="2025-05-28T00:25:22.059514200+08:00" level=info msg="[TCP] 127.0.0.1:63246 --> zws2.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:25:26.073443500+08:00" level=info msg="[TCP] 127.0.0.1:63253 --> zws2.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:25:26.252298400+08:00" level=info msg="[TCP] 127.0.0.1:63255 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-28T00:25:30.218259500+08:00" level=info msg="[TCP] 127.0.0.1:63260 --> zws2.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:25:30.371628000+08:00" level=info msg="[TCP] 127.0.0.1:63262 --> web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:25:31.299375600+08:00" level=info msg="[TCP] 127.0.0.1:63265 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:25:34.513032400+08:00" level=info msg="[TCP] 127.0.0.1:63279 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-05-28T00:25:59.611640100+08:00" level=info msg="[TCP] 127.0.0.1:63297 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:01.811292000+08:00" level=info msg="[TCP] 127.0.0.1:63311 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:02.603036900+08:00" level=info msg="[TCP] 127.0.0.1:63313 --> www.bing.com:443 using GLOBAL"
time="2025-05-28T00:26:05.652989800+08:00" level=info msg="[TCP] 127.0.0.1:63317 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:26:06.993970700+08:00" level=info msg="[TCP] 127.0.0.1:63322 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:06.998278500+08:00" level=info msg="[TCP] 127.0.0.1:63320 --> zws1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:06.998278500+08:00" level=info msg="[TCP] 127.0.0.1:63326 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:07.003609000+08:00" level=info msg="[TCP] 127.0.0.1:63324 --> zws4.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:08.242284000+08:00" level=info msg="[TCP] 127.0.0.1:63329 --> zws2.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:11.501715300+08:00" level=info msg="[TCP] 127.0.0.1:63333 --> zws4.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:12.675085800+08:00" level=info msg="[TCP] 127.0.0.1:63336 --> zws1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:13.837038200+08:00" level=info msg="[TCP] 127.0.0.1:63340 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:13.839294800+08:00" level=info msg="[TCP] 127.0.0.1:63339 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:18.172880700+08:00" level=info msg="[TCP] 127.0.0.1:63345 --> j.clarity.ms:443 using GLOBAL"
time="2025-05-28T00:26:32.920428300+08:00" level=info msg="[TCP] 127.0.0.1:63367 --> t.me:443 using GLOBAL"
time="2025-05-28T00:26:35.417514500+08:00" level=info msg="[TCP] 127.0.0.1:63371 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:26:48.458549400+08:00" level=info msg="[TCP] 127.0.0.1:63381 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:27:08.210646100+08:00" level=info msg="[TCP] 127.0.0.1:63406 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-28T00:27:09.593044900+08:00" level=info msg="[TCP] 127.0.0.1:63409 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:27:09.593044900+08:00" level=info msg="[TCP] 127.0.0.1:63410 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:27:09.886630900+08:00" level=info msg="[TCP] 127.0.0.1:63413 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:27:16.647578900+08:00" level=info msg="[TCP] 127.0.0.1:63420 --> go.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:27:20.188051300+08:00" level=info msg="[TCP] 127.0.0.1:63424 --> j.clarity.ms:443 using GLOBAL"
time="2025-05-28T00:27:36.165408900+08:00" level=info msg="[TCP] 127.0.0.1:63458 --> phx02pap006.storage.live.com:443 using GLOBAL"
time="2025-05-28T00:27:38.774960100+08:00" level=info msg="[TCP] 127.0.0.1:63461 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:27:38.774960100+08:00" level=info msg="[TCP] 127.0.0.1:63462 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:27:39.097668100+08:00" level=info msg="[TCP] 127.0.0.1:63466 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:27:39.700466700+08:00" level=info msg="[TCP] 127.0.0.1:63468 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:27:40.260155100+08:00" level=info msg="[TCP] 127.0.0.1:63470 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:27:59.430925800+08:00" level=info msg="[TCP] 127.0.0.1:63485 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:28:02.711227700+08:00" level=info msg="[TCP] 127.0.0.1:63499 --> www.bing.com:443 using GLOBAL"
time="2025-05-28T00:28:12.171970500+08:00" level=info msg="[TCP] 127.0.0.1:63508 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:28:12.575141300+08:00" level=info msg="[TCP] 127.0.0.1:63511 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:28:12.739480900+08:00" level=info msg="[TCP] 127.0.0.1:63513 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:28:22.179448300+08:00" level=info msg="[TCP] 127.0.0.1:63524 --> j.clarity.ms:443 using GLOBAL"
time="2025-05-28T00:28:39.871018000+08:00" level=info msg="[TCP] 127.0.0.1:63553 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:28:39.873522800+08:00" level=info msg="[TCP] 127.0.0.1:63555 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:28:39.876469700+08:00" level=info msg="[TCP] 127.0.0.1:63551 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:28:39.877648300+08:00" level=info msg="[TCP] 127.0.0.1:63549 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:28:41.360746600+08:00" level=info msg="[TCP] 127.0.0.1:63557 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:28:42.253369500+08:00" level=info msg="[TCP] 127.0.0.1:63560 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:28:50.523117600+08:00" level=info msg="[TCP] 127.0.0.1:63567 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 using GLOBAL"
time="2025-05-28T00:28:53.230227600+08:00" level=info msg="[TCP] 127.0.0.1:63571 --> activity.windows.com:443 using GLOBAL"
time="2025-05-28T00:28:54.321738700+08:00" level=info msg="[TCP] 127.0.0.1:63574 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:28:54.771403000+08:00" level=info msg="[TCP] 127.0.0.1:63576 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:28:54.921496500+08:00" level=info msg="[TCP] 127.0.0.1:63579 --> zhouql.vip:443 using GLOBAL"
time="2025-05-28T00:28:55.036266900+08:00" level=info msg="[TCP] 127.0.0.1:63583 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:28:55.039876200+08:00" level=info msg="[TCP] 127.0.0.1:63581 --> zhouql.vip:443 using GLOBAL"
time="2025-05-28T00:28:59.781069200+08:00" level=info msg="[TCP] 127.0.0.1:63591 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-28T00:29:00.897442300+08:00" level=info msg="[TCP] 127.0.0.1:63607 --> zhouql.vip:443 using GLOBAL"
time="2025-05-28T00:29:04.827773700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63593 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:04.827773700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63620 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:04.827773700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63624 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.541613500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63599 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.541613500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63625 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63600 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63603 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63602 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63605 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63601 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63631 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63627 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63604 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63626 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63629 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63628 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.542614300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63630 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.544514900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63606 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.544514900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63632 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:05.601542600+08:00" level=info msg="[TCP] 127.0.0.1:63648 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.603665100+08:00" level=info msg="[TCP] 127.0.0.1:63647 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.604958100+08:00" level=info msg="[TCP] 127.0.0.1:63663 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.604958100+08:00" level=info msg="[TCP] 127.0.0.1:63661 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.604958100+08:00" level=info msg="[TCP] 127.0.0.1:63664 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.606563100+08:00" level=info msg="[TCP] 127.0.0.1:63673 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.606563100+08:00" level=info msg="[TCP] 127.0.0.1:63658 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.606563100+08:00" level=info msg="[TCP] 127.0.0.1:63660 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.609302500+08:00" level=info msg="[TCP] 127.0.0.1:63665 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.609302500+08:00" level=info msg="[TCP] 127.0.0.1:63662 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.610550000+08:00" level=info msg="[TCP] 127.0.0.1:63672 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.610550000+08:00" level=info msg="[TCP] 127.0.0.1:63682 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.610550000+08:00" level=info msg="[TCP] 127.0.0.1:63670 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.610550000+08:00" level=info msg="[TCP] 127.0.0.1:63681 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.612831700+08:00" level=info msg="[TCP] 127.0.0.1:63671 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:05.612831700+08:00" level=info msg="[TCP] 127.0.0.1:63659 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:06.009861600+08:00" level=info msg="[TCP] 127.0.0.1:63690 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:06.042415300+08:00" level=info msg="[TCP] 127.0.0.1:63692 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:06.054505500+08:00" level=info msg="[TCP] 127.0.0.1:63694 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:06.141081500+08:00" level=info msg="[TCP] 127.0.0.1:63697 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:06.202241500+08:00" level=info msg="[TCP] 127.0.0.1:63699 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:06.268171500+08:00" level=info msg="[TCP] 127.0.0.1:63701 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:06.569323100+08:00" level=info msg="[TCP] 127.0.0.1:63703 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:29:06.860528000+08:00" level=info msg="[TCP] 127.0.0.1:63705 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:06.899720800+08:00" level=info msg="[TCP] 127.0.0.1:63708 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:06.899720800+08:00" level=info msg="[TCP] 127.0.0.1:63707 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:07.697786800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63621 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:07.758810900+08:00" level=info msg="[TCP] 127.0.0.1:63712 --> www.bing.com:443 using GLOBAL"
time="2025-05-28T00:29:07.804016100+08:00" level=info msg="[TCP] 127.0.0.1:63714 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-28T00:29:09.827790900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63642 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:09.827790900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63640 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:09.827790900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63641 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:13.869646100+08:00" level=info msg="[TCP] 127.0.0.1:63745 --> web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:16.366975200+08:00" level=info msg="[TCP] 127.0.0.1:63751 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:18.602751300+08:00" level=info msg="[TCP] 127.0.0.1:63754 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:18.656278200+08:00" level=info msg="[TCP] 127.0.0.1:63756 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-05-28T00:29:24.186834500+08:00" level=info msg="[TCP] 127.0.0.1:63761 --> j.clarity.ms:443 using GLOBAL"
time="2025-05-28T00:29:39.040264600+08:00" level=info msg="[TCP] 127.0.0.1:63784 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:29:44.106528100+08:00" level=info msg="[TCP] 127.0.0.1:63790 --> wwk.lanzouq.com:443 using GLOBAL"
time="2025-05-28T00:29:45.475905200+08:00" level=info msg="[TCP] 127.0.0.1:63792 --> wwk.lanzouq.com:443 using GLOBAL"
time="2025-05-28T00:29:45.943515300+08:00" level=info msg="[TCP] 127.0.0.1:63795 --> wwk.lanzouq.com:443 using GLOBAL"
time="2025-05-28T00:29:45.948086800+08:00" level=info msg="[TCP] 127.0.0.1:63797 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:29:46.232389500+08:00" level=info msg="[TCP] 127.0.0.1:63803 --> assets.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:46.233850200+08:00" level=info msg="[TCP] 127.0.0.1:63801 --> image.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:46.233850200+08:00" level=info msg="[TCP] 127.0.0.1:63799 --> assets.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:46.235864500+08:00" level=info msg="[TCP] 127.0.0.1:63800 --> assets.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:46.404498100+08:00" level=info msg="[TCP] 127.0.0.1:63807 --> assets.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:47.403239600+08:00" level=info msg="[TCP] 127.0.0.1:63810 --> statics.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:47.409474100+08:00" level=info msg="[TCP] 127.0.0.1:63812 --> statics.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:47.420943700+08:00" level=info msg="[TCP] 127.0.0.1:63814 --> image.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:47.883925200+08:00" level=info msg="[TCP] 127.0.0.1:63817 --> hm.baidu.com:443 using GLOBAL"
time="2025-05-28T00:29:47.897757400+08:00" level=info msg="[TCP] 127.0.0.1:63819 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-28T00:29:47.984737600+08:00" level=info msg="[TCP] 127.0.0.1:63828 --> assets.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:47.997654600+08:00" level=info msg="[TCP] 127.0.0.1:63827 --> image.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:48.004446700+08:00" level=info msg="[TCP] 127.0.0.1:63816 --> image.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:48.021582600+08:00" level=info msg="[TCP] 127.0.0.1:63818 --> hm.woozooo.com:443 using GLOBAL"
time="2025-05-28T00:29:48.271190500+08:00" level=info msg="[TCP] 127.0.0.1:63835 --> wwk.lanzouq.com:443 using GLOBAL"
time="2025-05-28T00:29:49.539276200+08:00" level=info msg="[TCP] 127.0.0.1:63848 --> developer-oss.lanrar.com:443 using GLOBAL"
time="2025-05-28T00:29:49.540512600+08:00" level=info msg="[TCP] 127.0.0.1:63849 --> developer-oss.lanrar.com:443 using GLOBAL"
time="2025-05-28T00:29:49.708369300+08:00" level=info msg="[TCP] 127.0.0.1:63853 --> c1026.dmpdmp.com:443 using GLOBAL"
time="2025-05-28T00:29:52.857364400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63821 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:52.857364400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63859 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:52.858800500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63826 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:52.858800500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63840 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:52.858800500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63841 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.846664400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63838 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.846664400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63846 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.846664400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63887 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.846664400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63876 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.860662300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63839 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.860662300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63880 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.860662300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63843 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.860662300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63844 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.860662300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63885 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.860662300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63884 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.862536600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63842 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.862536600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63883 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.862536600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63888 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.862536600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63847 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.875731900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63845 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:53.875731900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63886 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:54.466270300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63850 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:54.466270300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63858 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:54.857859500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63857 --> dl-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:57.857927100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63871 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:57.857927100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63870 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:57.858961300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63878 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:57.858961300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63882 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:57.858961300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63881 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:57.858961300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63879 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:57.858961300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63877 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.846683000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63896 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.846683000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63894 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.846683000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63893 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.846683000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63895 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.860922600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63906 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.860922600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63901 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.860922600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63902 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.860922600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63904 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.860922600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63905 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.860922600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63903 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.862980600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63911 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.862980600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63914 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.862980600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63913 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.862980600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63912 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.875743800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63919 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:58.875743800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63920 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:59.466318900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63925 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:59.466318900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63926 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:29:59.858463300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63927 --> dl-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:02.858980000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63938 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:02.858980000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63989 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:02.858980000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63941 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:02.858980000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63939 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:02.858980000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63950 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:03.847642100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63970 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:03.847642100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63964 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:03.861067800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63967 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:03.861067800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63965 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:03.861067800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63966 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:03.863362200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63971 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:03.863362200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63968 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:03.875925600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63969 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:06.577445700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63973 --> app-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:07.755766100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63990 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:07.859347000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63997 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:07.859347000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63995 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:07.859347000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64018 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:07.859347000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63996 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:07.859347000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64019 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:07.988293600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63998 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:08.847757100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64004 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:08.847757100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64003 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:08.861550300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64011 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:08.861550300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64009 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:08.861550300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64010 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:08.863592700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64017 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:08.863592700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64016 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:08.876330900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64024 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:11.437641300+08:00" level=info msg="[TCP] 127.0.0.1:64079 --> j.clarity.ms:443 using GLOBAL"
time="2025-05-28T00:30:11.578960500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64027 --> app-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:12.526856100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64033 --> msedge.b.tlu.dl.delivery.mp.microsoft.com:80 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:12.756429700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64034 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:12.859605300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64064 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:12.859605300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64059 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:12.859605300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64044 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:12.859605300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64043 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:12.988938000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64045 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:13.848068100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64069 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:13.848068100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64076 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:13.862081200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64072 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:13.862081200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64070 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:13.862081200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64071 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:13.863602200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64077 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:13.863602200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64073 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:13.876770700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64074 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:17.859980800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64111 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:17.859980800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64085 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:17.859980800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64116 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:17.859980800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64086 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:18.848294400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64102 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:18.848294400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64103 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:18.862211600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64110 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:18.862211600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64109 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:18.862211600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64108 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:18.863813600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64118 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:18.863813600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64117 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:18.877181400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64124 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:20.534413600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64126 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:22.860522500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64133 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:22.860564700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64134 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:22.860564700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64142 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:22.860564700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64145 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:23.801598200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64135 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:23.862612400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64154 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:23.862612400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64152 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:23.862612400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64153 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:23.863859500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64155 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:23.863859500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64158 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:24.857605200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64147 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:24.857668200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64157 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:24.887972800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64156 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:25.534902400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64160 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:27.860717700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64170 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:27.860717700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64191 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:27.860717700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64192 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:27.860717700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64171 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:28.802201800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64173 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:28.802201800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64205 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:28.862948500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64182 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:28.862948500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64184 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:28.862948500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64183 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:28.863979900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64190 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:28.863979900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64189 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:29.858626000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64197 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:29.858700900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64198 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:29.887980100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64199 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:32.861257300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64230 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:32.861257300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64212 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:32.861257300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64213 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:32.861257300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64229 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:33.803190600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64219 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:33.863237500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64234 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:33.863237500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64233 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:33.863237500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64232 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:33.864380700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64236 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:33.864380700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64235 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:35.865794700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64237 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:35.865862800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64243 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:35.896675200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64242 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:37.861448800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64276 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:37.861448800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64261 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:37.861591500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64260 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:37.861591500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64275 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:38.863640200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64268 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:38.863640200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64267 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:38.863640200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64266 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:38.864754000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64274 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:38.864754000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64273 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:40.189015200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64278 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:40.189077900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64294 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:40.517728900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64280 --> checkappexec.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:40.517796600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64281 --> checkappexec.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:40.866291100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64287 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:40.866291100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64286 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:40.898023700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64288 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:42.861809400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64299 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:42.861809400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64311 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:42.861809400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64310 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:42.861809400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64300 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:43.863665000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64312 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:43.863665000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64314 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:43.863665000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64313 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:43.865054700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64316 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:43.865054700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64315 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:45.189111300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64322 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:45.189111300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64321 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:45.866786000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64339 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:45.866786000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64333 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:46.912746700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64334 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:47.862156400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64345 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:47.862156400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64361 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:47.862156400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64360 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:47.862156400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64344 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:48.864030000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64352 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:48.864089100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64353 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:48.864089100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64351 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:48.865116700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64359 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:48.865116700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64358 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:49.150120400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64362 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:50.518263800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64379 --> checkappexec.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:50.867098400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64377 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:50.867098400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64376 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:51.913494100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64380 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:52.862693300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64396 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:52.862693300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64391 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:52.862693300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64397 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:52.862693300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64390 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:53.865457500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64407 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:53.865457500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64408 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:54.151313900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64398 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:54.873232900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64406 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:54.873232900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64404 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:54.873232900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64405 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:55.867322900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64424 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:55.867322900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64418 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:57.150676500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64419 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:57.862875600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64430 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:57.862875600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64441 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:57.862875600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64440 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:57.862875600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64431 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:57.924792800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64432 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:58.865998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64438 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:58.865998800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64439 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:59.874951500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64444 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:59.874951500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64442 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:30:59.874951500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64443 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:00.867609800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64457 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:00.867609800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64458 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:02.151705700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64461 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:02.151705700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64480 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:02.863492200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64478 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:02.863492200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64471 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:02.863492200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64477 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:02.863492200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64470 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:02.925562500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64472 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:04.877649900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64481 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:04.877649900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64487 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:05.868186800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64505 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:05.868186800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64512 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:05.893817600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64492 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:05.893876100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64493 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:05.893876100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64494 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:07.152075800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64511 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:07.152075800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64551 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:07.813953200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64513 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:07.863786500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64522 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:07.863786500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64519 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:07.863786500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64521 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:07.863786500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64518 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:08.937499900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64523 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:09.101448300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64528 --> storeedgefd.dsx.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:09.666148300+08:00" level=info msg="[TCP] 127.0.0.1:50438 --> zhouql.vip:443 using GLOBAL"
time="2025-05-28T00:31:09.676231300+08:00" level=info msg="[TCP] 127.0.0.1:55634 --> zhouql.vip:443 using GLOBAL"
time="2025-05-28T00:31:09.835366300+08:00" level=info msg="[TCP] 127.0.0.1:52053 --> bili.zhouql.vip:443 using GLOBAL"
time="2025-05-28T00:31:09.878536800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64534 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:09.878605400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64533 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:10.868481100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64540 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:10.868481100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64541 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:10.893922200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64547 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:10.893922200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64548 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:10.893922200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64546 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:11.115051700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64550 --> web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:12.152739900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64556 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:12.815171600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64557 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:12.864184100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64570 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:12.864184100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64567 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:12.864184100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64569 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:12.864184100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64566 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:13.938541800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64571 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:14.206763300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53217 --> redirector.gvt1.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:15.221883400+08:00" level=info msg="[TCP] 127.0.0.1:61656 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-28T00:31:15.868617600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52078 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:15.868617600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52073 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:15.890012400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52061 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:15.890108000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52072 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:15.894329200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52076 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:15.894450300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52074 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:15.894450300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52075 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:16.116198700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52066 --> web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:17.480661500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52079 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:17.864590300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52091 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:17.864590300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52089 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:17.864590300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52088 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:17.864590300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52090 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:19.135665100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52092 --> assets.activity.windows.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:19.206970300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61649 --> redirector.gvt1.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:19.943459200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61654 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:20.868909500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61664 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:20.868909500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61663 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:20.890122600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61669 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:20.890122600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61670 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:20.894580900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61677 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:20.894580900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61676 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:20.894580900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61675 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:21.146622200+08:00" level=info msg="[TCP] 127.0.0.1:61715 --> klpbbs.com:443 using GLOBAL"
time="2025-05-28T00:31:21.146622200+08:00" level=info msg="[TCP] 127.0.0.1:61714 --> klpbbs.com:443 using GLOBAL"
time="2025-05-28T00:31:22.481865200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61680 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:22.864945700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61689 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:22.864967200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61690 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:22.864967200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61693 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:22.864967200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61692 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:23.255823400+08:00" level=info msg="[TCP] 127.0.0.1:61734 --> hm.baidu.com:443 using GLOBAL"
time="2025-05-28T00:31:23.262954900+08:00" level=info msg="[TCP] 127.0.0.1:61738 --> hm.baidu.com:443 using GLOBAL"
time="2025-05-28T00:31:23.292772300+08:00" level=info msg="[TCP] 127.0.0.1:61737 --> cn.bing.com:443 using GLOBAL"
time="2025-05-28T00:31:23.974740800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61694 --> web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:24.944087600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61695 --> zws5.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:25.869089400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61724 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:25.869089400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61719 --> zws2-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:25.890137000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61725 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:25.890137000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61720 --> zws4-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:25.894980100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61723 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:25.894980100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61722 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:25.894980100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61721 --> zws1-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:26.002573200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61713 --> activity.windows.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:27.194089600+08:00" level=info msg="[TCP] 127.0.0.1:61791 --> www.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:27.203418100+08:00" level=info msg="[TCP] 127.0.0.1:61793 --> www.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:27.443568900+08:00" level=info msg="[TCP] 127.0.0.1:61800 --> s1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.443568900+08:00" level=info msg="[TCP] 127.0.0.1:61803 --> s1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.443568900+08:00" level=info msg="[TCP] 127.0.0.1:61805 --> s1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.443568900+08:00" level=info msg="[TCP] 127.0.0.1:61802 --> s1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.444298800+08:00" level=info msg="[TCP] 127.0.0.1:61804 --> s1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.445759700+08:00" level=info msg="[TCP] 127.0.0.1:61799 --> s1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.445759700+08:00" level=info msg="[TCP] 127.0.0.1:61806 --> s1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.445759700+08:00" level=info msg="[TCP] 127.0.0.1:61801 --> s1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.500193800+08:00" level=info msg="[TCP] 127.0.0.1:61825 --> i0.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.500193800+08:00" level=info msg="[TCP] 127.0.0.1:61824 --> i0.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.593005400+08:00" level=info msg="[TCP] 127.0.0.1:61834 --> i2.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.593005400+08:00" level=info msg="[TCP] 127.0.0.1:61831 --> i2.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.593005400+08:00" level=info msg="[TCP] 127.0.0.1:61835 --> i2.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.604860000+08:00" level=info msg="[TCP] 127.0.0.1:61838 --> i1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.606519800+08:00" level=info msg="[TCP] 127.0.0.1:61832 --> i1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.606519800+08:00" level=info msg="[TCP] 127.0.0.1:61833 --> i1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.612767000+08:00" level=info msg="[TCP] 127.0.0.1:61836 --> i1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.615176500+08:00" level=info msg="[TCP] 127.0.0.1:61837 --> i1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.671035100+08:00" level=info msg="[TCP] 127.0.0.1:61858 --> i0.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:27.694121800+08:00" level=info msg="[TCP] 127.0.0.1:61855 --> api.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:27.696123500+08:00" level=info msg="[TCP] 127.0.0.1:61857 --> api.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:27.696123500+08:00" level=info msg="[TCP] 127.0.0.1:61856 --> api.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:27.865913800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61730 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:27.865913800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61732 --> zws5-1.web.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.019253100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61733 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.070490800+08:00" level=info msg="[TCP] 127.0.0.1:61874 --> data.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:28.070490800+08:00" level=info msg="[TCP] 127.0.0.1:61880 --> s1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:31:28.074245900+08:00" level=info msg="[TCP] 127.0.0.1:61877 --> data.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:28.074245900+08:00" level=info msg="[TCP] 127.0.0.1:61869 --> data.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:28.074891600+08:00" level=info msg="[TCP] 127.0.0.1:61868 --> data.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:28.147092300+08:00" level=info msg="[TCP] 127.0.0.1:61888 --> cm.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:28.242163800+08:00" level=info msg="[TCP] 127.0.0.1:61893 --> cm.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:28.244670200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61741 --> www.google-analytics.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.317756500+08:00" level=info msg="[TCP] 127.0.0.1:61896 --> api.live.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:28.388197400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61744 --> pagead2.googlesyndication.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61745 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61746 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61749 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61752 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61892 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61762 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61777 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61748 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61904 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61760 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61753 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61755 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61756 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61759 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.803666600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61763 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.804655900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61747 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.804655900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61767 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.804655900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61758 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.804655900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61768 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.804655900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61766 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.804655900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61867 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.816495200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61750 --> copilot.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.816558300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61754 --> copilot.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.816558300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61761 --> copilot.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.830360000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61751 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.830360000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61757 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:28.830360000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61765 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:30.162412100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61770 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:30.162503500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61905 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:30.199645000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61775 --> prod.rewardsplatform.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:30.298928000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61776 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.062172000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61883 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.062233000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61889 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.147509100+08:00" level=info msg="[TCP] 127.0.0.1:61970 --> impression.biligame.com:443 using GLOBAL"
time="2025-05-28T00:31:33.334451900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61899 --> prod-apac-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.803788900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61920 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.803788900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61917 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.803788900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61919 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.803788900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61916 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.803788900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61910 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.803788900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61911 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.803860800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61918 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.804890400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61922 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.804890400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61924 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.804890400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61921 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.804890400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61923 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.817035100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61929 --> copilot.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:33.830836000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61934 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:35.162838100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61941 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:35.162838100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61940 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:35.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61942 --> prod.rewardsplatform.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:36.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61958 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:36.*********+08:00" level=info msg="[TCP] 127.0.0.1:62002 --> passport.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:36.*********+08:00" level=info msg="[TCP] 127.0.0.1:62003 --> passport.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:38.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61969 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:38.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61968 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:38.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61972 --> prod-apac-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:38.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61986 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:38.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62008 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:38.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62009 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:39.158434600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61995 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:40.163047000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62031 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:43.804337700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62019 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:43.804337700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62020 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:43.832009000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62030 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:44.158466600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62032 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:44.702690400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62038 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:45.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62043 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:48.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62050 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:62070 --> passport.bilicomics.com:443 using GLOBAL"
time="2025-05-28T00:31:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:62069 --> passport.bilicomic.com:443 using GLOBAL"
time="2025-05-28T00:31:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:62068 --> passport.bilibili.cn:443 using GLOBAL"
time="2025-05-28T00:31:49.*********+08:00" level=info msg="[TCP] 127.0.0.1:62067 --> passport.biligame.com:443 using GLOBAL"
time="2025-05-28T00:31:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:62077 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:62081 --> data.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:62080 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-05-28T00:31:50.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62057 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:52.*********+08:00" level=info msg="[TCP] 127.0.0.1:62092 --> message.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:31:53.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62064 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:53.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62065 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:54.263305400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62066 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:55.163685000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62089 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:58.692503000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62095 --> edge-consumer-static.azureedge.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:58.763248800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62096 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:58.763308600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62097 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:58.805594500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62098 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:58.805675200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62110 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:31:59.263892100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62104 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:00.146041000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62111 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:01.727910600+08:00" level=info msg="[TCP] 127.0.0.1:62152 --> i1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:32:03.693564100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62118 --> edge-consumer-static.azureedge.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:03.763324600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62128 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:03.763324600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62127 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:03.805785000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62134 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:03.805785000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62133 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:03.948082900+08:00" level=info msg="[TCP] 127.0.0.1:62169 --> space.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:32:03.953312900+08:00" level=info msg="[TCP] 127.0.0.1:62168 --> space.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:32:06.562010600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62141 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:06.562010600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62166 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:08.310186500+08:00" level=info msg="[TCP] 127.0.0.1:62188 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-28T00:32:08.807040300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62177 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:08.807040300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62178 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:08.809698600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62164 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:08.829628000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62165 --> www.bingapis.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:09.122665000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62174 --> storeedgefd.dsx.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:09.494669800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62175 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:09.499674600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62176 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:09.499674600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62185 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:11.062572100+08:00" level=info msg="[TCP] 127.0.0.1:62222 --> security.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:32:11.155113700+08:00" level=info msg="[TCP] 127.0.0.1:62223 --> xy36x131x207x35xy.mcdn.bilivideo.cn:4483 using GLOBAL"
time="2025-05-28T00:32:11.499377700+08:00" level=info msg="[TCP] 127.0.0.1:62227 --> hw-v2-web-player-tracker.biliapi.net:443 using GLOBAL"
time="2025-05-28T00:32:11.562147600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62190 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:11.588629000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62184 --> prod-apac-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:11.751205600+08:00" level=info msg="[TCP] 127.0.0.1:62235 --> xy123x138x84x12xy.mcdn.bilivideo.cn:8082 using GLOBAL"
time="2025-05-28T00:32:11.844349600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62186 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:12.385177500+08:00" level=info msg="[TCP] 127.0.0.1:62248 --> xy36x131x207x19xy.mcdn.bilivideo.cn:4483 using GLOBAL"
time="2025-05-28T00:32:13.807544200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62221 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:13.807544200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62195 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:13.807544200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62196 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:13.810162500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62197 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:13.829668400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62202 --> www.bingapis.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:13.830705900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62261 --> www.bingapis.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:14.494790600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62209 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:14.499942400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62234 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:14.499942400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62218 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:14.499942400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62219 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:16.562456000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62277 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:16.589287100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62236 --> prod-apac-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:16.844952000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62242 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:18.807602400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62255 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:18.811708200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62256 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:19.155341600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62268 --> assets.activity.windows.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:19.500121600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62274 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:21.155568800+08:00" level=info msg="[TCP] 127.0.0.1:62305 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-05-28T00:32:23.808079500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62323 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:23.808079500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62326 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:23.808079500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62324 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:23.812895000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62290 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:24.500172100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62304 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:25.234378000+08:00" level=info msg="[TCP] 127.0.0.1:62342 --> xy36x131x207x21xy.mcdn.bilivideo.cn:4483 using GLOBAL"
time="2025-05-28T00:32:25.483665100+08:00" level=info msg="[TCP] 127.0.0.1:62346 --> xy42x202x169x35xy.mcdn.bilivideo.cn:8082 using GLOBAL"
time="2025-05-28T00:32:26.562804900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62341 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:28.708991000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62325 --> static.edge.microsoftapp.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:28.808518600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62334 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:28.808518600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62333 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:28.808518600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62332 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:28.808518600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62331 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:29.500736200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62340 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:29.500736200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62343 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:30.682079700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62348 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:31.563242500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62353 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:33.710461200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62356 --> static.edge.microsoftapp.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:33.808622100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62365 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:34.500955800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62370 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:35.682721000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62372 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:36.563632600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62394 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:37.106917200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62392 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:38.808809900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62400 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:38.808809900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62401 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:39.501020500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62407 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:40.725374200+08:00" level=info msg="[TCP] 127.0.0.1:62433 --> xy113x57x7x140xy.mcdn.bilivideo.cn:8082 using GLOBAL"
time="2025-05-28T00:32:40.917800500+08:00" level=info msg="[TCP] 127.0.0.1:62435 --> bvc.bilivideo.com:443 using GLOBAL"
time="2025-05-28T00:32:40.939590900+08:00" level=info msg="[TCP] 127.0.0.1:62439 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-05-28T00:32:41.016666000+08:00" level=info msg="[TCP] 127.0.0.1:62441 --> hw-v2-web-player-tracker.biliapi.net:443 using GLOBAL"
time="2025-05-28T00:32:41.344755400+08:00" level=info msg="[TCP] 127.0.0.1:62445 --> api.live.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:32:41.564766500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62425 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:41.755789100+08:00" level=info msg="[TCP] 127.0.0.1:62454 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-05-28T00:32:42.108087800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62413 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:43.706670000+08:00" level=info msg="[TCP] 127.0.0.1:62465 --> message.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:32:43.809029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62457 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:43.809029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62424 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:43.809029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62436 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:43.809029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62423 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:43.809029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62453 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:44.070207900+08:00" level=info msg="[TCP] 127.0.0.1:62476 --> api.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:32:44.179021500+08:00" level=info msg="[TCP] 127.0.0.1:62479 --> boss.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:32:44.237956400+08:00" level=info msg="[TCP] 127.0.0.1:62482 --> bimp.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:32:44.296405400+08:00" level=info msg="[TCP] 127.0.0.1:62485 --> data.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:32:44.501180200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62430 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:44.501180200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62443 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:44.501180200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62463 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:44.507296000+08:00" level=info msg="[TCP] 127.0.0.1:62489 --> i0.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:32:45.225463200+08:00" level=info msg="[TCP] 127.0.0.1:62499 --> api.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:32:46.081448200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62444 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:46.081448200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62452 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:46.565078700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62503 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:48.129292200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62458 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:48.809465500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62474 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:48.809465500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62472 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:48.809465500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62521 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:48.809465500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62498 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:48.809465500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62473 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:48.812093800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62475 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:49.321265500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62488 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:49.501536500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62497 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:49.501536500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62496 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:51.081823100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62508 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:51.081969300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62509 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:51.145079900+08:00" level=info msg="[TCP] 127.0.0.1:62549 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:32:53.129996700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62515 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:53.809846900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62528 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:53.809846900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62527 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:53.809846900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62526 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:53.813444700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62529 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:54.322390800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62534 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:54.501855800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62544 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:56.565530900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62576 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:58.810113500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62562 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:58.818270800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62563 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:59.502250200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62574 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:32:59.843967500+08:00" level=info msg="[TCP] 127.0.0.1:54908 --> api.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:33:01.565775500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54911 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:03.818848000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62588 --> clients2.google.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:05.363759000+08:00" level=info msg="[TCP] 127.0.0.1:61292 --> i0.hdslb.com:80 using GLOBAL"
time="2025-05-28T00:33:05.415790700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54912 --> prod-apac-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:06.566364800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54929 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:08.810871300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54934 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:10.417355600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61295 --> prod-apac-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:11.566912100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61311 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:13.811308800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61310 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:16.566997400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61332 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:18.811392900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61323 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:18.811392900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61325 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:18.811392900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61326 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:18.811392900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61328 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:18.811392900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61324 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:18.811392900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61329 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:18.811392900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61327 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:23.344666000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61338 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:23.811992800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61349 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:23.811992800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61344 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:23.811992800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61345 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:23.811992800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61347 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:23.811992800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61346 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:23.812160900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61348 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:23.812160900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61343 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:26.568228300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61369 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:28.346396300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61358 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:28.812424400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61368 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:28.812424400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61367 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:33.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61382 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:33.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61381 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:33.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61383 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:52341 --> passport.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:33:38.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61401 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:38.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:61406 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:42.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52347 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:43.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52352 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:43.*********+08:00" level=info msg="[TCP] 127.0.0.1:58588 --> i1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:33:43.*********+08:00" level=info msg="[TCP] 127.0.0.1:52850 --> member.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:33:48.824325900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52853 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:48.983280900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52858 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:53.369263800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52862 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:53.824733900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52866 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:56.479678600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52873 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:33:58.370364200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52879 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:00.150482500+08:00" level=info msg="[TCP] 127.0.0.1:64938 --> zhouql.vip:443 using GLOBAL"
time="2025-05-28T00:34:05.123876300+08:00" level=info msg="[TCP] 127.0.0.1:64960 --> i0.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:34:06.523349300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64940 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:08.019361400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64957 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:08.383452400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64958 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:13.384087000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64968 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:16.038208300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64977 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:25.586275400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64989 --> go.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:28.053277800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64995 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:28.147364100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64996 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:33.892891800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65004 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:34.284936000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65009 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:38.894499500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65029 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:41.774156600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65038 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:42.425132100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65043 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:44.337695200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65045 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:44.337815500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65058 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:47.380380600+08:00" level=info msg="[TCP] 127.0.0.1:52582 --> api.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:34:47.426717000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65052 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:47.426717000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65068 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:49.338200500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65064 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:49.338200500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65065 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:51.141590700+08:00" level=info msg="[TCP] 127.0.0.1:52599 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:34:51.800893500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65069 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:52.426828200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52590 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:52.426828200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52589 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:57.427176000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52611 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:34:57.427176000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52607 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:00.056122300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52613 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:00.087930000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52618 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:02.427328800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52624 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:03.772648400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52626 --> access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:05.088957200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52628 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:08.774194000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52649 --> access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:16.371357600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52663 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:21.372524100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52672 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:25.880639500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52680 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:32.750769400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52692 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:33.038489500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52698 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:38.038512700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52718 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:40.359006200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52727 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:35:49.844272700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52738 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:36:05.144072600+08:00" level=info msg="[TCP] 127.0.0.1:52774 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:36:06.787491700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52765 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:36:06.787491700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52770 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:36:11.787869400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52781 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:36:11.787969400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52782 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:36:27.132910100+08:00" level=info msg="[TCP] 127.0.0.1:52803 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-05-28T00:36:31.106069800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52797 --> activity.windows.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:36:32.073928700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52802 --> v10.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:36:55.201423600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52835 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:00.746707300+08:00" level=info msg="[TCP] 127.0.0.1:54888 --> api.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:37:01.696085400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52845 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:05.149105900+08:00" level=info msg="[TCP] 127.0.0.1:54855 --> data.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:37:08.192185300+08:00" level=info msg="[TCP] 127.0.0.1:54862 --> loadingbaycn.webapp.163.com:443 using GLOBAL"
time="2025-05-28T00:37:08.782820000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54847 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:09.132609700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54852 --> storeedgefd.dsx.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:12.721090500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54861 --> array604.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:14.404503100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54867 --> array608.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:15.367724600+08:00" level=info msg="[TCP] 127.0.0.1:61292 --> i0.hdslb.com:80 using GLOBAL"
time="2025-05-28T00:37:16.535618500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54873 --> array615.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:16.794259300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54875 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:18.563625700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54876 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:19.163444600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54882 --> assets.activity.windows.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:21.795213400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54886 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:36.496413200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54914 --> checkappexec.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:41.824619000+08:00" level=info msg="[TCP] 127.0.0.1:54937 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:37:45.669960400+08:00" level=info msg="[TCP] 127.0.0.1:54950 --> i0.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:37:45.670513200+08:00" level=info msg="[TCP] 127.0.0.1:54953 --> i1.hdslb.com:443 using GLOBAL"
time="2025-05-28T00:37:46.890779400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54941 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:37:51.868465300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54956 --> array619.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:38:26.798952600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54997 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:38:31.800168900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55004 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:38:34.136189300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55011 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:38:35.360057100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55013 --> phx02pap006.storage.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:38:38.220829900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55030 --> array615.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:38:39.136888800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55036 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:38:43.224309500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55045 --> disc601.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:38:51.238266900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55057 --> disc601.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:38:56.244541100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55064 --> geo.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:01.247481100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55073 --> kv601.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:01.249988400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55078 --> disc601.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:03.952536800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55082 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:05.141990300+08:00" level=info msg="[TCP] 127.0.0.1:55112 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:39:05.511379000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55084 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:05.511458500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55086 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:05.511458500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55101 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:05.852654700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55085 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:05.852744200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55111 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:08.953345900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55103 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:09.020047600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55108 --> kv601.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:09.483645900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55110 --> access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:10.511642400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55119 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:10.511642400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55123 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:10.511642400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55130 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:10.511642400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55122 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:10.511642400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55121 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:10.511642400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55120 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:10.512148500+08:00" level=info msg="[TCP] 127.0.0.1:55144 --> cn.bing.com:443 using GLOBAL"
time="2025-05-28T00:39:10.853199800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55128 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:10.853199800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55129 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:14.484637200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55136 --> access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:15.011659200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55142 --> disc601.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:15.512286100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55162 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:15.512286100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55151 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:15.512286100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55152 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:15.512286100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55150 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:15.512286100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55157 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:15.853353500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55159 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:15.853353500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55163 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:17.012434400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55158 --> kv601.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:20.512708200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55187 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:20.512708200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55168 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:20.512708200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55169 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:20.854313100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55175 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:20.854313100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55176 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:20.854313100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55179 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:21.558797600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55178 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:25.029619700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55182 --> kv601.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:25.513251100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55192 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:25.513251100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55198 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:25.855249600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55209 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:25.855249600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55197 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:25.855249600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55199 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:28.418653200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55202 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:28.418653200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55224 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:28.810834100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55207 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:30.513404500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55215 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:30.855586700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55221 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:30.855586700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55243 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:30.855586700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55220 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:30.855586700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55223 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:32.524477800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55222 --> edgeassetservice.azureedge.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:33.029537400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55226 --> kv601.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:33.419379600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55242 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:33.419379600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55237 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:33.810916300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55232 --> substrate.office.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:35.856024900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55249 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:35.856024900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55250 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:36.802277900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55262 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:36.802359600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55272 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:37.524758300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55263 --> edgeassetservice.azureedge.net:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:38.419823200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55279 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:40.514230800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55300 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:40.856499500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55292 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:41.023857700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55284 --> geo.prod.do.dsp.mp.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:41.802789700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55290 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:41.802789700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55289 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:43.420559900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55319 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:39:43.420559900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55298 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
