2025-07-16 20:28:06 INFO - try to run core in service mode
2025-07-16 20:28:06 INFO - start service: {"log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-16-2028.log", "core_type": "verge-mihomo-alpha", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev"}
2025-07-16 20:28:06 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-16 20:28:06 INFO - No hotkeys configured
2025-07-16 20:28:06 INFO - Starting to create window
2025-07-16 20:28:06 INFO - Creating new window
2025-07-16 20:28:06 INFO - Window created successfully, attempting to show
2025-07-16 20:28:06 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-16 20:28:06 INFO - Successfully registered hotkey Control+Q for quit
2025-07-16 20:31:11 INFO - Starting to create window
2025-07-16 20:31:11 INFO - Found existing window, trying to show it
2025-07-16 20:31:11 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-16 20:31:11 INFO - Successfully registered hotkey Control+Q for quit
2025-07-16 20:31:26 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-16 20:31:26 INFO - Successfully registered hotkey Control+Q for quit
2025-07-16 20:31:26 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-16 20:31:26 INFO - Successfully registered hotkey Control+Q for quit
2025-07-16 20:31:44 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-16 20:31:44 INFO - Successfully registered hotkey Control+Q for quit
