Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-06T20:50:39.439490500+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-06T20:50:39.447731400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-06T20:50:39.447731400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-06T20:50:39.449305700+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-06T20:50:39.470552400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-06T20:50:39.470552400+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-06T20:50:39.740847900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-06T20:50:39.740847900+08:00" level=info msg="Load GeoSite rule: private"
time="2025-06-06T20:50:39.755907100+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-06T20:50:39.758909400+08:00" level=info msg="Initial configuration complete, total time: 313ms"
time="2025-06-06T20:50:39.759911200+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-06-06T20:50:39.760911200+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-06T20:50:39.761912200+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-06-06T20:50:39.761912200+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-06-06T20:50:39.761912200+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider Disney"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Discord"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider telegram"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-06T20:50:39.777509900+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-06T20:50:39.777509900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-06T20:50:39.777509900+08:00" level=info msg="Start initial provider Line"
time="2025-06-06T20:50:39.777509900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider BBC"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider TVB"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider NowE"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider TVer"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider google"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Lan"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider apple"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-06T20:50:39.776973000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-06T20:50:39.777475700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-06T20:50:42.104544100+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-06T20:50:42.105084500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-06T20:50:42.105084500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-06T20:50:42.105593600+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-06T20:50:42.105593600+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-06T20:50:42.106102800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-06T20:50:42.107124600+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-06T20:50:44.779536000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780691600+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-06T20:50:44.780691600+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780691600+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-06T20:50:44.780691600+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-06T20:50:44.780183000+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-06T20:50:44.779675900+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-06T20:50:44.780691600+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-06T20:50:44.782667600+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-06T20:50:44.782667600+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-06T20:50:44.782667600+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-06T20:50:44.782667600+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-06T20:50:44.783693600+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider BBC"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Discord"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Disney"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider NowE"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider telegram"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Line"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider apple"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Lan"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider google"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider TVB"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider TVer"
time="2025-06-06T20:50:44.784203400+08:00" level=info msg="Start initial provider HKOpenTV"
