2025-06-30 21:22:00 INFO - try to run core in service mode
2025-06-30 21:22:00 INFO - start service: {"bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-06-30-2122.log", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "core_type": "verge-mihomo-alpha"}
2025-06-30 21:22:00 INFO - Initializing hotkeys, global hotkey enabled: true
2025-06-30 21:22:00 INFO - No hotkeys configured
2025-06-30 21:22:00 INFO - Starting to create window
2025-06-30 21:22:00 INFO - Creating new window
2025-06-30 21:22:00 INFO - Window created successfully, attempting to show
2025-06-30 21:22:00 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-30 21:22:00 INFO - Successfully registered hotkey Control+Q for quit
2025-06-30 21:22:00 INFO - running timer task `R3SfPp0qApId`
