Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-05-28T00:42:50.687235100+08:00" level=info msg="Start initial configuration in progress"
time="2025-05-28T00:42:50.696026500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-05-28T00:42:50.696026500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-05-28T00:42:50.696538800+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-05-28T00:42:50.707745100+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-05-28T00:42:50.707745100+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-05-28T00:42:50.955965300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-05-28T00:42:50.956965800+08:00" level=info msg="Load GeoSite rule: private"
time="2025-05-28T00:42:50.974981700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-05-28T00:42:50.977984900+08:00" level=info msg="Initial configuration complete, total time: 285ms"
time="2025-05-28T00:42:50.977984900+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-05-28T00:42:50.979986500+08:00" level=info msg="Sniffer is loaded and working"
time="2025-05-28T00:42:50.979986500+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-05-28T00:42:50.979986500+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-05-28T00:42:50.979986500+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider AppleTV"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider TVB"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Acplay"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Discord"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider NowE"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Bahamut"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Line"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Spotify"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider OneDrive"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider BBC"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Twitter"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Disney"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Instagram"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider KKTV"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider BiliBili"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider OpenAI"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider telegram"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Niconico"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Peacock"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider google"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider TVer"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider microsoft"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Overcast"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider apple"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Hulu"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Tiktok"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider NaverTV"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider HBOHK"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Lan"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-05-28T00:42:50.980988100+08:00" level=info msg="Start initial provider Netflix"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982541300+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-05-28T00:42:55.981834700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:42:55.981834700+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-05-28T00:42:55.982551400+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-05-28T00:42:55.985611700+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-05-28T00:42:55.985611700+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-05-28T00:42:55.985611700+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-05-28T00:42:55.985611700+08:00" level=info msg="Start initial Compatible provider default"
time="2025-05-28T00:42:57.192952600+08:00" level=info msg="[TCP] 127.0.0.1:55798 --> products.aspose.app:443 using GLOBAL"
time="2025-05-28T00:42:57.196244300+08:00" level=info msg="[TCP] 127.0.0.1:55799 --> products.aspose.app:443 using GLOBAL"
time="2025-05-28T00:42:57.735948400+08:00" level=info msg="[TCP] 127.0.0.1:55825 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-28T00:42:58.279475700+08:00" level=info msg="[TCP] 127.0.0.1:55833 --> pagead2.googlesyndication.com:443 using GLOBAL"
time="2025-05-28T00:42:58.279475700+08:00" level=info msg="[TCP] 127.0.0.1:55831 --> cdn.jsdelivr.net:443 using GLOBAL"
time="2025-05-28T00:42:58.653296800+08:00" level=info msg="[TCP] 127.0.0.1:55836 --> code.jquery.com:443 using GLOBAL"
time="2025-05-28T00:42:58.673585500+08:00" level=info msg="[TCP] 127.0.0.1:55838 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-05-28T00:42:58.783746500+08:00" level=info msg="[TCP] 127.0.0.1:55840 --> pagead2.googlesyndication.com:443 using GLOBAL"
time="2025-05-28T00:42:59.119323900+08:00" level=info msg="[TCP] 127.0.0.1:55842 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:42:59.668739700+08:00" level=info msg="[TCP] 127.0.0.1:55844 --> download.clashverge.dev:443 using GLOBAL"
time="2025-05-28T00:42:59.737601700+08:00" level=info msg="[TCP] 127.0.0.1:55846 --> edge.microsoft.com:443 using GLOBAL"
time="2025-05-28T00:42:59.741153300+08:00" level=info msg="[TCP] 127.0.0.1:55847 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-05-28T00:43:04.828569000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55853 --> github.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:04.828569000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55858 --> www.googletagmanager.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:04.829799300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55859 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:04.829799300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55877 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:04.830313200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55860 --> edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:05.872865100+08:00" level=info msg="[TCP] 127.0.0.1:55885 --> sigma-fcount-webtech.proxima.nie.netease.com:443 using GLOBAL"
time="2025-05-28T00:43:09.174605300+08:00" level=info msg="[TCP] 127.0.0.1:55889 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:43:09.830070100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55884 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:13.724212200+08:00" level=info msg="[TCP] 127.0.0.1:55908 --> static.geetest.com:443 using GLOBAL"
time="2025-05-28T00:43:13.726244500+08:00" level=info msg="[TCP] 127.0.0.1:55907 --> www.iflyrec.com:443 using GLOBAL"
time="2025-05-28T00:43:13.727256700+08:00" level=info msg="[TCP] 127.0.0.1:55906 --> www.iflyrec.com:443 using GLOBAL"
time="2025-05-28T00:43:13.792389600+08:00" level=info msg="[TCP] 127.0.0.1:55909 --> idatalog.iflysec.com:443 using GLOBAL"
time="2025-05-28T00:43:14.603602000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55892 --> api.products.aspose.app:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:14.824598400+08:00" level=info msg="[TCP] 127.0.0.1:55923 --> cn.bing.com:443 using GLOBAL"
time="2025-05-28T00:43:14.830495700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55903 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:19.604332100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55917 --> api.products.aspose.app:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:19.623949500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55922 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:19.830859700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55932 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:19.830859700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55930 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:24.831300900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55946 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:24.831300900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55940 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:24.831300900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55941 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:25.860899000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55942 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:26.124677000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55943 --> bat.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:27.177100300+08:00" level=info msg="[TCP] 127.0.0.1:55966 --> data.bilibili.com:443 using GLOBAL"
time="2025-05-28T00:43:29.831804700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55956 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:29.831804700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55955 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:31.125821300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55959 --> bat.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:33.835022500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55971 --> login.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:34.053332100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55972 --> products.aspose.app:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:34.053932600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55973 --> code.jquery.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:34.156392100+08:00" level=info msg="[TCP] 127.0.0.1:56005 --> mcs.zijieapi.com:443 using GLOBAL"
time="2025-05-28T00:43:34.417638700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55974 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:34.832175900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55979 --> download.clashverge.dev:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:34.832175900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56008 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:34.832175900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55986 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:36.128317700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55981 --> bat.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:39.053965400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55999 --> products.aspose.app:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:39.055015000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56004 --> code.jquery.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:39.419042100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56009 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:39.832670200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56022 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:39.832670200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56020 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:39.833185400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56021 --> github.com:443 error: dns resolve failed: couldn't find ip"
time="2025-05-28T00:43:41.129385700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56023 --> bat.bing.com:443 error: dns resolve failed: couldn't find ip"
