2025-07-19 19:48:38 INFO - try to run core in service mode
2025-07-19 19:48:38 INFO - start service: {"config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-19-1948.log", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "core_type": "verge-mihomo-alpha"}
2025-07-19 19:48:38 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-19 19:48:38 INFO - No hotkeys configured
2025-07-19 19:48:38 INFO - Starting to create window
2025-07-19 19:48:38 INFO - Creating new window
2025-07-19 19:48:38 INFO - Window created successfully, attempting to show
2025-07-19 19:48:38 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-19 19:48:38 INFO - Successfully registered hotkey Control+Q for quit
2025-07-19 19:48:38 INFO - running timer task `R3SfPp0qApId`
2025-07-19 19:54:35 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-19 19:54:35 INFO - Successfully registered hotkey Control+Q for quit
