2025-06-17 05:53:57 INFO - try to run core in service mode
2025-06-17 05:53:57 INFO - start service: {"core_type": "verge-mihomo-alpha", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-06-17-0553.log", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe"}
2025-06-17 05:53:57 INFO - Initializing hotkeys, global hotkey enabled: true
2025-06-17 05:53:57 INFO - No hotkeys configured
2025-06-17 05:53:57 INFO - Starting to create window
2025-06-17 05:53:57 INFO - Creating new window
2025-06-17 05:53:57 INFO - Window created successfully, attempting to show
2025-06-17 05:53:57 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-17 05:53:57 INFO - Successfully registered hotkey Control+Q for quit
2025-06-17 05:54:18 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-17 05:54:18 INFO - Successfully registered hotkey Control+Q for quit
