Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-17T20:32:20.609524400+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-17T20:32:20.622189500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-17T20:32:20.622189500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-17T20:32:20.622699500+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-17T20:32:20.644813800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-17T20:32:20.645814900+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-17T20:32:20.925847300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-17T20:32:20.925847300+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-17T20:32:20.942633700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-17T20:32:20.945212100+08:00" level=info msg="Initial configuration complete, total time: 326ms"
time="2025-07-17T20:32:20.946234000+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-17T20:32:20.948390900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-17T20:32:20.948903300+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-17T20:32:20.948903300+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-17T20:32:20.948903300+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider NowE"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-17T20:32:20.958701200+08:00" level=info msg="Start initial provider TVB"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider apple"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider telegram"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider BBC"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Disney"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-17T20:32:20.958701200+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Discord"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Line"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider TVer"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider google"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-17T20:32:20.958694700+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider Lan"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-17T20:32:20.958191700+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-17T20:32:24.699813700+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-17T20:32:24.700322500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-17T20:32:24.700322500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-17T20:32:24.700844100+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-17T20:32:24.701355400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-17T20:32:24.701355400+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-17T20:32:24.702892400+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-17T20:32:25.961188200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.961290100+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-17T20:32:25.962328800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962328800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962328800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962328800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962328800+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-17T20:32:25.962328800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962328800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962328800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962328800+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-17T20:32:25.962328800+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-17T20:32:25.962328800+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-17T20:32:25.962328800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962328800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962328800+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-17T20:32:25.962328800+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-17T20:32:25.962328800+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-17T20:32:25.962328800+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-17T20:32:25.962328800+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-17T20:32:25.962837400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962837400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962837400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962837400+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-17T20:32:25.962837400+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-17T20:32:25.962837400+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-17T20:32:25.962837400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962837400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.962837400+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-17T20:32:25.962837400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-17T20:32:25.964383400+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-17T20:32:25.964383400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-17T20:32:25.964383400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-17T20:32:25.964380300+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-17T20:32:25.964383400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-17T20:32:25.963878000+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.963343700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:25.964383400+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-17T20:32:25.964383400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-17T20:32:25.964383400+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-17T20:32:25.963343700+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-17T20:32:25.964383400+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-17T20:32:25.966218200+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-17T20:32:25.966218200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-17T20:32:25.966725500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-17T20:32:25.966725500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-17T20:32:25.967755500+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-17T20:32:25.967755500+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-17T20:32:25.968257600+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-17T20:32:25.968257600+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-17T20:32:25.968257600+08:00" level=info msg="Start initial provider Discord"
time="2025-07-17T20:32:25.968257600+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-17T20:32:25.968257600+08:00" level=info msg="Start initial provider Disney"
time="2025-07-17T20:32:25.968257600+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider apple"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider TVer"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider TVB"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider NowE"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Lan"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Line"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider telegram"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider google"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider BBC"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-17T20:32:25.968257600+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-17T20:32:25.968265200+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-17T20:32:30.968864400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.970447000+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.968921200+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-17T20:32:30.968921200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-17T20:32:30.970456800+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-17T20:32:30.969944800+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-17T20:32:30.970456800+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-17T20:32:30.970456800+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-17T20:32:30.970456800+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-17T20:32:30.970456800+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-17T20:32:30.970456800+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-17T20:32:30.969430800+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-17T20:32:30.977507000+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-17T20:32:30.977507000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-17T20:32:30.977507000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-17T20:32:30.977507000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-17T20:32:31.339227900+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-17T20:32:31.339737600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-17T20:32:31.339737600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-17T20:32:31.340242400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-17T20:32:31.340242400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-17T20:32:31.340752000+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-17T20:32:31.342291900+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-17T20:32:31.343817600+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Line"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider TVer"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider google"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider NowE"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Disney"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Discord"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider apple"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider TVB"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider telegram"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider BBC"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-17T20:32:31.343829000+08:00" level=info msg="Start initial provider Lan"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344560700+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-17T20:32:36.345082100+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-17T20:32:36.345082100+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.345082100+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.345082100+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.345082100+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-17T20:32:36.345082100+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.344569700+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-17T20:32:36.345082100+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-17T20:32:36.345082100+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-17T20:32:36.345082100+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-17T20:32:36.343999400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T20:32:36.345082100+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-17T20:32:36.349963200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-17T20:32:36.349963200+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-17T20:32:36.349963200+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-17T20:32:36.349963200+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-17T20:32:44.109592300+08:00" level=error msg="🇻🇳越南01 [1.5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-17T20:32:44.109592300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-17T21:03:44.760931500+08:00" level=info msg="[TCP] 127.0.0.1:55738 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:03:48.339526300+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-17T21:03:48.339526300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-17T21:03:48.351236600+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-17T21:03:48.351236600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-17T21:03:48.357718300+08:00" level=error msg="🇸🇬新加坡04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-17T21:03:48.357718300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-17T21:03:49.164772900+08:00" level=info msg="[TCP] 127.0.0.1:55759 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-17T21:03:49.769079800+08:00" level=info msg="[TCP] 127.0.0.1:55762 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:04:01.517983500+08:00" level=info msg="[TCP] 127.0.0.1:55774 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:04:34.307713900+08:00" level=info msg="[TCP] 127.0.0.1:55800 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:04:36.138284200+08:00" level=info msg="[TCP] 127.0.0.1:55804 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:04:40.431570200+08:00" level=info msg="[TCP] 127.0.0.1:55812 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:04:43.303218000+08:00" level=info msg="[TCP] 127.0.0.1:55818 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:04:44.136971800+08:00" level=info msg="[TCP] 127.0.0.1:55826 --> www.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:05:03.819926000+08:00" level=info msg="[TCP] 127.0.0.1:55844 --> www.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:05:04.054288800+08:00" level=info msg="[TCP] 127.0.0.1:55847 --> www.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:05:10.317317100+08:00" level=info msg="[TCP] 127.0.0.1:55854 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:05:10.429295800+08:00" level=info msg="[TCP] 127.0.0.1:55857 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:05:16.499024500+08:00" level=info msg="[TCP] 127.0.0.1:55865 --> at.alicdn.com:443 using GLOBAL"
time="2025-07-17T21:05:17.608565900+08:00" level=info msg="[TCP] 127.0.0.1:55870 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:05:17.618346100+08:00" level=info msg="[TCP] 127.0.0.1:55873 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-17T21:05:19.531138100+08:00" level=info msg="[TCP] 127.0.0.1:55876 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:05:24.780397100+08:00" level=info msg="[TCP] 127.0.0.1:55884 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-17T21:05:36.203284800+08:00" level=info msg="[TCP] 127.0.0.1:55896 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:05:41.163791300+08:00" level=info msg="[TCP] 127.0.0.1:55904 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-17T21:05:41.289229300+08:00" level=info msg="[TCP] 127.0.0.1:55907 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:05:52.317722200+08:00" level=info msg="[TCP] 127.0.0.1:55920 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:05:56.453213900+08:00" level=info msg="[TCP] 127.0.0.1:55925 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:06:24.817643000+08:00" level=info msg="[TCP] 127.0.0.1:55949 --> at.alicdn.com:443 using GLOBAL"
time="2025-07-17T21:06:24.818685200+08:00" level=info msg="[TCP] 127.0.0.1:55948 --> www.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:06:38.567234200+08:00" level=info msg="[TCP] 127.0.0.1:55963 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:06:38.956830800+08:00" level=info msg="[TCP] 127.0.0.1:55970 --> kvm2.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:06:39.308088000+08:00" level=info msg="[TCP] 127.0.0.1:55980 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:06:42.646346800+08:00" level=info msg="[TCP] 127.0.0.1:56028 --> cn.bing.com:443 using GLOBAL"
time="2025-07-17T21:07:05.811578400+08:00" level=info msg="[TCP] 127.0.0.1:56092 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:06.358904000+08:00" level=info msg="[TCP] 127.0.0.1:56095 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:13.339218900+08:00" level=info msg="[TCP] 127.0.0.1:56107 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:07:16.976566700+08:00" level=info msg="[TCP] 127.0.0.1:56113 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-17T21:07:17.569430300+08:00" level=info msg="[TCP] 127.0.0.1:56116 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:07:17.815652400+08:00" level=info msg="[TCP] 127.0.0.1:56119 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:17.815652400+08:00" level=info msg="[TCP] 127.0.0.1:56120 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:17.889338000+08:00" level=info msg="[TCP] 127.0.0.1:56125 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:17.890521000+08:00" level=info msg="[TCP] 127.0.0.1:56128 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:17.934188000+08:00" level=info msg="[TCP] 127.0.0.1:56131 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:17.935829100+08:00" level=info msg="[TCP] 127.0.0.1:56132 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.021128300+08:00" level=info msg="[TCP] 127.0.0.1:56138 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.021128300+08:00" level=info msg="[TCP] 127.0.0.1:56139 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.021128300+08:00" level=info msg="[TCP] 127.0.0.1:56140 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.021643600+08:00" level=info msg="[TCP] 127.0.0.1:56143 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.021643600+08:00" level=info msg="[TCP] 127.0.0.1:56142 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.023438700+08:00" level=info msg="[TCP] 127.0.0.1:56145 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.023438700+08:00" level=info msg="[TCP] 127.0.0.1:56146 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.023438700+08:00" level=info msg="[TCP] 127.0.0.1:56141 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.024824100+08:00" level=info msg="[TCP] 127.0.0.1:56148 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.024824100+08:00" level=info msg="[TCP] 127.0.0.1:56147 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.026560700+08:00" level=info msg="[TCP] 127.0.0.1:56144 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.038857800+08:00" level=info msg="[TCP] 127.0.0.1:56137 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.043858400+08:00" level=info msg="[TCP] 127.0.0.1:56172 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.045369400+08:00" level=info msg="[TCP] 127.0.0.1:56174 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.046504000+08:00" level=info msg="[TCP] 127.0.0.1:56171 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.047234600+08:00" level=info msg="[TCP] 127.0.0.1:56173 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.315359900+08:00" level=info msg="[TCP] 127.0.0.1:56185 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:07:18.358245400+08:00" level=info msg="[TCP] 127.0.0.1:56188 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:07:18.469929200+08:00" level=info msg="[TCP] 127.0.0.1:56191 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.514000500+08:00" level=info msg="[TCP] 127.0.0.1:56194 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:07:18.737523000+08:00" level=info msg="[TCP] 127.0.0.1:56198 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:18.767152100+08:00" level=info msg="[TCP] 127.0.0.1:56201 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:19.034202000+08:00" level=info msg="[TCP] 127.0.0.1:56206 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:19.042696000+08:00" level=info msg="[TCP] 127.0.0.1:56209 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:19.044984000+08:00" level=info msg="[TCP] 127.0.0.1:56212 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:19.082175100+08:00" level=info msg="[TCP] 127.0.0.1:56215 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:19.476192100+08:00" level=info msg="[TCP] 127.0.0.1:56220 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:19.477218000+08:00" level=info msg="[TCP] 127.0.0.1:56221 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:19.982372900+08:00" level=info msg="[TCP] 127.0.0.1:56226 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:19.984619500+08:00" level=info msg="[TCP] 127.0.0.1:56229 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:20.054965800+08:00" level=info msg="[TCP] 127.0.0.1:56232 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:20.207430200+08:00" level=info msg="[TCP] 127.0.0.1:56235 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:23.246372000+08:00" level=info msg="[TCP] 127.0.0.1:56248 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-17T21:07:23.246372000+08:00" level=info msg="[TCP] 127.0.0.1:56251 --> www.doubao.com:443 using GLOBAL"
time="2025-07-17T21:07:23.333907800+08:00" level=info msg="[TCP] 127.0.0.1:56254 --> mssdk.bytedance.com:443 using GLOBAL"
time="2025-07-17T21:07:23.371975600+08:00" level=info msg="[TCP] 127.0.0.1:56257 --> www.doubao.com:443 using GLOBAL"
time="2025-07-17T21:07:23.494360000+08:00" level=info msg="[TCP] 127.0.0.1:56261 --> mcs.doubao.com:443 using GLOBAL"
time="2025-07-17T21:07:24.041907200+08:00" level=info msg="[TCP] 127.0.0.1:56264 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:07:24.043016700+08:00" level=info msg="[TCP] 127.0.0.1:56267 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:07:24.043016700+08:00" level=info msg="[TCP] 127.0.0.1:56268 --> www.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:07:24.213117500+08:00" level=info msg="[TCP] 127.0.0.1:56273 --> mcs.doubao.com:443 using GLOBAL"
time="2025-07-17T21:07:24.309474000+08:00" level=info msg="[TCP] 127.0.0.1:56276 --> p9-flow-imagex-sign.byteimg.com:443 using GLOBAL"
time="2025-07-17T21:07:24.361056800+08:00" level=info msg="[TCP] 127.0.0.1:56279 --> mcs.doubao.com:443 using GLOBAL"
time="2025-07-17T21:07:24.603355400+08:00" level=info msg="[TCP] 127.0.0.1:56283 --> kvm2.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:07:24.613546900+08:00" level=info msg="[TCP] 127.0.0.1:56287 --> p9-flow-imagex-sign.byteimg.com:443 using GLOBAL"
time="2025-07-17T21:07:24.615146100+08:00" level=info msg="[TCP] 127.0.0.1:56286 --> p9-flow-imagex-sign.byteimg.com:443 using GLOBAL"
time="2025-07-17T21:07:25.612654100+08:00" level=info msg="[TCP] 127.0.0.1:56294 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:07:26.366382500+08:00" level=info msg="[TCP] 127.0.0.1:56297 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:07:26.721722600+08:00" level=info msg="[TCP] 127.0.0.1:56300 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:07:29.062563200+08:00" level=info msg="[TCP] 127.0.0.1:56305 --> kvm2.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:07:29.306613800+08:00" level=info msg="[TCP] 127.0.0.1:56308 --> opt.doubao.com:443 using GLOBAL"
time="2025-07-17T21:07:32.788808300+08:00" level=info msg="[TCP] 127.0.0.1:56313 --> kvm2.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:07:36.302192500+08:00" level=info msg="[TCP] 127.0.0.1:56318 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:07:37.506317600+08:00" level=info msg="[TCP] 127.0.0.1:56323 --> at.alicdn.com:443 using GLOBAL"
time="2025-07-17T21:07:38.517314100+08:00" level=info msg="[TCP] 127.0.0.1:56326 --> at.alicdn.com:443 using GLOBAL"
time="2025-07-17T21:07:38.519898900+08:00" level=info msg="[TCP] 127.0.0.1:56327 --> q1.qlogo.cn:443 using GLOBAL"
time="2025-07-17T21:07:38.528590100+08:00" level=info msg="[TCP] 127.0.0.1:56332 --> q1.qlogo.cn:443 using GLOBAL"
time="2025-07-17T21:07:38.530714700+08:00" level=info msg="[TCP] 127.0.0.1:56333 --> q1.qlogo.cn:443 using GLOBAL"
time="2025-07-17T21:07:39.186665500+08:00" level=info msg="[TCP] 127.0.0.1:56338 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:07:39.948072200+08:00" level=info msg="[TCP] 127.0.0.1:56343 --> kvm2.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:07:44.658747500+08:00" level=info msg="[TCP] 127.0.0.1:56356 --> at.alicdn.com:443 using GLOBAL"
time="2025-07-17T21:07:49.101199000+08:00" level=info msg="[TCP] 127.0.0.1:56361 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:08:24.048976700+08:00" level=info msg="[TCP] 127.0.0.1:56388 --> www.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:08:24.309527100+08:00" level=info msg="[TCP] 127.0.0.1:56391 --> mcs.doubao.com:443 using GLOBAL"
time="2025-07-17T21:08:26.144447300+08:00" level=info msg="[TCP] 127.0.0.1:56396 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:08:26.275474200+08:00" level=info msg="[TCP] 127.0.0.1:56399 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:08:28.312959700+08:00" level=info msg="[TCP] 127.0.0.1:56402 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:08:29.541024400+08:00" level=info msg="[TCP] 127.0.0.1:56407 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:08:30.010053500+08:00" level=info msg="[TCP] 127.0.0.1:56410 --> activity.windows.com:443 using GLOBAL"
time="2025-07-17T21:08:35.501685600+08:00" level=info msg="[TCP] 127.0.0.1:56417 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-07-17T21:08:36.360819800+08:00" level=info msg="[TCP] 127.0.0.1:56420 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:08:42.148403200+08:00" level=info msg="[TCP] 127.0.0.1:56433 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:08:42.828661600+08:00" level=info msg="[TCP] 127.0.0.1:56436 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:08:43.535561000+08:00" level=info msg="[TCP] 127.0.0.1:56442 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:06.625107200+08:00" level=info msg="[TCP] 127.0.0.1:56462 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-17T21:09:07.285212000+08:00" level=info msg="[TCP] 127.0.0.1:56465 --> news.baidu.com:443 using GLOBAL"
time="2025-07-17T21:09:07.286294600+08:00" level=info msg="[TCP] 127.0.0.1:56466 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.478913200+08:00" level=info msg="[TCP] 127.0.0.1:56471 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.482448500+08:00" level=info msg="[TCP] 127.0.0.1:56474 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.582468000+08:00" level=info msg="[TCP] 127.0.0.1:56477 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.639216700+08:00" level=info msg="[TCP] 127.0.0.1:56481 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.640215200+08:00" level=info msg="[TCP] 127.0.0.1:56482 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.641216000+08:00" level=info msg="[TCP] 127.0.0.1:56485 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.642279700+08:00" level=info msg="[TCP] 127.0.0.1:56487 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.642279700+08:00" level=info msg="[TCP] 127.0.0.1:56483 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.643515400+08:00" level=info msg="[TCP] 127.0.0.1:56484 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.643515400+08:00" level=info msg="[TCP] 127.0.0.1:56486 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.645043300+08:00" level=info msg="[TCP] 127.0.0.1:56488 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.671450500+08:00" level=info msg="[TCP] 127.0.0.1:56480 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.672953000+08:00" level=info msg="[TCP] 127.0.0.1:56505 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.672953000+08:00" level=info msg="[TCP] 127.0.0.1:56506 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.673909600+08:00" level=info msg="[TCP] 127.0.0.1:56507 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.674871900+08:00" level=info msg="[TCP] 127.0.0.1:56511 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.674871900+08:00" level=info msg="[TCP] 127.0.0.1:56509 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.677816600+08:00" level=info msg="[TCP] 127.0.0.1:56510 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.680277300+08:00" level=info msg="[TCP] 127.0.0.1:56508 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.689887400+08:00" level=info msg="[TCP] 127.0.0.1:56529 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.690399700+08:00" level=info msg="[TCP] 127.0.0.1:56528 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.693930300+08:00" level=info msg="[TCP] 127.0.0.1:56530 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:07.805937200+08:00" level=info msg="[TCP] 127.0.0.1:56537 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:07.815776500+08:00" level=info msg="[TCP] 127.0.0.1:56540 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:07.820163600+08:00" level=info msg="[TCP] 127.0.0.1:56543 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:07.852802500+08:00" level=info msg="[TCP] 127.0.0.1:56546 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:07.867294300+08:00" level=info msg="[TCP] 127.0.0.1:56549 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.108705600+08:00" level=info msg="[TCP] 127.0.0.1:56552 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:08.186284400+08:00" level=info msg="[TCP] 127.0.0.1:56555 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:08.313987300+08:00" level=info msg="[TCP] 127.0.0.1:56558 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.387109500+08:00" level=info msg="[TCP] 127.0.0.1:56561 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.391419800+08:00" level=info msg="[TCP] 127.0.0.1:56564 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.455240900+08:00" level=info msg="[TCP] 127.0.0.1:56567 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.466712300+08:00" level=info msg="[TCP] 127.0.0.1:56570 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.470134800+08:00" level=info msg="[TCP] 127.0.0.1:56573 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.480090900+08:00" level=info msg="[TCP] 127.0.0.1:56576 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.507470200+08:00" level=info msg="[TCP] 127.0.0.1:56579 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.528371500+08:00" level=info msg="[TCP] 127.0.0.1:56582 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.594028200+08:00" level=info msg="[TCP] 127.0.0.1:56586 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.612682000+08:00" level=info msg="[TCP] 127.0.0.1:56589 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.625160900+08:00" level=info msg="[TCP] 127.0.0.1:56592 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:08.809627300+08:00" level=info msg="[TCP] 127.0.0.1:56596 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:09.395556400+08:00" level=info msg="[TCP] 127.0.0.1:56601 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:09.476367100+08:00" level=info msg="[TCP] 127.0.0.1:56604 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:11.879974300+08:00" level=info msg="[TCP] 127.0.0.1:56613 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:13.183789100+08:00" level=info msg="[TCP] 127.0.0.1:56616 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:09:13.469062200+08:00" level=info msg="[TCP] 127.0.0.1:56619 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:09:19.382934200+08:00" level=info msg="[TCP] 127.0.0.1:56627 --> at.alicdn.com:443 using GLOBAL"
time="2025-07-17T21:09:19.427809800+08:00" level=info msg="[TCP] 127.0.0.1:56630 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-17T21:09:19.587894900+08:00" level=info msg="[TCP] 127.0.0.1:56633 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-17T21:09:19.680773600+08:00" level=info msg="[TCP] 127.0.0.1:56636 --> kvm2.niuteyun.com:443 using GLOBAL"
time="2025-07-17T21:32:19.535356500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60698 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:24.537005600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60896 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:26.399534300+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-17T21:32:26.400568300+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-17T21:32:26.400568300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-17T21:32:26.401074800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-17T21:32:26.401074800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-17T21:32:26.401074800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-17T21:32:26.403112400+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-17T21:32:26.404128100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-17T21:32:26.410220800+08:00" level=warning msg="[TUN] default interface changed by monitor,  => 以太网"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="[TUN] Tun adapter listening at: Meta([********/30],[fdfe:dcba:9876::1/126]), mtu: 9000, auto route: true, auto redir: false, ip stack: Mixed"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Disney"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider telegram"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Lan"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider NowE"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Line"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Discord"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider TVer"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider TVB"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider BBC"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider google"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider apple"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-17T21:32:26.978492400+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979790800+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-17T21:32:31.980300100+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.980304100+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-17T21:32:31.979795700+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-17T21:32:31.980304100+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-17T21:32:31.980304100+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-17T21:32:31.980304100+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.980304100+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-17T21:32:31.980304100+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-17T21:32:31.979284800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-17T21:32:31.980304100+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-17T21:32:31.982516400+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-17T21:32:31.982516400+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-17T21:32:31.982516400+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-17T21:32:31.982516400+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-17T21:32:32.275868100+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:60833 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-17T21:32:32.506128700+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:60834 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-17T21:32:33.176204700+08:00" level=info msg="[TCP] ********:61069 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:33.401785200+08:00" level=info msg="[TCP] ********:61079 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:33.936338200+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:57001 --> [2402:4e00:1900:1902:0:9687:dec7:f0a3]:3478 using GLOBAL"
time="2025-07-17T21:32:33.966400600+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:60833 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-17T21:32:34.123802100+08:00" level=info msg="[UDP] ********:57000 --> 111.31.205.88:3478 using GLOBAL"
time="2025-07-17T21:32:34.183324400+08:00" level=info msg="[TCP] ********:61098 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:34.504771900+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:60834 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-17T21:32:35.045400300+08:00" level=info msg="[TCP] 127.0.0.1:61110 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-17T21:32:35.103544300+08:00" level=info msg="[TCP] 127.0.0.1:61113 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-17T21:32:35.358718100+08:00" level=info msg="[TCP] 127.0.0.1:61117 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-17T21:32:35.683357400+08:00" level=info msg="[TCP] ********:61123 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:35.981166800+08:00" level=info msg="[TCP] 127.0.0.1:61126 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:32:36.331919700+08:00" level=info msg="[TCP] 127.0.0.1:61131 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-07-17T21:32:36.468435100+08:00" level=info msg="[TCP] ********:61134 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:37.148336700+08:00" level=info msg="[TCP] ********:61141 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:37.480209700+08:00" level=info msg="[TCP] ********:61146 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:37.494354500+08:00" level=info msg="[TCP] ********:61149 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:38.026205200+08:00" level=info msg="[TCP] ********:61161 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:38.487939400+08:00" level=info msg="[TCP] ********:61174 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:38.526692200+08:00" level=info msg="[TCP] ********:61177 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:38.972779200+08:00" level=info msg="[TCP] ********:61187 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:38.972779200+08:00" level=info msg="[TCP] ********:61188 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:38.973803500+08:00" level=info msg="[TCP] ********:61189 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:39.973031400+08:00" level=info msg="[TCP] ********:61198 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:39.978194300+08:00" level=info msg="[TCP] ********:61201 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:39.983833100+08:00" level=info msg="[TCP] ********:61202 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:40.921614800+08:00" level=info msg="[TCP] 127.0.0.1:61209 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-17T21:32:41.103644800+08:00" level=info msg="[TCP] ********:61212 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:41.227705100+08:00" level=info msg="[TCP] 127.0.0.1:61216 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-17T21:32:41.229884700+08:00" level=info msg="[TCP] 127.0.0.1:61215 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-17T21:32:41.565399400+08:00" level=info msg="[TCP] 127.0.0.1:61221 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-17T21:32:41.614649600+08:00" level=info msg="[TCP] ********:61224 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:41.949698200+08:00" level=info msg="[TCP] ********:61227 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:42.707424800+08:00" level=info msg="[TCP] ********:61236 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:42.708448600+08:00" level=info msg="[TCP] ********:61230 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:42.708448600+08:00" level=info msg="[TCP] ********:61233 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:43.487661800+08:00" level=info msg="[TCP] ********:61241 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:43.495856000+08:00" level=info msg="[TCP] ********:61244 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:43.730253400+08:00" level=info msg="[TCP] ********:61247 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:43.967950400+08:00" level=info msg="[TCP] ********:61250 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:44.639298800+08:00" level=info msg="[TCP] 127.0.0.1:61253 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:32:44.940746400+08:00" level=info msg="[TCP] 127.0.0.1:61256 --> www.bing.com:443 using GLOBAL"
time="2025-07-17T21:32:45.647669500+08:00" level=info msg="[TCP] ********:61259 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:46.314962300+08:00" level=info msg="[TCP] ********:61262 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:46.314962300+08:00" level=info msg="[TCP] ********:61263 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:46.314962300+08:00" level=info msg="[TCP] ********:61264 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:46.595224300+08:00" level=info msg="[TCP] ********:61273 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:46.622509000+08:00" level=info msg="[TCP] ********:61276 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:47.425664100+08:00" level=info msg="[TCP] ********:61279 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:48.309380800+08:00" level=info msg="[TCP] ********:61282 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:48.343399700+08:00" level=info msg="[TCP] ********:61285 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:49.031937500+08:00" level=info msg="[TCP] ********:61288 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:49.031937500+08:00" level=info msg="[TCP] ********:61289 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:49.031937500+08:00" level=info msg="[TCP] ********:61290 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:49.439447300+08:00" level=info msg="[TCP] ********:61298 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:50.019097600+08:00" level=info msg="[TCP] ********:61302 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:50.044738800+08:00" level=info msg="[TCP] ********:61305 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:50.045762000+08:00" level=info msg="[TCP] ********:61308 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:50.107328600+08:00" level=info msg="[TCP] ********:61311 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:50.373747700+08:00" level=info msg="[TCP] ********:61314 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:50.409755200+08:00" level=info msg="[TCP] ********:61317 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:50.786129200+08:00" level=info msg="[TCP] ********:61320 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:51.155067200+08:00" level=info msg="[TCP] ********:61323 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:52.105926900+08:00" level=info msg="[TCP] ********:61326 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:52.915493600+08:00" level=info msg="[TCP] ********:61331 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:52.943416400+08:00" level=info msg="[TCP] ********:61334 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:53.693224900+08:00" level=info msg="[TCP] ********:61337 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:54.903175600+08:00" level=info msg="[TCP] ********:61340 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:55.030610800+08:00" level=info msg="[TCP] 127.0.0.1:61343 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:55.339530900+08:00" level=info msg="[TCP] 127.0.0.1:61346 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:55.457271800+08:00" level=info msg="[TCP] ********:61350 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:55.466003300+08:00" level=info msg="[TCP] ********:61353 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:55.595189700+08:00" level=info msg="[TCP] 127.0.0.1:61356 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:56.766650400+08:00" level=info msg="[TCP] ********:61360 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:32:57.279065300+08:00" level=info msg="[TCP] ********:61363 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:32:58.092809200+08:00" level=info msg="[TCP] ********:61366 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:58.810598600+08:00" level=info msg="[TCP] ********:61371 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:32:59.445154500+08:00" level=info msg="[UDP] ********:56999 --> 111.31.205.88:3478 using GLOBAL"
time="2025-07-17T21:32:59.593437800+08:00" level=info msg="[TCP] ********:61374 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:33:00.021274800+08:00" level=info msg="[TCP] ********:61377 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:33:00.034356700+08:00" level=info msg="[TCP] ********:61380 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:33:00.759016500+08:00" level=info msg="[TCP] ********:61383 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:33:01.155801000+08:00" level=info msg="[TCP] ********:61386 --> d5.api.augmentcode.com:443 using GLOBAL"
time="2025-07-17T21:33:01.179554700+08:00" level=info msg="[TCP] 127.0.0.1:61389 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:33:02.656334200+08:00" level=info msg="[TCP] ********:61394 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-17T21:33:04.280772300+08:00" level=info msg="[TCP] ********:61397 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-17T21:33:08.539768700+08:00" level=info msg="[TCP] 127.0.0.1:61404 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:33:15.130560700+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-17T21:33:15.131068100+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-17T21:33:15.131068100+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-17T21:33:15.131604300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-17T21:33:15.132116700+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-17T21:33:15.132116700+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-17T21:33:15.133646800+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-17T21:33:15.134663000+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider NowE"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Discord"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider BBC"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Disney"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Line"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider telegram"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider google"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider TVer"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Lan"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider TVB"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider apple"
time="2025-07-17T21:33:15.212693000+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-17T21:33:15.526746800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.528102400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.528102400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.528102400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.528624600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.529561000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.529561000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.529561000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.530774400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.530774400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.530774400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.530774400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.531296200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.531417900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.531417900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.531417900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.532923400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.532923400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.533924300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.534401200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.534401200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.534401200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.534401200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.534903700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.534908200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.534914200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.538320200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.538320200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.538834400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.538834400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.539341000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.539341000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.539341000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.539597100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.539597100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.539597100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.539597100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.540105500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.540105500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.540105500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.540105500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.540641100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.540641100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.550014200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.550014200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.550014200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.550528200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.550528200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.551564000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-17T21:33:15.629477100+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-17T21:33:15.630063600+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-17T21:33:15.641308800+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-17T21:33:15.652217500+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-17T21:33:15.653085100+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-17T21:33:15.659880200+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-17T21:33:15.670352900+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-17T21:33:15.672047800+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-17T21:33:15.682001000+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-17T21:33:15.693085400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-17T21:33:15.744390200+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-17T21:33:16.586065800+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-07-17T21:33:16.592449000+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-17T21:33:16.632192800+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-17T21:33:16.639615400+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-17T21:33:16.649758900+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-17T21:33:16.654460700+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-17T21:33:16.655190100+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-17T21:33:16.660000400+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-17T21:33:16.662677400+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-17T21:33:16.666336400+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-07-17T21:33:16.674172500+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-17T21:33:16.678936800+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-17T21:33:16.700399700+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-17T21:33:16.705714000+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-17T21:33:16.707237800+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-17T21:33:16.708688800+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-17T21:33:16.724692700+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-17T21:33:16.724692700+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-17T21:33:16.750011600+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-17T21:33:16.760391600+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-17T21:33:16.947160100+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-17T21:33:16.960778600+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-17T21:33:17.027848600+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-17T21:33:17.035459300+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-17T21:33:17.035459300+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-17T21:33:17.037231100+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-17T21:33:17.065051500+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-17T21:33:17.071613000+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-17T21:33:17.075264700+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-17T21:33:17.141955100+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-17T21:33:17.158153300+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-17T21:33:17.163089900+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-17T21:33:17.165008100+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-17T21:33:17.190112800+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-17T21:33:17.192869200+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-17T21:33:17.210905000+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-17T21:33:17.249538900+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-17T21:33:17.253317000+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-17T21:33:17.257398900+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-17T21:33:17.257398900+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-17T21:33:17.257398900+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-17T21:33:17.257398900+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-17T21:33:29.849979400+08:00" level=info msg="[TCP] 127.0.0.1:61940 --> login.live.com:443 using GLOBAL"
time="2025-07-17T21:33:30.732030000+08:00" level=info msg="[TCP] 127.0.0.1:61965 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:33:31.474735600+08:00" level=info msg="[TCP] 127.0.0.1:61975 --> graph.microsoft.com:443 using GLOBAL"
time="2025-07-17T21:33:40.620712300+08:00" level=warning msg="Mihomo shutting down"
