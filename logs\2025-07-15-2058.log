2025-07-15 20:58:20 INFO - try to run core in service mode
2025-07-15 20:58:20 INFO - start service: {"config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-15-2058.log", "core_type": "verge-mihomo-alpha", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev"}
2025-07-15 20:58:20 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-15 20:58:20 INFO - No hotkeys configured
2025-07-15 20:58:20 INFO - Starting to create window
2025-07-15 20:58:20 INFO - Creating new window
2025-07-15 20:58:20 INFO - Window created successfully, attempting to show
2025-07-15 20:58:20 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 20:58:20 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 20:58:20 INFO - running timer task `R3SfPp0qApId`
2025-07-15 20:58:29 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 20:58:29 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 20:58:44 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 20:58:44 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 20:58:45 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 20:58:45 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 20:59:14 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 20:59:14 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 20:59:24 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 20:59:24 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 21:02:07 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 21:02:07 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 21:02:07 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 21:02:07 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 23:26:47 INFO - Starting to create window
2025-07-15 23:26:47 INFO - Found existing window, trying to show it
2025-07-15 23:26:47 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 23:26:47 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 23:33:28 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 23:33:28 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 23:56:21 INFO - Starting to create window
2025-07-15 23:56:21 INFO - Found existing window, trying to show it
2025-07-15 23:56:21 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 23:56:21 INFO - Successfully registered hotkey Control+Q for quit
2025-07-15 23:56:31 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-15 23:56:31 INFO - Successfully registered hotkey Control+Q for quit
2025-07-16 00:01:13 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-16 00:01:13 INFO - Successfully registered hotkey Control+Q for quit
2025-07-16 00:01:15 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-16 00:01:15 INFO - Successfully registered hotkey Control+Q for quit
2025-07-16 00:02:12 INFO - Starting to create window
2025-07-16 00:02:12 INFO - Found existing window, trying to show it
2025-07-16 00:02:12 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-16 00:02:12 INFO - Successfully registered hotkey Control+Q for quit
2025-07-16 00:12:09 INFO - Starting to create window
2025-07-16 00:12:09 INFO - Found existing window, trying to show it
2025-07-16 00:12:09 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-16 00:12:09 INFO - Successfully registered hotkey Control+Q for quit
