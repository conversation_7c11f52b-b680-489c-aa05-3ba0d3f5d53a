Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-09T21:56:39.111186800+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-09T21:56:39.119412800+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-09T21:56:39.119412800+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-09T21:56:39.119921500+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-09T21:56:39.140874100+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-09T21:56:39.140874100+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-09T21:56:39.407571500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-09T21:56:39.407571500+08:00" level=info msg="Load GeoSite rule: private"
time="2025-06-09T21:56:39.426832800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-09T21:56:39.429397500+08:00" level=info msg="Initial configuration complete, total time: 313ms"
time="2025-06-09T21:56:39.430421300+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-06-09T21:56:39.431444600+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-09T21:56:39.431956400+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-06-09T21:56:39.431956400+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-06-09T21:56:39.431956400+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-06-09T21:56:39.437076000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-09T21:56:39.437076000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Lan"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider NowE"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Disney"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider google"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Discord"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider TVB"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider apple"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider TVer"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider BBC"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider Line"
time="2025-06-09T21:56:39.437130900+08:00" level=info msg="Start initial provider telegram"
time="2025-06-09T21:56:42.357765200+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-09T21:56:42.358275500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-09T21:56:42.358275500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-09T21:56:42.358809000+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-09T21:56:42.358809000+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-09T21:56:42.358809000+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-09T21:56:42.360346200+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-09T21:56:44.439926700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.439926700+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-09T21:56:44.440576700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.440576700+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-09T21:56:44.441087700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441087700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441087700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441087700+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-09T21:56:44.441087700+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-09T21:56:44.441087700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441087700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441087700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441087700+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-09T21:56:44.441087700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441087700+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-09T21:56:44.441087700+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-09T21:56:44.441087700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441087700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441087700+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-09T21:56:44.441087700+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-09T21:56:44.441087700+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-09T21:56:44.441087700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441087700+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-09T21:56:44.441087700+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-09T21:56:44.441598100+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-09T21:56:44.442111200+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-09T21:56:44.499818900+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-09T21:56:44.499818900+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-09T21:56:44.499818900+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-09T21:56:44.499818900+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider BBC"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Start initial provider telegram"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Start initial provider Lan"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-09T21:56:44.501861200+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-09T21:56:44.501358900+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-09T21:56:44.501861200+08:00" level=info msg="Start initial provider Line"
time="2025-06-09T21:56:44.501861200+08:00" level=info msg="Start initial provider TVer"
time="2025-06-09T21:56:44.501861200+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-09T21:56:44.501861200+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-09T21:56:44.501861200+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider apple"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider Disney"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-09T21:56:44.501865400+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider TVB"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider NowE"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider Discord"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-09T21:56:44.501870100+08:00" level=info msg="Start initial provider google"
time="2025-06-09T21:56:49.501399000+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-09T21:56:49.501399000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-09T21:56:49.503447800+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-09T21:56:49.503447800+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-09T21:56:49.503447800+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T21:56:49.502935100+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-09T21:56:49.502416000+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-09T21:56:49.503447800+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-09T21:56:49.508157700+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-09T21:56:49.508157700+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-09T21:56:49.508157700+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-09T21:56:49.508157700+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-09T21:56:49.844347800+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-09T21:56:49.844857200+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-09T21:56:49.844857200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-09T21:56:49.845390900+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-09T21:56:49.845390900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-09T21:56:49.845390900+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-09T21:56:49.846917500+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider TVB"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider telegram"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider BBC"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Disney"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider google"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Lan"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider NowE"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Line"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider TVer"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Discord"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider apple"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-09T21:56:49.847938000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-09T21:56:50.062065700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.062085800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.062601200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.063112200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.063618500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.063618500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.064126200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.064126200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.064650400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.066183900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.067241500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.067241500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.073410400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.073410400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.073410400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.073410400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.073410400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.073410400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.083693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.083693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.083693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.083693000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.084208900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.085607600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.085607600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.096778600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.096778600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.098290400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.098290400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.098802200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.098802200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.099484800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.101489900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.101489900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.102008400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.102517900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.102517900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.103030000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.103030000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.104400100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.104400100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:50.215750600+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-09T21:56:50.216768200+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-09T21:56:50.427028100+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-09T21:56:50.682551600+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-06-09T21:56:50.729960100+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-06-09T21:56:50.737917000+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-06-09T21:56:50.741090700+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-06-09T21:56:50.742319300+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-06-09T21:56:50.756181900+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-06-09T21:56:50.797690800+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-06-09T21:56:50.818423300+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-06-09T21:56:50.818928600+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-06-09T21:56:50.820609000+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-06-09T21:56:50.828126700+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-06-09T21:56:50.865707300+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-06-09T21:56:50.884761300+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-06-09T21:56:50.968131100+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-06-09T21:56:50.999829100+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-06-09T21:56:51.001556300+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-06-09T21:56:51.065149200+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-06-09T21:56:51.076272500+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-06-09T21:56:51.077741100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:51.078648500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:51.078648500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:51.078648500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:51.078648500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:51.078648500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:51.079614500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:51.094080300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:51.136376200+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-06-09T21:56:51.196168000+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-06-09T21:56:51.243899100+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-06-09T21:56:51.325746700+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-06-09T21:56:51.425529200+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-06-09T21:56:51.714121800+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-09T21:56:51.798198300+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-06-09T21:56:51.936961100+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-06-09T21:56:51.961663500+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-06-09T21:56:51.986620300+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-06-09T21:56:51.987878000+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-06-09T21:56:52.001866700+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-06-09T21:56:52.125691800+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-06-09T21:56:52.151213000+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-06-09T21:56:52.155517800+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-06-09T21:56:52.337967700+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-06-09T21:56:52.454882800+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-06-09T21:56:52.529696900+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-06-09T21:56:52.644681100+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-06-09T21:56:52.664136900+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-06-09T21:56:53.367863600+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-06-09T21:56:54.031976100+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-09T21:56:54.508793100+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-09T21:56:54.508793100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-09T21:56:55.255359300+08:00" level=error msg="🇺🇸美国02 [2x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-09T21:56:55.255359300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-09T21:56:56.324183600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-09T21:56:57.650816900+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-09T21:56:59.108948800+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-06-09T21:56:59.849538400+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": net/http: TLS handshake timeout"
time="2025-06-09T21:56:59.849538400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": net/http: TLS handshake timeout"
time="2025-06-09T21:57:08.725033000+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-09T21:57:09.849625700+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": context deadline exceeded"
time="2025-06-09T21:57:09.854239200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-09T21:57:09.854239200+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-09T21:57:09.854239200+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-09T21:57:09.854239200+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-09T21:57:10.940247900+08:00" level=info msg="[TCP] 127.0.0.1:55146 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:57:11.305863600+08:00" level=info msg="[TCP] 127.0.0.1:55151 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:57:14.833098200+08:00" level=info msg="[TCP] 127.0.0.1:55181 --> www.google.com:443 using GLOBAL"
time="2025-06-09T21:57:15.236584200+08:00" level=error msg="🇰🇷韩国02 [2x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-09T21:57:15.236584200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-09T21:57:15.385997900+08:00" level=info msg="[TCP] 127.0.0.1:55187 --> www.google.com:443 using GLOBAL"
time="2025-06-09T21:57:16.540172500+08:00" level=info msg="[TCP] 127.0.0.1:55195 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:57:16.867703600+08:00" level=info msg="[TCP] 127.0.0.1:55197 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:57:16.976205200+08:00" level=info msg="[TCP] 127.0.0.1:55190 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:57:17.355642400+08:00" level=info msg="[TCP] 127.0.0.1:55199 --> lh3.google.com:443 using GLOBAL"
time="2025-06-09T21:57:17.488971400+08:00" level=info msg="[TCP] 127.0.0.1:55193 --> lh3.google.com:443 using GLOBAL"
time="2025-06-09T21:57:17.609329100+08:00" level=info msg="[TCP] 127.0.0.1:55201 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:57:19.322764900+08:00" level=info msg="[TCP] 127.0.0.1:55208 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-09T21:57:19.330568500+08:00" level=info msg="[TCP] 127.0.0.1:55204 --> ogads-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-09T21:57:19.530257400+08:00" level=info msg="[TCP] 127.0.0.1:55210 --> ogs.google.com:443 using GLOBAL"
time="2025-06-09T21:57:19.639012700+08:00" level=info msg="[TCP] 127.0.0.1:55213 --> download.clashverge.dev:443 using GLOBAL"
time="2025-06-09T21:57:20.318712500+08:00" level=info msg="[TCP] 127.0.0.1:55215 --> play.google.com:443 using GLOBAL"
time="2025-06-09T21:57:21.*********+08:00" level=info msg="[TCP] 127.0.0.1:55219 --> play.google.com:443 using GLOBAL"
time="2025-06-09T21:57:22.*********+08:00" level=info msg="[TCP] 127.0.0.1:55222 --> play.google.com:443 using GLOBAL"
time="2025-06-09T21:57:23.*********+08:00" level=info msg="[TCP] 127.0.0.1:55225 --> play.google.com:443 using GLOBAL"
time="2025-06-09T21:57:23.*********+08:00" level=info msg="[TCP] 127.0.0.1:55227 --> accounts.google.com:443 using GLOBAL"
time="2025-06-09T21:57:24.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55206 --> waa-pa.clients6.google.com:443 error: h.cm.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T21:57:25.*********+08:00" level=info msg="[TCP] 127.0.0.1:55230 --> waa-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-09T21:57:25.*********+08:00" level=info msg="[TCP] 127.0.0.1:55233 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:57:25.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55217 --> ssl.gstatic.com:443 error: h.cm.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T21:57:25.*********+08:00" level=info msg="[TCP] 127.0.0.1:55236 --> ssl.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:57:26.*********+08:00" level=info msg="[TCP] 127.0.0.1:55238 --> waa-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-09T21:57:27.744740300+08:00" level=info msg="[TCP] 127.0.0.1:55240 --> waa-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-09T21:57:27.891595700+08:00" level=info msg="[TCP] 127.0.0.1:55243 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-09T21:57:32.161386300+08:00" level=info msg="[TCP] 127.0.0.1:55248 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:57:40.371030800+08:00" level=info msg="[TCP] 127.0.0.1:55256 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:57:41.312970600+08:00" level=info msg="[TCP] 127.0.0.1:55262 --> encrypted-tbn0.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:57:41.312970600+08:00" level=info msg="[TCP] 127.0.0.1:55260 --> encrypted-tbn0.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:57:41.327165900+08:00" level=info msg="[TCP] 127.0.0.1:55265 --> encrypted-tbn0.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:57:41.612329900+08:00" level=info msg="[TCP] 127.0.0.1:55269 --> encrypted-tbn0.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:57:41.614415100+08:00" level=info msg="[TCP] 127.0.0.1:55268 --> encrypted-tbn0.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:57:42.314072300+08:00" level=info msg="[TCP] 127.0.0.1:55264 --> encrypted-tbn0.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:57:43.299984700+08:00" level=info msg="[TCP] 127.0.0.1:55272 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T21:57:44.268072600+08:00" level=info msg="[TCP] 127.0.0.1:55276 --> www.googleadservices.com:443 using GLOBAL"
time="2025-06-09T21:57:45.419237800+08:00" level=info msg="[TCP] 127.0.0.1:55278 --> play.google.com:443 using GLOBAL"
time="2025-06-09T21:57:45.671725600+08:00" level=info msg="[TCP] 127.0.0.1:55284 --> play.google.com:443 using GLOBAL"
time="2025-06-09T21:57:45.673745900+08:00" level=info msg="[TCP] 127.0.0.1:55285 --> play.google.com:443 using GLOBAL"
time="2025-06-09T21:57:45.680137700+08:00" level=info msg="[TCP] 127.0.0.1:55282 --> play.google.com:443 using GLOBAL"
time="2025-06-09T21:57:45.710020800+08:00" level=info msg="[TCP] 127.0.0.1:55283 --> play.google.com:443 using GLOBAL"
time="2025-06-09T21:57:45.980053900+08:00" level=info msg="[TCP] 127.0.0.1:55298 --> tools.google.com:443 using GLOBAL"
time="2025-06-09T21:57:45.982568900+08:00" level=info msg="[TCP] 127.0.0.1:55294 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-06-09T21:57:45.982568900+08:00" level=info msg="[TCP] 127.0.0.1:55296 --> www.youtube.com:443 using GLOBAL"
time="2025-06-09T21:57:46.033934400+08:00" level=info msg="[TCP] 127.0.0.1:55300 --> s.ytimg.com:443 using GLOBAL"
time="2025-06-09T21:57:46.048592800+08:00" level=info msg="[TCP] 127.0.0.1:55292 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-06-09T21:57:46.672059400+08:00" level=info msg="[TCP] 127.0.0.1:55286 --> play.google.com:443 using GLOBAL"
time="2025-06-09T21:57:50.405616800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:55280 --> ogads-pa.clients6.google.com:443 error: h.cm.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T21:57:50.460812600+08:00" level=info msg="[TCP] 127.0.0.1:55307 --> ogads-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-09T21:58:09.059706200+08:00" level=info msg="[TCP] 127.0.0.1:55324 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-06-09T21:58:13.905882500+08:00" level=info msg="[TCP] 127.0.0.1:55332 --> www.google.com:443 using GLOBAL"
time="2025-06-09T21:58:15.251108600+08:00" level=info msg="[TCP] 127.0.0.1:55329 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:58:31.098938800+08:00" level=info msg="[TCP] 127.0.0.1:55350 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:58:40.556813700+08:00" level=info msg="[TCP] 127.0.0.1:55359 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:58:42.018982200+08:00" level=info msg="[TCP] 127.0.0.1:55367 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:58:42.018982200+08:00" level=info msg="[TCP] 127.0.0.1:55364 --> dl.google.com:443 using GLOBAL"
time="2025-06-09T21:58:42.191265500+08:00" level=info msg="[TCP] 127.0.0.1:55363 --> dl.google.com:443 using GLOBAL"
time="2025-06-09T21:58:42.595414000+08:00" level=info msg="[TCP] 127.0.0.1:55369 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T21:58:43.428150500+08:00" level=info msg="[TCP] 127.0.0.1:55372 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T21:58:43.434133600+08:00" level=info msg="[TCP] 127.0.0.1:55374 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:58:44.731432400+08:00" level=info msg="[TCP] 127.0.0.1:55378 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:58:46.136285900+08:00" level=info msg="[TCP] 127.0.0.1:55383 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T21:58:49.064964600+08:00" level=info msg="[TCP] 127.0.0.1:55381 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:58:49.216007200+08:00" level=info msg="[TCP] 127.0.0.1:55388 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T21:58:50.028039600+08:00" level=info msg="[TCP] 127.0.0.1:55390 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-06-09T21:58:51.546662800+08:00" level=info msg="[TCP] 127.0.0.1:55395 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-09T21:58:52.689620100+08:00" level=info msg="[TCP] 127.0.0.1:55394 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-09T21:58:53.574030800+08:00" level=info msg="[TCP] 127.0.0.1:55400 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:59:00.952519200+08:00" level=info msg="[TCP] 127.0.0.1:55407 --> www.google-analytics.com:443 using GLOBAL"
time="2025-06-09T21:59:01.624955900+08:00" level=info msg="[TCP] 127.0.0.1:55409 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:59:13.919413700+08:00" level=info msg="[TCP] 127.0.0.1:55421 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-09T21:59:14.216196500+08:00" level=info msg="[TCP] 127.0.0.1:55423 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-09T21:59:14.228465800+08:00" level=info msg="[TCP] 127.0.0.1:55425 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-09T21:59:16.439176000+08:00" level=info msg="[TCP] 127.0.0.1:55430 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-09T21:59:18.219681800+08:00" level=info msg="[TCP] 127.0.0.1:55432 --> activity.windows.com:443 using GLOBAL"
time="2025-06-09T21:59:21.240510400+08:00" level=info msg="[TCP] 127.0.0.1:55504 --> meeting.tencent.com:443 using GLOBAL"
time="2025-06-09T21:59:21.333964800+08:00" level=info msg="[TCP] 127.0.0.1:55507 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:59:22.595378500+08:00" level=info msg="[TCP] 127.0.0.1:55531 --> cdn.meeting.tencent.com:443 using GLOBAL"
time="2025-06-09T21:59:23.004333300+08:00" level=info msg="[TCP] 127.0.0.1:55535 --> cdn.meeting.tencent.com:443 using GLOBAL"
time="2025-06-09T21:59:23.968392700+08:00" level=info msg="[TCP] 127.0.0.1:55534 --> cdn.meeting.tencent.com:443 using GLOBAL"
time="2025-06-09T21:59:24.189396100+08:00" level=info msg="[TCP] 127.0.0.1:55539 --> aegis.qq.com:443 using GLOBAL"
time="2025-06-09T21:59:30.048892500+08:00" level=info msg="[TCP] 127.0.0.1:55606 --> publiclog.zhiyan.tencent-cloud.net:443 using GLOBAL"
time="2025-06-09T21:59:37.339776300+08:00" level=info msg="[TCP] 127.0.0.1:55622 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:59:40.488104900+08:00" level=info msg="[TCP] 127.0.0.1:55626 --> www.bingapis.com:443 using GLOBAL"
time="2025-06-09T21:59:42.350334100+08:00" level=info msg="[TCP] 127.0.0.1:55629 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T21:59:42.799507500+08:00" level=info msg="[TCP] 127.0.0.1:55632 --> dl-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:59:48.601065900+08:00" level=info msg="[TCP] 127.0.0.1:55641 --> ad.doubleclick.net:443 using GLOBAL"
time="2025-06-09T21:59:48.601573300+08:00" level=info msg="[TCP] 127.0.0.1:55638 --> 2542116.fls.doubleclick.net:443 using GLOBAL"
time="2025-06-09T21:59:48.622876200+08:00" level=info msg="[TCP] 127.0.0.1:55640 --> td.doubleclick.net:443 using GLOBAL"
time="2025-06-09T21:59:49.803942000+08:00" level=info msg="[TCP] 127.0.0.1:55644 --> www.googleadservices.com:443 using GLOBAL"
time="2025-06-09T21:59:50.106450100+08:00" level=info msg="[TCP] 127.0.0.1:55646 --> td.doubleclick.net:443 using GLOBAL"
time="2025-06-09T21:59:50.154410900+08:00" level=info msg="[TCP] 127.0.0.1:55648 --> adservice.google.com:443 using GLOBAL"
time="2025-06-09T21:59:50.154410900+08:00" level=info msg="[TCP] 127.0.0.1:55650 --> www.googleadservices.com:443 using GLOBAL"
time="2025-06-09T21:59:50.192614200+08:00" level=info msg="[TCP] 127.0.0.1:55656 --> www.googleadservices.com:443 using GLOBAL"
time="2025-06-09T21:59:50.192614200+08:00" level=info msg="[TCP] 127.0.0.1:55654 --> www.googleadservices.com:443 using GLOBAL"
time="2025-06-09T21:59:50.194184500+08:00" level=info msg="[TCP] 127.0.0.1:55657 --> www.googleadservices.com:443 using GLOBAL"
time="2025-06-09T21:59:50.194194200+08:00" level=info msg="[TCP] 127.0.0.1:55653 --> www.googleadservices.com:443 using GLOBAL"
time="2025-06-09T21:59:50.689702600+08:00" level=info msg="[TCP] 127.0.0.1:55661 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-09T21:59:50.852279600+08:00" level=info msg="[TCP] 127.0.0.1:55665 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-09T21:59:50.859498300+08:00" level=info msg="[TCP] 127.0.0.1:55668 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-09T21:59:50.860553000+08:00" level=info msg="[TCP] 127.0.0.1:55669 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-09T21:59:50.975521800+08:00" level=info msg="[TCP] 127.0.0.1:55664 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-09T21:59:51.096611100+08:00" level=info msg="[TCP] 127.0.0.1:55672 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-09T21:59:51.573624300+08:00" level=info msg="[TCP] 127.0.0.1:55675 --> www.google.com:443 using GLOBAL"
time="2025-06-09T21:59:51.574920500+08:00" level=info msg="[TCP] 127.0.0.1:55677 --> www.google.com:443 using GLOBAL"
time="2025-06-09T21:59:51.595140300+08:00" level=info msg="[TCP] 127.0.0.1:55681 --> www.google.com:443 using GLOBAL"
time="2025-06-09T21:59:51.618309600+08:00" level=info msg="[TCP] 127.0.0.1:55679 --> www.google.com:443 using GLOBAL"
time="2025-06-09T21:59:51.628095600+08:00" level=info msg="[TCP] 127.0.0.1:55684 --> www.google.com:443 using GLOBAL"
time="2025-06-09T21:59:51.636549900+08:00" level=info msg="[TCP] 127.0.0.1:55686 --> www.google.com:443 using GLOBAL"
time="2025-06-09T21:59:52.603596200+08:00" level=info msg="[TCP] 127.0.0.1:55688 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-06-09T21:59:53.162710500+08:00" level=info msg="[TCP] 127.0.0.1:55690 --> app-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:59:55.242939500+08:00" level=info msg="[TCP] 127.0.0.1:55694 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-06-09T21:59:59.196736900+08:00" level=info msg="[TCP] 127.0.0.1:55698 --> update.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:00:26.827415200+08:00" level=info msg="[TCP] 127.0.0.1:55724 --> default.exp-tas.com:443 using GLOBAL"
time="2025-06-09T22:00:31.162321700+08:00" level=info msg="[TCP] 127.0.0.1:55730 --> update.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:00:31.176151200+08:00" level=info msg="[TCP] 127.0.0.1:55733 --> dl.google.com:443 using GLOBAL"
time="2025-06-09T22:00:32.871489300+08:00" level=info msg="[TCP] 127.0.0.1:55736 --> c.pki.goog:80 using GLOBAL"
time="2025-06-09T22:00:33.679076300+08:00" level=info msg="[TCP] 127.0.0.1:55736 --> o.pki.goog:80 using GLOBAL"
time="2025-06-09T22:00:50.879177900+08:00" level=info msg="[TCP] 127.0.0.1:55760 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-09T22:00:50.879177900+08:00" level=info msg="[TCP] 127.0.0.1:55758 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T22:00:51.032668400+08:00" level=info msg="[TCP] 127.0.0.1:55757 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-09T22:00:59.601857600+08:00" level=info msg="[TCP] 127.0.0.1:55770 --> update.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:00:59.639884100+08:00" level=info msg="[TCP] 127.0.0.1:55772 --> dl.google.com:443 using GLOBAL"
time="2025-06-09T22:01:02.425552600+08:00" level=info msg="[TCP] 127.0.0.1:55776 --> edgedl.me.gvt1.com:80 using GLOBAL"
time="2025-06-09T22:01:38.795325900+08:00" level=info msg="[TCP] 127.0.0.1:55807 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:01:40.367816500+08:00" level=info msg="[TCP] 127.0.0.1:55810 --> clients2.google.com:80 using GLOBAL"
time="2025-06-09T22:01:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:55811 --> safebrowsingohttpgateway.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:01:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:55819 --> redirector.gvt1.com:443 using GLOBAL"
time="2025-06-09T22:01:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:55812 --> clientservices.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:01:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:55817 --> accounts.google.com:443 using GLOBAL"
time="2025-06-09T22:01:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:55818 --> redirector.gvt1.com:443 using GLOBAL"
time="2025-06-09T22:01:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:55828 --> accounts.google.com:443 using GLOBAL"
time="2025-06-09T22:01:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:55824 --> clientservices.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:01:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:55827 --> clients2.google.com:443 using GLOBAL"
time="2025-06-09T22:01:41.*********+08:00" level=info msg="[TCP] 127.0.0.1:55825 --> safebrowsingohttpgateway.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:01:41.*********+08:00" level=info msg="[TCP] 127.0.0.1:55833 --> r2---sn-a5mekn6k.gvt1.com:443 using GLOBAL"
time="2025-06-09T22:01:41.*********+08:00" level=info msg="[TCP] 127.0.0.1:55816 --> clients2.google.com:443 using GLOBAL"
time="2025-06-09T22:01:41.*********+08:00" level=info msg="[TCP] 127.0.0.1:55837 --> redirector.gvt1.com:80 using GLOBAL"
time="2025-06-09T22:01:41.*********+08:00" level=info msg="[TCP] 127.0.0.1:55840 --> r3---sn-a5mlrnll.gvt1.com:443 using GLOBAL"
time="2025-06-09T22:01:41.*********+08:00" level=info msg="[TCP] 127.0.0.1:55839 --> accounts.google.com:443 using GLOBAL"
time="2025-06-09T22:01:42.*********+08:00" level=info msg="[TCP] 127.0.0.1:55832 --> r3---sn-a5mlrnll.gvt1.com:443 using GLOBAL"
time="2025-06-09T22:01:42.*********+08:00" level=info msg="[TCP] 127.0.0.1:55843 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T22:01:42.*********+08:00" level=info msg="[TCP] 127.0.0.1:55846 --> android.clients.google.com:443 using GLOBAL"
time="2025-06-09T22:01:42.*********+08:00" level=info msg="[TCP] 127.0.0.1:55845 --> r3---sn-a5mekn6s.gvt1.com:80 using GLOBAL"
time="2025-06-09T22:01:42.*********+08:00" level=info msg="[TCP] 127.0.0.1:55849 --> android.clients.google.com:443 using GLOBAL"
time="2025-06-09T22:01:43.*********+08:00" level=info msg="[TCP] 127.0.0.1:55851 --> dl.google.com:443 using GLOBAL"
time="2025-06-09T22:01:43.*********+08:00" level=info msg="[TCP] 127.0.0.1:55854 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T22:01:43.*********+08:00" level=info msg="[TCP] 127.0.0.1:55856 --> mtalk.google.com:5228 using GLOBAL"
time="2025-06-09T22:01:44.*********+08:00" level=info msg="[TCP] 127.0.0.1:55858 --> dl.google.com:443 using GLOBAL"
time="2025-06-09T22:01:44.*********+08:00" level=info msg="[TCP] 127.0.0.1:55861 --> accountcapabilities-pa.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:01:44.*********+08:00" level=info msg="[TCP] 127.0.0.1:55863 --> accounts.google.com:443 using GLOBAL"
time="2025-06-09T22:01:46.*********+08:00" level=info msg="[TCP] 127.0.0.1:55865 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-06-09T22:01:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:55870 --> activity.windows.com:443 using GLOBAL"
time="2025-06-09T22:01:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:55873 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:01:50.*********+08:00" level=info msg="[TCP] 127.0.0.1:55875 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:01:51.*********+08:00" level=info msg="[TCP] 127.0.0.1:55881 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:01:51.*********+08:00" level=info msg="[TCP] 127.0.0.1:55882 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:01:51.*********+08:00" level=info msg="[TCP] 127.0.0.1:55879 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:01:51.*********+08:00" level=info msg="[TCP] 127.0.0.1:55877 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:01:51.*********+08:00" level=info msg="[TCP] 127.0.0.1:55885 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:01:52.*********+08:00" level=info msg="[TCP] 127.0.0.1:55887 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:01:53.*********+08:00" level=info msg="[TCP] 127.0.0.1:55891 --> content-autofill.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:01:56.*********+08:00" level=info msg="[TCP] 127.0.0.1:55897 --> accounts.youtube.com:443 using GLOBAL"
time="2025-06-09T22:01:57.*********+08:00" level=info msg="[TCP] 127.0.0.1:55899 --> play.google.com:443 using GLOBAL"
time="2025-06-09T22:02:03.*********+08:00" level=info msg="[TCP] 127.0.0.1:55905 --> update.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:02:06.*********+08:00" level=info msg="[TCP] 127.0.0.1:55909 --> play.google.com:443 using GLOBAL"
time="2025-06-09T22:02:10.*********+08:00" level=info msg="[TCP] 127.0.0.1:55914 --> android.clients.google.com:443 using GLOBAL"
time="2025-06-09T22:02:17.*********+08:00" level=info msg="[TCP] 127.0.0.1:55921 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:02:38.*********+08:00" level=info msg="[TCP] 127.0.0.1:55940 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:02:39.*********+08:00" level=info msg="[TCP] 127.0.0.1:55943 --> mtalk.google.com:443 using GLOBAL"
time="2025-06-09T22:02:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:55945 --> update.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:02:43.*********+08:00" level=info msg="[TCP] 127.0.0.1:55951 --> clientservices.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:02:43.*********+08:00" level=info msg="[TCP] 127.0.0.1:55953 --> safebrowsing.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:02:45.*********+08:00" level=info msg="[TCP] 127.0.0.1:55958 --> edgedl.me.gvt1.com:80 using GLOBAL"
time="2025-06-09T22:02:55.*********+08:00" level=info msg="[TCP] 127.0.0.1:55969 --> accounts.google.com:443 using GLOBAL"
time="2025-06-09T22:03:17.*********+08:00" level=info msg="[TCP] 127.0.0.1:55985 --> chromewebstore.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:03:17.*********+08:00" level=info msg="[TCP] 127.0.0.1:55987 --> chromewebstore.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:03:20.*********+08:00" level=info msg="[TCP] 127.0.0.1:55992 --> android.clients.google.com:443 using GLOBAL"
time="2025-06-09T22:03:23.*********+08:00" level=info msg="[TCP] 127.0.0.1:55996 --> accountcapabilities-pa.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:03:23.*********+08:00" level=info msg="[TCP] 127.0.0.1:55997 --> www.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:03:23.*********+08:00" level=info msg="[TCP] 127.0.0.1:56000 --> accounts.youtube.com:443 using GLOBAL"
time="2025-06-09T22:03:23.*********+08:00" level=info msg="[TCP] 127.0.0.1:56002 --> www.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:03:25.*********+08:00" level=info msg="[TCP] 127.0.0.1:56006 --> accounts.google.com.sg:443 using GLOBAL"
time="2025-06-09T22:03:25.*********+08:00" level=info msg="[TCP] 127.0.0.1:56010 --> securitydomain-pa.googleapis.com:443 using GLOBAL"
time="2025-06-09T22:03:25.*********+08:00" level=info msg="[TCP] 127.0.0.1:56008 --> clients4.google.com:443 using GLOBAL"
time="2025-06-09T22:03:26.*********+08:00" level=info msg="[TCP] 127.0.0.1:56012 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-09T22:03:27.*********+08:00" level=info msg="[TCP] 127.0.0.1:56015 --> myaccount.google.com:443 using GLOBAL"
time="2025-06-09T22:03:27.*********+08:00" level=info msg="[TCP] 127.0.0.1:56017 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-09T22:03:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:56024 --> www.google.com:443 using GLOBAL"
time="2025-06-09T22:03:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:56026 --> www.google.com:443 using GLOBAL"
time="2025-06-09T22:03:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:56030 --> www.google.com:443 using GLOBAL"
time="2025-06-09T22:03:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:56033 --> www.google.com:443 using GLOBAL"
time="2025-06-09T22:03:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:56028 --> www.google.com:443 using GLOBAL"
time="2025-06-09T22:03:35.*********+08:00" level=info msg="[TCP] 127.0.0.1:56038 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:03:36.*********+08:00" level=info msg="[TCP] 127.0.0.1:56037 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:03:36.040072500+08:00" level=info msg="[TCP] 127.0.0.1:56035 --> lh3.google.com:443 using GLOBAL"
time="2025-06-09T22:03:36.040589200+08:00" level=info msg="[TCP] 127.0.0.1:56036 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:03:36.299960100+08:00" level=info msg="[TCP] 127.0.0.1:56044 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T22:03:36.301182700+08:00" level=info msg="[TCP] 127.0.0.1:56046 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-09T22:03:37.061394400+08:00" level=info msg="[TCP] 127.0.0.1:56048 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-09T22:03:37.192892400+08:00" level=info msg="[TCP] 127.0.0.1:56051 --> ogads-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-09T22:03:37.196666600+08:00" level=info msg="[TCP] 127.0.0.1:56053 --> apis.google.com:443 using GLOBAL"
time="2025-06-09T22:03:38.064973700+08:00" level=info msg="[TCP] 127.0.0.1:56055 --> ogads-pa.clients6.google.com:443 using GLOBAL"
time="2025-06-09T22:03:38.417120200+08:00" level=info msg="[TCP] 127.0.0.1:56057 --> play.google.com:443 using GLOBAL"
time="2025-06-09T22:03:44.925864100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56072 --> lh3.googleusercontent.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T22:47:01.645706300+08:00" level=info msg="[TCP] 127.0.0.1:56274 --> www.cursor.com:443 using GLOBAL"
time="2025-06-09T22:47:03.182686600+08:00" level=info msg="[TCP] 127.0.0.1:56279 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:47:03.268604600+08:00" level=info msg="[TCP] 127.0.0.1:56281 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:47:04.181055800+08:00" level=info msg="[TCP] 127.0.0.1:56277 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:47:12.232560000+08:00" level=info msg="[TCP] 127.0.0.1:56292 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:47:13.353248700+08:00" level=info msg="[TCP] 127.0.0.1:56290 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-09T22:47:16.762637100+08:00" level=info msg="[TCP] 127.0.0.1:56301 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:47:16.892987800+08:00" level=info msg="[TCP] 127.0.0.1:56297 --> downloads.cursor.com:443 using GLOBAL"
time="2025-06-09T22:47:17.188301800+08:00" level=info msg="[TCP] 127.0.0.1:56298 --> downloads.cursor.com:443 using GLOBAL"
time="2025-06-09T22:47:17.690812200+08:00" level=info msg="[TCP] 127.0.0.1:56304 --> dl-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:47:20.376680900+08:00" level=info msg="[TCP] 127.0.0.1:56308 --> www.bingapis.com:443 using GLOBAL"
time="2025-06-09T22:47:24.163748400+08:00" level=info msg="[TCP] 127.0.0.1:56312 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-09T22:47:43.088926400+08:00" level=info msg="[TCP] 127.0.0.1:56326 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:47:45.396946800+08:00" level=info msg="[TCP] 127.0.0.1:56331 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T22:47:48.428955400+08:00" level=info msg="[TCP] 127.0.0.1:56335 --> edge-http.microsoft.com:80 using GLOBAL"
time="2025-06-09T22:48:12.187787600+08:00" level=info msg="[TCP] 127.0.0.1:56354 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-09T22:48:12.191648200+08:00" level=info msg="[TCP] 127.0.0.1:56358 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:48:13.311887800+08:00" level=info msg="[TCP] 127.0.0.1:56356 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:48:17.143546400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56360 --> api.vc.bilibili.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp 120.198.71.225:19812: i/o timeout\ndial tcp 120.241.29.152:19812: i/o timeout"
time="2025-06-09T22:48:17.196908300+08:00" level=info msg="[TCP] 127.0.0.1:56365 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:48:51.765304800+08:00" level=info msg="[TCP] 127.0.0.1:56400 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-09T22:48:51.767587800+08:00" level=info msg="[TCP] 127.0.0.1:56401 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-09T22:49:05.943854500+08:00" level=info msg="[TCP] 127.0.0.1:56416 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:49:10.526833700+08:00" level=info msg="[TCP] 127.0.0.1:56423 --> downloads.cursor.com:443 using GLOBAL"
time="2025-06-09T22:49:16.629430500+08:00" level=info msg="[TCP] 127.0.0.1:56542 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-09T22:49:17.336600300+08:00" level=info msg="[TCP] 127.0.0.1:56545 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-09T22:49:19.674665500+08:00" level=info msg="[TCP] 127.0.0.1:56553 --> downloads.cursor.com:443 using GLOBAL"
time="2025-06-09T22:49:21.735452400+08:00" level=info msg="[TCP] 127.0.0.1:56556 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:49:22.133165500+08:00" level=info msg="[TCP] 127.0.0.1:56559 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:49:23.848072200+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-09T22:49:23.848072200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-09T22:49:23.860824700+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-09T22:49:23.860824700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-09T22:49:23.860824700+08:00" level=error msg="🇭🇰香港01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-09T22:49:23.860824700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-09T22:49:26.345179200+08:00" level=info msg="[TCP] 127.0.0.1:56564 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:49:37.248500300+08:00" level=info msg="[TCP] 127.0.0.1:56574 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:49:38.202450400+08:00" level=info msg="[TCP] 127.0.0.1:56576 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:49:39.179108800+08:00" level=info msg="[TCP] 127.0.0.1:56579 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:49:45.267309300+08:00" level=info msg="[TCP] 127.0.0.1:56586 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T22:49:46.363132300+08:00" level=info msg="[TCP] 127.0.0.1:56589 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T22:49:50.173433900+08:00" level=info msg="[TCP] 127.0.0.1:56593 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-09T22:49:52.193262200+08:00" level=info msg="[TCP] 127.0.0.1:56596 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:50:07.268921800+08:00" level=info msg="[TCP] 127.0.0.1:56608 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:50:12.189296100+08:00" level=info msg="[TCP] 127.0.0.1:56613 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:50:12.329991200+08:00" level=info msg="[TCP] 127.0.0.1:56615 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:50:12.329991200+08:00" level=info msg="[TCP] 127.0.0.1:56617 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:50:16.428651700+08:00" level=info msg="[TCP] 127.0.0.1:56622 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-09T22:50:18.928763800+08:00" level=info msg="[TCP] 127.0.0.1:56625 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-06-09T22:50:20.425857800+08:00" level=info msg="[TCP] 127.0.0.1:56629 --> app-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:50:46.501856000+08:00" level=info msg="[TCP] 127.0.0.1:56650 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T22:50:48.213570400+08:00" level=info msg="[TCP] 127.0.0.1:56655 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T22:51:12.173456200+08:00" level=info msg="[TCP] 127.0.0.1:56674 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-09T22:51:12.223833400+08:00" level=info msg="[TCP] 127.0.0.1:56676 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:51:23.198593900+08:00" level=info msg="[TCP] 127.0.0.1:56686 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:51:59.440860300+08:00" level=info msg="[TCP] 127.0.0.1:56713 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T22:51:59.769520500+08:00" level=info msg="[TCP] 127.0.0.1:56715 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T22:52:12.184772100+08:00" level=info msg="[TCP] 127.0.0.1:56726 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-09T22:52:12.191438000+08:00" level=info msg="[TCP] 127.0.0.1:56728 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:52:12.257611100+08:00" level=info msg="[TCP] 127.0.0.1:56730 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:52:23.304331300+08:00" level=info msg="[TCP] 127.0.0.1:56740 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:52:30.369805700+08:00" level=info msg="[TCP] 127.0.0.1:56747 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-06-09T22:52:52.935802700+08:00" level=info msg="[TCP] 127.0.0.1:56764 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:53:12.185193100+08:00" level=info msg="[TCP] 127.0.0.1:56781 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:53:12.215502100+08:00" level=info msg="[TCP] 127.0.0.1:56779 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-09T22:53:12.317086500+08:00" level=info msg="[TCP] 127.0.0.1:56783 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:53:23.028395600+08:00" level=info msg="[TCP] 127.0.0.1:56793 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:53:23.296901100+08:00" level=info msg="[TCP] 127.0.0.1:56795 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:53:27.730587500+08:00" level=info msg="[TCP] 127.0.0.1:56800 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:53:31.745601300+08:00" level=info msg="[TCP] 127.0.0.1:56804 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-06-09T22:53:33.907497600+08:00" level=info msg="[TCP] 127.0.0.1:56808 --> redirector.gvt1.com:443 using GLOBAL"
time="2025-06-09T22:53:35.257670800+08:00" level=info msg="[TCP] 127.0.0.1:56811 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:53:35.354173600+08:00" level=info msg="[TCP] 127.0.0.1:56813 --> r2---sn-a5mekn6k.gvt1.com:443 using GLOBAL"
time="2025-06-09T22:53:37.766107000+08:00" level=info msg="[TCP] 127.0.0.1:56817 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:38.342074800+08:00" level=info msg="[TCP] 127.0.0.1:56819 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.602626300+08:00" level=info msg="[TCP] 127.0.0.1:56825 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.637609900+08:00" level=info msg="[TCP] 127.0.0.1:56827 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.672917500+08:00" level=info msg="[TCP] 127.0.0.1:56830 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.672917500+08:00" level=info msg="[TCP] 127.0.0.1:56844 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.674003700+08:00" level=info msg="[TCP] 127.0.0.1:56835 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.676001600+08:00" level=info msg="[TCP] 127.0.0.1:56836 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.678372500+08:00" level=info msg="[TCP] 127.0.0.1:56829 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.688063300+08:00" level=info msg="[TCP] 127.0.0.1:56837 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.688063300+08:00" level=info msg="[TCP] 127.0.0.1:56831 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.689580900+08:00" level=info msg="[TCP] 127.0.0.1:56838 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.689580900+08:00" level=info msg="[TCP] 127.0.0.1:56833 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.689580900+08:00" level=info msg="[TCP] 127.0.0.1:56842 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.700096500+08:00" level=info msg="[TCP] 127.0.0.1:56839 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.701096200+08:00" level=info msg="[TCP] 127.0.0.1:56834 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.734106700+08:00" level=info msg="[TCP] 127.0.0.1:56865 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.736565500+08:00" level=info msg="[TCP] 127.0.0.1:56866 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.777018400+08:00" level=info msg="[TCP] 127.0.0.1:56843 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.789341700+08:00" level=info msg="[TCP] 127.0.0.1:56867 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:41.848702000+08:00" level=info msg="[TCP] 127.0.0.1:56864 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:42.159135600+08:00" level=info msg="[TCP] 127.0.0.1:56876 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:42.194074800+08:00" level=info msg="[TCP] 127.0.0.1:56877 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:42.754301200+08:00" level=info msg="[TCP] 127.0.0.1:56882 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:42.939504000+08:00" level=info msg="[TCP] 127.0.0.1:56884 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:43.153125700+08:00" level=info msg="[TCP] 127.0.0.1:56874 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:43.159635000+08:00" level=info msg="[TCP] 127.0.0.1:56887 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:43.355851100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56821 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:53:43.411904900+08:00" level=info msg="[TCP] 127.0.0.1:56895 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:44.326245100+08:00" level=info msg="[TCP] 127.0.0.1:56889 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:44.735666900+08:00" level=info msg="[TCP] 127.0.0.1:56897 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:45.158587600+08:00" level=info msg="[TCP] 127.0.0.1:56900 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:45.159647100+08:00" level=info msg="[TCP] 127.0.0.1:56901 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:45.160155000+08:00" level=info msg="[TCP] 127.0.0.1:56902 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:46.617903600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56832 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:53:46.618915100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56840 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:53:46.618915100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56841 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:53:46.669071300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56861 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:53:46.677880900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56863 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:53:46.726886800+08:00" level=info msg="[TCP] 127.0.0.1:56908 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:47.332171800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56880 --> mobile.events.data.microsoft.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:53:47.739391000+08:00" level=info msg="[TCP] 127.0.0.1:56910 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:53:48.107227700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56890 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:53:52.792802800+08:00" level=info msg="[TCP] 127.0.0.1:56918 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-09T22:53:52.795745700+08:00" level=info msg="[TCP] 127.0.0.1:56917 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-09T22:53:53.171744500+08:00" level=info msg="[TCP] 127.0.0.1:56922 --> westus-0.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-06-09T22:53:56.960096100+08:00" level=info msg="[TCP] 127.0.0.1:56926 --> array615.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:53:58.714841200+08:00" level=info msg="[TCP] 127.0.0.1:56930 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:54:13.204708200+08:00" level=info msg="[TCP] 127.0.0.1:56940 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:54:50.420388400+08:00" level=info msg="[TCP] 127.0.0.1:56970 --> cursor.com:443 using GLOBAL"
time="2025-06-09T22:54:50.422430000+08:00" level=info msg="[TCP] 127.0.0.1:56973 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:54:50.424794900+08:00" level=info msg="[TCP] 127.0.0.1:56971 --> cursor.com:443 using GLOBAL"
time="2025-06-09T22:54:51.221823600+08:00" level=info msg="[TCP] 127.0.0.1:56977 --> www.cursor.com:443 using GLOBAL"
time="2025-06-09T22:54:51.399217600+08:00" level=info msg="[TCP] 127.0.0.1:56979 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:55:12.193294400+08:00" level=info msg="[TCP] 127.0.0.1:56996 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:55:14.788316900+08:00" level=info msg="[TCP] 127.0.0.1:56999 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:55:21.359292500+08:00" level=info msg="[TCP] 127.0.0.1:57007 --> www.cursor.com:443 using GLOBAL"
time="2025-06-09T22:55:22.276940700+08:00" level=info msg="[TCP] 127.0.0.1:57009 --> www.cursor.com:443 using GLOBAL"
time="2025-06-09T22:55:24.403477900+08:00" level=info msg="[TCP] 127.0.0.1:57016 --> api.workos.com:443 using GLOBAL"
time="2025-06-09T22:55:24.775760800+08:00" level=info msg="[TCP] 127.0.0.1:57013 --> xpaywalletcdn-prod.azureedge.net:443 using GLOBAL"
time="2025-06-09T22:55:25.090553700+08:00" level=info msg="[TCP] 127.0.0.1:57018 --> www.cursor.com:443 using GLOBAL"
time="2025-06-09T22:55:25.122909600+08:00" level=info msg="[TCP] 127.0.0.1:57020 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:55:25.279111400+08:00" level=info msg="[TCP] 127.0.0.1:57022 --> authenticate.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:55:26.949291800+08:00" level=info msg="[TCP] 127.0.0.1:57026 --> authenticator.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:55:43.587373400+08:00" level=info msg="[TCP] 127.0.0.1:57038 --> workos.imgix.net:443 using GLOBAL"
time="2025-06-09T22:55:45.622825200+08:00" level=info msg="[TCP] 127.0.0.1:57042 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T22:55:58.802478400+08:00" level=info msg="[TCP] 127.0.0.1:57055 --> authenticator.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:55:59.108603800+08:00" level=info msg="[TCP] 127.0.0.1:57060 --> authenticator.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:55:59.110612700+08:00" level=info msg="[TCP] 127.0.0.1:57062 --> authenticator.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:55:59.114333800+08:00" level=info msg="[TCP] 127.0.0.1:57063 --> authenticator.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:55:59.123150700+08:00" level=info msg="[TCP] 127.0.0.1:57061 --> authenticator.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:55:59.633645900+08:00" level=info msg="[TCP] 127.0.0.1:57059 --> authenticator.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:56:00.238995300+08:00" level=info msg="[TCP] 127.0.0.1:57070 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T22:56:01.234902500+08:00" level=info msg="[TCP] 127.0.0.1:57073 --> o207216.ingest.sentry.io:443 using GLOBAL"
time="2025-06-09T22:56:01.412106000+08:00" level=info msg="[TCP] 127.0.0.1:57075 --> api.workos.com:443 using GLOBAL"
time="2025-06-09T22:56:01.*********+08:00" level=info msg="[TCP] 127.0.0.1:57057 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:56:12.*********+08:00" level=info msg="[TCP] 127.0.0.1:57086 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:56:16.*********+08:00" level=info msg="[TCP] 127.0.0.1:57091 --> authenticate.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:56:18.*********+08:00" level=info msg="[TCP] 127.0.0.1:57094 --> accounts.google.com:443 using GLOBAL"
time="2025-06-09T22:56:21.*********+08:00" level=info msg="[TCP] 127.0.0.1:57098 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:56:33.*********+08:00" level=info msg="[TCP] 127.0.0.1:57111 --> accounts.youtube.com:443 using GLOBAL"
time="2025-06-09T22:56:34.*********+08:00" level=info msg="[TCP] 127.0.0.1:57113 --> ssl.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:56:35.*********+08:00" level=info msg="[TCP] 127.0.0.1:57116 --> ssl.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:56:38.*********+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57109 --> lh3.googleusercontent.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:56:38.*********+08:00" level=info msg="[TCP] 127.0.0.1:57120 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-06-09T22:56:44.*********+08:00" level=info msg="[TCP] 127.0.0.1:57126 --> play.google.com:443 using GLOBAL"
time="2025-06-09T22:56:45.*********+08:00" level=info msg="[TCP] 127.0.0.1:57129 --> play.google.com:443 using GLOBAL"
time="2025-06-09T22:56:45.*********+08:00" level=info msg="[TCP] 127.0.0.1:57131 --> play.google.com:443 using GLOBAL"
time="2025-06-09T22:56:45.777335300+08:00" level=info msg="[TCP] 127.0.0.1:57135 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-09T22:56:46.004183700+08:00" level=info msg="[TCP] 127.0.0.1:57133 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T22:57:02.340502100+08:00" level=info msg="[TCP] 127.0.0.1:57148 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:57:06.808176500+08:00" level=info msg="[TCP] 127.0.0.1:57153 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T22:57:12.289474000+08:00" level=info msg="[TCP] 127.0.0.1:57160 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:57:12.726766200+08:00" level=info msg="[TCP] 127.0.0.1:57158 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:57:13.743694400+08:00" level=info msg="[TCP] 127.0.0.1:57167 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.753071100+08:00" level=info msg="[TCP] 127.0.0.1:57165 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.791989700+08:00" level=info msg="[TCP] 127.0.0.1:57187 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.797564000+08:00" level=info msg="[TCP] 127.0.0.1:57166 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.800070300+08:00" level=info msg="[TCP] 127.0.0.1:57185 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.801071000+08:00" level=info msg="[TCP] 127.0.0.1:57184 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.801071000+08:00" level=info msg="[TCP] 127.0.0.1:57182 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.801071000+08:00" level=info msg="[TCP] 127.0.0.1:57189 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.830097000+08:00" level=info msg="[TCP] 127.0.0.1:57202 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.843033400+08:00" level=info msg="[TCP] 127.0.0.1:57186 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.843033400+08:00" level=info msg="[TCP] 127.0.0.1:57170 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:13.843033400+08:00" level=info msg="[TCP] 127.0.0.1:57169 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.745675900+08:00" level=info msg="[TCP] 127.0.0.1:57171 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.801884300+08:00" level=info msg="[TCP] 127.0.0.1:57164 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.801884300+08:00" level=info msg="[TCP] 127.0.0.1:57172 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.813729900+08:00" level=info msg="[TCP] 127.0.0.1:57183 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.836179000+08:00" level=info msg="[TCP] 127.0.0.1:57190 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.841351300+08:00" level=info msg="[TCP] 127.0.0.1:57200 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.882743100+08:00" level=info msg="[TCP] 127.0.0.1:57208 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.882743100+08:00" level=info msg="[TCP] 127.0.0.1:57210 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.884371900+08:00" level=info msg="[TCP] 127.0.0.1:57211 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.884371900+08:00" level=info msg="[TCP] 127.0.0.1:57206 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.886507900+08:00" level=info msg="[TCP] 127.0.0.1:57209 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.935865300+08:00" level=info msg="[TCP] 127.0.0.1:57207 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:14.952021700+08:00" level=info msg="[TCP] 127.0.0.1:57219 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.005930200+08:00" level=info msg="[TCP] 127.0.0.1:57222 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.007926000+08:00" level=info msg="[TCP] 127.0.0.1:57221 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.029235800+08:00" level=info msg="[TCP] 127.0.0.1:57205 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.043617500+08:00" level=info msg="[TCP] 127.0.0.1:57224 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.058697700+08:00" level=info msg="[TCP] 127.0.0.1:57241 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.059205500+08:00" level=info msg="[TCP] 127.0.0.1:57243 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.063148600+08:00" level=info msg="[TCP] 127.0.0.1:57248 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.064852000+08:00" level=info msg="[TCP] 127.0.0.1:57242 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.076882600+08:00" level=info msg="[TCP] 127.0.0.1:57246 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.089768900+08:00" level=info msg="[TCP] 127.0.0.1:57223 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.092953900+08:00" level=info msg="[TCP] 127.0.0.1:57249 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.097514400+08:00" level=info msg="[TCP] 127.0.0.1:57239 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.123458400+08:00" level=info msg="[TCP] 127.0.0.1:57272 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.125126300+08:00" level=info msg="[TCP] 127.0.0.1:57269 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.126313400+08:00" level=info msg="[TCP] 127.0.0.1:57247 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.126313400+08:00" level=info msg="[TCP] 127.0.0.1:57238 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.133215200+08:00" level=info msg="[TCP] 127.0.0.1:57267 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.133733700+08:00" level=info msg="[TCP] 127.0.0.1:57266 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.135322700+08:00" level=info msg="[TCP] 127.0.0.1:57265 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.144163000+08:00" level=info msg="[TCP] 127.0.0.1:57240 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.149076200+08:00" level=info msg="[TCP] 127.0.0.1:57228 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.189030700+08:00" level=info msg="[TCP] 127.0.0.1:57285 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.230105800+08:00" level=info msg="[TCP] 127.0.0.1:57287 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.254781300+08:00" level=info msg="[TCP] 127.0.0.1:57225 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.256780200+08:00" level=info msg="[TCP] 127.0.0.1:57227 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.274692400+08:00" level=info msg="[TCP] 127.0.0.1:57245 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.279510400+08:00" level=info msg="[TCP] 127.0.0.1:57274 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.635112400+08:00" level=info msg="[TCP] 127.0.0.1:57295 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.636559800+08:00" level=info msg="[TCP] 127.0.0.1:57298 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.639980400+08:00" level=info msg="[TCP] 127.0.0.1:57293 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.683235800+08:00" level=info msg="[TCP] 127.0.0.1:57294 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.685388100+08:00" level=info msg="[TCP] 127.0.0.1:57318 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.685388100+08:00" level=info msg="[TCP] 127.0.0.1:57306 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.698392700+08:00" level=info msg="[TCP] 127.0.0.1:57308 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.699287200+08:00" level=info msg="[TCP] 127.0.0.1:57307 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.699287200+08:00" level=info msg="[TCP] 127.0.0.1:57317 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.713088500+08:00" level=info msg="[TCP] 127.0.0.1:57310 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.717677800+08:00" level=info msg="[TCP] 127.0.0.1:57314 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.726662000+08:00" level=info msg="[TCP] 127.0.0.1:57336 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.726662000+08:00" level=info msg="[TCP] 127.0.0.1:57337 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.727989800+08:00" level=info msg="[TCP] 127.0.0.1:57340 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.754370300+08:00" level=info msg="[TCP] 127.0.0.1:57297 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.838162800+08:00" level=info msg="[TCP] 127.0.0.1:57312 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.846783700+08:00" level=info msg="[TCP] 127.0.0.1:57338 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.848804300+08:00" level=info msg="[TCP] 127.0.0.1:57350 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.901069500+08:00" level=info msg="[TCP] 127.0.0.1:57313 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.901069500+08:00" level=info msg="[TCP] 127.0.0.1:57311 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.912802200+08:00" level=info msg="[TCP] 127.0.0.1:57341 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:15.917896500+08:00" level=info msg="[TCP] 127.0.0.1:57351 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.024117600+08:00" level=info msg="[TCP] 127.0.0.1:57226 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.140298100+08:00" level=info msg="[TCP] 127.0.0.1:57244 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.149838500+08:00" level=info msg="[TCP] 127.0.0.1:57250 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.181036300+08:00" level=info msg="[TCP] 127.0.0.1:57270 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.201109600+08:00" level=info msg="[TCP] 127.0.0.1:57360 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.259956100+08:00" level=info msg="[TCP] 127.0.0.1:57366 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.337674500+08:00" level=info msg="[TCP] 127.0.0.1:57362 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.643487500+08:00" level=info msg="[TCP] 127.0.0.1:57373 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.729717300+08:00" level=info msg="[TCP] 127.0.0.1:57377 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.740575400+08:00" level=info msg="[TCP] 127.0.0.1:57375 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.740575400+08:00" level=info msg="[TCP] 127.0.0.1:57334 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.742472500+08:00" level=info msg="[TCP] 127.0.0.1:57316 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.743509800+08:00" level=info msg="[TCP] 127.0.0.1:57315 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.758950000+08:00" level=info msg="[TCP] 127.0.0.1:57379 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.761261600+08:00" level=info msg="[TCP] 127.0.0.1:57381 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.761261600+08:00" level=info msg="[TCP] 127.0.0.1:57390 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.763131300+08:00" level=info msg="[TCP] 127.0.0.1:57392 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.764852100+08:00" level=info msg="[TCP] 127.0.0.1:57386 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.766001800+08:00" level=info msg="[TCP] 127.0.0.1:57168 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.781436200+08:00" level=info msg="[TCP] 127.0.0.1:57352 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.801758800+08:00" level=info msg="[TCP] 127.0.0.1:57408 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.807153900+08:00" level=info msg="[TCP] 127.0.0.1:57385 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.807153900+08:00" level=info msg="[TCP] 127.0.0.1:57382 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.873708000+08:00" level=info msg="[TCP] 127.0.0.1:57339 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.873708000+08:00" level=info msg="[TCP] 127.0.0.1:57383 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.874993700+08:00" level=info msg="[TCP] 127.0.0.1:57335 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.876022100+08:00" level=info msg="[TCP] 127.0.0.1:57388 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.912435900+08:00" level=info msg="[TCP] 127.0.0.1:57380 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.914460700+08:00" level=info msg="[TCP] 127.0.0.1:57389 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:16.915524900+08:00" level=info msg="[TCP] 127.0.0.1:57387 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.049826600+08:00" level=info msg="[TCP] 127.0.0.1:57357 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.209845700+08:00" level=info msg="[TCP] 127.0.0.1:57411 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.308852800+08:00" level=info msg="[TCP] 127.0.0.1:57364 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.564034400+08:00" level=info msg="[TCP] 127.0.0.1:57368 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.590330500+08:00" level=info msg="[TCP] 127.0.0.1:57370 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.600724500+08:00" level=info msg="[TCP] 127.0.0.1:57413 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.600724500+08:00" level=info msg="[TCP] 127.0.0.1:57414 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.605836800+08:00" level=info msg="[TCP] 127.0.0.1:57419 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.605836800+08:00" level=info msg="[TCP] 127.0.0.1:57415 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.655383100+08:00" level=info msg="[TCP] 127.0.0.1:57427 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.657832200+08:00" level=info msg="[TCP] 127.0.0.1:57444 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.657836200+08:00" level=info msg="[TCP] 127.0.0.1:57441 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.659342300+08:00" level=info msg="[TCP] 127.0.0.1:57445 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.663587200+08:00" level=info msg="[TCP] 127.0.0.1:57432 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.663587200+08:00" level=info msg="[TCP] 127.0.0.1:57431 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.667235800+08:00" level=info msg="[TCP] 127.0.0.1:57438 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.667235800+08:00" level=info msg="[TCP] 127.0.0.1:57446 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.685121700+08:00" level=info msg="[TCP] 127.0.0.1:57416 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.734521400+08:00" level=info msg="[TCP] 127.0.0.1:57470 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.735484800+08:00" level=info msg="[TCP] 127.0.0.1:57477 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.776329300+08:00" level=info msg="[TCP] 127.0.0.1:57435 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.779599000+08:00" level=info msg="[TCP] 127.0.0.1:57447 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.791638900+08:00" level=info msg="[TCP] 127.0.0.1:57471 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.813041600+08:00" level=info msg="[TCP] 127.0.0.1:57512 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.815050100+08:00" level=info msg="[TCP] 127.0.0.1:57475 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.815050100+08:00" level=info msg="[TCP] 127.0.0.1:57503 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.817247000+08:00" level=info msg="[TCP] 127.0.0.1:57476 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.819506300+08:00" level=info msg="[TCP] 127.0.0.1:57482 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.850559800+08:00" level=info msg="[TCP] 127.0.0.1:57497 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.857114500+08:00" level=info msg="[TCP] 127.0.0.1:57506 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.857114500+08:00" level=info msg="[TCP] 127.0.0.1:57514 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.866982100+08:00" level=info msg="[TCP] 127.0.0.1:57429 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.867865600+08:00" level=info msg="[TCP] 127.0.0.1:57434 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.869385300+08:00" level=info msg="[TCP] 127.0.0.1:57443 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.883642600+08:00" level=info msg="[TCP] 127.0.0.1:57543 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.910119000+08:00" level=info msg="[TCP] 127.0.0.1:57473 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.921260700+08:00" level=info msg="[TCP] 127.0.0.1:57516 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.922261600+08:00" level=info msg="[TCP] 127.0.0.1:57505 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.940382600+08:00" level=info msg="[TCP] 127.0.0.1:57551 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.958208500+08:00" level=info msg="[TCP] 127.0.0.1:57407 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.959779900+08:00" level=info msg="[TCP] 127.0.0.1:57518 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.962605200+08:00" level=info msg="[TCP] 127.0.0.1:57515 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:17.990166100+08:00" level=info msg="[TCP] 127.0.0.1:57563 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.000750000+08:00" level=info msg="[TCP] 127.0.0.1:57568 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.000750000+08:00" level=info msg="[TCP] 127.0.0.1:57561 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.013489800+08:00" level=info msg="[TCP] 127.0.0.1:57536 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.013489800+08:00" level=info msg="[TCP] 127.0.0.1:57541 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.024385400+08:00" level=info msg="[TCP] 127.0.0.1:57572 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.026432600+08:00" level=info msg="[TCP] 127.0.0.1:57552 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.030146500+08:00" level=info msg="[TCP] 127.0.0.1:57578 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.036677200+08:00" level=info msg="[TCP] 127.0.0.1:57590 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.046553200+08:00" level=info msg="[TCP] 127.0.0.1:57592 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.048296600+08:00" level=info msg="[TCP] 127.0.0.1:57588 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.054223600+08:00" level=info msg="[TCP] 127.0.0.1:57591 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.059946900+08:00" level=info msg="[TCP] 127.0.0.1:57602 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.066220600+08:00" level=info msg="[TCP] 127.0.0.1:57604 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.102324800+08:00" level=info msg="[TCP] 127.0.0.1:57479 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.102324800+08:00" level=info msg="[TCP] 127.0.0.1:57474 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.106960800+08:00" level=info msg="[TCP] 127.0.0.1:57391 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.108403700+08:00" level=info msg="[TCP] 127.0.0.1:57499 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.110065700+08:00" level=info msg="[TCP] 127.0.0.1:57384 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.118951700+08:00" level=info msg="[TCP] 127.0.0.1:57544 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.129037800+08:00" level=info msg="[TCP] 127.0.0.1:57579 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.129037800+08:00" level=info msg="[TCP] 127.0.0.1:57577 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.129545600+08:00" level=info msg="[TCP] 127.0.0.1:57589 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.146503400+08:00" level=info msg="[TCP] 127.0.0.1:57273 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.148053500+08:00" level=info msg="[TCP] 127.0.0.1:57271 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.277415500+08:00" level=info msg="[TCP] 127.0.0.1:57606 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.635383600+08:00" level=info msg="[TCP] 127.0.0.1:57417 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.661303700+08:00" level=info msg="[TCP] 127.0.0.1:57430 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.689360000+08:00" level=info msg="[TCP] 127.0.0.1:57439 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.699521100+08:00" level=info msg="[TCP] 127.0.0.1:57616 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.699521100+08:00" level=info msg="[TCP] 127.0.0.1:57319 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.728949200+08:00" level=info msg="[TCP] 127.0.0.1:57633 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.729539100+08:00" level=info msg="[TCP] 127.0.0.1:57629 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.737979400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57188 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:18.747991100+08:00" level=info msg="[TCP] 127.0.0.1:57614 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.751675400+08:00" level=info msg="[TCP] 127.0.0.1:57630 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.752178600+08:00" level=info msg="[TCP] 127.0.0.1:57628 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.754386400+08:00" level=info msg="[TCP] 127.0.0.1:57645 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.758866600+08:00" level=info msg="[TCP] 127.0.0.1:57610 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.769244100+08:00" level=info msg="[TCP] 127.0.0.1:57437 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.769755500+08:00" level=info msg="[TCP] 127.0.0.1:57309 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.771304100+08:00" level=info msg="[TCP] 127.0.0.1:57428 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.771304100+08:00" level=info msg="[TCP] 127.0.0.1:57292 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.771304100+08:00" level=info msg="[TCP] 127.0.0.1:57296 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.772694500+08:00" level=info msg="[TCP] 127.0.0.1:57442 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.772694500+08:00" level=info msg="[TCP] 127.0.0.1:57480 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.772694500+08:00" level=info msg="[TCP] 127.0.0.1:57631 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.773431400+08:00" level=info msg="[TCP] 127.0.0.1:57642 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.776747400+08:00" level=info msg="[TCP] 127.0.0.1:57472 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.801601600+08:00" level=info msg="[TCP] 127.0.0.1:57660 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.803146000+08:00" level=info msg="[TCP] 127.0.0.1:57666 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.813098700+08:00" level=info msg="[TCP] 127.0.0.1:57509 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.814369400+08:00" level=info msg="[TCP] 127.0.0.1:57511 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.858950300+08:00" level=info msg="[TCP] 127.0.0.1:57498 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.862523200+08:00" level=info msg="[TCP] 127.0.0.1:57662 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.896904700+08:00" level=info msg="[TCP] 127.0.0.1:57647 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.952314000+08:00" level=info msg="[TCP] 127.0.0.1:57648 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.953249100+08:00" level=info msg="[TCP] 127.0.0.1:57478 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.953883000+08:00" level=info msg="[TCP] 127.0.0.1:57469 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.955432400+08:00" level=info msg="[TCP] 127.0.0.1:57507 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.955999100+08:00" level=info msg="[TCP] 127.0.0.1:57504 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.957026100+08:00" level=info msg="[TCP] 127.0.0.1:57510 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.959175100+08:00" level=info msg="[TCP] 127.0.0.1:57537 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.959175100+08:00" level=info msg="[TCP] 127.0.0.1:57547 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.964364500+08:00" level=info msg="[TCP] 127.0.0.1:57553 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.973125200+08:00" level=info msg="[TCP] 127.0.0.1:57620 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.974892600+08:00" level=info msg="[TCP] 127.0.0.1:57612 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.978081300+08:00" level=info msg="[TCP] 127.0.0.1:57643 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.978620500+08:00" level=info msg="[TCP] 127.0.0.1:57646 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.979847600+08:00" level=info msg="[TCP] 127.0.0.1:57481 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.981791600+08:00" level=info msg="[TCP] 127.0.0.1:57658 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.984378000+08:00" level=info msg="[TCP] 127.0.0.1:57535 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.992248600+08:00" level=info msg="[TCP] 127.0.0.1:57513 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:18.998848400+08:00" level=info msg="[TCP] 127.0.0.1:57569 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.015282900+08:00" level=info msg="[TCP] 127.0.0.1:57548 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.019329600+08:00" level=info msg="[TCP] 127.0.0.1:57550 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.033243000+08:00" level=info msg="[TCP] 127.0.0.1:57674 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.090908300+08:00" level=info msg="[TCP] 127.0.0.1:57575 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.090908300+08:00" level=info msg="[TCP] 127.0.0.1:57580 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.093517600+08:00" level=info msg="[TCP] 127.0.0.1:57594 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.095446900+08:00" level=info msg="[TCP] 127.0.0.1:57676 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.115151900+08:00" level=info msg="[TCP] 127.0.0.1:57670 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.116261600+08:00" level=info msg="[TCP] 127.0.0.1:57562 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.119964800+08:00" level=info msg="[TCP] 127.0.0.1:57574 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.121118100+08:00" level=info msg="[TCP] 127.0.0.1:57576 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.124257000+08:00" level=info msg="[TCP] 127.0.0.1:57517 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.125909400+08:00" level=info msg="[TCP] 127.0.0.1:57508 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.131222000+08:00" level=info msg="[TCP] 127.0.0.1:57593 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.205661900+08:00" level=info msg="[TCP] 127.0.0.1:57549 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.212718000+08:00" level=info msg="[TCP] 127.0.0.1:57685 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.212718000+08:00" level=info msg="[TCP] 127.0.0.1:57683 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.220206300+08:00" level=info msg="[TCP] 127.0.0.1:57682 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.220206300+08:00" level=info msg="[TCP] 127.0.0.1:57684 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.226635800+08:00" level=info msg="[TCP] 127.0.0.1:57672 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.235163100+08:00" level=info msg="[TCP] 127.0.0.1:57681 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.248638800+08:00" level=info msg="[TCP] 127.0.0.1:57680 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.306401900+08:00" level=info msg="[TCP] 127.0.0.1:57700 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.307553200+08:00" level=info msg="[TCP] 127.0.0.1:57698 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.335693400+08:00" level=info msg="[TCP] 127.0.0.1:57713 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.368597800+08:00" level=info msg="[TCP] 127.0.0.1:57716 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.369718600+08:00" level=info msg="[TCP] 127.0.0.1:57719 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.388406800+08:00" level=info msg="[TCP] 127.0.0.1:57717 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.439749600+08:00" level=info msg="[TCP] 127.0.0.1:57701 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.443489500+08:00" level=info msg="[TCP] 127.0.0.1:57721 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.455026700+08:00" level=info msg="[TCP] 127.0.0.1:57693 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.462841700+08:00" level=info msg="[TCP] 127.0.0.1:57699 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.472109200+08:00" level=info msg="[TCP] 127.0.0.1:57715 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.472612100+08:00" level=info msg="[TCP] 127.0.0.1:57729 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.487780300+08:00" level=info msg="[TCP] 127.0.0.1:57734 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.491987300+08:00" level=info msg="[TCP] 127.0.0.1:57735 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.491987300+08:00" level=info msg="[TCP] 127.0.0.1:57737 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.493666300+08:00" level=info msg="[TCP] 127.0.0.1:57732 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.496577800+08:00" level=info msg="[TCP] 127.0.0.1:57733 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.523053200+08:00" level=info msg="[TCP] 127.0.0.1:57746 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.533333200+08:00" level=info msg="[TCP] 127.0.0.1:57731 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.560095500+08:00" level=info msg="[TCP] 127.0.0.1:57679 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.564513000+08:00" level=info msg="[TCP] 127.0.0.1:57702 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.566188200+08:00" level=info msg="[TCP] 127.0.0.1:57720 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.576286200+08:00" level=info msg="[TCP] 127.0.0.1:57736 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.638061600+08:00" level=info msg="[TCP] 127.0.0.1:57749 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.643489600+08:00" level=info msg="[TCP] 127.0.0.1:57753 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.646298200+08:00" level=info msg="[TCP] 127.0.0.1:57755 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.660817300+08:00" level=info msg="[TCP] 127.0.0.1:57763 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.689360800+08:00" level=info msg="[TCP] 127.0.0.1:57751 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.698105500+08:00" level=info msg="[TCP] 127.0.0.1:57750 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.711584100+08:00" level=info msg="[TCP] 127.0.0.1:57615 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.719653100+08:00" level=info msg="[TCP] 127.0.0.1:57619 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.720705600+08:00" level=info msg="[TCP] 127.0.0.1:57617 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.721218200+08:00" level=info msg="[TCP] 127.0.0.1:57765 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.732618100+08:00" level=info msg="[TCP] 127.0.0.1:57767 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.767464400+08:00" level=info msg="[TCP] 127.0.0.1:57632 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.798426600+08:00" level=info msg="[TCP] 127.0.0.1:57661 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.830872600+08:00" level=info msg="[TCP] 127.0.0.1:57656 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.934099200+08:00" level=info msg="[TCP] 127.0.0.1:57780 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.935132300+08:00" level=info msg="[TCP] 127.0.0.1:57778 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.947253500+08:00" level=info msg="[TCP] 127.0.0.1:57771 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:19.960798500+08:00" level=info msg="[TCP] 127.0.0.1:57793 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.014758300+08:00" level=info msg="[TCP] 127.0.0.1:57754 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.021169100+08:00" level=info msg="[TCP] 127.0.0.1:57618 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.022494900+08:00" level=info msg="[TCP] 127.0.0.1:57634 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.031802100+08:00" level=info msg="[TCP] 127.0.0.1:57779 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.038147200+08:00" level=info msg="[TCP] 127.0.0.1:57777 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.041654000+08:00" level=info msg="[TCP] 127.0.0.1:57794 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.044261200+08:00" level=info msg="[TCP] 127.0.0.1:57797 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.069981300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57268 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:20.111602500+08:00" level=info msg="[TCP] 127.0.0.1:57668 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.119486800+08:00" level=info msg="[TCP] 127.0.0.1:57775 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.122890900+08:00" level=info msg="[TCP] 127.0.0.1:57782 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.124499600+08:00" level=info msg="[TCP] 127.0.0.1:57791 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.147340400+08:00" level=info msg="[TCP] 127.0.0.1:57803 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.176439000+08:00" level=info msg="[TCP] 127.0.0.1:57811 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.221815100+08:00" level=info msg="[TCP] 127.0.0.1:57799 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.252653200+08:00" level=info msg="[TCP] 127.0.0.1:57805 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.262734200+08:00" level=info msg="[TCP] 127.0.0.1:57818 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.267471300+08:00" level=info msg="[TCP] 127.0.0.1:57819 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.268510500+08:00" level=info msg="[TCP] 127.0.0.1:57817 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.292358000+08:00" level=info msg="[TCP] 127.0.0.1:57827 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.294507300+08:00" level=info msg="[TCP] 127.0.0.1:57828 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.309204900+08:00" level=info msg="[TCP] 127.0.0.1:57697 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.312297100+08:00" level=info msg="[TCP] 127.0.0.1:57809 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.318036500+08:00" level=info msg="[TCP] 127.0.0.1:57696 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.327746800+08:00" level=info msg="[TCP] 127.0.0.1:57837 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.327746800+08:00" level=info msg="[TCP] 127.0.0.1:57831 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.331050100+08:00" level=info msg="[TCP] 127.0.0.1:57835 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.361188600+08:00" level=info msg="[TCP] 127.0.0.1:57847 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.376176700+08:00" level=info msg="[TCP] 127.0.0.1:57718 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.381436600+08:00" level=info msg="[TCP] 127.0.0.1:57833 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.382851200+08:00" level=info msg="[TCP] 127.0.0.1:57711 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.407903200+08:00" level=info msg="[TCP] 127.0.0.1:57834 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.408938200+08:00" level=info msg="[TCP] 127.0.0.1:57836 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.408938200+08:00" level=info msg="[TCP] 127.0.0.1:57849 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.421576300+08:00" level=info msg="[TCP] 127.0.0.1:57845 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.440744600+08:00" level=info msg="[TCP] 127.0.0.1:57832 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.449067700+08:00" level=info msg="[TCP] 127.0.0.1:57851 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.490728900+08:00" level=info msg="[TCP] 127.0.0.1:57852 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.492202000+08:00" level=info msg="[TCP] 127.0.0.1:57861 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.494707300+08:00" level=info msg="[TCP] 127.0.0.1:57855 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.501111000+08:00" level=info msg="[TCP] 127.0.0.1:57859 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.502254200+08:00" level=info msg="[TCP] 127.0.0.1:57860 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.503470500+08:00" level=info msg="[TCP] 127.0.0.1:57857 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.511358500+08:00" level=info msg="[TCP] 127.0.0.1:57869 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.577797900+08:00" level=info msg="[TCP] 127.0.0.1:57856 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.592685500+08:00" level=info msg="[TCP] 127.0.0.1:57877 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.593232600+08:00" level=info msg="[TCP] 127.0.0.1:57876 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.595998700+08:00" level=info msg="[TCP] 127.0.0.1:57881 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.595998700+08:00" level=info msg="[TCP] 127.0.0.1:57879 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.598504800+08:00" level=info msg="[TCP] 127.0.0.1:57878 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.601208300+08:00" level=info msg="[TCP] 127.0.0.1:57871 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.606634200+08:00" level=info msg="[TCP] 127.0.0.1:57880 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.608508600+08:00" level=info msg="[TCP] 127.0.0.1:57875 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.610250100+08:00" level=info msg="[TCP] 127.0.0.1:57873 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.629161900+08:00" level=info msg="[TCP] 127.0.0.1:57891 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.631672000+08:00" level=info msg="[TCP] 127.0.0.1:57889 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.704735400+08:00" level=info msg="[TCP] 127.0.0.1:57893 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.757907400+08:00" level=info msg="[TCP] 127.0.0.1:57752 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.789444000+08:00" level=info msg="[TCP] 127.0.0.1:57769 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.847209900+08:00" level=info msg="[TCP] 127.0.0.1:57858 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.869063800+08:00" level=info msg="[TCP] 127.0.0.1:57897 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.907192000+08:00" level=info msg="[TCP] 127.0.0.1:57418 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.909699100+08:00" level=info msg="[TCP] 127.0.0.1:57440 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.910770800+08:00" level=info msg="[TCP] 127.0.0.1:57433 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.910770800+08:00" level=info msg="[TCP] 127.0.0.1:57436 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.950331800+08:00" level=info msg="[TCP] 127.0.0.1:57903 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.956394600+08:00" level=info msg="[TCP] 127.0.0.1:57895 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:20.966736400+08:00" level=info msg="[TCP] 127.0.0.1:57773 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.078447900+08:00" level=info msg="[TCP] 127.0.0.1:57914 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.087691900+08:00" level=info msg="[TCP] 127.0.0.1:57915 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.123820700+08:00" level=info msg="[TCP] 127.0.0.1:57924 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.130714300+08:00" level=info msg="[TCP] 127.0.0.1:57938 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.131742000+08:00" level=info msg="[TCP] 127.0.0.1:57937 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.138553500+08:00" level=info msg="[TCP] 127.0.0.1:57910 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.140035500+08:00" level=info msg="[TCP] 127.0.0.1:57807 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.141761700+08:00" level=info msg="[TCP] 127.0.0.1:57927 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.141761700+08:00" level=info msg="[TCP] 127.0.0.1:57925 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.181939200+08:00" level=info msg="[TCP] 127.0.0.1:57909 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.189795900+08:00" level=info msg="[TCP] 127.0.0.1:57943 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.294864300+08:00" level=info msg="[TCP] 127.0.0.1:57814 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.327188700+08:00" level=info msg="[TCP] 127.0.0.1:57947 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.357186100+08:00" level=info msg="[TCP] 127.0.0.1:57813 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.372552300+08:00" level=info msg="[TCP] 127.0.0.1:57952 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.373771000+08:00" level=info msg="[TCP] 127.0.0.1:57955 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.374601700+08:00" level=info msg="[TCP] 127.0.0.1:57951 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.404158700+08:00" level=info msg="[TCP] 127.0.0.1:57950 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.456215000+08:00" level=info msg="[TCP] 127.0.0.1:57971 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.457320300+08:00" level=info msg="[TCP] 127.0.0.1:57972 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.464258200+08:00" level=info msg="[TCP] 127.0.0.1:57949 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.469068700+08:00" level=info msg="[TCP] 127.0.0.1:57965 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.472535700+08:00" level=info msg="[TCP] 127.0.0.1:57970 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.474732600+08:00" level=info msg="[TCP] 127.0.0.1:57969 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.474732600+08:00" level=info msg="[TCP] 127.0.0.1:57973 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.575528100+08:00" level=info msg="[TCP] 127.0.0.1:57974 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.621060200+08:00" level=info msg="[TCP] 127.0.0.1:57953 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.625277600+08:00" level=info msg="[TCP] 127.0.0.1:57926 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.625277600+08:00" level=info msg="[TCP] 127.0.0.1:57911 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.627381000+08:00" level=info msg="[TCP] 127.0.0.1:57929 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.637205400+08:00" level=info msg="[TCP] 127.0.0.1:57945 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.642564700+08:00" level=info msg="[TCP] 127.0.0.1:57956 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.646487200+08:00" level=info msg="[TCP] 127.0.0.1:57968 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.648075400+08:00" level=info msg="[TCP] 127.0.0.1:57989 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.708280800+08:00" level=info msg="[TCP] 127.0.0.1:57982 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.712704100+08:00" level=info msg="[TCP] 127.0.0.1:57991 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.733735700+08:00" level=info msg="[TCP] 127.0.0.1:58005 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.740849800+08:00" level=info msg="[TCP] 127.0.0.1:58002 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.758553300+08:00" level=info msg="[TCP] 127.0.0.1:57644 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.780006100+08:00" level=info msg="[TCP] 127.0.0.1:58000 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.789498600+08:00" level=info msg="[TCP] 127.0.0.1:57986 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.823896700+08:00" level=info msg="[TCP] 127.0.0.1:57995 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.823896700+08:00" level=info msg="[TCP] 127.0.0.1:58018 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.824532100+08:00" level=info msg="[TCP] 127.0.0.1:58022 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.826770800+08:00" level=info msg="[TCP] 127.0.0.1:58017 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.834819500+08:00" level=info msg="[TCP] 127.0.0.1:58004 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.859120500+08:00" level=info msg="[TCP] 127.0.0.1:58016 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.862107600+08:00" level=info msg="[TCP] 127.0.0.1:58014 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.867665200+08:00" level=info msg="[TCP] 127.0.0.1:58020 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.869983700+08:00" level=info msg="[TCP] 127.0.0.1:57899 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.934750300+08:00" level=info msg="[TCP] 127.0.0.1:58021 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.967429300+08:00" level=info msg="[TCP] 127.0.0.1:58019 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:21.976270700+08:00" level=info msg="[TCP] 127.0.0.1:58032 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.042062500+08:00" level=info msg="[TCP] 127.0.0.1:58036 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.061069100+08:00" level=info msg="[TCP] 127.0.0.1:58038 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.077145700+08:00" level=info msg="[TCP] 127.0.0.1:57912 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.077655400+08:00" level=info msg="[TCP] 127.0.0.1:58040 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.085274900+08:00" level=info msg="[TCP] 127.0.0.1:57913 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.109219300+08:00" level=info msg="[TCP] 127.0.0.1:57923 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.112627200+08:00" level=info msg="[TCP] 127.0.0.1:57928 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.126564200+08:00" level=info msg="[TCP] 127.0.0.1:57907 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.141911200+08:00" level=info msg="[TCP] 127.0.0.1:58042 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.149423100+08:00" level=info msg="[TCP] 127.0.0.1:58044 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.292037900+08:00" level=info msg="[TCP] 127.0.0.1:58049 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.293966700+08:00" level=info msg="[TCP] 127.0.0.1:58047 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.316408000+08:00" level=info msg="[TCP] 127.0.0.1:58054 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.318116000+08:00" level=info msg="[TCP] 127.0.0.1:58059 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.318116000+08:00" level=info msg="[TCP] 127.0.0.1:58058 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.318116000+08:00" level=info msg="[TCP] 127.0.0.1:58061 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.318623000+08:00" level=info msg="[TCP] 127.0.0.1:58057 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.318623000+08:00" level=info msg="[TCP] 127.0.0.1:58052 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.318623000+08:00" level=info msg="[TCP] 127.0.0.1:58051 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.319958500+08:00" level=info msg="[TCP] 127.0.0.1:58064 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.320923300+08:00" level=info msg="[TCP] 127.0.0.1:58060 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.360988100+08:00" level=info msg="[TCP] 127.0.0.1:58082 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.369639600+08:00" level=info msg="[TCP] 127.0.0.1:58083 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.377311200+08:00" level=info msg="[TCP] 127.0.0.1:58080 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.397207700+08:00" level=info msg="[TCP] 127.0.0.1:58081 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.443212600+08:00" level=info msg="[TCP] 127.0.0.1:57954 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.461947800+08:00" level=info msg="[TCP] 127.0.0.1:58091 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.492191300+08:00" level=info msg="[TCP] 127.0.0.1:58079 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.495565300+08:00" level=info msg="[TCP] 127.0.0.1:57983 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.496367500+08:00" level=info msg="[TCP] 127.0.0.1:58089 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.542463700+08:00" level=info msg="[TCP] 127.0.0.1:58095 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.576053500+08:00" level=info msg="[TCP] 127.0.0.1:57993 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.687632400+08:00" level=info msg="[TCP] 127.0.0.1:58108 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.739784200+08:00" level=info msg="[TCP] 127.0.0.1:58102 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.740619200+08:00" level=info msg="[TCP] 127.0.0.1:58099 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.741224100+08:00" level=info msg="[TCP] 127.0.0.1:58106 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.742465400+08:00" level=info msg="[TCP] 127.0.0.1:58104 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.744040300+08:00" level=info msg="[TCP] 127.0.0.1:58001 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.797524000+08:00" level=info msg="[TCP] 127.0.0.1:58111 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.807966400+08:00" level=info msg="[TCP] 127.0.0.1:58113 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.816540700+08:00" level=info msg="[TCP] 127.0.0.1:58117 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.835656300+08:00" level=info msg="[TCP] 127.0.0.1:58003 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.837977700+08:00" level=info msg="[TCP] 127.0.0.1:58119 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.855642900+08:00" level=info msg="[TCP] 127.0.0.1:58126 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.856905700+08:00" level=info msg="[TCP] 127.0.0.1:58131 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.858092900+08:00" level=info msg="[TCP] 127.0.0.1:58133 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.858092900+08:00" level=info msg="[TCP] 127.0.0.1:58127 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.858092900+08:00" level=info msg="[TCP] 127.0.0.1:58134 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.859503800+08:00" level=info msg="[TCP] 127.0.0.1:58006 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.867739100+08:00" level=info msg="[TCP] 127.0.0.1:58034 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.892289100+08:00" level=info msg="[TCP] 127.0.0.1:58125 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.894774800+08:00" level=info msg="[TCP] 127.0.0.1:58124 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.897797800+08:00" level=info msg="[TCP] 127.0.0.1:58149 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.917397500+08:00" level=info msg="[TCP] 127.0.0.1:58122 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.917397500+08:00" level=info msg="[TCP] 127.0.0.1:58132 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:22.968283900+08:00" level=info msg="[TCP] 127.0.0.1:57781 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.039514400+08:00" level=info msg="[TCP] 127.0.0.1:58159 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.054622200+08:00" level=info msg="[TCP] 127.0.0.1:57800 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.061545100+08:00" level=info msg="[TCP] 127.0.0.1:58155 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.068275000+08:00" level=info msg="[TCP] 127.0.0.1:58150 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.068275000+08:00" level=info msg="[TCP] 127.0.0.1:58157 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.069421300+08:00" level=info msg="[TCP] 127.0.0.1:57783 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.127545300+08:00" level=info msg="[TCP] 127.0.0.1:58161 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.203726600+08:00" level=info msg="[TCP] 127.0.0.1:58165 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.214451200+08:00" level=info msg="[TCP] 127.0.0.1:58163 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.268920500+08:00" level=info msg="[TCP] 127.0.0.1:58167 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.277452300+08:00" level=info msg="[TCP] 127.0.0.1:57815 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.279619600+08:00" level=info msg="[TCP] 127.0.0.1:57816 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.324497800+08:00" level=info msg="[TCP] 127.0.0.1:58053 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.324497800+08:00" level=info msg="[TCP] 127.0.0.1:58056 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.331224700+08:00" level=info msg="[TCP] 127.0.0.1:58055 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.366812400+08:00" level=info msg="[TCP] 127.0.0.1:58169 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.379621800+08:00" level=info msg="[TCP] 127.0.0.1:57941 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.392503800+08:00" level=info msg="[TCP] 127.0.0.1:58063 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.446075800+08:00" level=info msg="[TCP] 127.0.0.1:58180 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.452363300+08:00" level=info msg="[TCP] 127.0.0.1:58173 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.457323700+08:00" level=info msg="[TCP] 127.0.0.1:58181 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.467683200+08:00" level=info msg="[TCP] 127.0.0.1:58179 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.479128200+08:00" level=info msg="[TCP] 127.0.0.1:58191 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.479128200+08:00" level=info msg="[TCP] 127.0.0.1:58193 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.479643900+08:00" level=info msg="[TCP] 127.0.0.1:58189 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.481255400+08:00" level=info msg="[TCP] 127.0.0.1:58194 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.482719300+08:00" level=info msg="[TCP] 127.0.0.1:58195 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.486498500+08:00" level=info msg="[TCP] 127.0.0.1:58177 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.491585900+08:00" level=info msg="[TCP] 127.0.0.1:58192 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.509318700+08:00" level=info msg="[TCP] 127.0.0.1:58206 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.509827900+08:00" level=info msg="[TCP] 127.0.0.1:58207 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.513192100+08:00" level=info msg="[TCP] 127.0.0.1:58210 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.515555800+08:00" level=info msg="[TCP] 127.0.0.1:58205 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.518226200+08:00" level=info msg="[TCP] 127.0.0.1:58203 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.522320900+08:00" level=info msg="[TCP] 127.0.0.1:58208 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.539954100+08:00" level=info msg="[TCP] 127.0.0.1:58219 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.554897000+08:00" level=info msg="[TCP] 127.0.0.1:58222 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.588936100+08:00" level=info msg="[TCP] 127.0.0.1:58223 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.592021400+08:00" level=info msg="[TCP] 127.0.0.1:58236 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.592021400+08:00" level=info msg="[TCP] 127.0.0.1:58239 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.682450500+08:00" level=info msg="[TCP] 127.0.0.1:58227 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.682450500+08:00" level=info msg="[TCP] 127.0.0.1:58224 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.718146900+08:00" level=info msg="[TCP] 127.0.0.1:58062 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.726044300+08:00" level=info msg="[TCP] 127.0.0.1:58171 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.728519400+08:00" level=info msg="[TCP] 127.0.0.1:58176 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.878189400+08:00" level=info msg="[TCP] 127.0.0.1:58241 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.892540200+08:00" level=info msg="[TCP] 127.0.0.1:58030 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.906291700+08:00" level=info msg="[TCP] 127.0.0.1:58130 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.907629300+08:00" level=info msg="[TCP] 127.0.0.1:58115 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.909062700+08:00" level=info msg="[TCP] 127.0.0.1:58123 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.909062700+08:00" level=info msg="[TCP] 127.0.0.1:58129 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.909062700+08:00" level=info msg="[TCP] 127.0.0.1:58250 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.924617900+08:00" level=info msg="[TCP] 127.0.0.1:58128 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.934771400+08:00" level=info msg="[TCP] 127.0.0.1:58252 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.938946100+08:00" level=info msg="[TCP] 127.0.0.1:58153 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.959313900+08:00" level=info msg="[TCP] 127.0.0.1:58247 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.974549000+08:00" level=info msg="[TCP] 127.0.0.1:58245 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.978439600+08:00" level=info msg="[TCP] 127.0.0.1:58256 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.979945900+08:00" level=info msg="[TCP] 127.0.0.1:58260 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.979945900+08:00" level=info msg="[TCP] 127.0.0.1:58262 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.989657700+08:00" level=info msg="[TCP] 127.0.0.1:58257 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.996669400+08:00" level=info msg="[TCP] 127.0.0.1:58254 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.997190000+08:00" level=info msg="[TCP] 127.0.0.1:57901 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:23.999645700+08:00" level=info msg="[TCP] 127.0.0.1:58261 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.028263500+08:00" level=info msg="[TCP] 127.0.0.1:58271 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.028780400+08:00" level=info msg="[TCP] 127.0.0.1:57905 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.032333500+08:00" level=info msg="[TCP] 127.0.0.1:58285 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.038735400+08:00" level=info msg="[TCP] 127.0.0.1:58276 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.038735400+08:00" level=info msg="[TCP] 127.0.0.1:58270 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.085825800+08:00" level=info msg="[TCP] 127.0.0.1:58289 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.101345900+08:00" level=info msg="[TCP] 127.0.0.1:58292 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.104095900+08:00" level=info msg="[TCP] 127.0.0.1:58293 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.114137000+08:00" level=info msg="[TCP] 127.0.0.1:58275 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.114137000+08:00" level=info msg="[TCP] 127.0.0.1:58272 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.134156900+08:00" level=info msg="[TCP] 127.0.0.1:58295 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.211258600+08:00" level=info msg="[TCP] 127.0.0.1:58273 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.212565700+08:00" level=info msg="[TCP] 127.0.0.1:58274 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.213547300+08:00" level=info msg="[TCP] 127.0.0.1:58258 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.219252500+08:00" level=info msg="[TCP] 127.0.0.1:58284 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.241679600+08:00" level=info msg="[TCP] 127.0.0.1:58294 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.289741500+08:00" level=info msg="[TCP] 127.0.0.1:58311 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.290741900+08:00" level=info msg="[TCP] 127.0.0.1:58308 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.292056500+08:00" level=info msg="[TCP] 127.0.0.1:58309 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.300407600+08:00" level=info msg="[TCP] 127.0.0.1:58310 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.313631200+08:00" level=info msg="[TCP] 127.0.0.1:58321 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.357714100+08:00" level=info msg="[TCP] 127.0.0.1:58312 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.368252900+08:00" level=info msg="[TCP] 127.0.0.1:58329 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.368252900+08:00" level=info msg="[TCP] 127.0.0.1:58328 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.369275600+08:00" level=info msg="[TCP] 127.0.0.1:58326 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.369275600+08:00" level=info msg="[TCP] 127.0.0.1:58327 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.370527400+08:00" level=info msg="[TCP] 127.0.0.1:58323 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.395389300+08:00" level=info msg="[TCP] 127.0.0.1:58340 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.411041500+08:00" level=info msg="[TCP] 127.0.0.1:58343 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.481789400+08:00" level=info msg="[TCP] 127.0.0.1:58175 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.482983700+08:00" level=info msg="[TCP] 127.0.0.1:58324 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.495349600+08:00" level=info msg="[TCP] 127.0.0.1:58345 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.495349600+08:00" level=info msg="[TCP] 127.0.0.1:58339 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.499114600+08:00" level=info msg="[TCP] 127.0.0.1:58204 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.502084300+08:00" level=info msg="[TCP] 127.0.0.1:58235 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.502622900+08:00" level=info msg="[TCP] 127.0.0.1:58221 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.505635100+08:00" level=info msg="[TCP] 127.0.0.1:58225 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.507879300+08:00" level=info msg="[TCP] 127.0.0.1:58347 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.588446900+08:00" level=info msg="[TCP] 127.0.0.1:58350 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.663307600+08:00" level=info msg="[TCP] 127.0.0.1:58357 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.785923000+08:00" level=info msg="[TCP] 127.0.0.1:58362 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.787131400+08:00" level=info msg="[TCP] 127.0.0.1:58366 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.808185900+08:00" level=info msg="[TCP] 127.0.0.1:58367 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.816366300+08:00" level=info msg="[TCP] 127.0.0.1:58359 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.821307700+08:00" level=info msg="[TCP] 127.0.0.1:58365 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:24.843238300+08:00" level=info msg="[TCP] 127.0.0.1:58375 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.002173400+08:00" level=info msg="[TCP] 127.0.0.1:58259 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.023592900+08:00" level=info msg="[TCP] 127.0.0.1:58383 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.033922200+08:00" level=info msg="[TCP] 127.0.0.1:58384 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.049174100+08:00" level=info msg="[TCP] 127.0.0.1:58386 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.049174100+08:00" level=info msg="[TCP] 127.0.0.1:58382 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.054065400+08:00" level=info msg="[TCP] 127.0.0.1:58396 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.064070300+08:00" level=info msg="[TCP] 127.0.0.1:58397 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.107726400+08:00" level=info msg="[TCP] 127.0.0.1:58296 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.109646900+08:00" level=info msg="[TCP] 127.0.0.1:58297 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.166770900+08:00" level=info msg="[TCP] 127.0.0.1:58402 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.188777200+08:00" level=info msg="[TCP] 127.0.0.1:58305 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.188777200+08:00" level=info msg="[TCP] 127.0.0.1:58291 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.235754900+08:00" level=info msg="[TCP] 127.0.0.1:58406 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.261203700+08:00" level=info msg="[TCP] 127.0.0.1:58423 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.266240600+08:00" level=info msg="[TCP] 127.0.0.1:58412 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.267974000+08:00" level=info msg="[TCP] 127.0.0.1:58410 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.299900200+08:00" level=info msg="[TCP] 127.0.0.1:58440 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.303940500+08:00" level=info msg="[TCP] 127.0.0.1:58307 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.305296400+08:00" level=info msg="[TCP] 127.0.0.1:58313 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.340250100+08:00" level=info msg="[TCP] 127.0.0.1:58425 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.342249400+08:00" level=info msg="[TCP] 127.0.0.1:58422 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.352786900+08:00" level=info msg="[TCP] 127.0.0.1:58442 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.371427200+08:00" level=info msg="[TCP] 127.0.0.1:58325 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.381431500+08:00" level=info msg="[TCP] 127.0.0.1:58444 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.399067100+08:00" level=info msg="[TCP] 127.0.0.1:58408 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.399573000+08:00" level=info msg="[TCP] 127.0.0.1:58409 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.416025800+08:00" level=info msg="[TCP] 127.0.0.1:58337 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.440820600+08:00" level=info msg="[TCP] 127.0.0.1:58426 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.442863300+08:00" level=info msg="[TCP] 127.0.0.1:58437 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.452256300+08:00" level=info msg="[TCP] 127.0.0.1:58446 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.473637500+08:00" level=info msg="[TCP] 127.0.0.1:58448 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.582239000+08:00" level=info msg="[TCP] 127.0.0.1:58454 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.671482900+08:00" level=info msg="[TCP] 127.0.0.1:58450 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.673002200+08:00" level=info msg="[TCP] 127.0.0.1:58355 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.748584500+08:00" level=info msg="[TCP] 127.0.0.1:58463 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.748584500+08:00" level=info msg="[TCP] 127.0.0.1:58460 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.749202300+08:00" level=info msg="[TCP] 127.0.0.1:58459 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.753927400+08:00" level=info msg="[TCP] 127.0.0.1:58464 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.773534100+08:00" level=info msg="[TCP] 127.0.0.1:58472 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.776361700+08:00" level=info msg="[TCP] 127.0.0.1:58478 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.777855000+08:00" level=info msg="[TCP] 127.0.0.1:58477 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.781362200+08:00" level=info msg="[TCP] 127.0.0.1:58458 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.783450700+08:00" level=info msg="[TCP] 127.0.0.1:58361 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.788116700+08:00" level=info msg="[TCP] 127.0.0.1:58474 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.801657200+08:00" level=info msg="[TCP] 127.0.0.1:58486 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.805076300+08:00" level=info msg="[TCP] 127.0.0.1:58489 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.811727100+08:00" level=info msg="[TCP] 127.0.0.1:57998 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.876280000+08:00" level=info msg="[TCP] 127.0.0.1:58377 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.981248300+08:00" level=info msg="[TCP] 127.0.0.1:58498 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:25.981248300+08:00" level=info msg="[TCP] 127.0.0.1:58493 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.008331500+08:00" level=info msg="[TCP] 127.0.0.1:58509 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.020595100+08:00" level=info msg="[TCP] 127.0.0.1:58497 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.022386900+08:00" level=info msg="[TCP] 127.0.0.1:58499 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.028182000+08:00" level=info msg="[TCP] 127.0.0.1:58495 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.035431600+08:00" level=info msg="[TCP] 127.0.0.1:58513 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.038437800+08:00" level=info msg="[TCP] 127.0.0.1:58511 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.043927800+08:00" level=info msg="[TCP] 127.0.0.1:58385 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.097201000+08:00" level=info msg="[TCP] 127.0.0.1:58515 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.114801300+08:00" level=info msg="[TCP] 127.0.0.1:58519 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.129788500+08:00" level=info msg="[TCP] 127.0.0.1:58521 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.141270100+08:00" level=info msg="[TCP] 127.0.0.1:58523 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.144167100+08:00" level=info msg="[TCP] 127.0.0.1:58528 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.145994200+08:00" level=info msg="[TCP] 127.0.0.1:58529 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.166507500+08:00" level=info msg="[TCP] 127.0.0.1:58537 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.187623100+08:00" level=info msg="[TCP] 127.0.0.1:58525 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.209663300+08:00" level=info msg="[TCP] 127.0.0.1:58494 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.240903800+08:00" level=info msg="[TCP] 127.0.0.1:58517 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.242711900+08:00" level=info msg="[TCP] 127.0.0.1:58404 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.246385100+08:00" level=info msg="[TCP] 127.0.0.1:58527 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.277960400+08:00" level=info msg="[TCP] 127.0.0.1:58421 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.278710700+08:00" level=info msg="[TCP] 127.0.0.1:58424 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.337515100+08:00" level=info msg="[TCP] 127.0.0.1:58539 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.460140800+08:00" level=info msg="[TCP] 127.0.0.1:58545 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.487780700+08:00" level=info msg="[TCP] 127.0.0.1:58543 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.579647400+08:00" level=info msg="[TCP] 127.0.0.1:58549 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.581849900+08:00" level=info msg="[TCP] 127.0.0.1:58551 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.582684700+08:00" level=info msg="[TCP] 127.0.0.1:58550 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.583957700+08:00" level=info msg="[TCP] 127.0.0.1:58547 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.588684100+08:00" level=info msg="[TCP] 127.0.0.1:58553 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.609233500+08:00" level=info msg="[TCP] 127.0.0.1:58564 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.609233500+08:00" level=info msg="[TCP] 127.0.0.1:58567 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.611542100+08:00" level=info msg="[TCP] 127.0.0.1:58562 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.611542100+08:00" level=info msg="[TCP] 127.0.0.1:58563 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.615160000+08:00" level=info msg="[TCP] 127.0.0.1:58566 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.647959300+08:00" level=info msg="[TCP] 127.0.0.1:58436 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.683146500+08:00" level=info msg="[TCP] 127.0.0.1:58548 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.686280700+08:00" level=info msg="[TCP] 127.0.0.1:58552 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.695911900+08:00" level=info msg="[TCP] 127.0.0.1:58575 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.707488800+08:00" level=info msg="[TCP] 127.0.0.1:58452 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.709279800+08:00" level=info msg="[TCP] 127.0.0.1:58226 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.710295800+08:00" level=info msg="[TCP] 127.0.0.1:58561 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.713851800+08:00" level=info msg="[TCP] 127.0.0.1:58243 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.719383400+08:00" level=info msg="[TCP] 127.0.0.1:58577 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.772173700+08:00" level=info msg="[TCP] 127.0.0.1:58462 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.779111700+08:00" level=info msg="[TCP] 127.0.0.1:58461 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.789531400+08:00" level=info msg="[TCP] 127.0.0.1:58476 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.822272800+08:00" level=info msg="[TCP] 127.0.0.1:58488 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.828895100+08:00" level=info msg="[TCP] 127.0.0.1:58584 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.830399700+08:00" level=info msg="[TCP] 127.0.0.1:58588 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.833089700+08:00" level=info msg="[TCP] 127.0.0.1:58589 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:26.898989900+08:00" level=info msg="[TCP] 127.0.0.1:58597 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.043222800+08:00" level=info msg="[TCP] 127.0.0.1:58496 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.046378300+08:00" level=info msg="[TCP] 127.0.0.1:58507 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.107254500+08:00" level=info msg="[TCP] 127.0.0.1:58606 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.113892800+08:00" level=info msg="[TCP] 127.0.0.1:58601 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.137228300+08:00" level=info msg="[TCP] 127.0.0.1:58621 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.139443900+08:00" level=info msg="[TCP] 127.0.0.1:58617 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.140567300+08:00" level=info msg="[TCP] 127.0.0.1:58616 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.152860500+08:00" level=info msg="[TCP] 127.0.0.1:58620 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.154130700+08:00" level=info msg="[TCP] 127.0.0.1:58615 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.159398800+08:00" level=info msg="[TCP] 127.0.0.1:58629 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.192647200+08:00" level=info msg="[TCP] 127.0.0.1:58400 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.193170800+08:00" level=info msg="[TCP] 127.0.0.1:58619 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.194125800+08:00" level=info msg="[TCP] 127.0.0.1:58618 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.205191500+08:00" level=info msg="[TCP] 127.0.0.1:58630 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.246357300+08:00" level=info msg="[TCP] 127.0.0.1:58607 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.247863000+08:00" level=info msg="[TCP] 127.0.0.1:58526 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.249315600+08:00" level=info msg="[TCP] 127.0.0.1:58524 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.312134600+08:00" level=info msg="[TCP] 127.0.0.1:58639 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.344497400+08:00" level=info msg="[TCP] 127.0.0.1:58637 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.360861000+08:00" level=info msg="[TCP] 127.0.0.1:58541 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.523085700+08:00" level=info msg="[TCP] 127.0.0.1:58646 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.539254000+08:00" level=info msg="[TCP] 127.0.0.1:58658 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.548946300+08:00" level=info msg="[TCP] 127.0.0.1:58648 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.551587000+08:00" level=info msg="[TCP] 127.0.0.1:58657 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.562801300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58097 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:27.574043200+08:00" level=info msg="[TCP] 127.0.0.1:58649 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.600693100+08:00" level=info msg="[TCP] 127.0.0.1:58666 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.618049400+08:00" level=info msg="[TCP] 127.0.0.1:58565 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.633364700+08:00" level=info msg="[TCP] 127.0.0.1:58644 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.636516400+08:00" level=info msg="[TCP] 127.0.0.1:58645 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.648066000+08:00" level=info msg="[TCP] 127.0.0.1:58647 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.649753400+08:00" level=info msg="[TCP] 127.0.0.1:58352 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.732413300+08:00" level=info msg="[TCP] 127.0.0.1:58643 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.749584600+08:00" level=info msg="[TCP] 127.0.0.1:58670 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.765281300+08:00" level=info msg="[TCP] 127.0.0.1:58579 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.767299600+08:00" level=info msg="[TCP] 127.0.0.1:58672 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.772444300+08:00" level=info msg="[TCP] 127.0.0.1:58581 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.786133100+08:00" level=info msg="[TCP] 127.0.0.1:58663 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.795272100+08:00" level=info msg="[TCP] 127.0.0.1:58363 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.797296900+08:00" level=info msg="[TCP] 127.0.0.1:58473 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.798507200+08:00" level=info msg="[TCP] 127.0.0.1:58475 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.801247900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58121 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:27.829551400+08:00" level=info msg="[TCP] 127.0.0.1:58583 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.830745800+08:00" level=info msg="[TCP] 127.0.0.1:58586 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.834522000+08:00" level=info msg="[TCP] 127.0.0.1:58585 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:27.860153900+08:00" level=info msg="[TCP] 127.0.0.1:58587 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.004398600+08:00" level=info msg="[TCP] 127.0.0.1:58364 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.007792700+08:00" level=info msg="[TCP] 127.0.0.1:58674 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.015082200+08:00" level=info msg="[TCP] 127.0.0.1:58380 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.031935100+08:00" level=info msg="[TCP] 127.0.0.1:58388 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.105678400+08:00" level=info msg="[TCP] 127.0.0.1:58599 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.106678900+08:00" level=info msg="[TCP] 127.0.0.1:58387 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.111920700+08:00" level=info msg="[TCP] 127.0.0.1:58685 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.111920700+08:00" level=info msg="[TCP] 127.0.0.1:58686 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.145763300+08:00" level=info msg="[TCP] 127.0.0.1:58681 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.149114300+08:00" level=info msg="[TCP] 127.0.0.1:58603 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.178554000+08:00" level=info msg="[TCP] 127.0.0.1:58680 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.180343400+08:00" level=info msg="[TCP] 127.0.0.1:58605 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.182346100+08:00" level=info msg="[TCP] 127.0.0.1:58602 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.183346400+08:00" level=info msg="[TCP] 127.0.0.1:58633 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.184864200+08:00" level=info msg="[TCP] 127.0.0.1:58604 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.188431100+08:00" level=info msg="[TCP] 127.0.0.1:58696 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.210963400+08:00" level=info msg="[TCP] 127.0.0.1:58684 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.210963400+08:00" level=info msg="[TCP] 127.0.0.1:58683 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.286191300+08:00" level=info msg="[TCP] 127.0.0.1:58635 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.301983400+08:00" level=info msg="[TCP] 127.0.0.1:58698 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.374792300+08:00" level=info msg="[TCP] 127.0.0.1:58700 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.393508800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58178 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:28.425940600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58190 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:28.450872500+08:00" level=info msg="[TCP] 127.0.0.1:58702 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.456388400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58209 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:28.496452200+08:00" level=info msg="[TCP] 127.0.0.1:58704 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.529894500+08:00" level=info msg="[TCP] 127.0.0.1:58706 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.538360300+08:00" level=info msg="[TCP] 127.0.0.1:58714 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.544231500+08:00" level=info msg="[TCP] 127.0.0.1:58715 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.544231500+08:00" level=info msg="[TCP] 127.0.0.1:58711 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.547128200+08:00" level=info msg="[TCP] 127.0.0.1:58659 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.575250100+08:00" level=info msg="[TCP] 127.0.0.1:58736 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.575764200+08:00" level=info msg="[TCP] 127.0.0.1:58737 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.576302300+08:00" level=info msg="[TCP] 127.0.0.1:58727 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.577842700+08:00" level=info msg="[TCP] 127.0.0.1:58730 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.577842700+08:00" level=info msg="[TCP] 127.0.0.1:58743 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.597531400+08:00" level=info msg="[TCP] 127.0.0.1:58741 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.598854400+08:00" level=info msg="[TCP] 127.0.0.1:58733 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.598854400+08:00" level=info msg="[TCP] 127.0.0.1:58739 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.599363900+08:00" level=info msg="[TCP] 127.0.0.1:58738 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.599363900+08:00" level=info msg="[TCP] 127.0.0.1:58740 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.602509700+08:00" level=info msg="[TCP] 127.0.0.1:58724 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.608840900+08:00" level=info msg="[TCP] 127.0.0.1:58725 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.610854000+08:00" level=info msg="[TCP] 127.0.0.1:58734 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.610854000+08:00" level=info msg="[TCP] 127.0.0.1:58732 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.613629400+08:00" level=info msg="[TCP] 127.0.0.1:58726 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.687728900+08:00" level=info msg="[TCP] 127.0.0.1:58767 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.688994800+08:00" level=info msg="[TCP] 127.0.0.1:58766 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.691477800+08:00" level=info msg="[TCP] 127.0.0.1:58768 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.694855700+08:00" level=info msg="[TCP] 127.0.0.1:58769 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.701533500+08:00" level=info msg="[TCP] 127.0.0.1:58776 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.715565000+08:00" level=info msg="[TCP] 127.0.0.1:58775 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.750977000+08:00" level=info msg="[TCP] 127.0.0.1:58784 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.751567200+08:00" level=info msg="[TCP] 127.0.0.1:58781 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.756644600+08:00" level=info msg="[TCP] 127.0.0.1:58785 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.770555300+08:00" level=info msg="[TCP] 127.0.0.1:58793 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.789227100+08:00" level=info msg="[TCP] 127.0.0.1:58799 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.790890300+08:00" level=info msg="[TCP] 127.0.0.1:58800 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.791404400+08:00" level=info msg="[TCP] 127.0.0.1:58798 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.791404400+08:00" level=info msg="[TCP] 127.0.0.1:58797 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.815950900+08:00" level=info msg="[TCP] 127.0.0.1:58795 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.815950900+08:00" level=info msg="[TCP] 127.0.0.1:58712 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.817388500+08:00" level=info msg="[TCP] 127.0.0.1:58716 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.822456100+08:00" level=info msg="[TCP] 127.0.0.1:58728 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.823610200+08:00" level=info msg="[TCP] 127.0.0.1:58735 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.855835600+08:00" level=info msg="[TCP] 127.0.0.1:58641 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.857729500+08:00" level=info msg="[TCP] 127.0.0.1:58708 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.859130100+08:00" level=info msg="[TCP] 127.0.0.1:58713 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.862288600+08:00" level=info msg="[TCP] 127.0.0.1:58731 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.862801500+08:00" level=info msg="[TCP] 127.0.0.1:58744 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.862801500+08:00" level=info msg="[TCP] 127.0.0.1:58729 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.863552200+08:00" level=info msg="[TCP] 127.0.0.1:58742 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.869818800+08:00" level=info msg="[TCP] 127.0.0.1:58668 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.882074400+08:00" level=info msg="[TCP] 127.0.0.1:58782 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.882074400+08:00" level=info msg="[TCP] 127.0.0.1:58813 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.883021100+08:00" level=info msg="[TCP] 127.0.0.1:58796 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.951431200+08:00" level=info msg="[TCP] 127.0.0.1:58820 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.952076500+08:00" level=info msg="[TCP] 127.0.0.1:58819 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.976061900+08:00" level=info msg="[TCP] 127.0.0.1:58822 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.981951400+08:00" level=info msg="[TCP] 127.0.0.1:58821 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:28.981951400+08:00" level=info msg="[TCP] 127.0.0.1:58823 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.012079700+08:00" level=info msg="[TCP] 127.0.0.1:58833 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.072169300+08:00" level=info msg="[TCP] 127.0.0.1:58676 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.078163200+08:00" level=info msg="[TCP] 127.0.0.1:58678 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.093156900+08:00" level=info msg="[TCP] 127.0.0.1:58801 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.097603000+08:00" level=info msg="[TCP] 127.0.0.1:58831 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.127162600+08:00" level=info msg="[TCP] 127.0.0.1:58817 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.128348300+08:00" level=info msg="[TCP] 127.0.0.1:58682 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.136017900+08:00" level=info msg="[TCP] 127.0.0.1:58835 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.168904000+08:00" level=info msg="[TCP] 127.0.0.1:58837 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.224519500+08:00" level=info msg="[TCP] 127.0.0.1:58839 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.229120400+08:00" level=info msg="[TCP] 127.0.0.1:58845 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.253779500+08:00" level=info msg="[TCP] 127.0.0.1:58856 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.358328400+08:00" level=info msg="[TCP] 127.0.0.1:58861 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.398560300+08:00" level=info msg="[TCP] 127.0.0.1:58866 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.413676600+08:00" level=info msg="[TCP] 127.0.0.1:58868 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.427675900+08:00" level=info msg="[TCP] 127.0.0.1:58865 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.441187700+08:00" level=info msg="[TCP] 127.0.0.1:58864 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.451568700+08:00" level=info msg="[TCP] 127.0.0.1:58843 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.454345400+08:00" level=info msg="[TCP] 127.0.0.1:58881 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.491580400+08:00" level=info msg="[TCP] 127.0.0.1:58434 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.509674900+08:00" level=info msg="[TCP] 127.0.0.1:58883 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.562057100+08:00" level=info msg="[TCP] 127.0.0.1:58867 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.563639400+08:00" level=info msg="[TCP] 127.0.0.1:58869 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.572971200+08:00" level=info msg="[TCP] 127.0.0.1:58710 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.601518800+08:00" level=info msg="[TCP] 127.0.0.1:58841 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.602890600+08:00" level=info msg="[TCP] 127.0.0.1:58855 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.614928000+08:00" level=info msg="[TCP] 127.0.0.1:58889 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.619903800+08:00" level=info msg="[TCP] 127.0.0.1:58890 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.633751500+08:00" level=info msg="[TCP] 127.0.0.1:58840 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.652495900+08:00" level=info msg="[TCP] 127.0.0.1:58863 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.659802800+08:00" level=info msg="[TCP] 127.0.0.1:58891 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.664052700+08:00" level=info msg="[TCP] 127.0.0.1:58887 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.675245400+08:00" level=info msg="[TCP] 127.0.0.1:58885 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.681658700+08:00" level=info msg="[TCP] 127.0.0.1:58892 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.695410600+08:00" level=info msg="[TCP] 127.0.0.1:58903 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.729726100+08:00" level=info msg="[TCP] 127.0.0.1:58905 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.750688400+08:00" level=info msg="[TCP] 127.0.0.1:58779 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.760668200+08:00" level=info msg="[TCP] 127.0.0.1:58893 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.764650700+08:00" level=info msg="[TCP] 127.0.0.1:58783 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.822730600+08:00" level=info msg="[TCP] 127.0.0.1:58811 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.825399200+08:00" level=info msg="[TCP] 127.0.0.1:58809 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.856778800+08:00" level=info msg="[TCP] 127.0.0.1:58901 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.872137400+08:00" level=info msg="[TCP] 127.0.0.1:58907 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.876587600+08:00" level=info msg="[TCP] 127.0.0.1:58780 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.884092900+08:00" level=info msg="[TCP] 127.0.0.1:58909 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.952844700+08:00" level=info msg="[TCP] 127.0.0.1:58911 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.959226200+08:00" level=info msg="[TCP] 127.0.0.1:58913 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:29.963784400+08:00" level=info msg="[TCP] 127.0.0.1:58818 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.001929700+08:00" level=info msg="[TCP] 127.0.0.1:58919 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.007000600+08:00" level=info msg="[TCP] 127.0.0.1:58815 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.009419400+08:00" level=info msg="[TCP] 127.0.0.1:58920 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.040799100+08:00" level=info msg="[TCP] 127.0.0.1:58917 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.044217200+08:00" level=info msg="[TCP] 127.0.0.1:58931 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.044217200+08:00" level=info msg="[TCP] 127.0.0.1:58936 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.047413800+08:00" level=info msg="[TCP] 127.0.0.1:58942 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.047929300+08:00" level=info msg="[TCP] 127.0.0.1:58937 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.050462600+08:00" level=info msg="[TCP] 127.0.0.1:58932 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.050462600+08:00" level=info msg="[TCP] 127.0.0.1:58933 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.054503500+08:00" level=info msg="[TCP] 127.0.0.1:58939 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.056798500+08:00" level=info msg="[TCP] 127.0.0.1:58944 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.059304500+08:00" level=info msg="[TCP] 127.0.0.1:58941 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.060723100+08:00" level=info msg="[TCP] 127.0.0.1:58935 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.064506300+08:00" level=info msg="[TCP] 127.0.0.1:58923 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.069333200+08:00" level=info msg="[TCP] 127.0.0.1:58938 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.071769800+08:00" level=info msg="[TCP] 127.0.0.1:58915 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.079829300+08:00" level=info msg="[TCP] 127.0.0.1:58921 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.082580200+08:00" level=info msg="[TCP] 127.0.0.1:58959 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.089271100+08:00" level=info msg="[TCP] 127.0.0.1:58963 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.089780100+08:00" level=info msg="[TCP] 127.0.0.1:58962 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.092502000+08:00" level=info msg="[TCP] 127.0.0.1:58961 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.098746600+08:00" level=info msg="[TCP] 127.0.0.1:58969 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.174564100+08:00" level=info msg="[TCP] 127.0.0.1:58973 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.182487100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58411 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:30.182487100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58407 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:30.185846200+08:00" level=info msg="[TCP] 127.0.0.1:58975 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.195704200+08:00" level=info msg="[TCP] 127.0.0.1:58971 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.210276000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58420 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:30.281635900+08:00" level=info msg="[TCP] 127.0.0.1:58981 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.292969900+08:00" level=info msg="[TCP] 127.0.0.1:58842 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.304952200+08:00" level=info msg="[TCP] 127.0.0.1:58983 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.315931200+08:00" level=info msg="[TCP] 127.0.0.1:58985 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.347259000+08:00" level=info msg="[TCP] 127.0.0.1:58989 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.353703900+08:00" level=info msg="[TCP] 127.0.0.1:58987 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.417281900+08:00" level=info msg="[TCP] 127.0.0.1:58997 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.426301100+08:00" level=info msg="[TCP] 127.0.0.1:58995 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.426301100+08:00" level=info msg="[TCP] 127.0.0.1:58991 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.444865900+08:00" level=info msg="[TCP] 127.0.0.1:59005 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.449881200+08:00" level=info msg="[TCP] 127.0.0.1:59009 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.499893300+08:00" level=info msg="[TCP] 127.0.0.1:59010 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.508679800+08:00" level=info msg="[TCP] 127.0.0.1:59024 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.509383700+08:00" level=info msg="[TCP] 127.0.0.1:59028 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.511889800+08:00" level=info msg="[TCP] 127.0.0.1:59026 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.511889800+08:00" level=info msg="[TCP] 127.0.0.1:59034 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.511889800+08:00" level=info msg="[TCP] 127.0.0.1:59042 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.511889800+08:00" level=info msg="[TCP] 127.0.0.1:59045 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.512890700+08:00" level=info msg="[TCP] 127.0.0.1:59035 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.512890700+08:00" level=info msg="[TCP] 127.0.0.1:59030 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.514986300+08:00" level=info msg="[TCP] 127.0.0.1:59037 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.514986300+08:00" level=info msg="[TCP] 127.0.0.1:59047 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.514986300+08:00" level=info msg="[TCP] 127.0.0.1:59029 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.514986300+08:00" level=info msg="[TCP] 127.0.0.1:59025 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.514986300+08:00" level=info msg="[TCP] 127.0.0.1:59040 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.525033100+08:00" level=info msg="[TCP] 127.0.0.1:59044 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.633744700+08:00" level=info msg="[TCP] 127.0.0.1:59089 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.634251700+08:00" level=info msg="[TCP] 127.0.0.1:59088 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.638863400+08:00" level=info msg="[TCP] 127.0.0.1:59095 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.638863400+08:00" level=info msg="[TCP] 127.0.0.1:59097 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.638863400+08:00" level=info msg="[TCP] 127.0.0.1:59087 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.638863400+08:00" level=info msg="[TCP] 127.0.0.1:59080 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.639373200+08:00" level=info msg="[TCP] 127.0.0.1:59091 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.640369800+08:00" level=info msg="[TCP] 127.0.0.1:58977 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.643808900+08:00" level=info msg="[TCP] 127.0.0.1:59082 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.646066600+08:00" level=info msg="[TCP] 127.0.0.1:58844 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.669857000+08:00" level=info msg="[TCP] 127.0.0.1:59096 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.669857000+08:00" level=info msg="[TCP] 127.0.0.1:59077 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.672025200+08:00" level=info msg="[TCP] 127.0.0.1:59076 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.674760400+08:00" level=info msg="[TCP] 127.0.0.1:59075 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.676393900+08:00" level=info msg="[TCP] 127.0.0.1:59086 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.682460100+08:00" level=info msg="[TCP] 127.0.0.1:59093 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.682460100+08:00" level=info msg="[TCP] 127.0.0.1:59081 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.697171500+08:00" level=info msg="[TCP] 127.0.0.1:58853 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.710512700+08:00" level=info msg="[TCP] 127.0.0.1:59103 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.712219000+08:00" level=info msg="[TCP] 127.0.0.1:59104 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.718793600+08:00" level=info msg="[TCP] 127.0.0.1:59007 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.720315400+08:00" level=info msg="[TCP] 127.0.0.1:59027 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.720315400+08:00" level=info msg="[TCP] 127.0.0.1:59039 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.721968700+08:00" level=info msg="[TCP] 127.0.0.1:59032 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.723169100+08:00" level=info msg="[TCP] 127.0.0.1:59021 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.724857900+08:00" level=info msg="[TCP] 127.0.0.1:58994 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.724857900+08:00" level=info msg="[TCP] 127.0.0.1:59046 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.729880100+08:00" level=info msg="[TCP] 127.0.0.1:59100 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.732588600+08:00" level=info msg="[TCP] 127.0.0.1:59083 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.739666700+08:00" level=info msg="[TCP] 127.0.0.1:59105 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.776104800+08:00" level=info msg="[TCP] 127.0.0.1:59099 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.779612800+08:00" level=info msg="[TCP] 127.0.0.1:59139 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.782305900+08:00" level=info msg="[TCP] 127.0.0.1:59135 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.782305900+08:00" level=info msg="[TCP] 127.0.0.1:59102 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.800030500+08:00" level=info msg="[TCP] 127.0.0.1:59141 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.800030500+08:00" level=info msg="[TCP] 127.0.0.1:59138 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.809792000+08:00" level=info msg="[TCP] 127.0.0.1:59134 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.818770400+08:00" level=info msg="[TCP] 127.0.0.1:59136 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.819826900+08:00" level=info msg="[TCP] 127.0.0.1:59131 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.859777900+08:00" level=info msg="[TCP] 127.0.0.1:59132 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.861799500+08:00" level=info msg="[TCP] 127.0.0.1:59133 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.949836700+08:00" level=info msg="[TCP] 127.0.0.1:59155 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.950834400+08:00" level=info msg="[TCP] 127.0.0.1:59164 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.950834400+08:00" level=info msg="[TCP] 127.0.0.1:59156 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.950834400+08:00" level=info msg="[TCP] 127.0.0.1:59161 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.951835300+08:00" level=info msg="[TCP] 127.0.0.1:59154 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.951835300+08:00" level=info msg="[TCP] 127.0.0.1:59147 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.951835300+08:00" level=info msg="[TCP] 127.0.0.1:59180 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.951835300+08:00" level=info msg="[TCP] 127.0.0.1:59178 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.954816900+08:00" level=info msg="[TCP] 127.0.0.1:59169 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.955324400+08:00" level=info msg="[TCP] 127.0.0.1:59171 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.955833800+08:00" level=info msg="[TCP] 127.0.0.1:59176 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.955833800+08:00" level=info msg="[TCP] 127.0.0.1:59159 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.979731100+08:00" level=info msg="[TCP] 127.0.0.1:59170 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.981008700+08:00" level=info msg="[TCP] 127.0.0.1:59158 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.982330100+08:00" level=info msg="[TCP] 127.0.0.1:59143 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.982330100+08:00" level=info msg="[TCP] 127.0.0.1:59175 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.985010300+08:00" level=info msg="[TCP] 127.0.0.1:59181 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.994203500+08:00" level=info msg="[TCP] 127.0.0.1:59152 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.994203500+08:00" level=info msg="[TCP] 127.0.0.1:59177 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.994203500+08:00" level=info msg="[TCP] 127.0.0.1:59148 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.995351100+08:00" level=info msg="[TCP] 127.0.0.1:59146 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:30.995351100+08:00" level=info msg="[TCP] 127.0.0.1:59163 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.013011900+08:00" level=info msg="[TCP] 127.0.0.1:59186 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.013011900+08:00" level=info msg="[TCP] 127.0.0.1:59184 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.015011300+08:00" level=info msg="[TCP] 127.0.0.1:59185 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.036597200+08:00" level=info msg="[TCP] 127.0.0.1:59183 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.042499200+08:00" level=info msg="[TCP] 127.0.0.1:58940 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.058911700+08:00" level=info msg="[TCP] 127.0.0.1:59160 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.058911700+08:00" level=info msg="[TCP] 127.0.0.1:59142 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.062930600+08:00" level=info msg="[TCP] 127.0.0.1:59172 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.062930600+08:00" level=info msg="[TCP] 127.0.0.1:59153 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.072560900+08:00" level=info msg="[TCP] 127.0.0.1:59221 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.074821200+08:00" level=info msg="[TCP] 127.0.0.1:59226 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.077293300+08:00" level=info msg="[TCP] 127.0.0.1:59219 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.077293300+08:00" level=info msg="[TCP] 127.0.0.1:59209 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.077293300+08:00" level=info msg="[TCP] 127.0.0.1:59215 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.077848600+08:00" level=info msg="[TCP] 127.0.0.1:59223 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.078358500+08:00" level=info msg="[TCP] 127.0.0.1:59238 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.079957100+08:00" level=info msg="[TCP] 127.0.0.1:59247 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.081027500+08:00" level=info msg="[TCP] 127.0.0.1:59249 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.081027500+08:00" level=info msg="[TCP] 127.0.0.1:59237 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.081027500+08:00" level=info msg="[TCP] 127.0.0.1:59232 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.081862600+08:00" level=info msg="[TCP] 127.0.0.1:59251 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.082741800+08:00" level=info msg="[TCP] 127.0.0.1:59229 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.083698300+08:00" level=info msg="[TCP] 127.0.0.1:59233 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.083698300+08:00" level=info msg="[TCP] 127.0.0.1:59246 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.083698300+08:00" level=info msg="[TCP] 127.0.0.1:59248 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.083698300+08:00" level=info msg="[TCP] 127.0.0.1:59263 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.083698300+08:00" level=info msg="[TCP] 127.0.0.1:59272 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.083698300+08:00" level=info msg="[TCP] 127.0.0.1:59234 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.085788700+08:00" level=info msg="[TCP] 127.0.0.1:59257 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.085788700+08:00" level=info msg="[TCP] 127.0.0.1:59239 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.085788700+08:00" level=info msg="[TCP] 127.0.0.1:59220 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.085788700+08:00" level=info msg="[TCP] 127.0.0.1:59254 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.085788700+08:00" level=info msg="[TCP] 127.0.0.1:59268 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.085788700+08:00" level=info msg="[TCP] 127.0.0.1:59262 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.085788700+08:00" level=info msg="[TCP] 127.0.0.1:59242 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.086302700+08:00" level=info msg="[TCP] 127.0.0.1:59267 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.086302700+08:00" level=info msg="[TCP] 127.0.0.1:59256 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.089880500+08:00" level=info msg="[TCP] 127.0.0.1:59269 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.089880500+08:00" level=info msg="[TCP] 127.0.0.1:59277 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.089880500+08:00" level=info msg="[TCP] 127.0.0.1:59276 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.089880500+08:00" level=info msg="[TCP] 127.0.0.1:59261 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.090389600+08:00" level=info msg="[TCP] 127.0.0.1:59225 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.090389600+08:00" level=info msg="[TCP] 127.0.0.1:59214 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.092157900+08:00" level=info msg="[TCP] 127.0.0.1:59222 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.092157900+08:00" level=info msg="[TCP] 127.0.0.1:59270 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.092692400+08:00" level=info msg="[TCP] 127.0.0.1:59252 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.092692400+08:00" level=info msg="[TCP] 127.0.0.1:59235 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.092692400+08:00" level=info msg="[TCP] 127.0.0.1:59230 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.092692400+08:00" level=info msg="[TCP] 127.0.0.1:59245 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.092692400+08:00" level=info msg="[TCP] 127.0.0.1:59284 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.093202700+08:00" level=info msg="[TCP] 127.0.0.1:59283 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.093234100+08:00" level=info msg="[TCP] 127.0.0.1:59281 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.093234100+08:00" level=info msg="[TCP] 127.0.0.1:59287 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.093234100+08:00" level=info msg="[TCP] 127.0.0.1:59297 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.096769900+08:00" level=info msg="[TCP] 127.0.0.1:59294 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.096769900+08:00" level=info msg="[TCP] 127.0.0.1:59315 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.096769900+08:00" level=info msg="[TCP] 127.0.0.1:59304 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.096769900+08:00" level=info msg="[TCP] 127.0.0.1:59313 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.096769900+08:00" level=info msg="[TCP] 127.0.0.1:59302 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.096769900+08:00" level=info msg="[TCP] 127.0.0.1:59299 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.096769900+08:00" level=info msg="[TCP] 127.0.0.1:59326 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.097794600+08:00" level=info msg="[TCP] 127.0.0.1:59306 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.097794600+08:00" level=info msg="[TCP] 127.0.0.1:59328 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.102518800+08:00" level=info msg="[TCP] 127.0.0.1:59293 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.102518800+08:00" level=info msg="[TCP] 127.0.0.1:59318 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.102518800+08:00" level=info msg="[TCP] 127.0.0.1:59312 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.102518800+08:00" level=info msg="[TCP] 127.0.0.1:59320 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.103030700+08:00" level=info msg="[TCP] 127.0.0.1:59305 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.103030700+08:00" level=info msg="[TCP] 127.0.0.1:59327 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.103030700+08:00" level=info msg="[TCP] 127.0.0.1:59300 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.103030700+08:00" level=info msg="[TCP] 127.0.0.1:59324 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.105536900+08:00" level=info msg="[TCP] 127.0.0.1:59317 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.107574600+08:00" level=info msg="[TCP] 127.0.0.1:59298 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.107574600+08:00" level=info msg="[TCP] 127.0.0.1:59291 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.107574600+08:00" level=info msg="[TCP] 127.0.0.1:59311 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.107574600+08:00" level=info msg="[TCP] 127.0.0.1:59278 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.160223000+08:00" level=info msg="[TCP] 127.0.0.1:59228 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.160223000+08:00" level=info msg="[TCP] 127.0.0.1:59216 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.160781300+08:00" level=info msg="[TCP] 127.0.0.1:59244 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.162532600+08:00" level=info msg="[TCP] 127.0.0.1:59255 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.162532600+08:00" level=info msg="[TCP] 127.0.0.1:59224 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.167603200+08:00" level=info msg="[TCP] 127.0.0.1:59259 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.168625900+08:00" level=info msg="[TCP] 127.0.0.1:59240 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.168625900+08:00" level=info msg="[TCP] 127.0.0.1:59309 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.168625900+08:00" level=info msg="[TCP] 127.0.0.1:59321 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.168625900+08:00" level=info msg="[TCP] 127.0.0.1:59243 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.168625900+08:00" level=info msg="[TCP] 127.0.0.1:58964 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.168625900+08:00" level=info msg="[TCP] 127.0.0.1:59303 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.168625900+08:00" level=info msg="[TCP] 127.0.0.1:59290 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.169137600+08:00" level=info msg="[TCP] 127.0.0.1:59271 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.169137600+08:00" level=info msg="[TCP] 127.0.0.1:59208 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.214444800+08:00" level=info msg="[TCP] 127.0.0.1:59372 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.217413200+08:00" level=info msg="[TCP] 127.0.0.1:59389 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.218430500+08:00" level=info msg="[TCP] 127.0.0.1:59385 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.218430500+08:00" level=info msg="[TCP] 127.0.0.1:59377 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.218939400+08:00" level=info msg="[TCP] 127.0.0.1:59388 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.223263800+08:00" level=info msg="[TCP] 127.0.0.1:59374 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.224763300+08:00" level=info msg="[TCP] 127.0.0.1:59382 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.224763300+08:00" level=info msg="[TCP] 127.0.0.1:59405 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.226223100+08:00" level=info msg="[TCP] 127.0.0.1:59398 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.227764300+08:00" level=info msg="[TCP] 127.0.0.1:59409 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.228272900+08:00" level=info msg="[TCP] 127.0.0.1:59397 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.229305600+08:00" level=info msg="[TCP] 127.0.0.1:59383 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.229305600+08:00" level=info msg="[TCP] 127.0.0.1:59401 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.229305600+08:00" level=info msg="[TCP] 127.0.0.1:59403 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.229305600+08:00" level=info msg="[TCP] 127.0.0.1:59393 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.242208100+08:00" level=info msg="[TCP] 127.0.0.1:59396 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.243589600+08:00" level=info msg="[TCP] 127.0.0.1:59408 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.243589600+08:00" level=info msg="[TCP] 127.0.0.1:59381 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.244159500+08:00" level=info msg="[TCP] 127.0.0.1:59392 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.246956700+08:00" level=info msg="[TCP] 127.0.0.1:59378 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.249264700+08:00" level=info msg="[TCP] 127.0.0.1:59384 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.252771100+08:00" level=info msg="[TCP] 127.0.0.1:59182 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.253772200+08:00" level=info msg="[TCP] 127.0.0.1:59167 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.254849800+08:00" level=info msg="[TCP] 127.0.0.1:58918 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.255357200+08:00" level=info msg="[TCP] 127.0.0.1:58922 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.255357200+08:00" level=info msg="[TCP] 127.0.0.1:59231 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.255357200+08:00" level=info msg="[TCP] 127.0.0.1:59157 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.256380100+08:00" level=info msg="[TCP] 127.0.0.1:59149 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.256380100+08:00" level=info msg="[TCP] 127.0.0.1:59322 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.256380100+08:00" level=info msg="[TCP] 127.0.0.1:58934 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.256888800+08:00" level=info msg="[TCP] 127.0.0.1:59173 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.258339300+08:00" level=info msg="[TCP] 127.0.0.1:59307 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.258339300+08:00" level=info msg="[TCP] 127.0.0.1:59265 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.258339300+08:00" level=info msg="[TCP] 127.0.0.1:59329 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.258339300+08:00" level=info msg="[TCP] 127.0.0.1:59236 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.258339300+08:00" level=info msg="[TCP] 127.0.0.1:59310 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.258339300+08:00" level=info msg="[TCP] 127.0.0.1:59296 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.259426300+08:00" level=info msg="[TCP] 127.0.0.1:59151 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.259426300+08:00" level=info msg="[TCP] 127.0.0.1:59210 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.259426300+08:00" level=info msg="[TCP] 127.0.0.1:59250 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.259426300+08:00" level=info msg="[TCP] 127.0.0.1:59273 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.259426300+08:00" level=info msg="[TCP] 127.0.0.1:59179 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.259426300+08:00" level=info msg="[TCP] 127.0.0.1:59264 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.259426300+08:00" level=info msg="[TCP] 127.0.0.1:59386 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.262193000+08:00" level=info msg="[TCP] 127.0.0.1:59402 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.262193000+08:00" level=info msg="[TCP] 127.0.0.1:59314 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.262193000+08:00" level=info msg="[TCP] 127.0.0.1:59282 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.262193000+08:00" level=info msg="[TCP] 127.0.0.1:59295 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.262193000+08:00" level=info msg="[TCP] 127.0.0.1:59241 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.262193000+08:00" level=info msg="[TCP] 127.0.0.1:59218 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.266559400+08:00" level=info msg="[TCP] 127.0.0.1:59371 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.266559400+08:00" level=info msg="[TCP] 127.0.0.1:59379 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.266559400+08:00" level=info msg="[TCP] 127.0.0.1:59400 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.299549000+08:00" level=info msg="[TCP] 127.0.0.1:59441 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.308987800+08:00" level=info msg="[TCP] 127.0.0.1:59438 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.337247300+08:00" level=info msg="[TCP] 127.0.0.1:59373 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.338649300+08:00" level=info msg="[TCP] 127.0.0.1:59380 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.338649300+08:00" level=info msg="[TCP] 127.0.0.1:59391 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.339167600+08:00" level=info msg="[TCP] 127.0.0.1:59399 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.340186200+08:00" level=info msg="[TCP] 127.0.0.1:59407 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.418298500+08:00" level=info msg="[TCP] 127.0.0.1:58996 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.422271100+08:00" level=info msg="[TCP] 127.0.0.1:58993 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.427844200+08:00" level=info msg="[TCP] 127.0.0.1:59390 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.428359000+08:00" level=info msg="[TCP] 127.0.0.1:59394 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.428359000+08:00" level=info msg="[TCP] 127.0.0.1:59376 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.429274900+08:00" level=info msg="[TCP] 127.0.0.1:59406 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.430780400+08:00" level=info msg="[TCP] 127.0.0.1:59375 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.433005300+08:00" level=info msg="[TCP] 127.0.0.1:59404 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.438814900+08:00" level=info msg="[TCP] 127.0.0.1:59487 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.439323900+08:00" level=info msg="[TCP] 127.0.0.1:59489 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.441071900+08:00" level=info msg="[TCP] 127.0.0.1:58979 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.442340800+08:00" level=info msg="[TCP] 127.0.0.1:59490 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.443965800+08:00" level=info msg="[TCP] 127.0.0.1:59440 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.446714300+08:00" level=info msg="[TCP] 127.0.0.1:59439 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.449875600+08:00" level=info msg="[TCP] 127.0.0.1:59488 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.450966400+08:00" level=info msg="[TCP] 127.0.0.1:59011 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.468217900+08:00" level=info msg="[TCP] 127.0.0.1:59008 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.470878900+08:00" level=info msg="[TCP] 127.0.0.1:58992 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.509664800+08:00" level=info msg="[TCP] 127.0.0.1:59036 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.513189100+08:00" level=info msg="[TCP] 127.0.0.1:59022 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.529544800+08:00" level=info msg="[TCP] 127.0.0.1:59041 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.576624100+08:00" level=info msg="[TCP] 127.0.0.1:59534 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.580606800+08:00" level=info msg="[TCP] 127.0.0.1:59023 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.589870100+08:00" level=info msg="[TCP] 127.0.0.1:59539 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.603658800+08:00" level=info msg="[TCP] 127.0.0.1:59556 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.607454200+08:00" level=info msg="[TCP] 127.0.0.1:59536 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.608165500+08:00" level=info msg="[TCP] 127.0.0.1:59555 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.609521400+08:00" level=info msg="[TCP] 127.0.0.1:59554 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.633251500+08:00" level=info msg="[TCP] 127.0.0.1:59092 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.639480000+08:00" level=info msg="[TCP] 127.0.0.1:59557 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.639480000+08:00" level=info msg="[TCP] 127.0.0.1:59084 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.716653900+08:00" level=info msg="[TCP] 127.0.0.1:59098 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.723721500+08:00" level=info msg="[TCP] 127.0.0.1:59019 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.724326300+08:00" level=info msg="[TCP] 127.0.0.1:59043 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.724884300+08:00" level=info msg="[TCP] 127.0.0.1:59033 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.725643200+08:00" level=info msg="[TCP] 127.0.0.1:59038 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.732025600+08:00" level=info msg="[TCP] 127.0.0.1:59537 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.741780800+08:00" level=info msg="[TCP] 127.0.0.1:59538 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.744566500+08:00" level=info msg="[TCP] 127.0.0.1:59598 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.755535700+08:00" level=info msg="[TCP] 127.0.0.1:59553 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.755535700+08:00" level=info msg="[TCP] 127.0.0.1:59094 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.758031500+08:00" level=info msg="[TCP] 127.0.0.1:59078 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.759537500+08:00" level=info msg="[TCP] 127.0.0.1:59597 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.761790500+08:00" level=info msg="[TCP] 127.0.0.1:59101 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.762870600+08:00" level=info msg="[TCP] 127.0.0.1:59079 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.766625600+08:00" level=info msg="[TCP] 127.0.0.1:59608 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.769132000+08:00" level=info msg="[TCP] 127.0.0.1:59607 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.777557100+08:00" level=info msg="[TCP] 127.0.0.1:59090 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.780081700+08:00" level=info msg="[TCP] 127.0.0.1:59552 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.786108900+08:00" level=info msg="[TCP] 127.0.0.1:59130 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.790531500+08:00" level=info msg="[TCP] 127.0.0.1:59606 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.792667500+08:00" level=info msg="[TCP] 127.0.0.1:59140 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.792667500+08:00" level=info msg="[TCP] 127.0.0.1:59137 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.794734400+08:00" level=info msg="[TCP] 127.0.0.1:59615 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.807805800+08:00" level=info msg="[TCP] 127.0.0.1:59605 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.913780700+08:00" level=info msg="[TCP] 127.0.0.1:59646 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.916994500+08:00" level=info msg="[TCP] 127.0.0.1:59645 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.928264500+08:00" level=info msg="[TCP] 127.0.0.1:59651 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.939174300+08:00" level=info msg="[TCP] 127.0.0.1:59613 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.939174300+08:00" level=info msg="[TCP] 127.0.0.1:59638 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.946618000+08:00" level=info msg="[TCP] 127.0.0.1:59661 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.950129500+08:00" level=info msg="[TCP] 127.0.0.1:59174 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.954236400+08:00" level=info msg="[TCP] 127.0.0.1:59162 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.955897700+08:00" level=info msg="[TCP] 127.0.0.1:59653 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.955897700+08:00" level=info msg="[TCP] 127.0.0.1:59652 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.957308800+08:00" level=info msg="[TCP] 127.0.0.1:59144 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.957308800+08:00" level=info msg="[TCP] 127.0.0.1:59145 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.957811800+08:00" level=info msg="[TCP] 127.0.0.1:59150 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.959227300+08:00" level=info msg="[TCP] 127.0.0.1:59165 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.964514400+08:00" level=info msg="[TCP] 127.0.0.1:59168 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:31.994505000+08:00" level=info msg="[TCP] 127.0.0.1:59667 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.004638300+08:00" level=info msg="[TCP] 127.0.0.1:59660 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.013735800+08:00" level=info msg="[TCP] 127.0.0.1:59669 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.076868700+08:00" level=info msg="[TCP] 127.0.0.1:59213 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.078301300+08:00" level=info msg="[TCP] 127.0.0.1:59211 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.084200700+08:00" level=info msg="[TCP] 127.0.0.1:59212 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.085886000+08:00" level=info msg="[TCP] 127.0.0.1:59288 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.088471900+08:00" level=info msg="[TCP] 127.0.0.1:59280 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.088471900+08:00" level=info msg="[TCP] 127.0.0.1:59217 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.089976800+08:00" level=info msg="[TCP] 127.0.0.1:59227 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.094137900+08:00" level=info msg="[TCP] 127.0.0.1:59258 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.094137900+08:00" level=info msg="[TCP] 127.0.0.1:59207 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.101557600+08:00" level=info msg="[TCP] 127.0.0.1:59319 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.102875000+08:00" level=info msg="[TCP] 127.0.0.1:59323 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.103960700+08:00" level=info msg="[TCP] 127.0.0.1:59308 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.103960700+08:00" level=info msg="[TCP] 127.0.0.1:59316 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.111796200+08:00" level=info msg="[TCP] 127.0.0.1:59275 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.116468400+08:00" level=info msg="[TCP] 127.0.0.1:59673 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.116468400+08:00" level=info msg="[TCP] 127.0.0.1:59289 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.116468400+08:00" level=info msg="[TCP] 127.0.0.1:59266 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.116975500+08:00" level=info msg="[TCP] 127.0.0.1:59325 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.117908400+08:00" level=info msg="[TCP] 127.0.0.1:59253 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.119291200+08:00" level=info msg="[TCP] 127.0.0.1:59301 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.119291200+08:00" level=info msg="[TCP] 127.0.0.1:59292 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.120828600+08:00" level=info msg="[TCP] 127.0.0.1:59286 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.122484700+08:00" level=info msg="[TCP] 127.0.0.1:59675 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.147121600+08:00" level=info msg="[TCP] 127.0.0.1:59274 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.178748500+08:00" level=info msg="[TCP] 127.0.0.1:59679 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.199230400+08:00" level=info msg="[TCP] 127.0.0.1:59685 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.214976100+08:00" level=info msg="[TCP] 127.0.0.1:59681 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.225088100+08:00" level=info msg="[TCP] 127.0.0.1:59395 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.227270700+08:00" level=info msg="[TCP] 127.0.0.1:59690 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.229231300+08:00" level=info msg="[TCP] 127.0.0.1:59689 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.237979100+08:00" level=info msg="[TCP] 127.0.0.1:59694 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.243085800+08:00" level=info msg="[TCP] 127.0.0.1:59687 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.254914800+08:00" level=info msg="[TCP] 127.0.0.1:59693 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.257815800+08:00" level=info msg="[TCP] 127.0.0.1:59697 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.267185800+08:00" level=info msg="[TCP] 127.0.0.1:59701 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.287969000+08:00" level=info msg="[TCP] 127.0.0.1:59705 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.294585300+08:00" level=info msg="[TCP] 127.0.0.1:59703 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.298857600+08:00" level=info msg="[TCP] 127.0.0.1:59706 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.307496500+08:00" level=info msg="[TCP] 127.0.0.1:59709 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.311538300+08:00" level=info msg="[TCP] 127.0.0.1:59711 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.317129200+08:00" level=info msg="[TCP] 127.0.0.1:59437 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.351437600+08:00" level=info msg="[TCP] 127.0.0.1:59715 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.357167000+08:00" level=info msg="[TCP] 127.0.0.1:59719 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.359974200+08:00" level=info msg="[TCP] 127.0.0.1:59713 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.365922200+08:00" level=info msg="[TCP] 127.0.0.1:59721 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.368552800+08:00" level=info msg="[TCP] 127.0.0.1:58859 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.371065200+08:00" level=info msg="[TCP] 127.0.0.1:59723 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.371726000+08:00" level=info msg="[TCP] 127.0.0.1:59716 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.451565800+08:00" level=info msg="[TCP] 127.0.0.1:59735 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.457384600+08:00" level=info msg="[TCP] 127.0.0.1:59733 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.488147500+08:00" level=info msg="[TCP] 127.0.0.1:59731 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.489652400+08:00" level=info msg="[TCP] 127.0.0.1:59739 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.493398000+08:00" level=info msg="[TCP] 127.0.0.1:59740 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.529628900+08:00" level=info msg="[TCP] 127.0.0.1:59725 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.551935000+08:00" level=info msg="[TCP] 127.0.0.1:58877 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.564592100+08:00" level=info msg="[TCP] 127.0.0.1:59743 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.578955800+08:00" level=info msg="[TCP] 127.0.0.1:59535 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.609067900+08:00" level=info msg="[TCP] 127.0.0.1:59747 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.623840600+08:00" level=info msg="[TCP] 127.0.0.1:59751 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.644410200+08:00" level=info msg="[TCP] 127.0.0.1:59749 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.654157600+08:00" level=info msg="[TCP] 127.0.0.1:59757 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.666997100+08:00" level=info msg="[TCP] 127.0.0.1:59752 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.670524100+08:00" level=info msg="[TCP] 127.0.0.1:59755 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.677816500+08:00" level=info msg="[TCP] 127.0.0.1:58888 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.714216100+08:00" level=info msg="[TCP] 127.0.0.1:59729 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.719678500+08:00" level=info msg="[TCP] 127.0.0.1:59765 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.733889900+08:00" level=info msg="[TCP] 127.0.0.1:59767 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.743504000+08:00" level=info msg="[TCP] 127.0.0.1:59771 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.745881000+08:00" level=info msg="[TCP] 127.0.0.1:59759 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.865889600+08:00" level=info msg="[TCP] 127.0.0.1:59773 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.869044900+08:00" level=info msg="[TCP] 127.0.0.1:59604 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.877920900+08:00" level=info msg="[TCP] 127.0.0.1:59779 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.976579100+08:00" level=info msg="[TCP] 127.0.0.1:59781 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:32.977788500+08:00" level=info msg="[TCP] 127.0.0.1:59785 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.012894700+08:00" level=info msg="[TCP] 127.0.0.1:59644 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.012894700+08:00" level=info msg="[TCP] 127.0.0.1:59789 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.015833200+08:00" level=info msg="[TCP] 127.0.0.1:59787 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.022427900+08:00" level=info msg="[TCP] 127.0.0.1:59783 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.027971500+08:00" level=info msg="[TCP] 127.0.0.1:59659 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.048819900+08:00" level=info msg="[TCP] 127.0.0.1:58943 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.073281500+08:00" level=info msg="[TCP] 127.0.0.1:59793 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.079732400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58694 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:33.116053500+08:00" level=info msg="[TCP] 127.0.0.1:59614 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.118787800+08:00" level=info msg="[TCP] 127.0.0.1:59777 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.121341600+08:00" level=info msg="[TCP] 127.0.0.1:59671 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.173914500+08:00" level=info msg="[TCP] 127.0.0.1:59677 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.187141500+08:00" level=info msg="[TCP] 127.0.0.1:59797 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.195665100+08:00" level=info msg="[TCP] 127.0.0.1:59799 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.243590500+08:00" level=info msg="[TCP] 127.0.0.1:59683 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.274155700+08:00" level=info msg="[TCP] 127.0.0.1:59699 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.310528800+08:00" level=info msg="[TCP] 127.0.0.1:59801 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.321572000+08:00" level=info msg="[TCP] 127.0.0.1:59807 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.345564300+08:00" level=info msg="[TCP] 127.0.0.1:59809 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.391861600+08:00" level=info msg="[TCP] 127.0.0.1:59811 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.427862100+08:00" level=info msg="[TCP] 127.0.0.1:59815 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.456253400+08:00" level=info msg="[TCP] 127.0.0.1:59006 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.472022400+08:00" level=info msg="[TCP] 127.0.0.1:59727 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.479655400+08:00" level=info msg="[TCP] 127.0.0.1:59791 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.489709500+08:00" level=info msg="[TCP] 127.0.0.1:59825 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.612236000+08:00" level=info msg="[TCP] 127.0.0.1:59795 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.627060300+08:00" level=info msg="[TCP] 127.0.0.1:59836 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.632412300+08:00" level=info msg="[TCP] 127.0.0.1:59031 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.636859400+08:00" level=info msg="[TCP] 127.0.0.1:59085 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.649371400+08:00" level=info msg="[TCP] 127.0.0.1:59840 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.656812400+08:00" level=info msg="[TCP] 127.0.0.1:59842 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.749131700+08:00" level=info msg="[TCP] 127.0.0.1:59805 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.753698200+08:00" level=info msg="[TCP] 127.0.0.1:59769 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.756362900+08:00" level=info msg="[TCP] 127.0.0.1:59813 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.761653000+08:00" level=info msg="[TCP] 127.0.0.1:59854 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.771497700+08:00" level=info msg="[TCP] 127.0.0.1:59819 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.799629300+08:00" level=info msg="[TCP] 127.0.0.1:59829 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.802489100+08:00" level=info msg="[TCP] 127.0.0.1:59844 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.909716500+08:00" level=info msg="[TCP] 127.0.0.1:59775 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.958792000+08:00" level=info msg="[TCP] 127.0.0.1:59166 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:33.982447600+08:00" level=info msg="[TCP] 127.0.0.1:59862 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.004278200+08:00" level=info msg="[TCP] 127.0.0.1:59858 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.021942700+08:00" level=info msg="[TCP] 127.0.0.1:59866 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.087282200+08:00" level=info msg="[TCP] 127.0.0.1:59260 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.088606500+08:00" level=info msg="[TCP] 127.0.0.1:59279 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.140045200+08:00" level=info msg="[TCP] 127.0.0.1:59870 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.140660100+08:00" level=info msg="[TCP] 127.0.0.1:59868 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.155438700+08:00" level=info msg="[TCP] 127.0.0.1:59285 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.225627900+08:00" level=info msg="[TCP] 127.0.0.1:59874 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.226700600+08:00" level=info msg="[TCP] 127.0.0.1:59872 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.231034000+08:00" level=info msg="[TCP] 127.0.0.1:59387 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.258301600+08:00" level=info msg="[TCP] 127.0.0.1:59803 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.289186200+08:00" level=info msg="[TCP] 127.0.0.1:59878 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.372847300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58879 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:34.381216300+08:00" level=info msg="[TCP] 127.0.0.1:59882 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.392835500+08:00" level=info msg="[TCP] 127.0.0.1:59884 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.412542500+08:00" level=info msg="[TCP] 127.0.0.1:59876 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.426807600+08:00" level=info msg="[TCP] 127.0.0.1:59817 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.455420700+08:00" level=info msg="[TCP] 127.0.0.1:59888 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.487409300+08:00" level=info msg="[TCP] 127.0.0.1:59823 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.542945300+08:00" level=info msg="[TCP] 127.0.0.1:59880 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.547633300+08:00" level=info msg="[TCP] 127.0.0.1:59831 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.559471700+08:00" level=info msg="[TCP] 127.0.0.1:59821 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.590158200+08:00" level=info msg="[TCP] 127.0.0.1:59833 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.602625100+08:00" level=info msg="[TCP] 127.0.0.1:59892 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.670645600+08:00" level=info msg="[TCP] 127.0.0.1:59894 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.769533200+08:00" level=info msg="[TCP] 127.0.0.1:59898 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.770044600+08:00" level=info msg="[TCP] 127.0.0.1:59848 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.770044600+08:00" level=info msg="[TCP] 127.0.0.1:59838 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.773345000+08:00" level=info msg="[TCP] 127.0.0.1:59850 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.820056900+08:00" level=info msg="[TCP] 127.0.0.1:59852 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.841394400+08:00" level=info msg="[TCP] 127.0.0.1:59846 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.845423700+08:00" level=info msg="[TCP] 127.0.0.1:59901 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.862811800+08:00" level=info msg="[TCP] 127.0.0.1:59629 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.885611200+08:00" level=info msg="[TCP] 127.0.0.1:59903 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.942184700+08:00" level=info msg="[TCP] 127.0.0.1:59860 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.966690500+08:00" level=info msg="[TCP] 127.0.0.1:59907 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.969055700+08:00" level=info msg="[TCP] 127.0.0.1:59864 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.972301500+08:00" level=info msg="[TCP] 127.0.0.1:59654 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.973798100+08:00" level=info msg="[TCP] 127.0.0.1:59662 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:34.993857100+08:00" level=info msg="[TCP] 127.0.0.1:59909 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.128343600+08:00" level=info msg="[TCP] 127.0.0.1:59905 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.218019700+08:00" level=info msg="[TCP] 127.0.0.1:59913 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.238773000+08:00" level=info msg="[TCP] 127.0.0.1:59915 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.411729000+08:00" level=info msg="[TCP] 127.0.0.1:59886 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.507506600+08:00" level=info msg="[TCP] 127.0.0.1:59919 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.516286200+08:00" level=info msg="[TCP] 127.0.0.1:59890 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.559702000+08:00" level=info msg="[TCP] 127.0.0.1:59917 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.566911800+08:00" level=info msg="[TCP] 127.0.0.1:59737 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.576423900+08:00" level=info msg="[TCP] 127.0.0.1:59745 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.690428600+08:00" level=info msg="[TCP] 127.0.0.1:59923 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.721194200+08:00" level=info msg="[TCP] 127.0.0.1:59896 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.821526100+08:00" level=info msg="[TCP] 127.0.0.1:59927 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.901088300+08:00" level=info msg="[TCP] 127.0.0.1:59761 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.907042700+08:00" level=info msg="[TCP] 127.0.0.1:59763 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.937535200+08:00" level=info msg="[TCP] 127.0.0.1:59929 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:35.962196300+08:00" level=info msg="[TCP] 127.0.0.1:59925 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:36.189712700+08:00" level=info msg="[TCP] 127.0.0.1:59931 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:36.218434800+08:00" level=info msg="[TCP] 127.0.0.1:59935 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:36.281395300+08:00" level=info msg="[TCP] 127.0.0.1:59937 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:36.594463600+08:00" level=info msg="[TCP] 127.0.0.1:59921 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:36.740169100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59616 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:36.762664400+08:00" level=info msg="[TCP] 127.0.0.1:59940 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:36.808620200+08:00" level=info msg="[TCP] 127.0.0.1:59856 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:36.876190500+08:00" level=info msg="[TCP] 127.0.0.1:59944 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:37.017912900+08:00" level=info msg="[TCP] 127.0.0.1:59946 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:37.063068700+08:00" level=info msg="[TCP] 127.0.0.1:59942 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:38.158353500+08:00" level=info msg="[TCP] 127.0.0.1:59911 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:38.219459700+08:00" level=info msg="[TCP] 127.0.0.1:59948 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:38.440270500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59827 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:38.916848300+08:00" level=info msg="[TCP] 127.0.0.1:59954 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:38.933948800+08:00" level=info msg="[TCP] 127.0.0.1:59953 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:38.959153300+08:00" level=info msg="[TCP] 127.0.0.1:59957 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:39.139106500+08:00" level=info msg="[TCP] 127.0.0.1:59961 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:39.243936800+08:00" level=info msg="[TCP] 127.0.0.1:59965 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:39.245136700+08:00" level=info msg="[TCP] 127.0.0.1:59963 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:39.717375900+08:00" level=info msg="[TCP] 127.0.0.1:59968 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:39.804316100+08:00" level=info msg="[TCP] 127.0.0.1:59951 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:40.030482300+08:00" level=info msg="[TCP] 127.0.0.1:59974 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:40.060164600+08:00" level=info msg="[TCP] 127.0.0.1:59972 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:40.803112100+08:00" level=info msg="[TCP] 127.0.0.1:59970 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:41.134619200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59933 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:41.272632900+08:00" level=info msg="[TCP] 127.0.0.1:59981 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:41.288557600+08:00" level=info msg="[TCP] 127.0.0.1:59979 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:41.784527900+08:00" level=info msg="[TCP] 127.0.0.1:59985 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:42.067214500+08:00" level=info msg="[TCP] 127.0.0.1:59959 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:42.093148700+08:00" level=info msg="[TCP] 127.0.0.1:59987 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:42.104040100+08:00" level=info msg="[TCP] 127.0.0.1:59977 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:42.489913600+08:00" level=info msg="[TCP] 127.0.0.1:59983 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T22:57:42.617959100+08:00" level=info msg="[TCP] 127.0.0.1:59991 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:42.690227900+08:00" level=info msg="[TCP] 127.0.0.1:59994 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:43.017006100+08:00" level=info msg="[TCP] 127.0.0.1:59998 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:43.045502000+08:00" level=info msg="[TCP] 127.0.0.1:60000 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:43.823704200+08:00" level=info msg="[TCP] 127.0.0.1:59996 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:44.763175700+08:00" level=info msg="[TCP] 127.0.0.1:60003 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:44.878830300+08:00" level=info msg="[TCP] 127.0.0.1:60005 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:45.578193200+08:00" level=info msg="[TCP] 127.0.0.1:60007 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:45.759210400+08:00" level=info msg="[TCP] 127.0.0.1:60010 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T22:57:45.999177000+08:00" level=info msg="[TCP] 127.0.0.1:60012 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:46.028962100+08:00" level=info msg="[TCP] 127.0.0.1:60014 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:46.646298100+08:00" level=info msg="[TCP] 127.0.0.1:60016 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:47.313094500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59989 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.198.71.225:19862: i/o timeout\ndial tcp 120.241.29.152:19862: i/o timeout"
time="2025-06-09T22:57:48.296565100+08:00" level=info msg="[TCP] 127.0.0.1:60021 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:48.482659400+08:00" level=info msg="[TCP] 127.0.0.1:60023 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T22:57:48.882067000+08:00" level=info msg="[TCP] 127.0.0.1:60026 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:49.052755900+08:00" level=info msg="[TCP] 127.0.0.1:60028 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:50.046378600+08:00" level=info msg="[TCP] 127.0.0.1:60019 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:51.023885000+08:00" level=info msg="[TCP] 127.0.0.1:60035 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:51.070683900+08:00" level=info msg="[TCP] 127.0.0.1:60033 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:51.602615600+08:00" level=info msg="[TCP] 127.0.0.1:60037 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:52.629663700+08:00" level=info msg="[TCP] 127.0.0.1:60030 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:57:53.701673000+08:00" level=info msg="[TCP] 127.0.0.1:60041 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-09T22:58:07.541606600+08:00" level=info msg="[TCP] 127.0.0.1:60052 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:58:08.442920800+08:00" level=info msg="[TCP] 127.0.0.1:60056 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:58:10.472090600+08:00" level=info msg="[TCP] 127.0.0.1:60058 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T22:58:11.316581900+08:00" level=info msg="[TCP] 127.0.0.1:60062 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T22:58:11.669070600+08:00" level=info msg="[TCP] 127.0.0.1:60064 --> download.clashverge.dev:443 using GLOBAL"
time="2025-06-09T22:58:12.186376100+08:00" level=info msg="[TCP] 127.0.0.1:60068 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:58:12.289100100+08:00" level=info msg="[TCP] 127.0.0.1:60066 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-09T22:58:12.883080200+08:00" level=info msg="[TCP] 127.0.0.1:60070 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-09T22:58:14.220094500+08:00" level=info msg="[TCP] 127.0.0.1:60072 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-09T22:58:28.527792100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60097 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T22:58:41.678515100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60113 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T22:58:41.678630400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60119 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T22:58:41.678630400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60123 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T22:58:41.678630400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60122 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T22:58:41.678630400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60118 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T22:58:41.678630400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60125 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T22:59:00.537859200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60159 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T22:59:08.550773700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:60172 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:11:06.207318100+08:00" level=info msg="[TCP] 127.0.0.1:62367 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:11:09.420748800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62359 --> docs.cursor.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:11:09.531416500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62364 --> mintlify.b-cdn.net:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:11:12.438136900+08:00" level=info msg="[TCP] 127.0.0.1:62388 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-09T23:11:14.308209800+08:00" level=info msg="[TCP] 127.0.0.1:62392 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T23:11:14.421578400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62376 --> docs.cursor.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:11:14.477743900+08:00" level=info msg="[TCP] 127.0.0.1:62394 --> mintlify.b-cdn.net:443 using GLOBAL"
time="2025-06-09T23:11:14.532201400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62381 --> mintlify.b-cdn.net:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:11:14.585390300+08:00" level=info msg="[TCP] 127.0.0.1:62396 --> mintlify.b-cdn.net:443 using GLOBAL"
time="2025-06-09T23:11:15.013522900+08:00" level=info msg="[TCP] 127.0.0.1:62398 --> mintlify.b-cdn.net:443 using GLOBAL"
time="2025-06-09T23:11:15.564057000+08:00" level=info msg="[TCP] 127.0.0.1:62400 --> docs.cursor.com:443 using GLOBAL"
time="2025-06-09T23:11:15.909306000+08:00" level=info msg="[TCP] 127.0.0.1:62403 --> docs.cursor.com:443 using GLOBAL"
time="2025-06-09T23:11:15.910896600+08:00" level=info msg="[TCP] 127.0.0.1:62402 --> docs.cursor.com:443 using GLOBAL"
time="2025-06-09T23:11:23.754166700+08:00" level=info msg="[TCP] 127.0.0.1:62414 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T23:11:23.760234000+08:00" level=info msg="[TCP] 127.0.0.1:62410 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T23:11:24.062004800+08:00" level=info msg="[TCP] 127.0.0.1:62416 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T23:11:28.183766800+08:00" level=info msg="[TCP] 127.0.0.1:62420 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:11:29.746294900+08:00" level=info msg="[TCP] 127.0.0.1:62424 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-09T23:11:32.172588700+08:00" level=info msg="[TCP] 127.0.0.1:62427 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:11:34.275977900+08:00" level=info msg="[TCP] 127.0.0.1:62433 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T23:11:34.584329100+08:00" level=info msg="[TCP] 127.0.0.1:62438 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T23:11:34.584329100+08:00" level=info msg="[TCP] 127.0.0.1:62441 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T23:11:34.584329100+08:00" level=info msg="[TCP] 127.0.0.1:62440 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T23:11:34.585354000+08:00" level=info msg="[TCP] 127.0.0.1:62439 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T23:11:34.585354000+08:00" level=info msg="[TCP] 127.0.0.1:62437 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T23:11:36.726453300+08:00" level=info msg="[TCP] 127.0.0.1:62452 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-09T23:11:37.050719100+08:00" level=info msg="[TCP] 127.0.0.1:62457 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-09T23:11:37.079953100+08:00" level=info msg="[TCP] 127.0.0.1:62462 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-09T23:11:37.138151300+08:00" level=info msg="[TCP] 127.0.0.1:62464 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:11:39.055598500+08:00" level=info msg="[TCP] 127.0.0.1:62455 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-09T23:11:39.056990900+08:00" level=info msg="[TCP] 127.0.0.1:62456 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-09T23:11:39.059766300+08:00" level=info msg="[TCP] 127.0.0.1:62454 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-09T23:11:44.195354000+08:00" level=info msg="[TCP] 127.0.0.1:62472 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T23:11:46.098237300+08:00" level=info msg="[TCP] 127.0.0.1:62476 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T23:11:46.518804100+08:00" level=info msg="[TCP] 127.0.0.1:62478 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T23:11:49.143127200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62471 --> user.aliyundrive.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:11:49.197683200+08:00" level=info msg="[TCP] 127.0.0.1:62482 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T23:11:51.766876200+08:00" level=info msg="[TCP] 127.0.0.1:62486 --> g.alicdn.com:443 using GLOBAL"
time="2025-06-09T23:11:52.188103900+08:00" level=info msg="[TCP] 127.0.0.1:62489 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-09T23:11:55.271144300+08:00" level=info msg="[TCP] 127.0.0.1:62494 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T23:12:03.183714200+08:00" level=info msg="[TCP] 127.0.0.1:62505 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-09T23:12:12.187431700+08:00" level=info msg="[TCP] 127.0.0.1:62519 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:12:14.545802000+08:00" level=info msg="[TCP] 127.0.0.1:62526 --> cursor-cdn.com:443 using GLOBAL"
time="2025-06-09T23:12:15.507339100+08:00" level=info msg="[TCP] 127.0.0.1:62522 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:12:15.509089700+08:00" level=info msg="[TCP] 127.0.0.1:62521 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:12:16.234050900+08:00" level=info msg="[TCP] 127.0.0.1:62529 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-09T23:12:17.125058200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62513 --> i0.hdslb.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:12:17.125571700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62515 --> api.vc.bilibili.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:12:17.132061500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62517 --> data.bilibili.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:12:17.179604400+08:00" level=info msg="[TCP] 127.0.0.1:62531 --> i0.hdslb.com:443 using GLOBAL"
time="2025-06-09T23:12:17.183463500+08:00" level=info msg="[TCP] 127.0.0.1:62533 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:12:27.282273100+08:00" level=info msg="[TCP] 127.0.0.1:62547 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.874467100+08:00" level=info msg="[TCP] 127.0.0.1:62555 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.874467100+08:00" level=info msg="[TCP] 127.0.0.1:62556 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.876228900+08:00" level=info msg="[TCP] 127.0.0.1:62554 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.877092400+08:00" level=info msg="[TCP] 127.0.0.1:62557 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.877092400+08:00" level=info msg="[TCP] 127.0.0.1:62562 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.878395400+08:00" level=info msg="[TCP] 127.0.0.1:62552 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.878395400+08:00" level=info msg="[TCP] 127.0.0.1:62563 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.879409000+08:00" level=info msg="[TCP] 127.0.0.1:62551 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.879409000+08:00" level=info msg="[TCP] 127.0.0.1:62560 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.879409000+08:00" level=info msg="[TCP] 127.0.0.1:62561 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.880134600+08:00" level=info msg="[TCP] 127.0.0.1:62553 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.881148200+08:00" level=info msg="[TCP] 127.0.0.1:62559 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.881148200+08:00" level=info msg="[TCP] 127.0.0.1:62558 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.928205800+08:00" level=info msg="[TCP] 127.0.0.1:62577 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.928205800+08:00" level=info msg="[TCP] 127.0.0.1:62578 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.956373900+08:00" level=info msg="[TCP] 127.0.0.1:62544 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.959942700+08:00" level=info msg="[TCP] 127.0.0.1:62597 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T23:12:29.981464100+08:00" level=info msg="[TCP] 127.0.0.1:62582 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.985255100+08:00" level=info msg="[TCP] 127.0.0.1:62581 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:29.986311000+08:00" level=info msg="[TCP] 127.0.0.1:62583 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:30.308790400+08:00" level=info msg="[TCP] 127.0.0.1:62588 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:30.308790400+08:00" level=info msg="[TCP] 127.0.0.1:62589 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:30.316260200+08:00" level=info msg="[TCP] 127.0.0.1:62601 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:30.316260200+08:00" level=info msg="[TCP] 127.0.0.1:62602 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:30.499901200+08:00" level=info msg="[TCP] 127.0.0.1:62549 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:30.842568800+08:00" level=info msg="[TCP] 127.0.0.1:62608 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:31.688502300+08:00" level=info msg="[TCP] 127.0.0.1:62612 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:31.775416600+08:00" level=info msg="[TCP] 127.0.0.1:62614 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-06-09T23:12:32.002960900+08:00" level=info msg="[TCP] 127.0.0.1:62617 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:36.273831900+08:00" level=info msg="[TCP] 127.0.0.1:62623 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:42.117559200+08:00" level=info msg="[TCP] 127.0.0.1:62631 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:51.527269800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62636 --> www.bing.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:12:53.496929800+08:00" level=info msg="[TCP] 127.0.0.1:62644 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:53.966000000+08:00" level=info msg="[TCP] 127.0.0.1:62647 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:54.601908300+08:00" level=info msg="[TCP] 127.0.0.1:62642 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T23:12:58.503123000+08:00" level=info msg="[TCP] 127.0.0.1:62660 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:12:59.454880600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62649 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:12:59.511200900+08:00" level=info msg="[TCP] 127.0.0.1:62662 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:00.453158200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62652 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:01.444824600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62654 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:01.503512200+08:00" level=info msg="[TCP] 127.0.0.1:62668 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:01.521032900+08:00" level=info msg="[TCP] 127.0.0.1:62665 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:02.444034500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62657 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:02.498435100+08:00" level=info msg="[TCP] 127.0.0.1:62670 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:03.496609300+08:00" level=info msg="[TCP] 127.0.0.1:62673 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:03.889613000+08:00" level=info msg="[TCP] 127.0.0.1:62677 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-09T23:13:03.892119400+08:00" level=info msg="[TCP] 127.0.0.1:62676 --> cursor.com:443 using GLOBAL"
time="2025-06-09T23:13:04.632900700+08:00" level=info msg="[TCP] 127.0.0.1:62692 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:04.835337700+08:00" level=info msg="[TCP] 127.0.0.1:62694 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:05.501149200+08:00" level=info msg="[TCP] 127.0.0.1:62703 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:05.623844700+08:00" level=info msg="[TCP] 127.0.0.1:62705 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:05.827331100+08:00" level=info msg="[TCP] 127.0.0.1:62707 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:06.023634300+08:00" level=info msg="[TCP] 127.0.0.1:62696 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:06.224915400+08:00" level=info msg="[TCP] 127.0.0.1:62698 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:06.324599000+08:00" level=info msg="[TCP] 127.0.0.1:62710 --> www.cursor.com:443 using GLOBAL"
time="2025-06-09T23:13:07.510706400+08:00" level=info msg="[TCP] 127.0.0.1:62715 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:08.503003000+08:00" level=info msg="[TCP] 127.0.0.1:62717 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:08.970448700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62680 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:09.015287100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62682 --> nav-edge.smartscreen.microsoft.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:09.066907900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62684 --> xpaywalletcdn-prod.azureedge.net:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:09.123769100+08:00" level=info msg="[TCP] 127.0.0.1:62720 --> xpaywalletcdn-prod.azureedge.net:443 using GLOBAL"
time="2025-06-09T23:13:09.172610000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62686 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:09.374818400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62688 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:09.452668800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62690 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:10.370421600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62701 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:11.455806100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62712 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:13.525435100+08:00" level=info msg="[TCP] 127.0.0.1:62725 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:14.453430900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62722 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:14.510891900+08:00" level=info msg="[TCP] 127.0.0.1:62737 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:14.523373600+08:00" level=info msg="[TCP] 127.0.0.1:62735 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:14.525331800+08:00" level=info msg="[TCP] 127.0.0.1:62727 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:17.124645900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62730 --> data.bilibili.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:17.179878800+08:00" level=info msg="[TCP] 127.0.0.1:62745 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:13:17.457796500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62732 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:17.511848700+08:00" level=info msg="[TCP] 127.0.0.1:62747 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:17.523227000+08:00" level=info msg="[TCP] 127.0.0.1:62743 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:18.509150600+08:00" level=info msg="[TCP] 127.0.0.1:62750 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:18.515376700+08:00" level=info msg="[TCP] 127.0.0.1:62740 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:19.508925300+08:00" level=info msg="[TCP] 127.0.0.1:62753 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:20.500107500+08:00" level=info msg="[TCP] 127.0.0.1:62755 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:21.499215200+08:00" level=info msg="[TCP] 127.0.0.1:62758 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:22.505668600+08:00" level=info msg="[TCP] 127.0.0.1:62761 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:25.503494600+08:00" level=info msg="[TCP] 127.0.0.1:62774 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:25.514827800+08:00" level=info msg="[TCP] 127.0.0.1:62771 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:28.332861700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62766 --> api.vc.bilibili.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:28.458143700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62768 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:29.401070600+08:00" level=info msg="[TCP] 127.0.0.1:62784 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:13:29.509157400+08:00" level=info msg="[TCP] 127.0.0.1:62788 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:29.522674700+08:00" level=info msg="[TCP] 127.0.0.1:62786 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:31.454481400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62776 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:32.449600100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62779 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:32.699802700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62781 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:33.508291600+08:00" level=info msg="[TCP] 127.0.0.1:62799 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:34.500698400+08:00" level=info msg="[TCP] 127.0.0.1:62802 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:34.527558200+08:00" level=info msg="[TCP] 127.0.0.1:62794 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:35.453687900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62791 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:35.505035900+08:00" level=info msg="[TCP] 127.0.0.1:62804 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:37.448709700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62796 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:38.512230100+08:00" level=info msg="[TCP] 127.0.0.1:62812 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:38.512230100+08:00" level=info msg="[TCP] 127.0.0.1:62810 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:39.511963000+08:00" level=info msg="[TCP] 127.0.0.1:62815 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:39.526530200+08:00" level=info msg="[TCP] 127.0.0.1:62807 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:40.516762200+08:00" level=info msg="[TCP] 127.0.0.1:62818 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:41.260797000+08:00" level=info msg="[TCP] 127.0.0.1:62820 --> mintlify.s3.us-west-1.amazonaws.com:443 using GLOBAL"
time="2025-06-09T23:13:41.505639700+08:00" level=info msg="[TCP] 127.0.0.1:62822 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:42.509002200+08:00" level=info msg="[TCP] 127.0.0.1:62828 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:43.499456700+08:00" level=info msg="[TCP] 127.0.0.1:62832 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:44.109463900+08:00" level=info msg="[TCP] 127.0.0.1:62834 --> upos-sz-mirror08h.bilivideo.com:443 using GLOBAL"
time="2025-06-09T23:13:44.257494100+08:00" level=info msg="[TCP] 127.0.0.1:62836 --> upos-sz-mirror08h.bilivideo.com:443 using GLOBAL"
time="2025-06-09T23:13:44.509705300+08:00" level=info msg="[TCP] 127.0.0.1:62838 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:45.803986700+08:00" level=info msg="[TCP] 127.0.0.1:62845 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T23:13:46.961717400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62824 --> xy121x31x234x228xy2408y875cy5000y8yy528xy.mcdn.bilivideo.cn:4483 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:47.187343900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62826 --> mintlify.s3.us-west-1.amazonaws.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:49.940774700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62840 --> api.bilibili.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:50.448893700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62842 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:51.455062500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62849 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:51.503899900+08:00" level=info msg="[TCP] 127.0.0.1:62876 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:51.516257400+08:00" level=info msg="[TCP] 127.0.0.1:62874 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:51.591895500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62851 --> www.bing.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:51.813024800+08:00" level=info msg="[TCP] 127.0.0.1:62881 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T23:13:51.888705300+08:00" level=info msg="[TCP] 127.0.0.1:62867 --> hw-v2-web-player-tracker.biliapi.net:443 using GLOBAL"
time="2025-06-09T23:13:51.961873900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62853 --> xy121x31x234x228xy2408y875cy5000y8yy528xy.mcdn.bilivideo.cn:4483 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:52.148343700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62855 --> api.bilibili.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:52.188386600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62857 --> mintlify.s3.us-west-1.amazonaws.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:52.456132600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62859 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:53.455849700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62861 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:53.823890800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62865 --> security.bilibili.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:54.444771100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62870 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:54.510791500+08:00" level=info msg="[TCP] 127.0.0.1:62896 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:54.832876200+08:00" level=info msg="[TCP] 127.0.0.1:62885 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-09T23:13:54.833929900+08:00" level=info msg="[TCP] 127.0.0.1:62883 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-09T23:13:54.875713200+08:00" level=info msg="[TCP] 127.0.0.1:62898 --> security.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:13:54.942158900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62872 --> api.bilibili.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:55.217739400+08:00" level=info msg="[TCP] 127.0.0.1:62887 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:13:55.576550800+08:00" level=info msg="[TCP] 127.0.0.1:62894 --> mintlify.s3.us-west-1.amazonaws.com:443 using GLOBAL"
time="2025-06-09T23:13:56.592514500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62878 --> www.bing.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:57.455994700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62889 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:57.474319900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62891 --> g.alicdn.com:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:13:57.498649500+08:00" level=info msg="[TCP] 127.0.0.1:62908 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:57.514268700+08:00" level=info msg="[TCP] 127.0.0.1:62906 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:57.524781100+08:00" level=info msg="[TCP] 127.0.0.1:62900 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:57.529697200+08:00" level=info msg="[TCP] 127.0.0.1:62910 --> g.alicdn.com:443 using GLOBAL"
time="2025-06-09T23:13:58.526602700+08:00" level=info msg="[TCP] 127.0.0.1:62903 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:59.509187500+08:00" level=info msg="[TCP] 127.0.0.1:62921 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:59.519605400+08:00" level=info msg="[TCP] 127.0.0.1:62913 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:13:59.526410400+08:00" level=info msg="[TCP] 127.0.0.1:62923 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-09T23:14:02.697049400+08:00" level=info msg="[TCP] 127.0.0.1:62942 --> cn.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:03.283823700+08:00" level=info msg="[TCP] 127.0.0.1:62944 --> cn.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:03.317803300+08:00" level=info msg="[TCP] 127.0.0.1:62946 --> cn.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:03.392329200+08:00" level=info msg="[TCP] 127.0.0.1:62934 --> cn.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:03.406040900+08:00" level=info msg="[TCP] 127.0.0.1:62936 --> www.google-analytics.com:443 using GLOBAL"
time="2025-06-09T23:14:03.495323700+08:00" level=info msg="[TCP] 127.0.0.1:62940 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:03.499658800+08:00" level=info msg="[TCP] 127.0.0.1:62948 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:03.500651600+08:00" level=info msg="[TCP] 127.0.0.1:62938 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-09T23:14:03.510857700+08:00" level=info msg="[TCP] 127.0.0.1:62927 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:04.064783600+08:00" level=info msg="[TCP] 127.0.0.1:62951 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-09T23:14:04.501698400+08:00" level=info msg="[TCP] 127.0.0.1:62953 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:04.521109900+08:00" level=info msg="[TCP] 127.0.0.1:62931 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:05.795887800+08:00" level=info msg="[TCP] 127.0.0.1:62956 --> c.msn.cn:443 using GLOBAL"
time="2025-06-09T23:14:05.810807700+08:00" level=info msg="[TCP] 127.0.0.1:62960 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-09T23:14:05.811876700+08:00" level=info msg="[TCP] 127.0.0.1:62958 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-09T23:14:05.843606500+08:00" level=info msg="[TCP] 127.0.0.1:62962 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-09T23:14:06.639376500+08:00" level=info msg="[TCP] 127.0.0.1:62971 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:08.193871000+08:00" level=info msg="[TCP] 127.0.0.1:62979 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-09T23:14:08.508219300+08:00" level=info msg="[TCP] 127.0.0.1:62981 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:08.519837300+08:00" level=info msg="[TCP] 127.0.0.1:62974 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:08.523042500+08:00" level=info msg="[TCP] 127.0.0.1:62967 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:08.551720300+08:00" level=info msg="[TCP] 127.0.0.1:62976 --> c.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:08.688363900+08:00" level=info msg="[TCP] 127.0.0.1:62983 --> www.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:10.079041000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62964 --> assets.msn.cn:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:14:10.508442400+08:00" level=info msg="[TCP] 127.0.0.1:62993 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:10.510949700+08:00" level=info msg="[TCP] 127.0.0.1:62991 --> th.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:10.909196000+08:00" level=info msg="[TCP] 127.0.0.1:63003 --> r.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:11.178196700+08:00" level=info msg="[TCP] 127.0.0.1:62996 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-06-09T23:14:11.183276800+08:00" level=info msg="[TCP] 127.0.0.1:62998 --> data.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:14:11.448417800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62969 --> api2.cursor.sh:443 error: h.cm2.xiaomi-api.xyz:19862 connect error: connect failed: dial tcp 120.241.29.152:19862: i/o timeout\ndial tcp 120.198.71.225:19862: i/o timeout"
time="2025-06-09T23:14:11.504812600+08:00" level=info msg="[TCP] 127.0.0.1:63005 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:11.514120200+08:00" level=info msg="[TCP] 127.0.0.1:63000 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:11.906625600+08:00" level=info msg="[TCP] 127.0.0.1:63007 --> th.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:12.141018900+08:00" level=info msg="[TCP] 127.0.0.1:63010 --> th.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:12.143846700+08:00" level=info msg="[TCP] 127.0.0.1:63011 --> th.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:12.143846700+08:00" level=info msg="[TCP] 127.0.0.1:63012 --> th.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:12.144361300+08:00" level=info msg="[TCP] 127.0.0.1:63009 --> th.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:12.179937400+08:00" level=info msg="[TCP] 127.0.0.1:63017 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:14:12.503759700+08:00" level=info msg="[TCP] 127.0.0.1:62988 --> th.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:12.506337000+08:00" level=info msg="[TCP] 127.0.0.1:62986 --> r.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:12.508691000+08:00" level=info msg="[TCP] 127.0.0.1:62985 --> r.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:12.509816900+08:00" level=info msg="[TCP] 127.0.0.1:63019 --> api2.cursor.sh:443 using GLOBAL"
time="2025-06-09T23:14:12.819094100+08:00" level=info msg="[TCP] 127.0.0.1:63026 --> api.bilibili.com:443 using GLOBAL"
time="2025-06-09T23:14:12.832243000+08:00" level=info msg="[TCP] 127.0.0.1:63032 --> g.alicdn.com:443 using GLOBAL"
time="2025-06-09T23:14:12.844925800+08:00" level=info msg="[TCP] 127.0.0.1:63033 --> user.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T23:14:12.845621200+08:00" level=info msg="[TCP] 127.0.0.1:63036 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T23:14:12.850729700+08:00" level=info msg="[TCP] 127.0.0.1:63034 --> www.aliyundrive.com:443 using GLOBAL"
time="2025-06-09T23:14:12.851237800+08:00" level=info msg="[TCP] 127.0.0.1:63031 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-09T23:14:12.886060200+08:00" level=info msg="[TCP] 127.0.0.1:63030 --> th.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:12.947128200+08:00" level=info msg="[TCP] 127.0.0.1:63029 --> r.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:13.078920400+08:00" level=info msg="[TCP] 127.0.0.1:63050 --> cn.bing.com:443 using GLOBAL"
time="2025-06-09T23:14:17.782278400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63021 --> s-gm.mmstat.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:17.782789000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63087 --> s-gm.mmstat.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:17.794303600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63035 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:17.794356300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63053 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:17.794356300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63055 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:18.142739200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63052 --> storage.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:18.456541400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63054 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:18.456619300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63086 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:18.456619300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63073 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:18.456619300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63057 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:18.456619300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63108 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:18.456619300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63065 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:22.795012800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63100 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:22.795012800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63099 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:22.795083400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63101 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:23.143387800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63103 --> storage.live.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:23.456934400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63131 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:23.456934400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63122 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:23.456934400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63114 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:23.456934400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63124 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:23.456934400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63119 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:28.457084300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63149 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:28.457084300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63156 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:28.457084300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63147 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:28.457084300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63139 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:28.457084300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63153 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:28.457084300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63154 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:33.457293000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63171 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:33.457293000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63165 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:33.457293000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63162 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:33.457293000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63167 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:33.457293000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63163 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:38.457563000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63185 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:38.457563000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63179 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:38.457563000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63183 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:38.457563000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63177 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:38.457563000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63181 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:43.458094900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63198 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:43.458094900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63192 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:43.458094900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63195 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:43.458094900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63190 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:43.458094900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63194 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:48.458163700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63204 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:48.458163700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63206 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:48.458163700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63209 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:48.458163700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63210 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:48.458163700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63205 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:53.458962100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63220 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:53.458962100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63221 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:53.458962100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63215 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:53.458962100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63218 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:53.458962100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63224 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:58.459350700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63229 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:58.459350700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63235 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:58.459350700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63231 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:58.459350700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63238 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:14:58.459350700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63237 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:03.460354800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63252 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:03.460354800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63247 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:03.460354800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63246 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:03.460354800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63249 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:03.460354800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63244 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:08.460587700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63265 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:08.460587700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63258 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:08.460587700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63263 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:08.460587700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63260 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:08.460587700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63262 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:13.460751700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63280 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:13.460751700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63271 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:13.460751700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63272 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:13.460751700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63274 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:13.460751700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63279 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:18.461253200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63291 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:18.461253200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63289 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:18.461253200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63288 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:18.461253200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63286 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:18.461253200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63293 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:23.461264500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63298 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:23.461264500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63304 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:23.461264500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63307 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:23.461264500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63300 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:23.461264500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63302 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:28.461707800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63320 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:28.461707800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63321 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:28.461707800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63313 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:28.461707800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63319 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:28.461707800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63314 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:28.461707800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63317 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:33.462571100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63332 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:33.462571100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63334 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:33.462571100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63330 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:33.462571100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63327 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:33.462571100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63329 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:38.462651900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63343 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:38.462651900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63339 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:38.462651900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63346 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:38.462651900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63341 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:38.462651900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63349 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:43.463003800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63361 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:43.463003800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63358 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:43.463003800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63355 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:43.463003800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63360 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:43.463003800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63356 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:48.463206200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63375 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:48.463206200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63371 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:48.463206200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63370 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:48.463206200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63368 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:48.463206200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63372 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:53.463807100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63386 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:53.463807100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63380 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:53.463807100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63381 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:53.463807100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63384 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:53.463807100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63385 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:58.463840500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63399 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:58.463840500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63398 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:58.463840500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63395 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:58.463840500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63393 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:15:58.463840500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63394 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:03.464207800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63414 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:03.464207800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63410 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:03.464207800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63409 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:03.464207800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63405 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:03.464207800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63411 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:08.465037100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63425 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:08.465037100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63422 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:08.465037100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63424 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:08.465037100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63420 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:08.465037100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63419 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:13.465544500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63440 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:13.465544500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63431 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:13.465544500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63433 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:13.465544500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63434 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:13.465544500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63438 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:18.466116100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63449 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:18.466116100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63452 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:18.466116100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63450 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:18.466116100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63447 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:18.466116100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63445 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:23.466273300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63459 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:23.466273300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63461 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:23.466273300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63463 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:23.466273300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63465 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:23.466273300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63458 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:28.467336800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63480 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:28.467336800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63477 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:28.467336800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63473 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:28.467336800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63474 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:28.467336800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63478 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:28.467336800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63471 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:33.468233100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63491 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:33.468233100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63485 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:33.468233100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63490 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:33.468233100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63493 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:33.468233100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63487 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:38.468456900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63506 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:38.468456900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63501 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:38.468456900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63505 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:38.468456900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63500 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:38.468456900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63503 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:43.468893600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63522 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:43.468893600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63516 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:43.468893600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63519 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:43.468893600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63514 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:43.468893600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63512 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:48.469539900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63535 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:48.469539900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63533 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:48.469539900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63527 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:48.469539900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63529 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:48.469539900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63531 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:53.469703900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63547 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:53.469703900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63542 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:53.469703900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63541 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:53.469703900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63546 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:53.469703900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63544 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:58.470087500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63556 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:58.470087500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63558 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:58.470087500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63552 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:58.470087500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63557 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:16:58.470087500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63561 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:03.470122500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63572 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:03.470122500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63567 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:03.470122500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63571 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:03.470122500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63566 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:03.470122500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63570 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:08.470611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63588 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:08.470611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63580 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:08.470611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63582 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:08.470611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63587 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:08.470611400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63579 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:13.470940200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63602 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:13.470940200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63595 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:13.470940200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63593 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:13.470940200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63600 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:13.470940200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63597 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:18.471249500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63614 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:18.471249500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63609 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:18.471249500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63608 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:18.471249500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63611 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:18.471249500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63613 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:23.471330700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63628 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:23.471330700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63622 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:23.471330700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63620 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:23.471330700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63626 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:23.471330700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63624 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63641 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63635 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63646 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63637 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63638 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63643 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63642 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63644 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63639 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63633 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:28.472082200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63640 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:33.472265000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63667 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:33.472265000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63653 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:33.472265000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63652 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:33.472265000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63655 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:33.472265000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63662 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:38.472828000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63678 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:38.472828000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63685 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:38.472828000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63681 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:38.472828000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63676 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:38.472828000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63680 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:38.472828000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63683 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:43.472958700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63690 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:43.472958700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63697 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:43.473468100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63692 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:43.472958700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63695 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:43.472958700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63694 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:48.473709400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63710 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:48.473709400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63707 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:48.473709400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63704 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:48.473709400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63705 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:48.473709400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63709 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:53.474149800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63723 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:53.474149800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63716 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:53.474149800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63721 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:53.474149800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63718 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:53.474149800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63719 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:58.474691800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63729 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:58.474691800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63737 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:58.474691800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63732 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:58.474691800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63733 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:17:58.474691800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63728 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:03.474831100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63750 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:03.474831100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63744 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:03.474831100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63745 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:03.474831100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63746 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:03.474831100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63749 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:08.475666300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63763 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:08.475666300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63760 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:08.475666300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63755 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:08.475666300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63759 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:08.475666300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63758 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:13.476073100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63776 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:13.476073100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63769 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:13.476073100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63775 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:13.476073100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63768 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:13.476073100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63772 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:18.476605200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63790 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:18.476605200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63785 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:18.476605200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63788 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:18.476605200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63784 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:18.476605200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63786 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:23.476772500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63806 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:23.476772500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63797 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:23.476772500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63801 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:23.476772500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63802 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:23.476772500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63799 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:28.477026900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63821 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:28.477026900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63812 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:28.477026900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63817 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:28.477026900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63815 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:28.477026900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63819 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:28.477026900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63820 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:33.477041000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63827 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:33.477041000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63834 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:33.477041000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63829 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:33.477041000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63832 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:33.477041000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63830 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:38.478047600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63846 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:38.478047600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63841 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:38.478047600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63844 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:38.478047600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63839 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:38.478047600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63843 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:43.478263400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63855 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:43.478263400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63857 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:43.478263400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63853 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:43.478263400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63858 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:43.478263400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63852 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:48.478974500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63867 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:48.478974500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63871 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:48.478974500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63873 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:48.478974500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63864 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:48.478974500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63875 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:53.479702500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63889 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:53.479702500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63887 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:53.479702500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63884 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:53.479702500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63882 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:53.479702500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63880 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:58.479793000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63904 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:58.479793000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63900 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:58.479793000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63903 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:58.479793000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63895 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:18:58.479793000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63899 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:03.480177000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63928 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:03.480177000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63913 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:03.480177000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63909 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:03.480177000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63915 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:03.480177000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63912 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:08.480742400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63937 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:08.480742400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63940 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:08.480742400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63934 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:08.480742400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63942 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:08.480742400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63950 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:13.481079200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63972 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:13.481079200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63965 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:13.481079200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63964 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:13.481079200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63962 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:13.481079200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63971 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:18.481113200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63997 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:18.481113200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63994 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:18.481113200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63977 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:18.481113200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63993 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:18.481113200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63989 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:23.481487000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64003 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:23.481487000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64006 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:23.481487000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64011 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:23.481487000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64008 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:23.481487000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64004 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:28.481767500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64024 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:28.481767500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64029 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:28.481767500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64027 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:28.481767500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64025 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:28.481767500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64030 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:28.481767500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64021 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:33.482485000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64044 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:33.482485000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64035 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:33.482485000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64042 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:33.482485000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64040 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:33.482485000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64038 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:38.482541200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64060 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:38.482541200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64051 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:38.482541200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64057 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:38.482541200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64050 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:38.482541200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64059 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:43.482989000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64067 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:43.482989000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64070 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:43.482989000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64075 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:43.482989000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64077 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:43.482989000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64069 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:48.483206400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64082 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:48.483206400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64089 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:48.483206400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64084 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:48.483206400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64086 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:48.483206400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64087 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:53.483697500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64102 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:53.483697500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64099 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:53.483697500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64101 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:53.483697500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64096 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:53.483697500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64095 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:58.484242500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64118 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:58.484242500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64114 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:58.484242500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64107 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:58.484242500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64112 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:19:58.484242500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64113 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:03.484669400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64124 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:03.484669400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64129 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:03.484669400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64130 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:03.484669400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64123 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:03.484669400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64127 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:08.485459700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64144 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:08.485459700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64137 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:08.485459700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64138 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:08.485459700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64143 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:08.485459700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64139 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:13.485651100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64153 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:13.485651100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64155 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:13.485651100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64150 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:13.485651100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64160 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:13.485651100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64163 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:18.485970600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64180 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:18.485970600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64171 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:18.485970600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64176 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:18.485970600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64179 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:18.485970600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64174 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:23.486556500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64191 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:23.486556500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64189 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:23.486556500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64186 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:23.486556500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64194 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:23.486556500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64188 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:28.487724300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64208 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:28.487724300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64201 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:28.487724300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64204 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:28.487724300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64203 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:28.487724300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64199 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:28.487724300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64205 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:33.487930600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64221 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:33.487930600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64214 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:33.487930600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64216 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:33.487930600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64220 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:33.487930600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64218 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:38.488152600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64236 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:38.488152600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64229 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:38.488152600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64227 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:38.488152600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64232 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:38.488152600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64234 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:43.488363200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64250 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:43.488363200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64244 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:43.488363200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64247 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:43.488363200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64242 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:43.488363200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64246 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:48.488807900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64263 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:48.488807900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64258 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:48.488807900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64260 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:48.488807900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64257 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:48.488807900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64262 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:53.489057200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64274 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:53.489057200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64269 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:53.489057200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64277 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:53.489057200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64280 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:53.489057200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64273 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:58.489322800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64287 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:58.489322800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64286 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:58.489322800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64291 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:58.489322800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64290 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:20:58.489322800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64292 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:03.489851900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64302 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:03.489851900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64307 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:03.489851900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64300 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:03.489851900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64303 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:03.489851900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64306 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:08.489882000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64323 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:08.489882000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64318 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:08.489882000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64316 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:08.489882000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64312 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:08.489882000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64317 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:13.490777200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64335 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:13.490777200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64334 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:13.490777200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64332 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:13.490777200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64329 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:13.490777200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64328 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:18.491023000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64349 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:18.491023000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64342 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:18.491023000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64343 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:18.491023000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64344 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:18.491023000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64346 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:23.491927400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64354 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:23.491927400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64361 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:23.491927400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64356 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:23.491927400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64359 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:23.491927400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64358 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:28.492020000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64374 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:28.492020000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64373 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:28.492020000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64367 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:28.492020000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64368 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:28.492020000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64372 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:28.492020000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64370 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:33.492957900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64392 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:33.492957900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64383 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:33.492957900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64386 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:33.492957900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64389 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:33.492957900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64381 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:38.493288100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64398 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:38.493288100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64405 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:38.493288100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64403 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:38.493288100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64402 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:38.493288100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64400 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:43.493760100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64416 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:43.493760100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64412 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:43.493760100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64417 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:43.493760100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64411 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:43.493760100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64414 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:48.494457900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64432 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:48.494457900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64426 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:48.494457900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64429 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:48.494457900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64425 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:48.494457900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64423 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:53.495053600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64447 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:53.495053600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64440 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:53.495053600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64438 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:53.495053600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64445 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:53.495053600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64444 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:58.495247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64465 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:58.495247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64457 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:58.495247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64460 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:58.495247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64462 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:21:58.495247500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64454 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:03.495709700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64473 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:03.495709700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64478 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:03.495709700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64470 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:03.495709700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64475 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:03.495709700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64474 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:08.496283900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64489 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:08.496283900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64483 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:08.496283900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64484 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:08.496283900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64488 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:08.496283900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64487 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:13.496310100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64505 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:13.496310100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64499 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:13.496310100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64497 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:13.496310100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64496 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:13.496310100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64504 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:18.496837200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64514 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:18.496837200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64510 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:18.496837200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64515 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:18.496837200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64513 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:18.496837200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64517 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:23.497303700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64524 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:23.497303700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64526 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:23.497303700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64523 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:23.497303700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64530 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:23.497303700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64528 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64545 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64542 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64544 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64541 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64539 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64538 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64548 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64546 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64536 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64549 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:28.497584800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64540 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:33.497899100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64561 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:33.497899100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64559 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:33.497899100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64558 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:33.497899100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64554 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:33.497899100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64556 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:38.498146900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64574 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:38.498146900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64570 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:38.498146900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64571 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:38.498146900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64573 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:38.498146900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64567 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:38.498146900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64568 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:43.498170300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64582 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:43.498170300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64585 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:43.498170300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64583 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:43.498170300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64580 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:43.498170300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64587 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:48.498342300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64599 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:48.498342300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64594 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:48.498342300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64592 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:48.498342300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64596 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:48.498342300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64597 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:53.498380300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64612 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:53.498380300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64608 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:53.498380300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64605 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:53.498380300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64606 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:53.498380300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64610 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:58.498593000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64626 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:58.498593000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64624 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:58.498593000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64622 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:58.498593000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64619 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:22:58.498593000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64621 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:03.498892400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64637 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:03.498892400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64635 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:03.498892400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64636 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:03.498892400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64631 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:03.498892400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64632 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:08.498923600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64650 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:08.498923600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64644 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:08.498923600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64649 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:08.498923600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64646 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:08.498923600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64645 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:13.499275000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64658 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:13.499275000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64662 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:13.499275000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64665 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:13.499275000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64659 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:13.499275000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64655 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:18.499556300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64670 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:18.499556300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64678 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:18.499556300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64675 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:18.499556300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64673 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:18.499556300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64677 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:23.499851400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64692 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:23.499851400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64687 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:23.499851400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64689 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:23.499851400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64686 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:23.499851400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64684 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:28.500326600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64706 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:28.500326600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64704 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:28.500326600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64702 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:28.500326600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64703 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:28.500326600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64700 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:28.500326600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64697 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:33.500347000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64718 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:33.500347000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64712 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:33.500347000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64717 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:33.500347000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64713 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:33.500347000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64715 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:38.500518200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64731 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:38.500518200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64727 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:38.500518200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64726 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:38.500518200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64729 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:38.500518200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64724 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:43.500661800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64736 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:43.500661800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64743 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:43.500661800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64740 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:43.500661800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64738 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:43.500661800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64741 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:48.501352500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64766 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:48.501352500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64751 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:48.501352500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64760 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:48.501352500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64749 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:48.501352500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64765 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:53.501388500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64781 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:53.501388500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64774 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:53.501388500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64779 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:53.501388500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64772 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:53.501388500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64775 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:58.501822700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64793 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:58.501822700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64791 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:58.501822700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64786 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:58.501822700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64790 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:23:58.501822700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64788 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:03.501960300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64807 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:03.501960300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64806 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:03.501960300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64799 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:03.501960300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64800 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:03.501960300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64803 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:08.502896400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64822 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:08.502896400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64813 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:08.502896400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64816 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:08.502896400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64817 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:08.502896400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64819 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:13.502977900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64828 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:13.502977900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64835 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:13.502977900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64834 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:13.502977900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64827 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:13.502977900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64831 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:18.503283900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64848 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:18.503283900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64846 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:18.503283900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64842 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:18.503283900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64844 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:18.503283900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64843 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:23.503647500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64863 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:23.503647500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64860 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:23.503647500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64859 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:23.503647500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64857 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:23.503647500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64853 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:28.504024400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64870 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:28.504024400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64872 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:28.504024400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64877 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:28.504024400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64874 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:28.504024400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64878 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:28.504024400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64876 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:33.504498900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64891 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:33.504498900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64889 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:33.504498900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64887 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:33.504498900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64886 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:33.504498900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64884 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:38.504667600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64903 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:38.504667600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64902 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:38.504667600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64898 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:38.504667600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64905 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:38.504667600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64896 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:43.505708400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64912 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:43.505708400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64916 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:43.505708400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64911 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:43.505708400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64914 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:43.505708400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64917 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:48.505981100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64923 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:48.505981100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64927 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:48.505981100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64929 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:48.505981100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64926 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:48.505981100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64931 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:53.506529100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64943 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:53.506529100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64941 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:53.506529100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64938 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:53.506529100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64940 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:53.506529100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64936 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:58.507413500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64949 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:58.507413500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64953 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:58.507413500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64955 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:58.507413500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64956 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:24:58.507413500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64951 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:03.507806300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64966 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:03.507806300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64962 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:03.507806300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64968 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:03.507806300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64971 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:03.507806300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64967 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:08.508131800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64982 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:08.508131800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64976 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:08.508131800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64983 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:08.508131800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64980 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:08.508131800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64977 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:13.508595100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64991 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:13.508595100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64997 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:13.508595100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64990 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:13.508595100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64992 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:13.508595100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:64996 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:18.508836700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65011 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:18.508836700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65002 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:18.508836700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65005 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:18.508836700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65007 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:18.508836700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65006 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:23.509664800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65017 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:23.509664800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65022 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:23.509664800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65023 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:23.509664800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65016 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:23.509664800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65019 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:28.510590900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65029 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:28.510590900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65031 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:28.510590900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65040 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:28.510590900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65039 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:28.510590900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65037 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:28.510590900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65032 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:33.511218900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65052 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:33.511218900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65047 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:33.511218900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65050 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:33.511218900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65049 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:33.511218900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65045 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:38.511473700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65066 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:38.511473700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65058 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:38.511473700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65061 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:38.511473700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65059 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:38.511473700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65065 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:43.511544400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65072 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:43.511544400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65079 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:43.511544400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65077 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:43.511544400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65074 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:43.511544400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65075 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:48.511724500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65084 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:48.511724500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65086 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:48.511724500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65090 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:48.511724500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65095 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:48.511724500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65089 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:53.511848000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65105 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:53.511848000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65107 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:53.511848000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65102 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:53.511848000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65103 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:53.511848000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65108 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:58.512055000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65122 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:58.512055000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65116 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:58.512055000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65120 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:58.512055000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65114 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:25:58.512055000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65118 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:03.512432600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65135 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:03.512432600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65127 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:03.512432600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65128 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:03.512432600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65131 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:03.512432600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65132 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:08.512595700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65149 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:08.512595700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65145 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:08.512595700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65142 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:08.512595700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65148 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:08.512595700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65143 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:13.512718900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65172 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:13.512718900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65168 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:13.512718900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65158 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:13.512718900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65166 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:13.512718900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65165 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:18.513148700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65182 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:18.513148700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65178 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:18.513148700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65183 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:18.513148700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65177 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:18.513148700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65181 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:23.513410100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65197 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:23.513410100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65194 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:23.513410100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65191 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:23.513410100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65192 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:23.513410100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65189 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:28.514426200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65213 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:28.514426200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65210 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:28.514426200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65211 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:28.514426200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65203 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:28.514426200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65209 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:28.514426200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65207 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:28.514426200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65205 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:33.515317900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65226 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:33.515317900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65225 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:33.515317900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65219 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:33.515317900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65223 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:33.515317900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65220 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:33.515317900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65222 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:38.516068100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65234 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:38.516068100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65236 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:38.516068100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65232 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:38.516068100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65240 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:38.516068100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65238 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:43.516361800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65253 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:43.516361800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65246 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:43.516361800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65248 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:43.516361800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65250 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:43.516361800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65245 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:43.516361800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65251 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:48.516834500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65267 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:48.516834500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65269 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:48.516834500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65264 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:48.516834500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65260 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:48.516834500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65259 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:53.517593900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65280 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:53.517593900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65278 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:53.517593900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65277 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:53.517593900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65282 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:53.517593900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65275 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:58.518560800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65295 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:58.518560800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65289 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:58.518560800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65292 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:58.518560800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65293 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:58.518560800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65287 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:26:58.518560800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65290 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:03.519019100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65304 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:03.519019100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65303 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:03.519019100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65302 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:03.519019100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65308 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:03.519019100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65309 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:08.519124300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65324 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:08.519124300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65314 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:08.519124300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65321 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:08.519124300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65319 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:08.519124300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65317 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:13.519464500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65337 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:13.519464500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65336 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:13.519464500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65329 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:13.519464500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65333 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:13.519464500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65330 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:18.520110600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65344 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:18.520110600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65353 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:18.520110600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65355 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:18.520110600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65350 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:18.520110600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65345 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:23.520139200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65370 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:23.520139200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65364 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:23.520139200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65366 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:23.520139200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65361 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:23.520139200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65367 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:23.520139200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65365 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65381 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65380 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65378 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65387 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65384 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65375 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65376 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65383 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65379 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65385 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:28.520483400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65389 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:33.520774000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65412 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:33.520774000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65398 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:33.520774000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65405 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:33.520774000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65397 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:33.520774000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65395 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:38.521200700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65425 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:38.521200700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65421 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:38.521200700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65422 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:38.521200700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65417 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:38.521200700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65419 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:38.521200700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65423 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:43.521732500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65436 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:43.521732500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65437 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:43.521732500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65434 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:43.521732500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65432 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:43.521732500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65431 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:48.522594600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65450 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:48.522594600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65443 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:48.522594600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65445 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:48.522594600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65446 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:48.522594600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65448 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:53.523088100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65462 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:53.523088100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65459 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:53.523088100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65455 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:53.523088100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65457 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:53.523088100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65460 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:58.523517200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65469 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:58.523517200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65474 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:58.523517200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65471 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:58.523517200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65468 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:27:58.523517200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65473 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:03.523778100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65483 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:03.523778100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65487 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:03.523778100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65484 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:03.523778100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65480 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:03.523778100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65482 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:08.524047000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65500 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:08.524047000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65498 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:08.524047000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65499 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:08.524047000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65495 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:08.524047000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65494 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:13.524313700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65508 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:13.524313700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65509 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:13.524313700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65516 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:13.524313700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65510 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:13.524313700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65515 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:18.524526200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65524 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:18.524526200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65521 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:18.524526200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65526 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:18.524526200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65529 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:18.524526200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65525 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:23.524730900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49152 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:23.524730900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49160 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:23.524730900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49155 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:23.524730900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:65534 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:23.524730900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49156 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:28.524832000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49174 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:28.524832000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49167 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:28.524832000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49173 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:28.524832000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49171 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:28.524832000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49169 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:28.524832000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49168 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:33.525096900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49184 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:33.525096900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49186 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:33.525096900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49181 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:33.525096900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49183 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:33.525096900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49179 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:38.525224500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49195 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:38.525224500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49198 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:38.525224500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49192 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:38.525224500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49197 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:38.525224500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49193 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:43.525363200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49204 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:43.525363200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49208 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:43.525363200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49206 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:43.525363200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49210 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:43.525363200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49212 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:48.526204900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49312 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:48.526204900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49219 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:48.526204900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49253 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:48.526204900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49223 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:48.526204900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49217 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:53.526541300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49352 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:53.526541300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49326 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:53.526541300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49329 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:53.526541300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49351 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:53.526541300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49335 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:58.526972600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49408 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:58.526972600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49430 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:58.526972600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49376 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:58.526972600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49373 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:28:58.526972600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49432 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:03.527352000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49438 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:03.527352000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49445 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:03.527352000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49443 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:03.527352000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49444 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:03.527352000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49441 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:08.527556300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49461 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:08.527556300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49453 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:08.527556300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49455 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:08.527556300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49452 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:08.527556300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49459 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:13.527587000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49466 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:13.527587000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49473 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:13.527587000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49470 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:13.527587000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49469 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:13.527587000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49480 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:18.528602000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49510 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:18.528602000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49492 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:18.528602000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49506 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:18.528602000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49488 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:18.528602000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49486 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:23.528675300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49529 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:23.528675300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49523 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:23.528675300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49525 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:23.528675300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49519 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:23.528675300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49528 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:28.528844900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49541 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:28.528844900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49538 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:28.528844900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49540 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:28.528844900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49539 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:28.528844900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49548 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:28.528844900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49535 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:33.528911700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49557 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:33.528911700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49566 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:33.528911700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49568 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:33.528911700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49563 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:33.528911700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49554 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:38.529171700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49578 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:38.529171700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49575 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:38.529171700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49577 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:38.529171700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49581 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:38.529171700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49597 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:43.529191100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49614 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:43.529191100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49612 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:43.529191100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49606 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:43.529191100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49604 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:43.529191100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49616 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:48.529725000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49636 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:48.529725000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49625 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:48.529725000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49623 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:48.529725000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49627 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:48.529725000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49633 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:53.530152100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49643 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:53.530152100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49655 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:53.530152100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49649 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:53.530152100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49653 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:53.530152100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49648 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:58.530905500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49677 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:58.530905500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49662 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:58.530905500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49672 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:58.530905500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49670 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:29:58.530905500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49673 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:03.531278500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49710 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:03.531278500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49704 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:03.531278500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49700 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:03.531278500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49708 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:03.531278500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49697 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:08.531314800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49727 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:08.531314800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49718 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:08.531314800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49721 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:08.531314800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49720 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:08.531314800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49724 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:13.532289000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49747 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:13.532289000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49732 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:13.532289000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49742 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:13.532289000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49745 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:13.532289000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49735 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:18.533413800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49759 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:18.533413800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49757 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:18.533413800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49763 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:18.533413800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49755 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:18.533413800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49762 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:23.533931500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49795 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:23.533931500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49769 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:23.533931500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49782 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:23.533931500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49786 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:23.533931500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49789 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:28.533983200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49801 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:28.533983200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49803 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:28.533983200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49808 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:28.533983200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49807 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:28.533983200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49809 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:28.533983200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49806 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:33.534362100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49827 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:33.534362100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49818 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:33.534362100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49826 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:33.534362100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49817 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:33.534362100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49822 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:38.535029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49840 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:38.535029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49842 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:38.535029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49844 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:38.535029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49836 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:38.535029500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49832 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:43.535225300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49867 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:43.535225300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49860 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:43.535225300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49850 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:43.535225300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49852 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:43.535225300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49864 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:48.536195300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49876 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:48.536195300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49874 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:48.536195300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49881 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:48.536195300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49879 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:48.536195300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49877 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:53.536446500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49898 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:53.536446500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49894 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:53.536446500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49895 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:53.536446500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49892 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:53.536446500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49888 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:58.536733300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49912 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:58.536733300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49914 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:58.536733300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49910 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:58.536733300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49907 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:30:58.536733300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49908 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:03.536862000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49936 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:03.536862000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49924 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:03.536862000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49920 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:03.536862000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49932 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:03.536862000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49922 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:08.537296300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49955 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:08.537296300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49964 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:08.537296300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49948 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:08.537296300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49943 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:08.537296300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49961 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:13.537825700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49970 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:13.537825700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49972 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:13.537825700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49979 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:13.537825700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49981 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:13.537825700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49973 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:18.538347200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49989 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:18.538347200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49995 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:18.538347200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49986 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:18.538347200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49991 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:18.538347200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49992 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:23.539089300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50006 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:23.539089300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50004 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:23.539089300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50001 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:23.539089300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50000 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:23.539089300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50005 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:28.539467400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50023 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:28.539467400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50017 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:28.539467400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50022 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:28.539467400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50016 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:28.539467400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50021 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:28.539467400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50018 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-06-09T23:31:32.741520800+08:00" level=warning msg="Mihomo shutting down"
