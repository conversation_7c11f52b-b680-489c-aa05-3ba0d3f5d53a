2025-07-18 17:52:04 INFO - try to run core in service mode
2025-07-18 17:52:04 INFO - start service: {"bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "core_type": "verge-mihomo-alpha", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-18-1752.log"}
2025-07-18 17:52:05 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-18 17:52:05 INFO - No hotkeys configured
2025-07-18 17:52:05 INFO - Starting to create window
2025-07-18 17:52:05 INFO - Creating new window
2025-07-18 17:52:05 INFO - Window created successfully, attempting to show
2025-07-18 17:52:05 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 17:52:05 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 17:53:56 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 17:53:56 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 17:53:56 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 17:53:56 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 17:58:37 INFO - Starting to create window
2025-07-18 17:58:37 INFO - Found existing window, trying to show it
2025-07-18 17:58:37 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 17:58:37 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:03:33 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:03:33 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:03:33 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:03:33 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:03:54 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:03:54 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:03:54 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:03:54 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:06:52 INFO - Starting to create window
2025-07-18 18:06:52 INFO - Found existing window, trying to show it
2025-07-18 18:06:52 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:06:52 INFO - Successfully registered hotkey Control+Q for quit
