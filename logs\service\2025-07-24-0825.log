Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-24T08:25:29.096669100+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-24T08:25:29.108190500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-24T08:25:29.108190500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-24T08:25:29.109206300+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-24T08:25:29.135045700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-24T08:25:29.135045700+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-24T08:25:29.447967300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-24T08:25:29.447967300+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-24T08:25:29.468696200+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-24T08:25:29.471698700+08:00" level=info msg="Initial configuration complete, total time: 367ms"
time="2025-07-24T08:25:29.472700100+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-24T08:25:29.474701300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-24T08:25:29.475702800+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-24T08:25:29.475702800+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-24T08:25:29.475702800+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-24T08:25:29.486215300+08:00" level=info msg="Start initial provider Lan"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Line"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider TVB"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider apple"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Disney"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider google"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider BBC"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider TVer"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider Discord"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider telegram"
time="2025-07-24T08:25:29.487216300+08:00" level=info msg="Start initial provider NowE"
time="2025-07-24T08:25:32.529798700+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-24T08:25:32.530301600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-24T08:25:32.530301600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-24T08:25:32.530829100+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-24T08:25:32.530829100+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-24T08:25:32.531335400+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-24T08:25:32.532430700+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-24T08:25:34.490669700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-24T08:25:34.490901500+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-24T08:25:34.491404900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491404900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491404900+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-24T08:25:34.491404900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491404900+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-24T08:25:34.491404900+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-24T08:25:34.492425300+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-24T08:25:34.493004000+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-24T08:25:34.493004000+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-24T08:25:34.493004000+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:34.491917300+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-24T08:25:34.493004000+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-24T08:25:34.491917300+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-24T08:25:34.492477000+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-24T08:25:34.498315300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-24T08:25:34.498315300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-24T08:25:34.498315300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-24T08:25:34.498315300+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Disney"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Discord"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider google"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider telegram"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider TVB"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider BBC"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Line"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Lan"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider NowE"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider TVer"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider apple"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-24T08:25:34.499863500+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.500952800+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-24T08:25:39.500446300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:25:39.501460600+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-24T08:25:39.503431900+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-24T08:25:39.503431900+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-24T08:25:39.503935800+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-24T08:25:39.503935800+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-24T08:25:39.856306300+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-24T08:25:39.856821600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-24T08:25:39.856821600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-24T08:25:39.857357000+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-24T08:25:39.857357000+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-24T08:25:39.857357000+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-24T08:25:39.858885400+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-24T08:25:39.859911600+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Discord"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider TVB"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider google"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider apple"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider telegram"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider TVer"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Line"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Disney"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider NowE"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Lan"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider BBC"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-24T08:25:39.860425900+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-24T08:25:40.061824700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.061824700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.063520700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.063520700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.063520700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.063520700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.063520700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.064936700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.064936700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.064936700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.064936700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.064936700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.066869100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.066869100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.067372100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.067372100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.069872000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.069872000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.069872000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.070374500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.070374500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.070374500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.070374500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.070891100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.070891100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.070891100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.070891100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.070891100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.077180400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.077180400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.077684300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.077684300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.077684300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.078202900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.078202900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.078202900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.078202900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.078729100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.078729100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.078729100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.078729100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.079240900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.079240900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.079240900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.079240900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.079753700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.079753700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.079753700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.079753700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.603804800+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-24T08:25:40.624730300+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-24T08:25:40.633819600+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-24T08:25:40.640539100+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-24T08:25:40.640539100+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-24T08:25:40.646895800+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-24T08:25:40.655790300+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-24T08:25:40.661016000+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-24T08:25:40.672926600+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-07-24T08:25:40.690947300+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-24T08:25:40.692984500+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-24T08:25:40.693766400+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-24T08:25:40.696034900+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-24T08:25:40.701482000+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-24T08:25:40.705155700+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-24T08:25:40.712134400+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-24T08:25:40.721635200+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-24T08:25:40.750223700+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-24T08:25:40.755076300+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-24T08:25:40.760905700+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-07-24T08:25:40.770424700+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-24T08:25:40.772478300+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-07-24T08:25:40.778478000+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-24T08:25:40.783836100+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-24T08:25:40.818327300+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-24T08:25:40.832920900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:40.976495300+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-24T08:25:40.997737800+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-24T08:25:41.005023100+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-24T08:25:41.061302300+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-07-24T08:25:41.082716100+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-24T08:25:41.083694800+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-24T08:25:41.084198300+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-24T08:25:41.109453600+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-24T08:25:41.141029000+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-24T08:25:41.184854600+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-24T08:25:41.240066200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:41.335298300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T08:25:41.468857000+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-24T08:25:41.553538000+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-24T08:25:45.301989400+08:00" level=error msg="🇹🇷土耳其01 [2x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-24T08:25:45.301989400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-24T08:25:47.306056500+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-24T08:25:47.306056500+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-24T08:25:47.306056500+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-24T08:25:47.306056500+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-24T08:25:47.306056500+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-24T08:25:47.311439400+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-24T08:25:47.311942700+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-24T08:25:47.312845200+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-24T08:25:47.315508000+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-24T08:25:48.586400100+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/users/sign_in\": EOF"
time="2025-07-24T08:25:48.621782900+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-24T08:25:48.621782900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-24T08:25:48.622806300+08:00" level=error msg="🇸🇬新加坡01 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-24T08:25:48.622806300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-24T08:25:48.633975700+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-24T08:25:48.633975700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-24T08:25:49.861004300+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": net/http: TLS handshake timeout"
time="2025-07-24T08:25:49.861004300+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": net/http: TLS handshake timeout"
time="2025-07-24T08:25:49.866410200+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-24T08:25:49.866410200+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-24T08:25:49.866410200+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-24T08:25:49.866410200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-24T08:25:53.437993100+08:00" level=info msg="[TCP] 127.0.0.1:51702 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-24T08:25:54.951073900+08:00" level=info msg="[TCP] 127.0.0.1:51731 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:08.589622000+08:00" level=info msg="[TCP] 127.0.0.1:51768 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:26:09.024886300+08:00" level=info msg="[TCP] 127.0.0.1:51772 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:09.507829300+08:00" level=info msg="[TCP] 127.0.0.1:51775 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:10.619583300+08:00" level=info msg="[TCP] 127.0.0.1:51779 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:11.019822700+08:00" level=info msg="[TCP] 127.0.0.1:51782 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T08:26:11.146353900+08:00" level=info msg="[TCP] 127.0.0.1:51785 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:11.159956700+08:00" level=info msg="[TCP] 127.0.0.1:51788 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:26:11.271690400+08:00" level=info msg="[TCP] 127.0.0.1:51791 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:11.296594800+08:00" level=info msg="[TCP] 127.0.0.1:51794 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:15.486542100+08:00" level=info msg="[TCP] 127.0.0.1:51800 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:26:19.334860300+08:00" level=info msg="[TCP] 127.0.0.1:51808 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:26.491211000+08:00" level=info msg="[TCP] 127.0.0.1:51821 --> download.windowsupdate.com:80 using GLOBAL"
time="2025-07-24T08:26:29.354325300+08:00" level=info msg="[TCP] 127.0.0.1:51826 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:32.522498900+08:00" level=info msg="[TCP] 127.0.0.1:51833 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:36.644375800+08:00" level=info msg="[TCP] 127.0.0.1:51841 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:26:38.517516600+08:00" level=info msg="[TCP] 127.0.0.1:51845 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:26:38.596489800+08:00" level=info msg="[TCP] 127.0.0.1:51848 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:26:38.862889600+08:00" level=info msg="[TCP] 127.0.0.1:51851 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:26:39.051375400+08:00" level=info msg="[TCP] 127.0.0.1:51855 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:39.057270600+08:00" level=info msg="[TCP] 127.0.0.1:51858 --> cp801.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:39.376339400+08:00" level=info msg="[TCP] 127.0.0.1:51861 --> au.download.windowsupdate.com:80 using GLOBAL"
time="2025-07-24T08:26:39.970940100+08:00" level=info msg="[TCP] 127.0.0.1:51864 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T08:26:40.427521400+08:00" level=info msg="[TCP] 127.0.0.1:51867 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:42.831409100+08:00" level=info msg="[TCP] 127.0.0.1:51872 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:26:44.734378700+08:00" level=info msg="[TCP] 127.0.0.1:51876 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:26:44.735902700+08:00" level=info msg="[TCP] 127.0.0.1:51877 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:26:47.529296800+08:00" level=info msg="[TCP] 127.0.0.1:51884 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:27:11.821699200+08:00" level=info msg="[TCP] 127.0.0.1:51905 --> activity.windows.com:443 using GLOBAL"
time="2025-07-24T08:27:26.557920000+08:00" level=info msg="[TCP] 127.0.0.1:51919 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:27:28.498764800+08:00" level=info msg="[TCP] 127.0.0.1:51923 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:27:29.490551200+08:00" level=info msg="[TCP] 127.0.0.1:51926 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:27:32.758955900+08:00" level=info msg="[TCP] 127.0.0.1:51933 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:27:44.744705000+08:00" level=info msg="[TCP] 127.0.0.1:51943 --> download.clashverge.dev:443 using GLOBAL"
time="2025-07-24T08:27:51.077365600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51948 --> github.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:27:51.084068100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51953 --> zhijuanwang.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:27:51.617928900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51954 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:27:56.084219400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51967 --> zhijuanwang.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:27:56.618782700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51973 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:30:39.847034000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52125 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T08:30:39.899575100+08:00" level=info msg="[TCP] 127.0.0.1:52346 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:30:40.196590200+08:00" level=info msg="[TCP] 127.0.0.1:52350 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T08:30:40.210753500+08:00" level=info msg="[TCP] 127.0.0.1:52353 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:30:42.117814800+08:00" level=info msg="[TCP] 127.0.0.1:52357 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-24T08:31:04.687246300+08:00" level=info msg="[TCP] 127.0.0.1:52379 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:31:05.824342300+08:00" level=info msg="[TCP] 127.0.0.1:52382 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:31:08.451663200+08:00" level=info msg="[TCP] 127.0.0.1:52387 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:31:18.225833100+08:00" level=info msg="[TCP] 127.0.0.1:52399 --> cs.dds.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:31:26.692752500+08:00" level=info msg="[TCP] 127.0.0.1:52410 --> nav.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:31:32.382150900+08:00" level=info msg="[TCP] 127.0.0.1:52417 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:31:35.545770700+08:00" level=info msg="[TCP] 127.0.0.1:52422 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:31:36.678230100+08:00" level=info msg="[TCP] 127.0.0.1:52425 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-24T08:31:46.053859100+08:00" level=info msg="[TCP] 127.0.0.1:52437 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T08:31:47.902075700+08:00" level=info msg="[TCP] 127.0.0.1:52441 --> egde.nelreports.net:443 using GLOBAL"
time="2025-07-24T08:32:04.685377800+08:00" level=info msg="[TCP] 127.0.0.1:52457 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T09:11:23.065147600+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-24T09:11:23.065655500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-24T09:11:23.065655500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-24T09:11:23.066161700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-24T09:11:23.066677800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-24T09:11:23.066677800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-24T09:11:23.068241400+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-24T09:11:23.069257600+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-24T09:11:23.087933800+08:00" level=warning msg="[TUN] default interface changed by monitor,  => 以太网"
time="2025-07-24T09:11:23.414592200+08:00" level=info msg="[TUN] Tun adapter listening at: Meta([********/30],[fdfe:dcba:9876::1/126]), mtu: 9000, auto route: true, auto redir: false, ip stack: Mixed"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider telegram"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider TVB"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider google"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider Lan"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-24T09:11:23.415926700+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-24T09:11:23.415926700+08:00" level=info msg="Start initial provider TVer"
time="2025-07-24T09:11:23.415926700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider NowE"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider Line"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider BBC"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider Disney"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-24T09:11:23.415926700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider apple"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider Discord"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-24T09:11:23.415926700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-24T09:11:23.415103600+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-24T09:11:23.415613600+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-24T09:11:28.415540500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-24T09:11:28.416648300+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416648300+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-24T09:11:28.416135300+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-24T09:11:28.416648300+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-24T09:11:28.415632400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-24T09:11:28.416648300+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-24T09:11:28.416648300+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-24T09:11:28.419852300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-24T09:11:28.419852300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-24T09:11:28.420364300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-24T09:11:28.420364300+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-24T09:11:28.619869900+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:56334 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-24T09:11:29.093973500+08:00" level=info msg="[TCP] ********:54739 --> kv801.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:11:29.100718200+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:59155 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-24T09:11:30.239221700+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:57917 --> [2605:e440:19::1b5]:9101 using GLOBAL"
time="2025-07-24T09:11:30.445001200+08:00" level=error msg="🇷🇺俄罗斯01 [2x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": read tcp [2409:8a15:2050:8210:ccf2:705c:ae77:c841]:54768->[2409:8754:5630:210:0:1:6666:3]:19892: use of closed network connection"
time="2025-07-24T09:11:30.445001200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-24T09:11:30.461909900+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:51547 --> [2605:e440:8::1:20d]:9101 using GLOBAL"
time="2025-07-24T09:11:32.204411200+08:00" level=info msg="[TCP] ********:54795 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:11:32.237186400+08:00" level=info msg="[TCP] ********:54798 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-24T09:11:34.632546800+08:00" level=info msg="[TCP] ********:54825 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:11:35.747293400+08:00" level=error msg="🇯🇵日本04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-24T09:11:35.747293400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-24T09:11:37.071673700+08:00" level=info msg="[TCP] ********:54837 --> pcmanager.api.eu.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:11:38.057009400+08:00" level=info msg="[UDP] [fdfe:dcba:9876::1]:61876 --> [2402:4e00:1900:1903:0:9687:df13:8edb]:3478 using GLOBAL"
time="2025-07-24T09:11:38.072473100+08:00" level=info msg="[UDP] ********:61875 --> **************:3478 using GLOBAL"
time="2025-07-24T09:11:42.544208200+08:00" level=info msg="[TCP] ********:54843 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T09:11:43.370401800+08:00" level=info msg="[TCP] ********:54847 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:11:47.434678400+08:00" level=info msg="[TCP] ********:54852 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:11:49.884736200+08:00" level=info msg="[TCP] ********:54857 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T09:11:52.934081400+08:00" level=info msg="[UDP] ********:61874 --> **************:3478 using GLOBAL"
time="2025-07-24T09:12:05.425394200+08:00" level=info msg="[TCP] ********:54870 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:12:06.986035600+08:00" level=info msg="[TCP] ********:54874 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-24T09:12:06.986035600+08:00" level=info msg="[TCP] ********:54875 --> www.msftconnecttest.com:80 using GLOBAL"
time="2025-07-24T09:12:07.612625000+08:00" level=info msg="[TCP] ********:54881 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-07-24T09:12:11.566849900+08:00" level=info msg="[TCP] ********:54886 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:12:14.228724500+08:00" level=info msg="[TCP] ********:54891 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:12:19.811915100+08:00" level=info msg="[TCP] ********:54899 --> catalog.gamepass.com:443 using GLOBAL"
time="2025-07-24T09:12:42.672114900+08:00" level=info msg="[TCP] ********:54917 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T09:13:24.011571600+08:00" level=info msg="[TCP] ********:54948 --> api.viewdepth.cn:443 using GLOBAL"
time="2025-07-24T09:14:01.815689600+08:00" level=info msg="[TCP] ********:54975 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-24T09:14:59.990806200+08:00" level=info msg="[TCP] ********:55018 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-07-24T09:15:00.217853500+08:00" level=info msg="[TCP] ********:55025 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:15:04.930572900+08:00" level=warning msg="[UDP] Resolve Ip error: couldn't find ip"
time="2025-07-24T09:15:06.711957000+08:00" level=info msg="[TCP] ********:55036 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-24T09:15:07.290769500+08:00" level=info msg="[TCP] ********:55040 --> my.microsoftpersonalcontent.com:443 using GLOBAL"
time="2025-07-24T09:15:07.337277500+08:00" level=info msg="[TCP] ********:55043 --> my.microsoftpersonalcontent.com:443 using GLOBAL"
time="2025-07-24T09:15:07.528633700+08:00" level=info msg="[TCP] ********:55046 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:15:08.376059800+08:00" level=info msg="[TCP] ********:55049 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 using GLOBAL"
time="2025-07-24T09:15:28.387125300+08:00" level=info msg="[TCP] ********:55065 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:16.509115400+08:00" level=info msg="[TCP] ********:55101 --> psuite.vivo.com:443 using GLOBAL"
time="2025-07-24T09:16:16.511276300+08:00" level=info msg="[TCP] ********:55100 --> psuite.vivo.com:443 using GLOBAL"
time="2025-07-24T09:16:16.515071400+08:00" level=info msg="[TCP] ********:55102 --> psuite.vivo.com:443 using GLOBAL"
time="2025-07-24T09:16:17.089795000+08:00" level=info msg="[TCP] ********:55111 --> psuite.vivo.com:443 using GLOBAL"
time="2025-07-24T09:16:18.526584000+08:00" level=info msg="[TCP] ********:55116 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:19.192774700+08:00" level=info msg="[TCP] ********:55119 --> edge.skype.com:443 using GLOBAL"
time="2025-07-24T09:16:19.215358200+08:00" level=info msg="[TCP] ********:55122 --> westeurope-5.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-24T09:16:22.323074800+08:00" level=info msg="[TCP] ********:55128 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:22.670837500+08:00" level=info msg="[TCP] ********:55131 --> psuite.vivo.com:443 using GLOBAL"
time="2025-07-24T09:16:23.804890900+08:00" level=info msg="[TCP] ********:55135 --> psuite.vivo.com:443 using GLOBAL"
time="2025-07-24T09:16:25.878390700+08:00" level=info msg="[TCP] ********:55139 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:26.622381900+08:00" level=info msg="[TCP] ********:55143 --> westeurope-5.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-24T09:16:31.869661600+08:00" level=info msg="[TCP] ********:55149 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-24T09:16:32.403538000+08:00" level=info msg="[TCP] ********:55152 --> 194599-ipv4fdsmte.gr.global.aa-rt.sharepoint.com:443 using GLOBAL"
time="2025-07-24T09:16:34.414117300+08:00" level=info msg="[TCP] ********:55156 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:34.935335300+08:00" level=info msg="[TCP] ********:55160 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:35.216351900+08:00" level=info msg="[TCP] ********:55163 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T09:16:36.360533000+08:00" level=info msg="[TCP] ********:55167 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-24T09:16:36.405024900+08:00" level=info msg="[TCP] ********:55170 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:37.169015400+08:00" level=info msg="[TCP] ********:55173 --> *************:443 using GLOBAL"
time="2025-07-24T09:16:37.334480200+08:00" level=info msg="[TCP] ********:55176 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:38.631767900+08:00" level=info msg="[TCP] ********:55180 --> 203.205.137.114:80 using GLOBAL"
time="2025-07-24T09:16:39.194033100+08:00" level=info msg="[TCP] ********:55184 --> aqqmusic.tc.qq.com:80 using GLOBAL"
time="2025-07-24T09:16:39.515042300+08:00" level=info msg="[TCP] ********:55187 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-24T09:16:39.889254100+08:00" level=info msg="[TCP] ********:55190 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-24T09:16:41.868797100+08:00" level=info msg="[TCP] ********:55195 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-24T09:16:42.060360400+08:00" level=info msg="[TCP] ********:55198 --> storage.live.com:443 using GLOBAL"
time="2025-07-24T09:16:42.180562800+08:00" level=info msg="[TCP] ********:55201 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-24T09:16:42.181587400+08:00" level=info msg="[TCP] ********:55202 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-24T09:16:42.558288600+08:00" level=info msg="[TCP] ********:55207 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:42.577385700+08:00" level=info msg="[TCP] ********:55210 --> config.teams.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:42.998845200+08:00" level=info msg="[TCP] ********:55213 --> onedriveclucproddm20050.blob.core.windows.net:443 using GLOBAL"
time="2025-07-24T09:16:45.376752700+08:00" level=info msg="[TCP] ********:55218 --> aqqmusic.tc.qq.com:80 using GLOBAL"
time="2025-07-24T09:16:45.647147700+08:00" level=info msg="[TCP] ********:55222 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-24T09:16:45.650311800+08:00" level=info msg="[TCP] ********:55221 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-24T09:16:46.148624100+08:00" level=info msg="[TCP] ********:55227 --> onedriveclucproddm20050.blob.core.windows.net:443 using GLOBAL"
time="2025-07-24T09:16:50.070678300+08:00" level=info msg="[TCP] ********:55233 --> config.teams.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:51.597469600+08:00" level=info msg="[TCP] ********:55237 --> aqqmusic.tc.qq.com:80 using GLOBAL"
time="2025-07-24T09:16:52.075982200+08:00" level=info msg="[TCP] ********:55240 --> ollama.com:443 using GLOBAL"
time="2025-07-24T09:16:52.213406200+08:00" level=info msg="[TCP] ********:55243 --> 203.205.137.157:80 using GLOBAL"
time="2025-07-24T09:16:52.632548600+08:00" level=info msg="[TCP] ********:55246 --> 203.205.137.159:80 using GLOBAL"
time="2025-07-24T09:16:53.062860400+08:00" level=info msg="[TCP] ********:55250 --> ws6.stream.qqmusic.qq.com:80 using GLOBAL"
time="2025-07-24T09:16:53.301696800+08:00" level=info msg="[TCP] ********:55253 --> config.teams.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:16:53.314355600+08:00" level=info msg="[TCP] ********:55256 --> ws6.stream.qqmusic.qq.com:80 using GLOBAL"
time="2025-07-24T09:16:53.928030800+08:00" level=info msg="[TCP] ********:55259 --> ws.stream.qqmusic.qq.com:80 using GLOBAL"
time="2025-07-24T09:17:00.194480900+08:00" level=info msg="[TCP] ********:55267 --> ws.stream.qqmusic.qq.com:80 using GLOBAL"
time="2025-07-24T09:17:00.404214100+08:00" level=info msg="[TCP] ********:55270 --> aqqmusic.tc.qq.com:80 using GLOBAL"
time="2025-07-24T09:17:00.840409700+08:00" level=info msg="[TCP] ********:55273 --> aqqmusic.tc.qq.com:443 using GLOBAL"
time="2025-07-24T09:17:08.092037900+08:00" level=info msg="[TCP] ********:55281 --> aqqmusic.tc.qq.com:443 using GLOBAL"
time="2025-07-24T09:17:11.735828200+08:00" level=info msg="[TCP] ********:55286 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:17:15.264731100+08:00" level=info msg="[TCP] ********:55292 --> aqqmusic.tc.qq.com:443 using GLOBAL"
time="2025-07-24T09:17:20.661709300+08:00" level=info msg="[TCP] ********:55298 --> westeurope-5.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-24T09:17:35.368699100+08:00" level=info msg="[TCP] ********:55310 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-24T09:17:36.305432400+08:00" level=info msg="[TCP] ********:55314 --> api.steampowered.com:443 using GLOBAL"
time="2025-07-24T09:17:37.781706200+08:00" level=info msg="[TCP] ********:55318 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T09:17:37.781706200+08:00" level=info msg="[TCP] ********:55319 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T09:17:37.860198700+08:00" level=info msg="[TCP] ********:55324 --> substrate.office.com:443 using GLOBAL"
time="2025-07-24T09:17:37.861289900+08:00" level=info msg="[TCP] ********:55327 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T09:17:38.782382900+08:00" level=info msg="[TCP] ********:55331 --> browser.pipe.aria.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:17:39.214708100+08:00" level=info msg="[TCP] ********:55334 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-24T09:17:39.889071700+08:00" level=info msg="[TCP] ********:55338 --> fp.msedge.net:443 using GLOBAL"
time="2025-07-24T09:17:40.800784900+08:00" level=info msg="[TCP] ********:55341 --> substrate.office.com:443 using GLOBAL"
time="2025-07-24T09:17:40.903533800+08:00" level=info msg="[TCP] ********:55344 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-24T09:17:42.003873900+08:00" level=info msg="[TCP] ********:55349 --> ***************:60381 using GLOBAL"
time="2025-07-24T09:17:42.752276800+08:00" level=info msg="[TCP] ********:55353 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-24T09:17:42.785033100+08:00" level=info msg="[TCP] ********:55356 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T09:17:44.834357500+08:00" level=info msg="[TCP] ********:55360 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:17:47.076171700+08:00" level=info msg="[TCP] ********:55364 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-24T09:17:50.041533700+08:00" level=info msg="[TCP] ********:55369 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:17:52.435072400+08:00" level=info msg="[TCP] ********:55374 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-07-24T09:18:00.343331800+08:00" level=info msg="[TCP] ********:55387 --> api.github.com:443 using GLOBAL"
time="2025-07-24T09:18:00.637161900+08:00" level=info msg="[TCP] ********:55390 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-24T09:18:00.753709400+08:00" level=info msg="[TCP] ********:55393 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:18:00.769366200+08:00" level=info msg="[TCP] ********:55396 --> api.github.com:443 using GLOBAL"
time="2025-07-24T09:18:00.772434500+08:00" level=info msg="[TCP] ********:55397 --> api.github.com:443 using GLOBAL"
time="2025-07-24T09:18:01.348066400+08:00" level=info msg="[TCP] ********:55403 --> education.github.com:443 using GLOBAL"
time="2025-07-24T09:18:01.488347700+08:00" level=info msg="[TCP] ********:55406 --> login.microsoftonline.com:443 using GLOBAL"
time="2025-07-24T09:18:02.166941100+08:00" level=info msg="[TCP] ********:55409 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:18:02.313065000+08:00" level=info msg="[TCP] ********:55412 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-24T09:18:02.699566800+08:00" level=info msg="[TCP] ********:55415 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-24T09:18:03.321836500+08:00" level=info msg="[TCP] ********:55419 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:18:04.738535000+08:00" level=info msg="[TCP] ********:55423 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:18:04.766466900+08:00" level=info msg="[TCP] ********:55426 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:18:05.304115600+08:00" level=info msg="[TCP] ********:55429 --> www.bing.com:443 using GLOBAL"
time="2025-07-24T09:18:10.529934500+08:00" level=info msg="[TCP] ********:55436 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:18:17.580876500+08:00" level=info msg="[TCP] ********:55443 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-07-24T09:18:20.409076900+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-24T09:18:20.410125600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-24T09:18:20.410125600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-24T09:18:20.410639700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-24T09:18:20.411154500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-24T09:18:20.411154500+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-24T09:18:20.412715500+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-24T09:18:20.414249700+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-24T09:18:20.439193200+08:00" level=info msg="[TCP] ********:55450 --> ***************:60381 using GLOBAL"
time="2025-07-24T09:18:20.569137400+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-24T09:18:20.569137400+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-24T09:18:20.569137400+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider Lan"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-24T09:18:20.570371000+08:00" level=info msg="Start initial provider BBC"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider TVer"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Discord"
time="2025-07-24T09:18:20.570371000+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider apple"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider Line"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider TVB"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider google"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-24T09:18:20.569868600+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-24T09:18:20.570371000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-24T09:18:20.570371000+08:00" level=info msg="Start initial provider telegram"
time="2025-07-24T09:18:20.570371000+08:00" level=info msg="Start initial provider NowE"
time="2025-07-24T09:18:20.570371000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-24T09:18:20.570371000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-24T09:18:20.570371000+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Disney"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-24T09:18:20.569640200+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-24T09:18:20.774970100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.775472600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.776328900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.776972200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.776972200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.777582600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.777582600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.777582600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.777582600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.778807000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.779310700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.779310700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.779310700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.781777500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.781777500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.781777500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.782279900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.782279900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.782279900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.782279900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.782279900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.782801400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.786244100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.786244100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.786746800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.786746800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.786746800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.787260300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.787260300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.787260300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.787260300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.787260300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.787775100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.787775100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.787775100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.787775100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.788292800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.788292800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.788292800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.796642100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.796642100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.797144700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.797144700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.797144700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.797144700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.797144700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.797657700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.797657700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:20.797657700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:21.335087700+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-24T09:18:21.338131000+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-24T09:18:21.361480000+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-24T09:18:21.363547300+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-24T09:18:21.366978600+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-24T09:18:21.371461500+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-24T09:18:21.373660200+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-24T09:18:21.373660200+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-24T09:18:21.382061000+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-24T09:18:21.396806600+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-24T09:18:21.400466300+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-24T09:18:21.404132600+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-24T09:18:21.415497500+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-07-24T09:18:21.417656900+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-24T09:18:21.430537300+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-24T09:18:21.431325600+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-07-24T09:18:21.432848500+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-24T09:18:21.434254800+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-24T09:18:21.449687400+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-24T09:18:21.451855400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/users/sign_in\": net/http: HTTP/1.x transport connection broken: unexpected EOF"
time="2025-07-24T09:18:21.452958000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:21.452958000+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-24T09:18:21.457172000+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/users/sign_in\": net/http: HTTP/1.x transport connection broken: unexpected EOF"
time="2025-07-24T09:18:21.466240800+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-24T09:18:21.469560500+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-24T09:18:21.471847300+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-24T09:18:21.471847300+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-24T09:18:21.475150700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:21.478634100+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-07-24T09:18:21.484588800+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-24T09:18:21.485350400+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-24T09:18:21.486107400+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-24T09:18:21.486855700+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-24T09:18:21.510861000+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-24T09:18:21.523361900+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-24T09:18:21.584843600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-24T09:18:21.594096800+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-24T09:18:21.622651300+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-24T09:18:21.629188700+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-24T09:18:21.685386600+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-24T09:18:21.835414800+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-24T09:18:21.978247900+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-24T09:18:21.982736600+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-24T09:18:21.984259400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-24T09:18:21.986866900+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-24T09:18:21.987551100+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-24T09:18:21.988171800+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/users/sign_in\": EOF"
time="2025-07-24T09:18:21.988171800+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-24T09:18:21.988919200+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-24T09:18:21.988919200+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/users/sign_in\": EOF"
time="2025-07-24T09:18:21.989422200+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-24T09:18:21.990331900+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-24T09:18:21.999085400+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-24T09:18:21.999085400+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-24T09:18:21.999085400+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-24T09:18:21.999085400+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-24T10:04:27.005115700+08:00" level=warning msg="Mihomo shutting down"
