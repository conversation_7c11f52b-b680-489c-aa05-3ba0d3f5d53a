2025-06-04 22:49:16 INFO - try to run core in service mode
2025-06-04 22:49:16 INFO - start service: {"config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "core_type": "verge-mihomo-alpha", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-06-04-2249.log"}
2025-06-04 22:49:17 INFO - Initializing hotkeys, global hotkey enabled: true
2025-06-04 22:49:17 INFO - No hotkeys configured
2025-06-04 22:49:17 INFO - Starting to create window
2025-06-04 22:49:17 INFO - Creating new window
2025-06-04 22:49:17 INFO - Window created successfully, attempting to show
2025-06-04 22:49:17 INFO - running timer task `R3SfPp0qApId`
2025-06-04 22:49:17 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-04 22:49:17 INFO - Successfully registered hotkey Control+Q for quit
2025-06-04 22:49:42 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-04 22:49:42 INFO - Successfully registered hotkey Control+Q for quit
2025-06-04 22:49:57 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-04 22:49:57 INFO - Successfully registered hotkey Control+Q for quit
2025-06-04 23:04:02 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-04 23:04:02 INFO - Successfully registered hotkey Control+Q for quit
2025-06-04 23:04:02 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-04 23:04:02 INFO - Successfully registered hotkey Control+Q for quit
2025-06-04 23:04:19 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-04 23:04:19 INFO - Successfully registered hotkey Control+Q for quit
2025-06-04 23:54:17 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-04 23:54:17 INFO - Successfully registered hotkey Control+Q for quit
2025-06-04 23:54:34 INFO - stop the core by service
2025-06-04 23:54:34 ERROR - Failed to unregister hotkey: HotKey { mods: Modifiers(CONTROL), key: KeyQ, id: 524323 }
