Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-11T20:43:31.314932800+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-11T20:43:31.323665900+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-11T20:43:31.323665900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-11T20:43:31.326008000+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-11T20:43:31.347100100+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-11T20:43:31.347613000+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-11T20:43:31.652267900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-11T20:43:31.652267900+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-11T20:43:31.669321000+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-11T20:43:31.672323500+08:00" level=info msg="Initial configuration complete, total time: 351ms"
time="2025-07-11T20:43:31.672323500+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-11T20:43:31.673324500+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-11T20:43:31.674325200+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-11T20:43:31.674325200+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-11T20:43:31.674325200+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Disney"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider NowE"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider google"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Discord"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider TVer"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider TVB"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Line"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Lan"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider apple"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider telegram"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider BBC"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-11T20:43:31.683334300+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-11T20:43:36.685382300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685382300+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.687483900+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.686463100+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.687483900+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-11T20:43:36.687483900+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-11T20:43:36.687483900+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-11T20:43:36.685950800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:43:36.687483900+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-11T20:43:36.687483900+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-11T20:43:36.686973300+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-11T20:43:36.689147300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-11T20:43:36.689147300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-11T20:43:36.689147300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-11T20:43:36.689147300+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-11T20:43:46.880713600+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-11T20:43:46.880713600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-11T20:43:46.881502600+08:00" level=error msg="🇰🇷韩国01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-11T20:43:46.881502600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-11T20:43:46.881502600+08:00" level=error msg="🇰🇷韩国02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-11T20:43:46.881502600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-11T20:43:46.882016500+08:00" level=error msg="🇰🇷韩国03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-11T20:43:46.882016500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-11T20:43:46.897274700+08:00" level=error msg="🇰🇷韩国02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-11T20:43:46.897274700+08:00" level=error msg="🇰🇷韩国01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-11T20:43:46.897274700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-11T20:43:46.897274700+08:00" level=error msg="🇰🇷韩国03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-11T20:43:46.897274700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-11T20:43:46.897274700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-11T20:43:47.479927800+08:00" level=info msg="[TCP] 127.0.0.1:54472 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:43:47.485115000+08:00" level=info msg="[TCP] 127.0.0.1:54471 --> github.com:443 using GLOBAL"
time="2025-07-11T20:43:47.534577700+08:00" level=info msg="[TCP] 127.0.0.1:54477 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-11T20:43:48.250158400+08:00" level=info msg="[TCP] 127.0.0.1:54480 --> app.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:43:49.465430300+08:00" level=info msg="[TCP] 127.0.0.1:54485 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:43:49.649541200+08:00" level=info msg="[TCP] 127.0.0.1:54489 --> evs.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:43:49.964998400+08:00" level=info msg="[TCP] 127.0.0.1:54492 --> evs.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:43:50.847613300+08:00" level=info msg="[TCP] 127.0.0.1:54497 --> api.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:43:50.848865400+08:00" level=info msg="[TCP] 127.0.0.1:54503 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-11T20:43:50.848865400+08:00" level=info msg="[TCP] 127.0.0.1:54502 --> www.google.com:443 using GLOBAL"
time="2025-07-11T20:43:50.850312600+08:00" level=info msg="[TCP] 127.0.0.1:54496 --> api.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:43:50.859377500+08:00" level=info msg="[TCP] 127.0.0.1:54508 --> us.i.posthog.com:443 using GLOBAL"
time="2025-07-11T20:43:50.995144700+08:00" level=info msg="[TCP] 127.0.0.1:54511 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:43:51.003759700+08:00" level=info msg="[TCP] 127.0.0.1:54514 --> www.google.com:443 using GLOBAL"
time="2025-07-11T20:43:54.965994900+08:00" level=info msg="[TCP] 127.0.0.1:54529 --> download.clashverge.dev:443 using GLOBAL"
time="2025-07-11T20:43:56.986263300+08:00" level=info msg="[TCP] 127.0.0.1:54539 --> api.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:44:08.226716200+08:00" level=info msg="[TCP] 127.0.0.1:54568 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:44:12.731314100+08:00" level=info msg="[TCP] 127.0.0.1:54589 --> github.com:443 using GLOBAL"
time="2025-07-11T20:44:12.731314100+08:00" level=info msg="[TCP] 127.0.0.1:54590 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:44:13.308344500+08:00" level=info msg="[TCP] 127.0.0.1:54595 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:44:13.988168400+08:00" level=info msg="[TCP] 127.0.0.1:54600 --> app.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:44:14.317424000+08:00" level=info msg="[TCP] 127.0.0.1:54604 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:44:14.701351400+08:00" level=info msg="[TCP] 127.0.0.1:54607 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:44:15.667409600+08:00" level=info msg="[TCP] 127.0.0.1:54611 --> evs.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:44:15.994188200+08:00" level=info msg="[TCP] 127.0.0.1:54614 --> evs.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:44:16.796669800+08:00" level=info msg="[TCP] 127.0.0.1:54620 --> us.i.posthog.com:443 using GLOBAL"
time="2025-07-11T20:44:16.816818600+08:00" level=info msg="[TCP] 127.0.0.1:54628 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-11T20:44:16.817332100+08:00" level=info msg="[TCP] 127.0.0.1:54624 --> api.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:44:16.817332100+08:00" level=info msg="[TCP] 127.0.0.1:54627 --> www.google.com:443 using GLOBAL"
time="2025-07-11T20:44:16.818415700+08:00" level=info msg="[TCP] 127.0.0.1:54623 --> api.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:44:17.312942800+08:00" level=info msg="[TCP] 127.0.0.1:54636 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:44:29.012984300+08:00" level=info msg="[TCP] 127.0.0.1:54660 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:44:29.114097400+08:00" level=info msg="[TCP] 127.0.0.1:54665 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:44:29.719603200+08:00" level=info msg="[TCP] 127.0.0.1:54668 --> us.i.posthog.com:443 using GLOBAL"
time="2025-07-11T20:44:29.772420700+08:00" level=info msg="[TCP] 127.0.0.1:54671 --> cdn.jsdelivr.net:443 using GLOBAL"
time="2025-07-11T20:44:30.601406000+08:00" level=info msg="[TCP] 127.0.0.1:54677 --> mintlify.s3.us-west-1.amazonaws.com:443 using GLOBAL"
time="2025-07-11T20:44:31.554537400+08:00" level=info msg="[TCP] 127.0.0.1:54681 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:44:31.883918400+08:00" level=info msg="[TCP] 127.0.0.1:54684 --> mintlify.b-cdn.net:443 using GLOBAL"
time="2025-07-11T20:44:32.763420400+08:00" level=info msg="[TCP] 127.0.0.1:54691 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:44:32.767191500+08:00" level=info msg="[TCP] 127.0.0.1:54694 --> cdn.lr-in-prod.com:443 using GLOBAL"
time="2025-07-11T20:44:32.884698200+08:00" level=info msg="[TCP] 127.0.0.1:54697 --> events-proxy.mintlify.com:443 using GLOBAL"
time="2025-07-11T20:44:35.693601000+08:00" level=info msg="[TCP] 127.0.0.1:54704 --> cdn.segment.com:443 using GLOBAL"
time="2025-07-11T20:44:35.694509200+08:00" level=info msg="[TCP] 127.0.0.1:54703 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-11T20:44:36.264655200+08:00" level=info msg="[TCP] 127.0.0.1:54709 --> mintlify.s3.us-west-1.amazonaws.com:443 using GLOBAL"
time="2025-07-11T20:44:36.282837300+08:00" level=info msg="[TCP] 127.0.0.1:54712 --> mintlify.s3.us-west-1.amazonaws.com:443 using GLOBAL"
time="2025-07-11T20:44:36.284409800+08:00" level=info msg="[TCP] 127.0.0.1:54715 --> cdn.segment.com:443 using GLOBAL"
time="2025-07-11T20:44:36.659083000+08:00" level=info msg="[TCP] 127.0.0.1:54718 --> mintlify.s3.us-west-1.amazonaws.com:443 using GLOBAL"
time="2025-07-11T20:44:37.757322300+08:00" level=info msg="[TCP] 127.0.0.1:54721 --> cdn.getkoala.com:443 using GLOBAL"
time="2025-07-11T20:44:38.347632500+08:00" level=info msg="[TCP] 127.0.0.1:54726 --> api.getkoala.com:443 using GLOBAL"
time="2025-07-11T20:44:38.603004600+08:00" level=info msg="[TCP] 127.0.0.1:54729 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-11T20:44:38.603004600+08:00" level=info msg="[TCP] 127.0.0.1:54732 --> mintlify.s3-us-west-1.amazonaws.com:443 using GLOBAL"
time="2025-07-11T20:44:39.324982000+08:00" level=info msg="[TCP] 127.0.0.1:54741 --> api.getkoala.com:443 using GLOBAL"
time="2025-07-11T20:44:48.413986600+08:00" level=info msg="[TCP] 127.0.0.1:54775 --> github.com:443 using GLOBAL"
time="2025-07-11T20:45:08.279623400+08:00" level=info msg="[TCP] 127.0.0.1:54826 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:45:15.564484100+08:00" level=info msg="[TCP] 127.0.0.1:54855 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:45:30.305127500+08:00" level=info msg="[TCP] 127.0.0.1:54908 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:46:08.331130700+08:00" level=info msg="[TCP] 127.0.0.1:55009 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:46:33.316884300+08:00" level=info msg="[TCP] 127.0.0.1:55077 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:46:48.318039400+08:00" level=info msg="[TCP] 127.0.0.1:55138 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:46:54.187505600+08:00" level=info msg="[TCP] 127.0.0.1:55156 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:46:54.501039800+08:00" level=info msg="[TCP] 127.0.0.1:55159 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:46:54.641832800+08:00" level=info msg="[TCP] 127.0.0.1:55162 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:46:54.642670700+08:00" level=info msg="[TCP] 127.0.0.1:55163 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:46:54.720594300+08:00" level=info msg="[TCP] 127.0.0.1:55168 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:46:55.736288000+08:00" level=info msg="[TCP] 127.0.0.1:55173 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:46:55.824433600+08:00" level=info msg="[TCP] 127.0.0.1:55177 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:46:55.826910700+08:00" level=info msg="[TCP] 127.0.0.1:55176 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:46:58.417130200+08:00" level=info msg="[TCP] 127.0.0.1:55182 --> marketplace.cursorapi.com:443 using GLOBAL"
time="2025-07-11T20:46:59.226018300+08:00" level=info msg="[TCP] 127.0.0.1:55187 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:46:59.227690700+08:00" level=info msg="[TCP] 127.0.0.1:55188 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:46:59.295671000+08:00" level=info msg="[TCP] 127.0.0.1:55193 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:46:59.322633500+08:00" level=info msg="[TCP] 127.0.0.1:55197 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:46:59.325212400+08:00" level=info msg="[TCP] 127.0.0.1:55196 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-11T20:46:59.433014100+08:00" level=info msg="[TCP] 127.0.0.1:55202 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:46:59.434704500+08:00" level=info msg="[TCP] 127.0.0.1:55203 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:46:59.586658300+08:00" level=info msg="[TCP] 127.0.0.1:55208 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.593558300+08:00" level=info msg="[TCP] 127.0.0.1:55210 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.594071700+08:00" level=info msg="[TCP] 127.0.0.1:55211 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.594782100+08:00" level=info msg="[TCP] 127.0.0.1:55213 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.595294600+08:00" level=info msg="[TCP] 127.0.0.1:55209 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.596253000+08:00" level=info msg="[TCP] 127.0.0.1:55214 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.598750300+08:00" level=info msg="[TCP] 127.0.0.1:55223 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.598750300+08:00" level=info msg="[TCP] 127.0.0.1:55212 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.598750300+08:00" level=info msg="[TCP] 127.0.0.1:55216 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.598750300+08:00" level=info msg="[TCP] 127.0.0.1:55217 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.599264900+08:00" level=info msg="[TCP] 127.0.0.1:55215 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.599264900+08:00" level=info msg="[TCP] 127.0.0.1:55219 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.599776500+08:00" level=info msg="[TCP] 127.0.0.1:55218 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.599776500+08:00" level=info msg="[TCP] 127.0.0.1:55225 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.599776500+08:00" level=info msg="[TCP] 127.0.0.1:55220 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.599776500+08:00" level=info msg="[TCP] 127.0.0.1:55222 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.601268600+08:00" level=info msg="[TCP] 127.0.0.1:55224 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.604536700+08:00" level=info msg="[TCP] 127.0.0.1:55228 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.604536700+08:00" level=info msg="[TCP] 127.0.0.1:55221 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.604536700+08:00" level=info msg="[TCP] 127.0.0.1:55226 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.605063900+08:00" level=info msg="[TCP] 127.0.0.1:55235 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.605063900+08:00" level=info msg="[TCP] 127.0.0.1:55232 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.605063900+08:00" level=info msg="[TCP] 127.0.0.1:55227 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.605577900+08:00" level=info msg="[TCP] 127.0.0.1:55231 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.605577900+08:00" level=info msg="[TCP] 127.0.0.1:55229 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.605577900+08:00" level=info msg="[TCP] 127.0.0.1:55230 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.605577900+08:00" level=info msg="[TCP] 127.0.0.1:55236 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.902004100+08:00" level=info msg="[TCP] 127.0.0.1:55291 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:46:59.917020500+08:00" level=info msg="[TCP] 127.0.0.1:55296 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:00.354630800+08:00" level=info msg="[TCP] 127.0.0.1:55299 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-11T20:47:00.526630000+08:00" level=info msg="[TCP] 127.0.0.1:55302 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-11T20:47:00.610761800+08:00" level=info msg="[TCP] 127.0.0.1:55305 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:00.713859200+08:00" level=info msg="[TCP] 127.0.0.1:55308 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:00.721321000+08:00" level=info msg="[TCP] 127.0.0.1:55311 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:00.745366700+08:00" level=info msg="[TCP] 127.0.0.1:55314 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:00.754413300+08:00" level=info msg="[TCP] 127.0.0.1:55317 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:00.761915000+08:00" level=info msg="[TCP] 127.0.0.1:55320 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:00.771891700+08:00" level=info msg="[TCP] 127.0.0.1:55323 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.029911300+08:00" level=info msg="[TCP] 127.0.0.1:55326 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.049590000+08:00" level=info msg="[TCP] 127.0.0.1:55329 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.056395500+08:00" level=info msg="[TCP] 127.0.0.1:55332 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.057661000+08:00" level=info msg="[TCP] 127.0.0.1:55333 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.057661000+08:00" level=info msg="[TCP] 127.0.0.1:55337 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.060778200+08:00" level=info msg="[TCP] 127.0.0.1:55336 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.063195000+08:00" level=info msg="[TCP] 127.0.0.1:55335 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.063195000+08:00" level=info msg="[TCP] 127.0.0.1:55334 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.089958800+08:00" level=info msg="[TCP] 127.0.0.1:55351 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.099745500+08:00" level=info msg="[TCP] 127.0.0.1:55354 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.103252700+08:00" level=info msg="[TCP] 127.0.0.1:55355 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.234697700+08:00" level=info msg="[TCP] 127.0.0.1:55361 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.381459700+08:00" level=info msg="[TCP] 127.0.0.1:55365 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.429339400+08:00" level=info msg="[TCP] 127.0.0.1:55368 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-11T20:47:01.431870100+08:00" level=info msg="[TCP] 127.0.0.1:55370 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-11T20:47:01.431870100+08:00" level=info msg="[TCP] 127.0.0.1:55369 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-11T20:47:01.461574200+08:00" level=info msg="[TCP] 127.0.0.1:55378 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.465578200+08:00" level=info msg="[TCP] 127.0.0.1:55381 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.523738300+08:00" level=info msg="[TCP] 127.0.0.1:55384 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:01.641126800+08:00" level=info msg="[TCP] 127.0.0.1:55390 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:02.026019800+08:00" level=info msg="[TCP] 127.0.0.1:55394 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:02.038089000+08:00" level=info msg="[TCP] 127.0.0.1:55397 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:02.211199300+08:00" level=info msg="[TCP] 127.0.0.1:55401 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:02.239954500+08:00" level=info msg="[TCP] 127.0.0.1:55404 --> mcp.context7.com:443 using GLOBAL"
time="2025-07-11T20:47:02.251415500+08:00" level=info msg="[TCP] 127.0.0.1:55407 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:02.358971900+08:00" level=info msg="[TCP] 127.0.0.1:55410 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:47:02.611281600+08:00" level=info msg="[TCP] 127.0.0.1:55413 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:03.320691200+08:00" level=info msg="[TCP] 127.0.0.1:55417 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:03.450200600+08:00" level=info msg="[TCP] 127.0.0.1:55420 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:03.866772800+08:00" level=info msg="[TCP] 127.0.0.1:55424 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-11T20:47:05.233468400+08:00" level=info msg="[TCP] 127.0.0.1:55430 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:06.569082500+08:00" level=info msg="[TCP] 127.0.0.1:55435 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:06.569082500+08:00" level=info msg="[TCP] 127.0.0.1:55438 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:06.569596100+08:00" level=info msg="[TCP] 127.0.0.1:55436 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:06.569596100+08:00" level=info msg="[TCP] 127.0.0.1:55434 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:06.571470600+08:00" level=info msg="[TCP] 127.0.0.1:55437 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:06.676291500+08:00" level=info msg="[TCP] 127.0.0.1:55449 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:47:06.999247600+08:00" level=info msg="[TCP] 127.0.0.1:55452 --> api.segment.io:443 using GLOBAL"
time="2025-07-11T20:47:07.624276100+08:00" level=info msg="[TCP] 127.0.0.1:55455 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:08.398815400+08:00" level=info msg="[TCP] 127.0.0.1:55460 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:47:08.522746900+08:00" level=info msg="[TCP] 127.0.0.1:55463 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-11T20:47:09.234890200+08:00" level=info msg="[TCP] 127.0.0.1:55466 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:09.283654600+08:00" level=info msg="[TCP] 127.0.0.1:55469 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:10.317348600+08:00" level=info msg="[TCP] 127.0.0.1:55482 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:47:11.147029200+08:00" level=info msg="[TCP] 127.0.0.1:55487 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:12.547331500+08:00" level=info msg="[TCP] 127.0.0.1:55490 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:15.006862900+08:00" level=info msg="[TCP] 127.0.0.1:55495 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:16.566312200+08:00" level=info msg="[TCP] 127.0.0.1:55498 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:16.636211600+08:00" level=info msg="[TCP] 127.0.0.1:55501 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:22.309397500+08:00" level=info msg="[TCP] 127.0.0.1:55508 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:23.699412300+08:00" level=info msg="[TCP] 127.0.0.1:55513 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:24.096268500+08:00" level=info msg="[TCP] 127.0.0.1:55516 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:26.565006600+08:00" level=info msg="[TCP] 127.0.0.1:55523 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:26.574853800+08:00" level=info msg="[TCP] 127.0.0.1:55526 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:29.804754100+08:00" level=info msg="[TCP] 127.0.0.1:55531 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:47:30.307767700+08:00" level=info msg="[TCP] 127.0.0.1:55534 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:31.779845400+08:00" level=info msg="[TCP] 127.0.0.1:55537 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-11T20:47:32.365605700+08:00" level=info msg="[TCP] 127.0.0.1:55542 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-11T20:47:34.432100300+08:00" level=info msg="[TCP] 127.0.0.1:55545 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:36.527759400+08:00" level=info msg="[TCP] 127.0.0.1:55550 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:47:36.580217700+08:00" level=info msg="[TCP] 127.0.0.1:55553 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:37.692627100+08:00" level=info msg="[TCP] 127.0.0.1:55556 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:39.724254800+08:00" level=info msg="[TCP] 127.0.0.1:55562 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:41.797682200+08:00" level=info msg="[TCP] 127.0.0.1:55577 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:42.319109700+08:00" level=info msg="[TCP] 127.0.0.1:55580 --> us.i.posthog.com:443 using GLOBAL"
time="2025-07-11T20:47:46.584901500+08:00" level=info msg="[TCP] 127.0.0.1:55585 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:46.586125700+08:00" level=info msg="[TCP] 127.0.0.1:55586 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:49.260654200+08:00" level=info msg="[TCP] 127.0.0.1:55593 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:47:55.180496700+08:00" level=info msg="[TCP] 127.0.0.1:55600 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:47:55.823522500+08:00" level=info msg="[TCP] 127.0.0.1:55603 --> activity.windows.com:443 using GLOBAL"
time="2025-07-11T20:47:56.553429800+08:00" level=info msg="[TCP] 127.0.0.1:55608 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:56.583740900+08:00" level=info msg="[TCP] 127.0.0.1:55611 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:47:56.646920400+08:00" level=info msg="[TCP] 127.0.0.1:55614 --> augment.talewua.com:80 using GLOBAL"
time="2025-07-11T20:47:58.136533800+08:00" level=info msg="[TCP] 127.0.0.1:55617 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:48:05.255908000+08:00" level=info msg="[TCP] 127.0.0.1:55624 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:05.611718700+08:00" level=info msg="[TCP] 127.0.0.1:55628 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:48:06.595326300+08:00" level=info msg="[TCP] 127.0.0.1:55633 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:08.458552100+08:00" level=info msg="[TCP] 127.0.0.1:55636 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:48:14.667004300+08:00" level=info msg="[TCP] 127.0.0.1:55654 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:48:16.608835400+08:00" level=info msg="[TCP] 127.0.0.1:55659 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:26.562316500+08:00" level=info msg="[TCP] 127.0.0.1:55668 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:26.623086200+08:00" level=info msg="[TCP] 127.0.0.1:55671 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:28.574559500+08:00" level=info msg="[TCP] 127.0.0.1:55676 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:28.711324400+08:00" level=info msg="[TCP] 127.0.0.1:55679 --> otheve.beacon.qq.com:443 using GLOBAL"
time="2025-07-11T20:48:36.625825400+08:00" level=info msg="[TCP] 127.0.0.1:55699 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:36.626894400+08:00" level=info msg="[TCP] 127.0.0.1:55698 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:38.290814500+08:00" level=info msg="[TCP] 127.0.0.1:55704 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:48:43.517003000+08:00" level=info msg="[TCP] 127.0.0.1:55721 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:43.528039500+08:00" level=info msg="[TCP] 127.0.0.1:55724 --> internal-api-lark-api.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:43.840492400+08:00" level=info msg="[TCP] 127.0.0.1:55727 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:45.628600900+08:00" level=info msg="[TCP] 127.0.0.1:55732 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:45.707251300+08:00" level=info msg="[TCP] 127.0.0.1:55735 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:45.734727400+08:00" level=info msg="[TCP] 127.0.0.1:55738 --> internal-api-lark-api.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:46.387159100+08:00" level=info msg="[TCP] 127.0.0.1:55741 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:46.396104000+08:00" level=info msg="[TCP] 127.0.0.1:55744 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:46.628191900+08:00" level=info msg="[TCP] 127.0.0.1:55747 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:46.736562100+08:00" level=info msg="[TCP] 127.0.0.1:55750 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:47.322624200+08:00" level=info msg="[TCP] 127.0.0.1:55753 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:47.361056900+08:00" level=info msg="[TCP] 127.0.0.1:55756 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:48:47.363858700+08:00" level=info msg="[TCP] 127.0.0.1:55757 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:48:47.484219700+08:00" level=info msg="[TCP] 127.0.0.1:55762 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:47.629826600+08:00" level=info msg="[TCP] 127.0.0.1:55765 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:47.849836500+08:00" level=info msg="[TCP] 127.0.0.1:55768 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:48:48.382192100+08:00" level=info msg="[TCP] 127.0.0.1:55772 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:48:49.387078500+08:00" level=info msg="[TCP] 127.0.0.1:55777 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:48:50.605120000+08:00" level=info msg="[TCP] 127.0.0.1:55780 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:48:54.979219000+08:00" level=info msg="[TCP] 127.0.0.1:55787 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:48:56.634738800+08:00" level=info msg="[TCP] 127.0.0.1:55790 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:56.636333900+08:00" level=info msg="[TCP] 127.0.0.1:55791 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:59.385616100+08:00" level=info msg="[TCP] 127.0.0.1:55798 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:48:59.400118500+08:00" level=info msg="[TCP] 127.0.0.1:55801 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:06.717986100+08:00" level=info msg="[TCP] 127.0.0.1:55809 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:07.154112300+08:00" level=info msg="[TCP] 127.0.0.1:55813 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:49:07.460337600+08:00" level=info msg="[TCP] 127.0.0.1:55816 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:49:07.842586600+08:00" level=info msg="[TCP] 127.0.0.1:55819 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:49:10.702285100+08:00" level=info msg="[TCP] 127.0.0.1:55834 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:16.721447600+08:00" level=info msg="[TCP] 127.0.0.1:55841 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:17.107764300+08:00" level=info msg="[TCP] 127.0.0.1:55844 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:49:19.466116800+08:00" level=info msg="[TCP] 127.0.0.1:55849 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:20.495428900+08:00" level=info msg="[TCP] 127.0.0.1:55852 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:22.714197800+08:00" level=info msg="[TCP] 127.0.0.1:55857 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:23.969168400+08:00" level=info msg="[TCP] 127.0.0.1:55860 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:25.916901600+08:00" level=info msg="[TCP] 127.0.0.1:55866 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:26.557537200+08:00" level=info msg="[TCP] 127.0.0.1:55869 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:26.727144800+08:00" level=info msg="[TCP] 127.0.0.1:55872 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:36.768463300+08:00" level=info msg="[TCP] 127.0.0.1:55881 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:38.294569800+08:00" level=info msg="[TCP] 127.0.0.1:55886 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:49:41.316553800+08:00" level=info msg="[TCP] 127.0.0.1:55901 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:49:41.552024100+08:00" level=info msg="[TCP] 127.0.0.1:55904 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:41.554364000+08:00" level=info msg="[TCP] 127.0.0.1:55905 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:41.820163800+08:00" level=info msg="[TCP] 127.0.0.1:55910 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:49:42.086638100+08:00" level=info msg="[TCP] 127.0.0.1:55913 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:45.594028500+08:00" level=info msg="[TCP] 127.0.0.1:55918 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:49:46.815165500+08:00" level=info msg="[TCP] 127.0.0.1:55923 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:47.482197700+08:00" level=info msg="[TCP] 127.0.0.1:55926 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:49:51.520188800+08:00" level=info msg="[TCP] 127.0.0.1:55931 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:51.652874300+08:00" level=info msg="[TCP] 127.0.0.1:55934 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:51.835388700+08:00" level=info msg="[TCP] 127.0.0.1:55937 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:52.570969800+08:00" level=info msg="[TCP] 127.0.0.1:55942 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:52.813650600+08:00" level=info msg="[TCP] 127.0.0.1:55945 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:49:53.613188500+08:00" level=info msg="[TCP] 127.0.0.1:55948 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:53.676867700+08:00" level=info msg="[TCP] 127.0.0.1:55951 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:53.760276900+08:00" level=info msg="[TCP] 127.0.0.1:55954 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:54.638402500+08:00" level=info msg="[TCP] 127.0.0.1:55957 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:54.981173000+08:00" level=info msg="[TCP] 127.0.0.1:55960 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:55.656048200+08:00" level=info msg="[TCP] 127.0.0.1:55965 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:56.001479800+08:00" level=info msg="[TCP] 127.0.0.1:55968 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:49:56.557336000+08:00" level=info msg="[TCP] 127.0.0.1:55971 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:56.821197000+08:00" level=info msg="[TCP] 127.0.0.1:55974 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:49:57.641722300+08:00" level=info msg="[TCP] 127.0.0.1:55977 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:00.402746400+08:00" level=info msg="[TCP] 127.0.0.1:55982 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:02.389499700+08:00" level=info msg="[TCP] 127.0.0.1:55988 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:02.418810300+08:00" level=info msg="[TCP] 127.0.0.1:55991 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-11T20:50:04.344573100+08:00" level=info msg="[TCP] 127.0.0.1:55994 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:05.881994800+08:00" level=info msg="[TCP] 127.0.0.1:55999 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-11T20:50:06.364706200+08:00" level=info msg="[TCP] 127.0.0.1:56002 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:06.864060500+08:00" level=info msg="[TCP] 127.0.0.1:56005 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:50:07.310085300+08:00" level=info msg="[TCP] 127.0.0.1:56008 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:50:07.324349800+08:00" level=info msg="[TCP] 127.0.0.1:56011 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:50:07.327603800+08:00" level=info msg="[TCP] 127.0.0.1:56012 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:50:08.330598300+08:00" level=info msg="[TCP] 127.0.0.1:56022 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:08.582310100+08:00" level=info msg="[TCP] 127.0.0.1:56025 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:50:10.243370400+08:00" level=info msg="[TCP] 127.0.0.1:56028 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:12.223085600+08:00" level=info msg="[TCP] 127.0.0.1:56043 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:14.206515800+08:00" level=info msg="[TCP] 127.0.0.1:56048 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:14.309448900+08:00" level=info msg="[TCP] 127.0.0.1:56051 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:50:16.240769900+08:00" level=info msg="[TCP] 127.0.0.1:56054 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:16.915541900+08:00" level=info msg="[TCP] 127.0.0.1:56058 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:50:18.230870300+08:00" level=info msg="[TCP] 127.0.0.1:56063 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:19.867744600+08:00" level=info msg="[TCP] 127.0.0.1:56068 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:19.901434600+08:00" level=info msg="[TCP] 127.0.0.1:56072 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:19.902554500+08:00" level=info msg="[TCP] 127.0.0.1:56071 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:19.903534900+08:00" level=info msg="[TCP] 127.0.0.1:56073 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:20.947240600+08:00" level=info msg="[TCP] 127.0.0.1:56082 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:20.953302100+08:00" level=info msg="[TCP] 127.0.0.1:56085 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:21.723158600+08:00" level=info msg="[TCP] 127.0.0.1:56088 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:23.206729400+08:00" level=info msg="[TCP] 127.0.0.1:56093 --> cdnjs.cloudflare.com:443 using GLOBAL"
time="2025-07-11T20:50:26.552298400+08:00" level=info msg="[TCP] 127.0.0.1:56098 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:50:26.963229200+08:00" level=info msg="[TCP] 127.0.0.1:56101 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:50:28.570323100+08:00" level=info msg="[TCP] 127.0.0.1:56104 --> d17.api.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:50:33.809326000+08:00" level=info msg="[TCP] 127.0.0.1:56113 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:50:33.809326000+08:00" level=info msg="[TCP] 127.0.0.1:56111 --> docs.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:50:33.831895500+08:00" level=info msg="[TCP] 127.0.0.1:56117 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:50:33.871849400+08:00" level=info msg="[TCP] 127.0.0.1:56120 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-11T20:50:34.655518700+08:00" level=info msg="[TCP] 127.0.0.1:56126 --> events-proxy.mintlify.com:443 using GLOBAL"
time="2025-07-11T20:50:34.657343100+08:00" level=info msg="[TCP] 127.0.0.1:56123 --> cdn.segment.com:443 using GLOBAL"
time="2025-07-11T20:50:34.957919200+08:00" level=info msg="[TCP] 127.0.0.1:56129 --> events-proxy.mintlify.com:443 using GLOBAL"
time="2025-07-11T20:50:34.999598500+08:00" level=info msg="[TCP] 127.0.0.1:56132 --> cdn.getkoala.com:443 using GLOBAL"
time="2025-07-11T20:50:35.402193100+08:00" level=info msg="[TCP] 127.0.0.1:56137 --> cdn.segment.com:443 using GLOBAL"
time="2025-07-11T20:50:35.796863900+08:00" level=info msg="[TCP] 127.0.0.1:56140 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:50:36.045615300+08:00" level=info msg="[TCP] 127.0.0.1:56143 --> api.getkoala.com:443 using GLOBAL"
time="2025-07-11T20:50:36.240113700+08:00" level=info msg="[TCP] 127.0.0.1:56146 --> www.google.com:443 using GLOBAL"
time="2025-07-11T20:50:36.241140500+08:00" level=info msg="[TCP] 127.0.0.1:56147 --> api.grdt.augmentcode.com:443 using GLOBAL"
time="2025-07-11T20:50:36.923288900+08:00" level=info msg="[TCP] 127.0.0.1:56152 --> api.getkoala.com:443 using GLOBAL"
time="2025-07-11T20:50:40.311383200+08:00" level=info msg="[TCP] 127.0.0.1:56157 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-11T20:50:44.785335100+08:00" level=info msg="[TCP] 127.0.0.1:56174 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:44.808401100+08:00" level=info msg="[TCP] 127.0.0.1:56177 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:46.254519100+08:00" level=info msg="[TCP] 127.0.0.1:56183 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:46.264254000+08:00" level=info msg="[TCP] 127.0.0.1:56180 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:46.315798000+08:00" level=info msg="[TCP] 127.0.0.1:56186 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:50:46.638707600+08:00" level=info msg="[TCP] 127.0.0.1:56189 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:47.192481700+08:00" level=info msg="[TCP] 127.0.0.1:56192 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:47.401902700+08:00" level=info msg="[TCP] 127.0.0.1:56195 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:50:48.404444500+08:00" level=info msg="[TCP] 127.0.0.1:56200 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-11T20:50:48.409923100+08:00" level=info msg="[TCP] 127.0.0.1:56203 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:49.828414700+08:00" level=info msg="[TCP] 127.0.0.1:56206 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:50.813488800+08:00" level=info msg="[TCP] 127.0.0.1:56211 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:50.839683500+08:00" level=info msg="[TCP] 127.0.0.1:56214 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:53.791884400+08:00" level=info msg="[TCP] 127.0.0.1:56219 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:55.012591100+08:00" level=info msg="[TCP] 127.0.0.1:56222 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:55.803061500+08:00" level=info msg="[TCP] 127.0.0.1:56225 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:57.318023900+08:00" level=info msg="[TCP] 127.0.0.1:56230 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:50:57.829993500+08:00" level=info msg="[TCP] 127.0.0.1:56233 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:50:59.815116300+08:00" level=info msg="[TCP] 127.0.0.1:56238 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:01.811829300+08:00" level=info msg="[TCP] 127.0.0.1:56241 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:03.817880100+08:00" level=info msg="[TCP] 127.0.0.1:56246 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:05.832486000+08:00" level=info msg="[TCP] 127.0.0.1:56250 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:06.738794000+08:00" level=info msg="[TCP] 127.0.0.1:56254 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:07.834110200+08:00" level=info msg="[TCP] 127.0.0.1:56257 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:08.632197700+08:00" level=info msg="[TCP] 127.0.0.1:56260 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:51:09.859807000+08:00" level=info msg="[TCP] 127.0.0.1:56265 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:10.718455500+08:00" level=info msg="[TCP] 127.0.0.1:56278 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:12.062430600+08:00" level=info msg="[TCP] 127.0.0.1:56283 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:12.597607900+08:00" level=info msg="[TCP] 127.0.0.1:56286 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:14.528443300+08:00" level=info msg="[TCP] 127.0.0.1:56291 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:16.486860100+08:00" level=info msg="[TCP] 127.0.0.1:56296 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:18.644718400+08:00" level=info msg="[TCP] 127.0.0.1:56301 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:18.726861300+08:00" level=info msg="[TCP] 127.0.0.1:56304 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:18.773816600+08:00" level=info msg="[TCP] 127.0.0.1:56307 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:19.877037100+08:00" level=info msg="[TCP] 127.0.0.1:56310 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:23.891632900+08:00" level=info msg="[TCP] 127.0.0.1:56316 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:23.894511600+08:00" level=info msg="[TCP] 127.0.0.1:56315 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:32.623963300+08:00" level=info msg="[TCP] 127.0.0.1:56335 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:33.819715400+08:00" level=info msg="[TCP] 127.0.0.1:56340 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:51:41.130786200+08:00" level=info msg="[TCP] 127.0.0.1:56361 --> login.live.com:443 using GLOBAL"
time="2025-07-11T20:51:42.021920000+08:00" level=info msg="[TCP] 127.0.0.1:56364 --> static.nvidiagrid.net:443 using GLOBAL"
time="2025-07-11T20:51:53.742706300+08:00" level=info msg="[TCP] 127.0.0.1:56380 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:53.967961100+08:00" level=info msg="[TCP] 127.0.0.1:56384 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:53.971053800+08:00" level=info msg="[TCP] 127.0.0.1:56383 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:54.989647300+08:00" level=info msg="[TCP] 127.0.0.1:56391 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:56.035018700+08:00" level=info msg="[TCP] 127.0.0.1:56394 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:56.037035800+08:00" level=info msg="[TCP] 127.0.0.1:56395 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:57.011487200+08:00" level=info msg="[TCP] 127.0.0.1:56400 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:58.027950600+08:00" level=info msg="[TCP] 127.0.0.1:56405 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:51:58.564747200+08:00" level=info msg="[TCP] 127.0.0.1:56408 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:01.061920000+08:00" level=info msg="[TCP] 127.0.0.1:56414 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:04.656276100+08:00" level=info msg="[TCP] 127.0.0.1:56423 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:04.667402200+08:00" level=info msg="[TCP] 127.0.0.1:56426 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:04.714517500+08:00" level=info msg="[TCP] 127.0.0.1:56429 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:04.961562600+08:00" level=info msg="[TCP] 127.0.0.1:56432 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:04.963143800+08:00" level=info msg="[TCP] 127.0.0.1:56433 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:04.992118200+08:00" level=info msg="[TCP] 127.0.0.1:56439 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:04.992861000+08:00" level=info msg="[TCP] 127.0.0.1:56438 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:06.022073300+08:00" level=info msg="[TCP] 127.0.0.1:56444 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:06.171405100+08:00" level=info msg="[TCP] 127.0.0.1:56447 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:52:07.247547400+08:00" level=info msg="[TCP] 127.0.0.1:56452 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:08.697720400+08:00" level=info msg="[TCP] 127.0.0.1:56461 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:52:09.097782100+08:00" level=info msg="[TCP] 127.0.0.1:56466 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:10.102192000+08:00" level=info msg="[TCP] 127.0.0.1:56471 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:10.131903200+08:00" level=info msg="[TCP] 127.0.0.1:56474 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:10.505398600+08:00" level=info msg="[TCP] 127.0.0.1:56487 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-11T20:52:10.507050700+08:00" level=info msg="[TCP] 127.0.0.1:56488 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-11T20:52:12.093611800+08:00" level=info msg="[TCP] 127.0.0.1:56493 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:14.289609400+08:00" level=info msg="[TCP] 127.0.0.1:56499 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:14.911499100+08:00" level=info msg="[TCP] 127.0.0.1:56502 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:52:16.392053700+08:00" level=info msg="[TCP] 127.0.0.1:56507 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:18.457551100+08:00" level=info msg="[TCP] 127.0.0.1:56510 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:19.211761100+08:00" level=info msg="[TCP] 127.0.0.1:56515 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:52:20.727683800+08:00" level=info msg="[TCP] 127.0.0.1:56518 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:21.305015800+08:00" level=info msg="[TCP] 127.0.0.1:56521 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:52:21.321742700+08:00" level=info msg="[TCP] 127.0.0.1:56524 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-11T20:52:21.389067800+08:00" level=info msg="[TCP] 127.0.0.1:56527 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:52:21.908233900+08:00" level=info msg="[TCP] 127.0.0.1:56530 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:52:22.118121800+08:00" level=info msg="[TCP] 127.0.0.1:56534 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:52:22.427455900+08:00" level=info msg="[TCP] 127.0.0.1:56538 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:52:22.802707200+08:00" level=info msg="[TCP] 127.0.0.1:56541 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:22.878350800+08:00" level=info msg="[TCP] 127.0.0.1:56544 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:52:23.313005300+08:00" level=info msg="[TCP] 127.0.0.1:56547 --> api.getkoala.com:443 using GLOBAL"
time="2025-07-11T20:52:23.703858500+08:00" level=info msg="[TCP] 127.0.0.1:56550 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:52:24.798040400+08:00" level=info msg="[TCP] 127.0.0.1:56553 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:26.906082100+08:00" level=info msg="[TCP] 127.0.0.1:56559 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:28.106205800+08:00" level=info msg="[TCP] 127.0.0.1:56562 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:28.848800400+08:00" level=info msg="[TCP] 127.0.0.1:56567 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:31.305110600+08:00" level=info msg="[TCP] 127.0.0.1:56571 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:52:33.817194800+08:00" level=info msg="[TCP] 127.0.0.1:56576 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:33.817194800+08:00" level=info msg="[TCP] 127.0.0.1:56575 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:34.796631400+08:00" level=info msg="[TCP] 127.0.0.1:56583 --> internal-api-drive-stream.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:52:53.761166600+08:00" level=info msg="[TCP] 127.0.0.1:56619 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:54.245630100+08:00" level=info msg="[TCP] 127.0.0.1:56622 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-11T20:52:54.991993300+08:00" level=info msg="[TCP] 127.0.0.1:56625 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:52:59.095106200+08:00" level=info msg="[TCP] 127.0.0.1:56632 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:53:03.339645500+08:00" level=info msg="[TCP] 127.0.0.1:56637 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:53:04.769181000+08:00" level=info msg="[TCP] 127.0.0.1:56640 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:53:06.487310300+08:00" level=info msg="[TCP] 127.0.0.1:56645 --> v20.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:53:08.755822700+08:00" level=info msg="[TCP] 127.0.0.1:56652 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:53:21.259807700+08:00" level=info msg="[TCP] 127.0.0.1:56674 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:53:24.706832600+08:00" level=info msg="[TCP] 127.0.0.1:56679 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:53:32.797112700+08:00" level=info msg="[TCP] 127.0.0.1:56687 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:53:43.044072900+08:00" level=info msg="[TCP] 127.0.0.1:56708 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:53:45.264309700+08:00" level=info msg="[TCP] 127.0.0.1:56713 --> activity.windows.com:443 using GLOBAL"
time="2025-07-11T20:53:47.242686400+08:00" level=info msg="[TCP] 127.0.0.1:56717 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:53:49.792071300+08:00" level=info msg="[TCP] 127.0.0.1:56722 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-11T20:53:54.989254800+08:00" level=info msg="[TCP] 127.0.0.1:56729 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:03.342263100+08:00" level=info msg="[TCP] 127.0.0.1:56739 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:54:08.813749300+08:00" level=info msg="[TCP] 127.0.0.1:56744 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:54:13.308161300+08:00" level=info msg="[TCP] 127.0.0.1:56761 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:54:16.669786300+08:00" level=info msg="[TCP] 127.0.0.1:56766 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:17.974250800+08:00" level=info msg="[TCP] 127.0.0.1:56769 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:18.910906500+08:00" level=info msg="[TCP] 127.0.0.1:56774 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:19.051476800+08:00" level=info msg="[TCP] 127.0.0.1:56777 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:19.088124500+08:00" level=info msg="[TCP] 127.0.0.1:56780 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:54:19.321351000+08:00" level=info msg="[TCP] 127.0.0.1:56783 --> internal-api-lark-api.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:54:19.348006600+08:00" level=info msg="[TCP] 127.0.0.1:56786 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:19.348006600+08:00" level=info msg="[TCP] 127.0.0.1:56788 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:19.348006600+08:00" level=info msg="[TCP] 127.0.0.1:56789 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:19.348896200+08:00" level=info msg="[TCP] 127.0.0.1:56787 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:19.361741400+08:00" level=info msg="[TCP] 127.0.0.1:56798 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:20.095159600+08:00" level=info msg="[TCP] 127.0.0.1:56801 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:20.149287100+08:00" level=info msg="[TCP] 127.0.0.1:56805 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:20.152626500+08:00" level=info msg="[TCP] 127.0.0.1:56804 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:20.197359500+08:00" level=info msg="[TCP] 127.0.0.1:56810 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:20.202295000+08:00" level=info msg="[TCP] 127.0.0.1:56813 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:20.317633800+08:00" level=info msg="[TCP] 127.0.0.1:56816 --> internal-api-security.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:54:20.328094900+08:00" level=info msg="[TCP] 127.0.0.1:56819 --> internal-api-lark-api.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:54:21.160843700+08:00" level=info msg="[TCP] 127.0.0.1:56822 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:25.794527200+08:00" level=info msg="[TCP] 127.0.0.1:56829 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:54:26.666008400+08:00" level=info msg="[TCP] 127.0.0.1:56832 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:26.801711300+08:00" level=info msg="[TCP] 127.0.0.1:56835 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:28.804264200+08:00" level=info msg="[TCP] 127.0.0.1:56840 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:29.811617600+08:00" level=info msg="[TCP] 127.0.0.1:56843 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:30.040705000+08:00" level=info msg="[TCP] 127.0.0.1:56846 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:32.314622300+08:00" level=info msg="[TCP] 127.0.0.1:56851 --> slardar-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:54:32.505768200+08:00" level=info msg="[TCP] 127.0.0.1:56854 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-11T20:54:32.941322900+08:00" level=info msg="[TCP] 127.0.0.1:56857 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:32.961003600+08:00" level=info msg="[TCP] 127.0.0.1:56860 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:32.962999000+08:00" level=info msg="[TCP] 127.0.0.1:56861 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:32.989181200+08:00" level=info msg="[TCP] 127.0.0.1:56866 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:33.887274800+08:00" level=info msg="[TCP] 127.0.0.1:56871 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:34.145983600+08:00" level=info msg="[TCP] 127.0.0.1:56874 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:37.392611700+08:00" level=info msg="[TCP] 127.0.0.1:56879 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:37.396463200+08:00" level=info msg="[TCP] 127.0.0.1:56882 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:38.264644400+08:00" level=info msg="[TCP] 127.0.0.1:56885 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:54:39.243399600+08:00" level=info msg="[TCP] 127.0.0.1:56888 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:39.261529000+08:00" level=info msg="[TCP] 127.0.0.1:56891 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:40.782025800+08:00" level=info msg="[TCP] 127.0.0.1:56906 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:41.693295600+08:00" level=info msg="[TCP] 127.0.0.1:56909 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:41.857930500+08:00" level=info msg="[TCP] 127.0.0.1:56912 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:43.899880400+08:00" level=info msg="[TCP] 127.0.0.1:56917 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:45.264290800+08:00" level=info msg="[TCP] 127.0.0.1:56920 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-11T20:54:46.562958400+08:00" level=info msg="[TCP] 127.0.0.1:56925 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:48.929008800+08:00" level=info msg="[TCP] 127.0.0.1:56930 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:48.965017000+08:00" level=info msg="[TCP] 127.0.0.1:56933 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:50.555568600+08:00" level=info msg="[TCP] 127.0.0.1:56936 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:53.935517100+08:00" level=info msg="[TCP] 127.0.0.1:56942 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:53.937318700+08:00" level=info msg="[TCP] 127.0.0.1:56941 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:54:54.978975700+08:00" level=info msg="[TCP] 127.0.0.1:56949 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:00.970592500+08:00" level=info msg="[TCP] 127.0.0.1:56956 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:01.002883800+08:00" level=info msg="[TCP] 127.0.0.1:56959 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:01.930042600+08:00" level=info msg="[TCP] 127.0.0.1:56962 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:01.959954400+08:00" level=info msg="[TCP] 127.0.0.1:56965 --> cursor.talewua.com:80 using GLOBAL"
time="2025-07-11T20:55:02.966139700+08:00" level=info msg="[TCP] 127.0.0.1:56968 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:03.931225800+08:00" level=info msg="[TCP] 127.0.0.1:56973 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:04.934706100+08:00" level=info msg="[TCP] 127.0.0.1:56977 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:04.938020700+08:00" level=info msg="[TCP] 127.0.0.1:56976 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:06.946677400+08:00" level=info msg="[TCP] 127.0.0.1:56983 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:08.869992300+08:00" level=info msg="[TCP] 127.0.0.1:56987 --> www.bing.com:443 using GLOBAL"
time="2025-07-11T20:55:08.963739600+08:00" level=info msg="[TCP] 127.0.0.1:56990 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:09.247243200+08:00" level=info msg="[TCP] 127.0.0.1:56993 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:10.043034900+08:00" level=info msg="[TCP] 127.0.0.1:56998 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:14.224052300+08:00" level=info msg="[TCP] 127.0.0.1:57013 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:14.224565200+08:00" level=info msg="[TCP] 127.0.0.1:57014 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-11T20:55:14.322088300+08:00" level=info msg="[TCP] 127.0.0.1:57019 --> mcs-bd.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:55:20.322386000+08:00" level=info msg="[TCP] 127.0.0.1:57026 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:55:20.329563400+08:00" level=info msg="[TCP] 127.0.0.1:57029 --> ck0m0jpema.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:55:20.358027900+08:00" level=info msg="[TCP] 127.0.0.1:57032 --> api.steampowered.com:443 using GLOBAL"
time="2025-07-11T20:55:20.946287700+08:00" level=info msg="[TCP] 127.0.0.1:57035 --> internal-api-security-tenant-probe.feishu.cn:443 using GLOBAL"
time="2025-07-11T20:55:31.448872600+08:00" level=info msg="[TCP] 127.0.0.1:57047 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:55:36.100452300+08:00" level=info msg="[TCP] 127.0.0.1:57061 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:55:36.107963100+08:00" level=info msg="[TCP] 127.0.0.1:57060 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:55:36.750119400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57051 --> cursor.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:55:41.362019900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57064 --> augment.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:55:41.362141700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57068 --> augment.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:55:41.478471800+08:00" level=info msg="[TCP] 127.0.0.1:57087 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:55:46.362232700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57091 --> augment.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:55:58.707850000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57107 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:55:58.707850000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57108 --> api2.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:56:04.302642500+08:00" level=info msg="[TCP] 127.0.0.1:57123 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:56:06.437928200+08:00" level=info msg="[TCP] 127.0.0.1:57135 --> news.baidu.com:443 using GLOBAL"
time="2025-07-11T20:56:09.543865700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57131 --> augment.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:56:09.543865700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57140 --> augment.talewua.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:56:09.980927500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57134 --> api.segment.io:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:56:15.182079000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57152 --> api.segment.io:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:56:20.347724600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57176 --> api.segment.io:443 error: dns resolve failed: couldn't find ip"
time="2025-07-11T20:56:25.921537500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:57204 --> api.segment.io:443 error: dns resolve failed: couldn't find ip"
time="2025-07-12T00:56:28.581507500+08:00" level=warning msg="Mihomo shutting down"
