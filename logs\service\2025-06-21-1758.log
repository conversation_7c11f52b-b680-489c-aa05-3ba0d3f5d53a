Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-21T17:58:17.032343700+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-21T17:58:17.040434400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-21T17:58:17.041436900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-21T17:58:17.042301700+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-21T17:58:17.064127700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-21T17:58:17.064624100+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-21T17:58:17.326308900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-21T17:58:17.330259700+08:00" level=info msg="Load GeoSite rule: private"
time="2025-06-21T17:58:17.344490100+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-21T17:58:17.347147900+08:00" level=info msg="Initial configuration complete, total time: 309ms"
time="2025-06-21T17:58:17.347147900+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-06-21T17:58:17.347147900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-21T17:58:17.347147900+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-06-21T17:58:17.347147900+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-06-21T17:58:17.347147900+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-06-21T17:58:17.374296500+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider BBC"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Discord"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Line"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Disney"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider telegram"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider TVB"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider apple"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider NowE"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Lan"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider TVer"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider google"
time="2025-06-21T17:58:17.374349300+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-21T17:58:22.375808800+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.375883900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.375883900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.375883900+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-21T17:58:22.375883900+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-21T17:58:22.375883900+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376394500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-21T17:58:22.376906400+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-21T17:58:22.376394500+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-21T17:58:22.378498400+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-21T17:58:22.378498400+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-21T17:58:22.378498400+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-21T17:58:22.378498400+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-21T17:58:29.378095600+08:00" level=error msg="🇮🇳印度-下载专享[0.5x] ipv6 only failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-21T17:58:29.378095600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-21T18:35:16.842406800+08:00" level=info msg="[TCP] 127.0.0.1:54858 --> us-east-1.console.aws.amazon.com:443 using GLOBAL"
time="2025-06-21T18:35:17.167257700+08:00" level=info msg="[TCP] 127.0.0.1:54860 --> us-east-1.prod.pr.panorama.console.api.aws:443 using GLOBAL"
time="2025-06-21T18:35:17.799255300+08:00" level=info msg="[TCP] 127.0.0.1:54862 --> aws-payment-encryption-prod.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:35:18.420894100+08:00" level=info msg="[TCP] 127.0.0.1:54865 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T18:35:19.264360200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54846 --> sgali-mcs.byteoversea.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T18:35:19.317350900+08:00" level=info msg="[TCP] 127.0.0.1:54871 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-21T18:35:19.791293400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54852 --> us-east-1.prod.pr.panorama.console.api.aws:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T18:35:20.102686700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54855 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T18:35:20.168851000+08:00" level=info msg="[TCP] 127.0.0.1:54874 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T18:35:20.203323800+08:00" level=info msg="[TCP] 127.0.0.1:54876 --> aws-payments-gateway-service.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:35:22.305537700+08:00" level=info msg="[TCP] 127.0.0.1:54880 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T18:35:22.613448300+08:00" level=info msg="[TCP] 127.0.0.1:54883 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T18:35:22.613448300+08:00" level=info msg="[TCP] 127.0.0.1:54886 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T18:35:22.614845800+08:00" level=info msg="[TCP] 127.0.0.1:54882 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T18:35:22.614845800+08:00" level=info msg="[TCP] 127.0.0.1:54884 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T18:35:22.616534200+08:00" level=info msg="[TCP] 127.0.0.1:54885 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T18:35:23.444031100+08:00" level=info msg="[TCP] 127.0.0.1:54893 --> dataplane.rum.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:35:25.629214000+08:00" level=info msg="[TCP] 127.0.0.1:54897 --> aws-payments-gateway-service.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:35:26.319753200+08:00" level=info msg="[TCP] 127.0.0.1:54900 --> ecsnew.console.aliyun.com:443 using GLOBAL"
time="2025-06-21T18:35:26.773269500+08:00" level=info msg="[TCP] 127.0.0.1:54902 --> ecsnew.console.aliyun.com:443 using GLOBAL"
time="2025-06-21T18:35:27.329915700+08:00" level=info msg="[TCP] 127.0.0.1:54904 --> arms-retcode.aliyuncs.com:443 using GLOBAL"
time="2025-06-21T18:35:28.309721100+08:00" level=info msg="[TCP] 127.0.0.1:54907 --> gm.mmstat.com:443 using GLOBAL"
time="2025-06-21T18:35:30.230725600+08:00" level=info msg="[TCP] 127.0.0.1:54910 --> aws-payments-gateway-service.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:35:43.438819600+08:00" level=info msg="[TCP] 127.0.0.1:54924 --> dataplane.rum.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:35:44.392600300+08:00" level=info msg="[TCP] 127.0.0.1:54927 --> api.trae.ai:443 using GLOBAL"
time="2025-06-21T18:35:50.632777200+08:00" level=info msg="[TCP] 127.0.0.1:54936 --> api-sg-central.trae.ai:443 using GLOBAL"
time="2025-06-21T18:35:54.307495700+08:00" level=info msg="[TCP] 127.0.0.1:54943 --> lf3-static.bytednsdoc.com:443 using GLOBAL"
time="2025-06-21T18:35:54.464000000+08:00" level=info msg="[TCP] 127.0.0.1:54949 --> lf3-static.bytednsdoc.com:443 using GLOBAL"
time="2025-06-21T18:36:03.092195000+08:00" level=info msg="[TCP] 127.0.0.1:54963 --> arms-retcode.aliyuncs.com:443 using GLOBAL"
time="2025-06-21T18:36:04.054114000+08:00" level=info msg="[TCP] 127.0.0.1:54965 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-21T18:36:04.471129000+08:00" level=info msg="[TCP] 127.0.0.1:54968 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-21T18:36:04.500575300+08:00" level=info msg="[TCP] 127.0.0.1:54970 --> www.bing.com:443 using GLOBAL"
time="2025-06-21T18:36:04.723252100+08:00" level=info msg="[TCP] 127.0.0.1:54973 --> www.bing.com:443 using GLOBAL"
time="2025-06-21T18:36:08.439271400+08:00" level=info msg="[TCP] 127.0.0.1:54978 --> dataplane.rum.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:36:11.905406400+08:00" level=info msg="[TCP] 127.0.0.1:54984 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-21T18:36:15.466898800+08:00" level=info msg="[TCP] 127.0.0.1:54990 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T18:36:20.956786000+08:00" level=info msg="[TCP] 127.0.0.1:54997 --> us-east-1.prod.pr.panorama.console.api.aws:443 using GLOBAL"
time="2025-06-21T18:36:28.311067200+08:00" level=info msg="[TCP] 127.0.0.1:55004 --> ecsnew.console.aliyun.com:443 using GLOBAL"
time="2025-06-21T18:36:28.685601600+08:00" level=info msg="[TCP] 127.0.0.1:55007 --> ecsnew.console.aliyun.com:443 using GLOBAL"
time="2025-06-21T18:36:29.179236200+08:00" level=info msg="[TCP] 127.0.0.1:55009 --> arms-retcode.aliyuncs.com:443 using GLOBAL"
time="2025-06-21T18:36:30.306055800+08:00" level=info msg="[TCP] 127.0.0.1:55012 --> gm.mmstat.com:443 using GLOBAL"
time="2025-06-21T18:36:34.321008000+08:00" level=info msg="[TCP] 127.0.0.1:55017 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T18:36:35.447345600+08:00" level=info msg="[TCP] 127.0.0.1:55021 --> us-east-1.console.aws.amazon.com:443 using GLOBAL"
time="2025-06-21T18:36:37.310131500+08:00" level=info msg="[TCP] 127.0.0.1:55024 --> aws-payment-encryption-prod.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:36:45.867034000+08:00" level=info msg="[TCP] 127.0.0.1:55034 --> aws-payments-gateway-service.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:36:48.439009200+08:00" level=info msg="[TCP] 127.0.0.1:55040 --> dataplane.rum.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:36:49.637741600+08:00" level=info msg="[TCP] 127.0.0.1:55042 --> console.aws.amazon.com:443 using GLOBAL"
time="2025-06-21T18:36:50.281352600+08:00" level=info msg="[TCP] 127.0.0.1:55045 --> support.console.aws.amazon.com:443 using GLOBAL"
time="2025-06-21T18:36:51.703903800+08:00" level=info msg="[TCP] 127.0.0.1:55049 --> us-east-1.signin.aws.amazon.com:443 using GLOBAL"
time="2025-06-21T18:36:53.997036700+08:00" level=info msg="[TCP] 127.0.0.1:55052 --> global.console.aws.amazon.com:443 using GLOBAL"
time="2025-06-21T18:36:53.999109500+08:00" level=info msg="[TCP] 127.0.0.1:55053 --> a.b.cdn.console.awsstatic.com:443 using GLOBAL"
time="2025-06-21T18:36:54.220265000+08:00" level=info msg="[TCP] 127.0.0.1:55060 --> a.b.cdn.console.awsstatic.com:443 using GLOBAL"
time="2025-06-21T18:36:54.221862600+08:00" level=info msg="[TCP] 127.0.0.1:55059 --> a.b.cdn.console.awsstatic.com:443 using GLOBAL"
time="2025-06-21T18:36:54.221862600+08:00" level=info msg="[TCP] 127.0.0.1:55058 --> a.b.cdn.console.awsstatic.com:443 using GLOBAL"
time="2025-06-21T18:36:54.221862600+08:00" level=info msg="[TCP] 127.0.0.1:55056 --> a.b.cdn.console.awsstatic.com:443 using GLOBAL"
time="2025-06-21T18:36:54.223409400+08:00" level=info msg="[TCP] 127.0.0.1:55057 --> a.b.cdn.console.awsstatic.com:443 using GLOBAL"
time="2025-06-21T18:36:54.224395700+08:00" level=info msg="[TCP] 127.0.0.1:55061 --> a.b.cdn.console.awsstatic.com:443 using GLOBAL"
time="2025-06-21T18:36:54.302579100+08:00" level=info msg="[TCP] 127.0.0.1:55068 --> global.ccs.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:36:54.308712800+08:00" level=info msg="[TCP] 127.0.0.1:55070 --> global.console.aws.amazon.com:443 using GLOBAL"
time="2025-06-21T18:36:55.063187000+08:00" level=info msg="[TCP] 127.0.0.1:55077 --> us-east-1.console.aws.amazon.com:443 using GLOBAL"
time="2025-06-21T18:36:55.066010700+08:00" level=info msg="[TCP] 127.0.0.1:55076 --> cell-0.us-east-1.prod.telemetry.console.api.aws:443 using GLOBAL"
time="2025-06-21T18:36:55.066010700+08:00" level=info msg="[TCP] 127.0.0.1:55074 --> prod.log.shortbread.analytics.console.aws.a2z.com:443 using GLOBAL"
time="2025-06-21T18:36:55.182031900+08:00" level=info msg="[TCP] 127.0.0.1:55080 --> prod.log.shortbread.analytics.console.aws.a2z.com:443 using GLOBAL"
time="2025-06-21T18:36:55.284611800+08:00" level=info msg="[TCP] 127.0.0.1:55082 --> cell-0.us-east-1.prod.telemetry.console.api.aws:443 using GLOBAL"
time="2025-06-21T18:36:55.541068900+08:00" level=info msg="[TCP] 127.0.0.1:55084 --> support.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:36:55.706613300+08:00" level=info msg="[TCP] 127.0.0.1:55086 --> cell-0.us-east-1.prod.telemetry.console.api.aws:443 using GLOBAL"
time="2025-06-21T18:36:56.072633200+08:00" level=info msg="[TCP] 127.0.0.1:55089 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-21T18:36:56.106912200+08:00" level=info msg="[TCP] 127.0.0.1:55091 --> us-east-1.console-api.aws.amazon.com:443 using GLOBAL"
time="2025-06-21T18:36:56.172298900+08:00" level=info msg="[TCP] 127.0.0.1:55093 --> console.aws.amazon.com:443 using GLOBAL"
time="2025-06-21T18:36:57.188755700+08:00" level=info msg="[TCP] 127.0.0.1:55095 --> api.us-east-1.prod.support-console.support.aws.dev:443 using GLOBAL"
time="2025-06-21T18:36:57.202954200+08:00" level=info msg="[TCP] 127.0.0.1:55102 --> api.us-east-1.prod.support-console.support.aws.dev:443 using GLOBAL"
time="2025-06-21T18:36:57.202954200+08:00" level=info msg="[TCP] 127.0.0.1:55098 --> api.us-east-1.prod.support-console.support.aws.dev:443 using GLOBAL"
time="2025-06-21T18:36:57.202954200+08:00" level=info msg="[TCP] 127.0.0.1:55100 --> health.us-east-1.amazonaws.com:443 using GLOBAL"
time="2025-06-21T18:36:57.204904000+08:00" level=info msg="[TCP] 127.0.0.1:55097 --> api.us-east-1.prod.support-console.support.aws.dev:443 using GLOBAL"
time="2025-06-21T18:36:57.297439100+08:00" level=info msg="[TCP] 127.0.0.1:55105 --> ap-northeast-1.ccs.amazonaws.com:443 using GLOBAL"
time="2025-06-21T21:27:57.231874400+08:00" level=info msg="[TCP] 127.0.0.1:50471 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T21:27:57.524153500+08:00" level=info msg="[TCP] 127.0.0.1:50473 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-21T21:27:57.524153500+08:00" level=info msg="[TCP] 127.0.0.1:50475 --> c.msn.cn:443 using GLOBAL"
time="2025-06-21T21:27:57.526733900+08:00" level=info msg="[TCP] 127.0.0.1:50476 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-21T21:27:57.537678500+08:00" level=info msg="[TCP] 127.0.0.1:50479 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-21T21:27:57.586634800+08:00" level=info msg="[TCP] 127.0.0.1:50481 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-21T21:27:57.704873200+08:00" level=info msg="[TCP] 127.0.0.1:50483 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:27:57.827642200+08:00" level=info msg="[TCP] 127.0.0.1:50485 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-21T21:27:57.828851600+08:00" level=info msg="[TCP] 127.0.0.1:50486 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-21T21:27:57.891061200+08:00" level=info msg="[TCP] 127.0.0.1:50489 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-21T21:27:57.892679000+08:00" level=info msg="[TCP] 127.0.0.1:50490 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-21T21:27:57.950153100+08:00" level=info msg="[TCP] 127.0.0.1:50491 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-21T21:27:58.590120600+08:00" level=info msg="[TCP] 127.0.0.1:50497 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-21T21:27:58.653076400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50455 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-21T21:27:58.709481500+08:00" level=info msg="[TCP] 127.0.0.1:50499 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:28:00.565043700+08:00" level=info msg="[TCP] 127.0.0.1:50506 --> c.amazon-adsystem.com:443 using GLOBAL"
time="2025-06-21T21:28:00.566356200+08:00" level=info msg="[TCP] 127.0.0.1:50505 --> s.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:01.572960000+08:00" level=info msg="[TCP] 127.0.0.1:50502 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:01.572960000+08:00" level=info msg="[TCP] 127.0.0.1:50503 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:02.511404900+08:00" level=info msg="[TCP] 127.0.0.1:50512 --> ntp.msn.com:443 using GLOBAL"
time="2025-06-21T21:28:02.596312000+08:00" level=info msg="[TCP] 127.0.0.1:50515 --> micro.rubiconproject.com:443 using GLOBAL"
time="2025-06-21T21:28:02.596837000+08:00" level=info msg="[TCP] 127.0.0.1:50514 --> securepubads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:28:03.350615500+08:00" level=info msg="[TCP] 127.0.0.1:50523 --> cdn.jsdelivr.net:443 using GLOBAL"
time="2025-06-21T21:28:03.353893600+08:00" level=info msg="[TCP] 127.0.0.1:50521 --> c.amazon-adsystem.com:443 using GLOBAL"
time="2025-06-21T21:28:03.355870900+08:00" level=info msg="[TCP] 127.0.0.1:50525 --> securepubads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:28:03.372741800+08:00" level=info msg="[TCP] 127.0.0.1:50527 --> th.bing.com:443 using GLOBAL"
time="2025-06-21T21:28:03.374334400+08:00" level=info msg="[TCP] 127.0.0.1:50529 --> www.bing.com:443 using GLOBAL"
time="2025-06-21T21:28:03.374334400+08:00" level=info msg="[TCP] 127.0.0.1:50539 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-06-21T21:28:03.375071100+08:00" level=info msg="[TCP] 127.0.0.1:50535 --> api.msn.com:443 using GLOBAL"
time="2025-06-21T21:28:03.375071100+08:00" level=info msg="[TCP] 127.0.0.1:50537 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-21T21:28:03.375785500+08:00" level=info msg="[TCP] 127.0.0.1:50532 --> c.bing.com:443 using GLOBAL"
time="2025-06-21T21:28:03.377127700+08:00" level=info msg="[TCP] 127.0.0.1:50531 --> c.msn.com:443 using GLOBAL"
time="2025-06-21T21:28:03.423789900+08:00" level=info msg="[TCP] 127.0.0.1:50541 --> assets.msn.com:443 using GLOBAL"
time="2025-06-21T21:28:03.628506300+08:00" level=info msg="[TCP] 127.0.0.1:50518 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-06-21T21:28:03.861653900+08:00" level=info msg="[TCP] 127.0.0.1:50543 --> fundingchoicesmessages.google.com:443 using GLOBAL"
time="2025-06-21T21:28:04.313500900+08:00" level=info msg="[TCP] 127.0.0.1:50545 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:28:04.315006300+08:00" level=info msg="[TCP] 127.0.0.1:50549 --> cdn-ima.33across.com:443 using GLOBAL"
time="2025-06-21T21:28:04.316816600+08:00" level=info msg="[TCP] 127.0.0.1:50550 --> static.criteo.net:443 using GLOBAL"
time="2025-06-21T21:28:04.316816600+08:00" level=info msg="[TCP] 127.0.0.1:50547 --> securepubads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:28:04.323391200+08:00" level=info msg="[TCP] 127.0.0.1:50553 --> pagead2.googlesyndication.com:443 using GLOBAL"
time="2025-06-21T21:28:04.444705000+08:00" level=info msg="[TCP] 127.0.0.1:50557 --> cdn-ima.33across.com:443 using GLOBAL"
time="2025-06-21T21:28:05.181273200+08:00" level=info msg="[TCP] 127.0.0.1:50562 --> pagead2.googlesyndication.com:443 using GLOBAL"
time="2025-06-21T21:28:05.182825600+08:00" level=info msg="[TCP] 127.0.0.1:50561 --> fundingchoicesmessages.google.com:443 using GLOBAL"
time="2025-06-21T21:28:05.246942500+08:00" level=info msg="[TCP] 127.0.0.1:50565 --> static.ads-twitter.com:443 using GLOBAL"
time="2025-06-21T21:28:05.334561400+08:00" level=info msg="[TCP] 127.0.0.1:50567 --> assets.msn.com:443 using GLOBAL"
time="2025-06-21T21:28:05.849661200+08:00" level=info msg="[TCP] 127.0.0.1:50559 --> gum.criteo.com:443 using GLOBAL"
time="2025-06-21T21:28:06.189094400+08:00" level=info msg="[TCP] 127.0.0.1:50575 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:28:06.190250000+08:00" level=info msg="[TCP] 127.0.0.1:50579 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:28:06.190250000+08:00" level=info msg="[TCP] 127.0.0.1:50570 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:28:06.190938100+08:00" level=info msg="[TCP] 127.0.0.1:50573 --> t.co:443 using GLOBAL"
time="2025-06-21T21:28:06.193148300+08:00" level=info msg="[TCP] 127.0.0.1:50587 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:28:06.193148300+08:00" level=info msg="[TCP] 127.0.0.1:50580 --> t.co:443 using GLOBAL"
time="2025-06-21T21:28:06.193663000+08:00" level=info msg="[TCP] 127.0.0.1:50577 --> t.co:443 using GLOBAL"
time="2025-06-21T21:28:06.204834100+08:00" level=info msg="[TCP] 127.0.0.1:50569 --> t.co:443 using GLOBAL"
time="2025-06-21T21:28:06.219533200+08:00" level=info msg="[TCP] 127.0.0.1:50585 --> t.co:443 using GLOBAL"
time="2025-06-21T21:28:06.219533200+08:00" level=info msg="[TCP] 127.0.0.1:50581 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:28:06.396342500+08:00" level=info msg="[TCP] 127.0.0.1:50590 --> td.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:28:06.453097500+08:00" level=info msg="[TCP] 127.0.0.1:50592 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-06-21T21:28:06.500739500+08:00" level=info msg="[TCP] 127.0.0.1:50594 --> fundingchoicesmessages.google.com:443 using GLOBAL"
time="2025-06-21T21:28:06.504774000+08:00" level=info msg="[TCP] 127.0.0.1:50596 --> fundingchoicesmessages.google.com:443 using GLOBAL"
time="2025-06-21T21:28:06.668721000+08:00" level=info msg="[TCP] 127.0.0.1:50598 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:28:06.800766600+08:00" level=info msg="[TCP] 127.0.0.1:50600 --> fundingchoicesmessages.google.com:443 using GLOBAL"
time="2025-06-21T21:28:06.927085600+08:00" level=info msg="[TCP] 127.0.0.1:50604 --> ag.gbc.criteo.com:443 using GLOBAL"
time="2025-06-21T21:28:06.929130400+08:00" level=info msg="[TCP] 127.0.0.1:50602 --> dnacdn.net:443 using GLOBAL"
time="2025-06-21T21:28:06.934251100+08:00" level=info msg="[TCP] 127.0.0.1:50606 --> gem.gbc.criteo.com:443 using GLOBAL"
time="2025-06-21T21:28:07.208339900+08:00" level=info msg="[TCP] 127.0.0.1:50608 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:28:07.330329600+08:00" level=info msg="[TCP] 127.0.0.1:50610 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:07.334206100+08:00" level=info msg="[TCP] 127.0.0.1:50612 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:28:07.468208700+08:00" level=info msg="[TCP] 127.0.0.1:50615 --> stats.g.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:28:07.508552500+08:00" level=info msg="[TCP] 127.0.0.1:50619 --> www.google-analytics.com:443 using GLOBAL"
time="2025-06-21T21:28:07.509877100+08:00" level=info msg="[TCP] 127.0.0.1:50620 --> cdn.onesignal.com:443 using GLOBAL"
time="2025-06-21T21:28:07.519541500+08:00" level=info msg="[TCP] 127.0.0.1:50624 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:07.520055700+08:00" level=info msg="[TCP] 127.0.0.1:50623 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:07.521916200+08:00" level=info msg="[TCP] 127.0.0.1:50627 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:07.521916200+08:00" level=info msg="[TCP] 127.0.0.1:50630 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:07.524308200+08:00" level=info msg="[TCP] 127.0.0.1:50629 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:08.009226500+08:00" level=info msg="[TCP] 127.0.0.1:50635 --> onesignal.com:443 using GLOBAL"
time="2025-06-21T21:28:08.306871000+08:00" level=info msg="[TCP] 127.0.0.1:50637 --> pixon.ads-pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:08.316374200+08:00" level=info msg="[TCP] 127.0.0.1:50639 --> pixon.ads-pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:08.317466300+08:00" level=info msg="[TCP] 127.0.0.1:50640 --> pixon.ads-pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:08.500425400+08:00" level=info msg="[TCP] 127.0.0.1:50617 --> imp.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:08.523389500+08:00" level=info msg="[TCP] 127.0.0.1:50625 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:08.607001900+08:00" level=info msg="[TCP] 127.0.0.1:50643 --> a.pixiv.org:443 using GLOBAL"
time="2025-06-21T21:28:09.227961800+08:00" level=info msg="[TCP] 127.0.0.1:50645 --> pagead2.googlesyndication.com:443 using GLOBAL"
time="2025-06-21T21:28:11.632648000+08:00" level=info msg="[TCP] 127.0.0.1:50649 --> s.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:12.904479000+08:00" level=info msg="[TCP] 127.0.0.1:50653 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:19.157042500+08:00" level=info msg="[TCP] 127.0.0.1:50660 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-21T21:28:19.289211100+08:00" level=info msg="[TCP] 127.0.0.1:50662 --> hm.baidu.com:443 using GLOBAL"
time="2025-06-21T21:28:22.318458500+08:00" level=info msg="[TCP] 127.0.0.1:50667 --> www.bing.com:443 using GLOBAL"
time="2025-06-21T21:28:24.960441500+08:00" level=info msg="[TCP] 127.0.0.1:50671 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:28:31.992907300+08:00" level=info msg="[TCP] 127.0.0.1:50679 --> s.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:38.226182900+08:00" level=info msg="[TCP] 127.0.0.1:50696 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:38.226182900+08:00" level=info msg="[TCP] 127.0.0.1:50693 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:38.231780100+08:00" level=info msg="[TCP] 127.0.0.1:50695 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:38.237255100+08:00" level=info msg="[TCP] 127.0.0.1:50703 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:38.238376900+08:00" level=info msg="[TCP] 127.0.0.1:50702 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:38.239410300+08:00" level=info msg="[TCP] 127.0.0.1:50705 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:38.245217100+08:00" level=info msg="[TCP] 127.0.0.1:50704 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:38.247141800+08:00" level=info msg="[TCP] 127.0.0.1:50701 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:38.490426200+08:00" level=info msg="[TCP] 127.0.0.1:50686 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:38.499788600+08:00" level=info msg="[TCP] 127.0.0.1:50688 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:38.560372900+08:00" level=info msg="[TCP] 127.0.0.1:50713 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:38.561511100+08:00" level=info msg="[TCP] 127.0.0.1:50691 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:38.674951700+08:00" level=info msg="[TCP] 127.0.0.1:50715 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:38.681386000+08:00" level=info msg="[TCP] 127.0.0.1:50719 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:38.715261300+08:00" level=info msg="[TCP] 127.0.0.1:50694 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:38.745866700+08:00" level=info msg="[TCP] 127.0.0.1:50711 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:38.794487300+08:00" level=info msg="[TCP] 127.0.0.1:50716 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:39.125409600+08:00" level=info msg="[TCP] 127.0.0.1:50721 --> s.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:39.440885900+08:00" level=info msg="[TCP] 127.0.0.1:50723 --> s.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:39.448494300+08:00" level=info msg="[TCP] 127.0.0.1:50726 --> s.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:39.544107200+08:00" level=info msg="[TCP] 127.0.0.1:50725 --> s.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:39.546470600+08:00" level=info msg="[TCP] 127.0.0.1:50724 --> s.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:54.047054900+08:00" level=info msg="[TCP] 127.0.0.1:50744 --> s.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:55.512455500+08:00" level=info msg="[TCP] 127.0.0.1:50747 --> stats.g.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:28:55.513545600+08:00" level=info msg="[TCP] 127.0.0.1:50749 --> pixon.ads-pixiv.net:443 using GLOBAL"
time="2025-06-21T21:28:55.882167400+08:00" level=info msg="[TCP] 127.0.0.1:50751 --> a.pixiv.org:443 using GLOBAL"
time="2025-06-21T21:28:57.219007600+08:00" level=info msg="[TCP] 127.0.0.1:50755 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:28:58.121227700+08:00" level=info msg="[TCP] 127.0.0.1:50759 --> s.pximg.net:443 using GLOBAL"
time="2025-06-21T21:28:58.983848300+08:00" level=info msg="[TCP] 127.0.0.1:50761 --> c.amazon-adsystem.com:443 using GLOBAL"
time="2025-06-21T21:28:59.552458800+08:00" level=info msg="[TCP] 127.0.0.1:50764 --> www.google-analytics.com:443 using GLOBAL"
time="2025-06-21T21:28:59.682629600+08:00" level=info msg="[TCP] 127.0.0.1:50766 --> static.ads-twitter.com:443 using GLOBAL"
time="2025-06-21T21:28:59.704162000+08:00" level=info msg="[TCP] 127.0.0.1:50768 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:28:59.708782900+08:00" level=info msg="[TCP] 127.0.0.1:50770 --> td.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:28:59.719878000+08:00" level=info msg="[TCP] 127.0.0.1:50772 --> googleads.g.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:28:59.869876100+08:00" level=info msg="[TCP] 127.0.0.1:50774 --> www.google-analytics.com:443 using GLOBAL"
time="2025-06-21T21:28:59.995733100+08:00" level=info msg="[TCP] 127.0.0.1:50776 --> fundingchoicesmessages.google.com:443 using GLOBAL"
time="2025-06-21T21:29:00.010316500+08:00" level=info msg="[TCP] 127.0.0.1:50778 --> td.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:29:00.189445500+08:00" level=info msg="[TCP] 127.0.0.1:50780 --> t.co:443 using GLOBAL"
time="2025-06-21T21:29:00.192955800+08:00" level=info msg="[TCP] 127.0.0.1:50781 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:29:00.418318900+08:00" level=info msg="[TCP] 127.0.0.1:50784 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:29:00.492402000+08:00" level=info msg="[TCP] 127.0.0.1:50789 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:29:00.493549300+08:00" level=info msg="[TCP] 127.0.0.1:50788 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:29:00.495692300+08:00" level=info msg="[TCP] 127.0.0.1:50786 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:29:00.495692300+08:00" level=info msg="[TCP] 127.0.0.1:50790 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:29:00.496894900+08:00" level=info msg="[TCP] 127.0.0.1:50787 --> analytics.twitter.com:443 using GLOBAL"
time="2025-06-21T21:29:00.948348600+08:00" level=info msg="[TCP] 127.0.0.1:50797 --> www.recaptcha.net:443 using GLOBAL"
time="2025-06-21T21:29:01.025657900+08:00" level=info msg="[TCP] 127.0.0.1:50799 --> imp.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:29:01.346221300+08:00" level=info msg="[TCP] 127.0.0.1:50801 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-21T21:29:01.375448500+08:00" level=info msg="[TCP] 127.0.0.1:50803 --> fundingchoicesmessages.google.com:443 using GLOBAL"
time="2025-06-21T21:29:01.376468700+08:00" level=info msg="[TCP] 127.0.0.1:50805 --> pagead2.googlesyndication.com:443 using GLOBAL"
time="2025-06-21T21:29:01.885051400+08:00" level=info msg="[TCP] 127.0.0.1:50807 --> www.recaptcha.net:443 using GLOBAL"
time="2025-06-21T21:29:01.987811300+08:00" level=info msg="[TCP] 127.0.0.1:50809 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:29:02.537886200+08:00" level=info msg="[TCP] 127.0.0.1:50814 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-21T21:29:02.538676300+08:00" level=info msg="[TCP] 127.0.0.1:50813 --> www.gstatic.com:443 using GLOBAL"
time="2025-06-21T21:29:03.162795200+08:00" level=info msg="[TCP] 127.0.0.1:50817 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-21T21:29:03.165926700+08:00" level=info msg="[TCP] 127.0.0.1:50819 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-06-21T21:29:04.743106900+08:00" level=info msg="[TCP] 127.0.0.1:50831 --> www.recaptcha.net:443 using GLOBAL"
time="2025-06-21T21:29:08.724290000+08:00" level=info msg="[TCP] 127.0.0.1:50837 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:29:22.374701400+08:00" level=info msg="[TCP] 127.0.0.1:50852 --> www.bing.com:443 using GLOBAL"
time="2025-06-21T21:29:35.383369200+08:00" level=info msg="[TCP] 127.0.0.1:50864 --> array612.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:29:36.532629100+08:00" level=info msg="[TCP] 127.0.0.1:50867 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:30:00.937374600+08:00" level=info msg="[TCP] 127.0.0.1:50890 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:30:22.431267200+08:00" level=info msg="[TCP] 127.0.0.1:50911 --> www.bing.com:443 using GLOBAL"
time="2025-06-21T21:30:41.134824400+08:00" level=info msg="[TCP] 127.0.0.1:50929 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:30:45.064698000+08:00" level=info msg="[TCP] 127.0.0.1:50934 --> stats.g.doubleclick.net:443 using GLOBAL"
time="2025-06-21T21:30:45.118630200+08:00" level=info msg="[TCP] 127.0.0.1:50936 --> www.recaptcha.net:443 using GLOBAL"
time="2025-06-21T21:30:45.126061500+08:00" level=info msg="[TCP] 127.0.0.1:50938 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-21T21:30:45.463090400+08:00" level=info msg="[TCP] 127.0.0.1:50941 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-21T21:30:45.620214400+08:00" level=info msg="[TCP] 127.0.0.1:50943 --> www.recaptcha.net:443 using GLOBAL"
time="2025-06-21T21:30:46.839708700+08:00" level=info msg="[TCP] 127.0.0.1:50947 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:30:46.839708700+08:00" level=info msg="[TCP] 127.0.0.1:50945 --> pixon.ads-pixiv.net:443 using GLOBAL"
time="2025-06-21T21:30:46.862285400+08:00" level=info msg="[TCP] 127.0.0.1:50949 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T21:30:47.033085900+08:00" level=info msg="[TCP] 127.0.0.1:50951 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:30:47.143459400+08:00" level=info msg="[TCP] 127.0.0.1:50956 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:30:47.143459400+08:00" level=info msg="[TCP] 127.0.0.1:50954 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:30:47.144250100+08:00" level=info msg="[TCP] 127.0.0.1:50955 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:30:47.145547600+08:00" level=info msg="[TCP] 127.0.0.1:50957 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:30:47.148456700+08:00" level=info msg="[TCP] 127.0.0.1:50963 --> a.pixiv.org:443 using GLOBAL"
time="2025-06-21T21:30:47.149604400+08:00" level=info msg="[TCP] 127.0.0.1:50953 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:30:47.169887300+08:00" level=info msg="[TCP] 127.0.0.1:50966 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T21:30:47.172021100+08:00" level=info msg="[TCP] 127.0.0.1:50969 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T21:30:47.172021100+08:00" level=info msg="[TCP] 127.0.0.1:50967 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T21:30:47.173521800+08:00" level=info msg="[TCP] 127.0.0.1:50970 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T21:30:47.178503000+08:00" level=info msg="[TCP] 127.0.0.1:50968 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T21:30:51.050536900+08:00" level=info msg="[TCP] 127.0.0.1:50980 --> www.google-analytics.com:443 using GLOBAL"
time="2025-06-21T21:30:58.148031900+08:00" level=info msg="[TCP] 127.0.0.1:50988 --> lf3-static.bytednsdoc.com:443 using GLOBAL"
time="2025-06-21T21:31:00.590451900+08:00" level=info msg="[TCP] 127.0.0.1:50994 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:31:00.605659300+08:00" level=info msg="[TCP] 127.0.0.1:50996 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:31:00.901285500+08:00" level=info msg="[TCP] 127.0.0.1:50998 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:31:03.332827000+08:00" level=info msg="[TCP] 127.0.0.1:51009 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:31:22.488460100+08:00" level=info msg="[TCP] 127.0.0.1:51029 --> www.bing.com:443 using GLOBAL"
time="2025-06-21T21:31:45.031417800+08:00" level=info msg="[TCP] 127.0.0.1:51052 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:31:57.238128500+08:00" level=info msg="[TCP] 127.0.0.1:51076 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-21T21:32:04.954563300+08:00" level=info msg="[TCP] 127.0.0.1:51087 --> www.pixiv.net:443 using GLOBAL"
time="2025-06-21T21:32:11.936361300+08:00" level=info msg="[TCP] 127.0.0.1:51093 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:32:12.243860100+08:00" level=info msg="[TCP] 127.0.0.1:51098 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:32:12.246206900+08:00" level=info msg="[TCP] 127.0.0.1:51097 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:32:12.251707800+08:00" level=info msg="[TCP] 127.0.0.1:51099 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:32:12.255011100+08:00" level=info msg="[TCP] 127.0.0.1:51103 --> i.pximg.net:443 using GLOBAL"
time="2025-06-21T21:32:22.567727900+08:00" level=info msg="[TCP] 127.0.0.1:51115 --> www.bing.com:443 using GLOBAL"
time="2025-06-21T21:32:26.622318200+08:00" level=info msg="[TCP] 127.0.0.1:51119 --> go.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:32:26.655447000+08:00" level=info msg="[TCP] 127.0.0.1:51121 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-21T21:32:26.657522900+08:00" level=info msg="[TCP] 127.0.0.1:51122 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-21T21:32:33.771640100+08:00" level=info msg="[TCP] 127.0.0.1:51132 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:32:58.270372800+08:00" level=info msg="[TCP] 127.0.0.1:51156 --> westus-0.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-06-21T21:33:20.831313800+08:00" level=info msg="[TCP] 127.0.0.1:51180 --> www.google-analytics.com:443 using GLOBAL"
time="2025-06-21T21:33:20.861891600+08:00" level=info msg="[TCP] 127.0.0.1:51182 --> errortrace.dev:443 using GLOBAL"
time="2025-06-21T21:33:22.845019800+08:00" level=info msg="[TCP] 127.0.0.1:51187 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-21T21:33:27.924595300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51189 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-22T00:53:01.102781900+08:00" level=warning msg="Mihomo shutting down"
