Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-24T21:58:06.936439800+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-24T21:58:06.945160700+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-24T21:58:06.945160700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-24T21:58:06.945670600+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-24T21:58:06.961475100+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-24T21:58:06.961475100+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-24T21:58:07.213249400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-24T21:58:07.213249400+08:00" level=info msg="Load GeoSite rule: private"
time="2025-06-24T21:58:07.228427900+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-24T21:58:07.230983900+08:00" level=info msg="Initial configuration complete, total time: 289ms"
time="2025-06-24T21:58:07.231492700+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-06-24T21:58:07.232513300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-24T21:58:07.233021900+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-06-24T21:58:07.233021900+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-06-24T21:58:07.233021900+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Line"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Lan"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Discord"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider TVer"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider apple"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider google"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider TVB"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider telegram"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Disney"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider NowE"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider BBC"
time="2025-06-24T21:58:07.238623900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-24T21:58:10.056893300+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-24T21:58:10.057400900+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-24T21:58:10.057400900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-24T21:58:10.057927400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-24T21:58:10.057927400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-24T21:58:10.058437200+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-24T21:58:10.059458900+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-24T21:58:12.239708100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.239708100+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-24T21:58:12.240391400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240391400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240391400+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-24T21:58:12.240391400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240391400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-24T21:58:12.240391400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240391400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240391400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240391400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240391400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240391400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-24T21:58:12.240391400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240391400+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-24T21:58:12.240391400+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-24T21:58:12.240391400+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-24T21:58:12.240391400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240391400+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-24T21:58:12.240391400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-24T21:58:12.240391400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-24T21:58:12.240391400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-24T21:58:12.241411600+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-24T21:58:12.240898600+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-24T21:58:12.241429600+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-24T21:58:12.246396000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-24T21:58:12.246396000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-24T21:58:12.246396000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-24T21:58:12.246396000+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider apple"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider TVB"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider BBC"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider google"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Lan"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Discord"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Disney"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider NowE"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Line"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider TVer"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider telegram"
time="2025-06-24T21:58:12.247917000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-24T21:58:13.382892500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.384276200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.384276200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.385289400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.386491900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.386491900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.386491900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.387269700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.387269700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.387269700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.387269700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.388705700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.388705700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.388705700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.388705700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.390053200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.390053200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.390053200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.390053200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.390053200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.390565200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.390565200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.390565200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.390565200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.393817100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.393817100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.393817100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.393817100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.393817100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.394334600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.394334600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.394334600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.394334600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.394334600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.394334600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.394837100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.394849800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.394849800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.394849800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.399430500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.399430500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.399430500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.399430500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.399954700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.399954700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.399954700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.400466400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.400466400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.400466400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:13.941849600+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-06-24T21:58:13.949501400+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-06-24T21:58:13.964266000+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-06-24T21:58:13.973221800+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-06-24T21:58:13.976665700+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-06-24T21:58:13.983088400+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-06-24T21:58:13.986601900+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-06-24T21:58:13.996306400+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-06-24T21:58:13.999572400+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-06-24T21:58:14.003801700+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-06-24T21:58:14.004410600+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-06-24T21:58:14.005550700+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-06-24T21:58:14.013916700+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-06-24T21:58:14.014498000+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-06-24T21:58:14.016392200+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-06-24T21:58:14.024472200+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-06-24T21:58:14.029953200+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-06-24T21:58:14.033907400+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-06-24T21:58:14.037532700+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-06-24T21:58:14.040314000+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-06-24T21:58:14.041328100+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-06-24T21:58:14.047820600+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-06-24T21:58:14.053836900+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-06-24T21:58:14.057104000+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-06-24T21:58:14.064293900+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-06-24T21:58:14.064870700+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-06-24T21:58:14.067761200+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-06-24T21:58:14.069645800+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-06-24T21:58:14.078544900+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-06-24T21:58:14.090228600+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-06-24T21:58:14.092456900+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-06-24T21:58:14.096157600+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-06-24T21:58:14.100145800+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-06-24T21:58:14.101399700+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-06-24T21:58:14.106889700+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-06-24T21:58:14.106889700+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-06-24T21:58:14.142316700+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-06-24T21:58:14.143857200+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-06-24T21:58:14.150861300+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-06-24T21:58:14.151754100+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-06-24T21:58:14.171820700+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-06-24T21:58:14.199629000+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-06-24T21:58:14.232025500+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-06-24T21:58:14.243169000+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-06-24T21:58:14.363697500+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-06-24T21:58:14.371189900+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-06-24T21:58:14.479839400+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-06-24T21:58:14.479839400+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-06-24T21:58:14.491288900+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-06-24T21:58:14.495171600+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-24T21:58:14.495171600+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-24T21:58:14.495171600+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-24T21:58:14.495171600+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-24T21:58:14.806757100+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-24T21:58:14.807776200+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-24T21:58:14.807776200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-24T21:58:14.807776200+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-24T21:58:14.808286300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-24T21:58:14.808286300+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-24T21:58:14.809678400+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-24T21:58:14.810811400+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider telegram"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Disney"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider google"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider NowE"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Discord"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider apple"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider BBC"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Lan"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider TVer"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider Line"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider TVB"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-24T21:58:14.811325200+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-24T21:58:15.015928200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.016942200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.016942200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.019018800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.019018800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.019018800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.020693500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.020693500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.020693500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.020693500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.020693500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.020693500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.021217600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.022948000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.022948000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.022948000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.022948000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.022948000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.022948000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.022948000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.023460900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.023460900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.023460900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.023460900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.023460900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.023460900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.023460900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.023460900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.023970800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.023970800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.028729400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.028729400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.028729400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.028729400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.028729400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.028729400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029233100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029244000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029244000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029244000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029244000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029244000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029244000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029244000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029756100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029756100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029756100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.029756100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.032749700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:15.577789200+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-06-24T21:58:15.593015200+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-06-24T21:58:15.602347200+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-06-24T21:58:15.603936400+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-06-24T21:58:15.612649700+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-06-24T21:58:15.616931200+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-06-24T21:58:15.629666100+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-06-24T21:58:15.642110700+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-06-24T21:58:15.646094400+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-06-24T21:58:15.649837600+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-06-24T21:58:15.650348300+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-06-24T21:58:15.653202000+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-06-24T21:58:15.658165600+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-06-24T21:58:15.665286000+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-06-24T21:58:15.666055500+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-06-24T21:58:15.667702700+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-06-24T21:58:15.675922100+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-06-24T21:58:15.677389200+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-06-24T21:58:15.683663400+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-06-24T21:58:15.684165900+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-06-24T21:58:15.690881300+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-06-24T21:58:15.695957800+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-06-24T21:58:15.695957800+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-06-24T21:58:15.701846100+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-06-24T21:58:15.706326400+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-06-24T21:58:15.712230600+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-06-24T21:58:15.713130300+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-06-24T21:58:15.714416300+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-06-24T21:58:15.716657600+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-06-24T21:58:15.718437000+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-06-24T21:58:15.722007300+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-06-24T21:58:15.736101800+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-06-24T21:58:15.736708700+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-06-24T21:58:15.748694900+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-06-24T21:58:15.748694900+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-06-24T21:58:15.756228200+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-06-24T21:58:15.765905600+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-06-24T21:58:15.777009100+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-06-24T21:58:15.777940000+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-06-24T21:58:15.788536300+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-06-24T21:58:15.792202300+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-06-24T21:58:15.800698400+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-06-24T21:58:15.928764700+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-06-24T21:58:15.930416600+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-06-24T21:58:16.053942000+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-06-24T21:58:16.098614400+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-06-24T21:58:17.067118600+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-06-24T21:58:17.179766200+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-06-24T21:58:18.083074000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-06-24T21:58:18.613444100+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-06-24T21:58:18.617209000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-24T21:58:18.617209000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-24T21:58:18.617209000+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-24T21:58:18.617209000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-24T21:58:26.523894200+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:58:26.523894200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:58:26.536799800+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T21:58:26.536799800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T21:58:27.093930200+08:00" level=info msg="[TCP] 127.0.0.1:51508 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-24T21:58:34.888831800+08:00" level=info msg="[TCP] 127.0.0.1:51517 --> nodejs.org:443 using GLOBAL"
time="2025-06-24T21:58:35.329529800+08:00" level=info msg="[TCP] 127.0.0.1:51519 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-24T21:58:36.852531200+08:00" level=info msg="[TCP] 127.0.0.1:51523 --> www.bing.com:443 using GLOBAL"
time="2025-06-24T21:58:41.387901900+08:00" level=info msg="[TCP] 127.0.0.1:51528 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-06-24T21:58:41.564055400+08:00" level=info msg="[TCP] 127.0.0.1:51530 --> app-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-24T21:58:51.795737800+08:00" level=info msg="[TCP] 127.0.0.1:51542 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:58:53.307258400+08:00" level=info msg="[TCP] 127.0.0.1:51545 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:58:53.606826500+08:00" level=info msg="[TCP] 127.0.0.1:51548 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:58:53.607331900+08:00" level=info msg="[TCP] 127.0.0.1:51550 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:58:53.611870400+08:00" level=info msg="[TCP] 127.0.0.1:51549 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:58:53.611870400+08:00" level=info msg="[TCP] 127.0.0.1:51547 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:58:53.612378600+08:00" level=info msg="[TCP] 127.0.0.1:51551 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:58:58.085857800+08:00" level=info msg="[TCP] 127.0.0.1:51561 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-06-24T21:59:07.146132700+08:00" level=info msg="[TCP] 127.0.0.1:51571 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-24T21:59:07.309616300+08:00" level=info msg="[TCP] 127.0.0.1:51573 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-24T21:59:17.145143000+08:00" level=info msg="[TCP] 127.0.0.1:51585 --> ops.gx.nvidia.com:443 using GLOBAL"
time="2025-06-24T21:59:32.067760500+08:00" level=info msg="[TCP] 127.0.0.1:51607 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-06-24T21:59:38.032039900+08:00" level=info msg="[TCP] 127.0.0.1:51613 --> login.live.com:443 using GLOBAL"
time="2025-06-24T21:59:38.324322800+08:00" level=info msg="[TCP] 127.0.0.1:51616 --> ocsp.digicert.com:80 using GLOBAL"
time="2025-06-24T21:59:39.046869400+08:00" level=info msg="[TCP] 127.0.0.1:51619 --> assets.msn.com:443 using GLOBAL"
time="2025-06-24T21:59:39.108014700+08:00" level=info msg="[TCP] 127.0.0.1:51621 --> assets.activity.windows.com:443 using GLOBAL"
time="2025-06-24T21:59:40.189143300+08:00" level=info msg="[TCP] 127.0.0.1:51624 --> activity.windows.com:443 using GLOBAL"
time="2025-06-24T21:59:43.039222600+08:00" level=info msg="[TCP] 127.0.0.1:51629 --> graph.microsoft.com:443 using GLOBAL"
time="2025-06-24T21:59:47.594990400+08:00" level=info msg="[TCP] 127.0.0.1:51633 --> api.trae.ai:443 using GLOBAL"
time="2025-06-24T21:59:49.710104800+08:00" level=info msg="[TCP] 127.0.0.1:51639 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:59:50.007237500+08:00" level=info msg="[TCP] 127.0.0.1:51643 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:59:50.008934800+08:00" level=info msg="[TCP] 127.0.0.1:51645 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:59:50.010481000+08:00" level=info msg="[TCP] 127.0.0.1:51641 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:59:50.010993600+08:00" level=info msg="[TCP] 127.0.0.1:51644 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:59:50.012129000+08:00" level=info msg="[TCP] 127.0.0.1:51642 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T21:59:51.491126800+08:00" level=info msg="[TCP] 127.0.0.1:51657 --> api-sg-central.trae.ai:443 using GLOBAL"
time="2025-06-24T21:59:51.730072500+08:00" level=info msg="[TCP] 127.0.0.1:51665 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T21:59:53.358415900+08:00" level=info msg="[TCP] 127.0.0.1:51671 --> lf3-static.bytednsdoc.com:443 using GLOBAL"
time="2025-06-24T22:00:03.034464200+08:00" level=info msg="[TCP] 127.0.0.1:51685 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-24T22:00:16.159611900+08:00" level=info msg="[TCP] 127.0.0.1:51716 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:00:27.843022700+08:00" level=info msg="[TCP] 127.0.0.1:51740 --> store.steampowered.com:443 using GLOBAL"
time="2025-06-24T22:00:31.748462900+08:00" level=info msg="[TCP] 127.0.0.1:51747 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:00:31.998459200+08:00" level=info msg="[TCP] 127.0.0.1:51750 --> storeedgefd.dsx.mp.microsoft.com:443 using GLOBAL"
time="2025-06-24T22:00:36.964397400+08:00" level=info msg="[TCP] 127.0.0.1:51756 --> www.bing.com:443 using GLOBAL"
time="2025-06-24T22:00:44.456585000+08:00" level=info msg="[TCP] 127.0.0.1:51772 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:00:44.459974300+08:00" level=info msg="[TCP] 127.0.0.1:51774 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:00:48.198963400+08:00" level=info msg="[TCP] 127.0.0.1:51784 --> activity.windows.com:443 using GLOBAL"
time="2025-06-24T22:00:52.260034200+08:00" level=info msg="[TCP] 127.0.0.1:51793 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:00:53.735991800+08:00" level=info msg="[TCP] 127.0.0.1:51795 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:01:36.665026000+08:00" level=info msg="[TCP] 127.0.0.1:51836 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:01:43.848549800+08:00" level=info msg="[TCP] 127.0.0.1:51858 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:01:47.155011800+08:00" level=info msg="[TCP] 127.0.0.1:51862 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:02:12.287731300+08:00" level=info msg="[TCP] 127.0.0.1:51891 --> login.live.com:443 using GLOBAL"
time="2025-06-24T22:02:14.195606500+08:00" level=info msg="[TCP] 127.0.0.1:51897 --> cs.dds.microsoft.com:443 using GLOBAL"
time="2025-06-24T22:02:34.502091000+08:00" level=info msg="[TCP] 127.0.0.1:51916 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:02:35.177491100+08:00" level=info msg="[TCP] 127.0.0.1:51920 --> tos-sg16-share.vodupload.com:443 using GLOBAL"
time="2025-06-24T22:02:46.435836900+08:00" level=info msg="[TCP] 127.0.0.1:51932 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:02:47.861844700+08:00" level=info msg="[TCP] 127.0.0.1:51936 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:02:48.163948600+08:00" level=info msg="[TCP] 127.0.0.1:51943 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:02:48.166313900+08:00" level=info msg="[TCP] 127.0.0.1:51941 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:02:48.170387000+08:00" level=info msg="[TCP] 127.0.0.1:51940 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:02:48.171717800+08:00" level=info msg="[TCP] 127.0.0.1:51939 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:02:49.179253500+08:00" level=info msg="[TCP] 127.0.0.1:51942 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:03:39.892627600+08:00" level=info msg="[TCP] 127.0.0.1:51997 --> download.clashverge.dev:443 using GLOBAL"
time="2025-06-24T22:03:40.519988500+08:00" level=info msg="[TCP] 127.0.0.1:51999 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:07:02.379860800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52361 --> graph.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T22:30:44.600220300+08:00" level=info msg="[TCP] 127.0.0.1:54695 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:30:46.355243500+08:00" level=info msg="[TCP] 127.0.0.1:54703 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:30:46.664108300+08:00" level=info msg="[TCP] 127.0.0.1:54706 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:30:46.665814500+08:00" level=info msg="[TCP] 127.0.0.1:54707 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:30:46.682443800+08:00" level=info msg="[TCP] 127.0.0.1:54705 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:30:46.771339800+08:00" level=info msg="[TCP] 127.0.0.1:54708 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:30:47.676052900+08:00" level=info msg="[TCP] 127.0.0.1:54709 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:30:49.771679700+08:00" level=error msg="🇯🇵日本01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:30:49.771679700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:30:49.771976400+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:30:49.771976400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:30:49.781825500+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:30:49.781825500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:30:49.781825500+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:30:49.781825500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:31:03.199533200+08:00" level=info msg="[TCP] 127.0.0.1:54742 --> www.doc88.com:443 using GLOBAL"
time="2025-06-24T22:31:55.539396400+08:00" level=info msg="[TCP] 127.0.0.1:54790 --> sso.zxxk.com:443 using GLOBAL"
time="2025-06-24T22:31:58.177654500+08:00" level=info msg="[TCP] 127.0.0.1:54794 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:32:01.170987500+08:00" level=info msg="[TCP] 127.0.0.1:54800 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:32:01.473945600+08:00" level=info msg="[TCP] 127.0.0.1:54803 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:32:01.473945600+08:00" level=info msg="[TCP] 127.0.0.1:54808 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:32:01.475516300+08:00" level=info msg="[TCP] 127.0.0.1:54802 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:32:01.478305800+08:00" level=info msg="[TCP] 127.0.0.1:54807 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:32:01.478305800+08:00" level=info msg="[TCP] 127.0.0.1:54806 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:32:03.173005900+08:00" level=info msg="[TCP] 127.0.0.1:54922 --> www.doc88.com:443 using GLOBAL"
time="2025-06-24T22:32:12.671178100+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:32:12.671178100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:32:12.671178100+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:32:12.671178100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:32:12.671688000+08:00" level=error msg="🇸🇬新加坡01 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:32:12.671688000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:32:12.682017300+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:32:12.682017300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:32:12.682017300+08:00" level=error msg="🇸🇬新加坡01 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:32:12.682017300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:32:21.015757200+08:00" level=info msg="[TCP] 127.0.0.1:54944 --> sso.zxxk.com:443 using GLOBAL"
time="2025-06-24T22:32:34.542188600+08:00" level=info msg="[TCP] 127.0.0.1:54973 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:32:35.793400100+08:00" level=info msg="[TCP] 127.0.0.1:54976 --> tos-sg16-share.vodupload.com:443 using GLOBAL"
time="2025-06-24T22:32:45.789095400+08:00" level=info msg="[TCP] 127.0.0.1:55005 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:32:45.797104500+08:00" level=info msg="[TCP] 127.0.0.1:55007 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:32:47.857689000+08:00" level=info msg="[TCP] 127.0.0.1:55011 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-24T22:32:52.215042800+08:00" level=info msg="[TCP] 127.0.0.1:55017 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:32:58.374928200+08:00" level=info msg="[TCP] 127.0.0.1:55024 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:33:00.526776000+08:00" level=info msg="[TCP] 127.0.0.1:55038 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:33:04.190896900+08:00" level=info msg="[TCP] 127.0.0.1:55048 --> login.live.com:443 using GLOBAL"
time="2025-06-24T22:33:05.301538900+08:00" level=info msg="[TCP] 127.0.0.1:55052 --> prod.rewardsplatform.microsoft.com:443 using GLOBAL"
time="2025-06-24T22:33:05.485656500+08:00" level=info msg="[TCP] 127.0.0.1:55054 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:33:11.870104700+08:00" level=info msg="[TCP] 127.0.0.1:55076 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:33:11.873217200+08:00" level=info msg="[TCP] 127.0.0.1:55078 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:33:17.672709000+08:00" level=info msg="[TCP] 127.0.0.1:55088 --> static.nvidiagrid.net:443 using GLOBAL"
time="2025-06-24T22:33:19.558511100+08:00" level=info msg="[TCP] 127.0.0.1:55091 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:33:24.341362900+08:00" level=info msg="[TCP] 127.0.0.1:55109 --> sso.zxxk.com:443 using GLOBAL"
time="2025-06-24T22:33:24.361549500+08:00" level=info msg="[TCP] 127.0.0.1:55111 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:33:27.329362500+08:00" level=info msg="[TCP] 127.0.0.1:55223 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:33:28.220982600+08:00" level=info msg="[TCP] 127.0.0.1:55227 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:33:28.226857200+08:00" level=info msg="[TCP] 127.0.0.1:55229 --> www.doc88.com:443 using GLOBAL"
time="2025-06-24T22:33:28.582174500+08:00" level=info msg="[TCP] 127.0.0.1:55231 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:33:32.221087100+08:00" level=info msg="[TCP] 127.0.0.1:55245 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:33:32.221795100+08:00" level=info msg="[TCP] 127.0.0.1:55247 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:33:35.657019900+08:00" level=error msg="🇯🇵日本01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:33:35.657019900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:33:35.657830600+08:00" level=error msg="🇯🇵日本03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:33:35.657830600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:33:35.669549000+08:00" level=error msg="🇯🇵日本03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-06-24T22:33:35.669549000+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-24T22:33:39.887441200+08:00" level=info msg="[TCP] 127.0.0.1:55256 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:33:42.924277500+08:00" level=info msg="[TCP] 127.0.0.1:55264 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:33:46.020963800+08:00" level=info msg="[TCP] 127.0.0.1:55276 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:33:50.759419100+08:00" level=info msg="[TCP] 127.0.0.1:55282 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:33:54.255066300+08:00" level=info msg="[TCP] 127.0.0.1:55291 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:33:57.143881000+08:00" level=info msg="[TCP] 127.0.0.1:55295 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:33:57.690892100+08:00" level=info msg="[TCP] 127.0.0.1:55298 --> activity.windows.com:443 using GLOBAL"
time="2025-06-24T22:34:00.230376900+08:00" level=info msg="[TCP] 127.0.0.1:55303 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:04.124694400+08:00" level=info msg="[TCP] 127.0.0.1:55309 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:34:04.625910600+08:00" level=info msg="[TCP] 127.0.0.1:55312 --> trae-api-sg.mchost.guru:443 using GLOBAL"
time="2025-06-24T22:34:07.048014800+08:00" level=info msg="[TCP] 127.0.0.1:55316 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:13.871697700+08:00" level=info msg="[TCP] 127.0.0.1:55324 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:20.834572900+08:00" level=info msg="[TCP] 127.0.0.1:55332 --> maliva-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:28.345709700+08:00" level=info msg="[TCP] 127.0.0.1:55343 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:28.851098100+08:00" level=info msg="[TCP] 127.0.0.1:55345 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:28.865931900+08:00" level=info msg="[TCP] 127.0.0.1:55347 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:29.248683000+08:00" level=info msg="[TCP] 127.0.0.1:55351 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.306576200+08:00" level=info msg="[TCP] 127.0.0.1:55355 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.591215400+08:00" level=info msg="[TCP] 127.0.0.1:55357 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.613172200+08:00" level=info msg="[TCP] 127.0.0.1:55360 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.614770100+08:00" level=info msg="[TCP] 127.0.0.1:55361 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.620931500+08:00" level=info msg="[TCP] 127.0.0.1:55362 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.620931500+08:00" level=info msg="[TCP] 127.0.0.1:55363 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.623486000+08:00" level=info msg="[TCP] 127.0.0.1:55359 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.895603300+08:00" level=info msg="[TCP] 127.0.0.1:55370 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.897186500+08:00" level=info msg="[TCP] 127.0.0.1:55372 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.905710100+08:00" level=info msg="[TCP] 127.0.0.1:55374 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.927574400+08:00" level=info msg="[TCP] 127.0.0.1:55376 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:30.964780600+08:00" level=info msg="[TCP] 127.0.0.1:55378 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:31.051351400+08:00" level=info msg="[TCP] 127.0.0.1:55380 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:31.351463100+08:00" level=info msg="[TCP] 127.0.0.1:55382 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:31.358816500+08:00" level=info msg="[TCP] 127.0.0.1:55386 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:31.428013700+08:00" level=info msg="[TCP] 127.0.0.1:55388 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:31.660830800+08:00" level=info msg="[TCP] 127.0.0.1:55390 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:31.666100700+08:00" level=info msg="[TCP] 127.0.0.1:55392 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:32.369719800+08:00" level=info msg="[TCP] 127.0.0.1:55384 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:32.694673200+08:00" level=info msg="[TCP] 127.0.0.1:55395 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T22:34:34.467083500+08:00" level=info msg="[TCP] 127.0.0.1:55399 --> deff.nelreports.net:443 using GLOBAL"
time="2025-06-24T22:34:34.778664600+08:00" level=info msg="[TCP] 127.0.0.1:55401 --> deff.nelreports.net:443 using GLOBAL"
time="2025-06-24T22:34:45.675245400+08:00" level=info msg="[TCP] 127.0.0.1:55414 --> sso.zxxk.com:443 using GLOBAL"
time="2025-06-24T22:34:48.167432700+08:00" level=info msg="[TCP] 127.0.0.1:55418 --> sgali-mcs.byteoversea.com:443 using GLOBAL"
time="2025-06-24T23:07:39.281145700+08:00" level=info msg="[TCP] 127.0.0.1:58980 --> chat.deepseek.com:443 using GLOBAL"
time="2025-06-24T23:07:39.306721100+08:00" level=info msg="[TCP] 127.0.0.1:58982 --> gator.volces.com:443 using GLOBAL"
time="2025-06-24T23:07:39.415817000+08:00" level=info msg="[TCP] 127.0.0.1:58984 --> chat.deepseek.com:443 using GLOBAL"
time="2025-06-24T23:07:39.871357800+08:00" level=info msg="[TCP] 127.0.0.1:58987 --> gator.volces.com:443 using GLOBAL"
time="2025-06-24T23:07:40.648003900+08:00" level=info msg="[TCP] 127.0.0.1:58990 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-24T23:07:40.723883800+08:00" level=info msg="[TCP] 127.0.0.1:58992 --> www.bing.com:443 using GLOBAL"
time="2025-06-24T23:07:41.119814700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58966 --> mon-va.byteoversea.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T23:07:41.120815700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58972 --> mon-va.byteoversea.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T23:07:41.120815700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58977 --> mon-va.byteoversea.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T23:07:41.120815700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58973 --> mon-va.byteoversea.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T23:07:41.120815700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58974 --> mon-va.byteoversea.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T23:07:41.120815700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58976 --> mon-va.byteoversea.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T23:07:41.176360100+08:00" level=info msg="[TCP] 127.0.0.1:59007 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T23:07:41.181237400+08:00" level=info msg="[TCP] 127.0.0.1:59006 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T23:07:41.182238500+08:00" level=info msg="[TCP] 127.0.0.1:59005 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T23:07:41.230410100+08:00" level=info msg="[TCP] 127.0.0.1:59003 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T23:07:41.423803700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:58975 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-24T23:07:41.561664400+08:00" level=info msg="[TCP] 127.0.0.1:59016 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-24T23:07:42.204029600+08:00" level=info msg="[TCP] 127.0.0.1:59004 --> mon-va.byteoversea.com:443 using GLOBAL"
time="2025-06-24T23:07:46.123477400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59008 --> mon-va.byteoversea.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp **************:19812: i/o timeout"
time="2025-06-24T23:07:49.779240600+08:00" level=info msg="[TCP] 127.0.0.1:59025 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-24T23:07:50.919769300+08:00" level=info msg="[TCP] 127.0.0.1:59029 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-06-24T23:07:51.290897500+08:00" level=info msg="[TCP] 127.0.0.1:59027 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-06-24T23:07:53.045721800+08:00" level=info msg="[TCP] 127.0.0.1:59033 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-06-24T23:08:03.181980500+08:00" level=info msg="[TCP] 127.0.0.1:59044 --> www.doc88.com:443 using GLOBAL"
time="2025-06-24T23:08:06.197801700+08:00" level=info msg="[TCP] 127.0.0.1:59049 --> apmplus.volces.com:443 using GLOBAL"
time="2025-06-24T23:08:06.228836500+08:00" level=info msg="[TCP] 127.0.0.1:59053 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-06-24T23:08:06.493429500+08:00" level=info msg="[TCP] 127.0.0.1:59055 --> static.deepseek.com:443 using GLOBAL"
time="2025-06-24T23:08:06.495344400+08:00" level=info msg="[TCP] 127.0.0.1:59057 --> widget.intercom.io:443 using GLOBAL"
time="2025-06-24T23:08:06.523929800+08:00" level=info msg="[TCP] 127.0.0.1:59059 --> gator.volces.com:443 using GLOBAL"
time="2025-06-24T23:08:06.678246900+08:00" level=info msg="[TCP] 127.0.0.1:59061 --> gator.volces.com:443 using GLOBAL"
time="2025-06-24T23:08:07.990859900+08:00" level=info msg="[TCP] 127.0.0.1:59064 --> api-iam.intercom.io:443 using GLOBAL"
time="2025-06-24T23:08:08.338677100+08:00" level=info msg="[TCP] 127.0.0.1:59051 --> tab.volces.com:443 using GLOBAL"
time="2025-06-24T23:08:08.693700800+08:00" level=info msg="[TCP] 127.0.0.1:59067 --> apmplus.volces.com:443 using GLOBAL"
time="2025-06-24T23:08:09.544580500+08:00" level=info msg="[TCP] 127.0.0.1:59069 --> nexus-websocket-a.intercom.io:443 using GLOBAL"
time="2025-06-24T23:08:30.802894400+08:00" level=info msg="[TCP] 127.0.0.1:59090 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-25T00:54:10.126145900+08:00" level=warning msg="Mihomo shutting down"
