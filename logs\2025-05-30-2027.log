2025-05-30 20:28:00 INFO - try to run core in service mode
2025-05-30 20:28:00 INFO - start service: {"bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "core_type": "verge-mihomo-alpha", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-05-30-2028.log"}
2025-05-30 20:28:00 INFO - Initializing hotkeys, global hotkey enabled: true
2025-05-30 20:28:00 INFO - No hotkeys configured
2025-05-30 20:28:00 INFO - Starting to create window
2025-05-30 20:28:00 INFO - Creating new window
2025-05-30 20:28:00 INFO - Window created successfully, attempting to show
2025-05-30 20:28:00 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-30 20:28:00 INFO - Successfully registered hotkey Control+Q for quit
2025-05-30 20:28:00 INFO - running timer task `R3SfPp0qApId`
2025-05-30 20:29:17 INFO - Starting to create window
2025-05-30 20:29:17 INFO - Found existing window, trying to show it
2025-05-30 20:29:17 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-30 20:29:17 INFO - Successfully registered hotkey Control+Q for quit
2025-05-30 20:46:40 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-05-30 20:46:40 INFO - Successfully registered hotkey Control+Q for quit
2025-05-30 21:23:16 INFO - stop the core by service
2025-05-30 21:23:16 ERROR - Failed to unregister hotkey: HotKey { mods: Modifiers(CONTROL), key: KeyQ, id: 524323 }
