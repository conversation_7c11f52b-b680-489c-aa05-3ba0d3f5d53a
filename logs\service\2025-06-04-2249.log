Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-06-04T22:49:17.007502200+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-04T22:49:17.016775800+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-04T22:49:17.016775800+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-04T22:49:17.017286800+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-06-04T22:49:17.042039900+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-04T22:49:17.042039900+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-06-04T22:49:17.291895800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-04T22:49:17.291895800+08:00" level=info msg="Load GeoSite rule: private"
time="2025-06-04T22:49:17.307966600+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-04T22:49:17.311072600+08:00" level=info msg="Initial configuration complete, total time: 298ms"
time="2025-06-04T22:49:17.311583800+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-06-04T22:49:17.312606400+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-04T22:49:17.312606400+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-06-04T22:49:17.312606400+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-06-04T22:49:17.312606400+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider NowE"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider telegram"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Lan"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider BBC"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Disney"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider google"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Line"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Discord"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider apple"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider TVB"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider TVer"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-04T22:49:17.318234000+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-04T22:49:19.881016600+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-04T22:49:19.881539900+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-04T22:49:19.881539900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-04T22:49:19.882041700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-04T22:49:19.882050600+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-04T22:49:19.882050600+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-04T22:49:19.883070000+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-04T22:49:22.320169200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-04T22:49:22.320777400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-04T22:49:22.321798900+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-04T22:49:22.321285000+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:22.321290300+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-04T22:49:22.321798900+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-04T22:49:22.321798900+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-04T22:49:22.320265700+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-04T22:49:22.323554900+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-04T22:49:22.323554900+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-04T22:49:22.323554900+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-04T22:49:22.323554900+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Discord"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider telegram"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider BBC"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider NowE"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider TVB"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider google"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider apple"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Lan"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Line"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Disney"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider TVer"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-04T22:49:22.325124800+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-04T22:49:27.326160300+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-04T22:49:27.325648200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-04T22:49:27.326668800+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-04T22:49:27.328706600+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-04T22:49:27.328706600+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-04T22:49:27.328706600+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-04T22:49:27.328706600+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-04T22:49:27.617344100+08:00" level=info msg="Start initial configuration in progress"
time="2025-06-04T22:49:27.617854200+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-06-04T22:49:27.617854200+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-06-04T22:49:27.618366400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-06-04T22:49:27.618366400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-06-04T22:49:27.618366400+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-06-04T22:49:27.619599400+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider TVer"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider KKTV"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Spotify"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider BBC"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Hulu"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider telegram"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Disney"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider NaverTV"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Acplay"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Instagram"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Discord"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider google"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Peacock"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider microsoft"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Niconico"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider BiliBili"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider OpenAI"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Overcast"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Twitter"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Bahamut"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Line"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider AppleTV"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Tiktok"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider HBOHK"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider apple"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Lan"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider NowE"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider OneDrive"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider Netflix"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider TVB"
time="2025-06-04T22:49:27.620619300+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-06-04T22:49:28.343231600+08:00" level=error msg="🇭🇰香港01 [5x] failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-04T22:49:28.343231600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621726500+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-06-04T22:49:32.622251100+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-06-04T22:49:32.622240800+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-06-04T22:49:32.622251100+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-06-04T22:49:32.622251100+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.622251100+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621738400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-06-04T22:49:32.622251100+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.622251100+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-06-04T22:49:32.621164700+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:32.622251100+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-06-04T22:49:32.622251100+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-06-04T22:49:32.622251100+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-06-04T22:49:32.622251100+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-06-04T22:49:32.624725200+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-06-04T22:49:32.625237200+08:00" level=info msg="Start initial Compatible provider default"
time="2025-06-04T22:49:32.625237200+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-06-04T22:49:32.625237200+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-06-04T22:49:32.757899500+08:00" level=error msg="距离下次重置剩余：8 天 failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-06-04T22:49:32.757899500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-06-04T22:49:37.672919900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50523 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:37.672919900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50522 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:37.673433900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50519 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:37.673433900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50521 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:38.424098500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50540 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T22:49:38.434081100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50544 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T22:49:38.437678500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50549 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:38.437678500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50541 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:38.437678500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50546 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:38.437678500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50548 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:39.425778400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50575 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T22:49:39.426449800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50578 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T22:49:39.429521800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50581 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:39.430031300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50576 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:39.430031300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50582 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:39.430031300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50577 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:41.440595800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50596 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T22:49:41.440595800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50598 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T22:49:41.455661600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50601 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:41.455661600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50597 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:41.455661600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50602 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:41.456171900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50603 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:45.447096700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50615 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T22:49:45.447096700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50617 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T22:49:45.461551700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50620 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:45.461551700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50616 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:45.461551700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50621 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:45.462060900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50622 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:45.661304300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50628 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:45.661304300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50629 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:46.657974000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50633 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:46.657974000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50634 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:47.714818800+08:00" level=info msg="[TCP] 127.0.0.1:50663 --> **************:443 using GLOBAL"
time="2025-06-04T22:49:47.717840200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50638 --> download.clashverge.dev:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:47.727401400+08:00" level=info msg="[TCP] 127.0.0.1:50664 --> **************:80 using GLOBAL"
time="2025-06-04T22:49:47.777397300+08:00" level=info msg="[TCP] 127.0.0.1:50670 --> github.com:443 using GLOBAL"
time="2025-06-04T22:49:48.346670500+08:00" level=info msg="[TCP] 127.0.0.1:50672 --> objects.githubusercontent.com:443 using GLOBAL"
time="2025-06-04T22:49:48.671440600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50646 --> **************:443 error: connect failed: dial tcp **************:443: i/o timeout"
time="2025-06-04T22:49:48.672104600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50647 --> **************:80 error: connect failed: dial tcp **************:80: i/o timeout"
time="2025-06-04T22:49:48.730886700+08:00" level=info msg="[TCP] 127.0.0.1:50675 --> ***************:443 using GLOBAL"
time="2025-06-04T22:49:48.743155200+08:00" level=info msg="[TCP] 127.0.0.1:50676 --> ***************:80 using GLOBAL"
time="2025-06-04T22:49:49.972635100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50652 --> td.telegram.org:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T22:49:50.464762300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50653 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T22:49:50.474301100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50654 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T22:49:50.549703400+08:00" level=info msg="[TCP] 127.0.0.1:50683 --> *************:443 using GLOBAL"
time="2025-06-04T22:49:50.551429200+08:00" level=info msg="[TCP] 127.0.0.1:50684 --> *************:80 using GLOBAL"
time="2025-06-04T22:49:51.359797600+08:00" level=info msg="[TCP] 127.0.0.1:50689 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-04T22:49:51.846024400+08:00" level=info msg="[TCP] 127.0.0.1:50692 --> g.alicdn.com:443 using GLOBAL"
time="2025-06-04T22:49:53.485668700+08:00" level=info msg="[TCP] 127.0.0.1:50699 --> *************:80 using GLOBAL"
time="2025-06-04T22:49:53.495243400+08:00" level=info msg="[TCP] 127.0.0.1:50698 --> *************:443 using GLOBAL"
time="2025-06-04T22:49:54.403332400+08:00" level=info msg="[TCP] 127.0.0.1:50706 --> **************:443 using GLOBAL"
time="2025-06-04T22:49:54.405852600+08:00" level=info msg="[TCP] 127.0.0.1:50704 --> **************:443 using GLOBAL"
time="2025-06-04T22:49:54.405852600+08:00" level=info msg="[TCP] 127.0.0.1:50709 --> **************:80 using GLOBAL"
time="2025-06-04T22:49:54.406372800+08:00" level=info msg="[TCP] 127.0.0.1:50705 --> **************:443 using GLOBAL"
time="2025-06-04T22:49:54.406923900+08:00" level=info msg="[TCP] 127.0.0.1:50707 --> **************:80 using GLOBAL"
time="2025-06-04T22:49:54.406923900+08:00" level=info msg="[TCP] 127.0.0.1:50710 --> **************:80 using GLOBAL"
time="2025-06-04T22:49:55.092006200+08:00" level=info msg="[TCP] 127.0.0.1:50718 --> 149.154.167.91:80 using GLOBAL"
time="2025-06-04T22:49:55.094660000+08:00" level=info msg="[TCP] 127.0.0.1:50717 --> 149.154.167.91:443 using GLOBAL"
time="2025-06-04T22:49:55.262110900+08:00" level=info msg="[TCP] 127.0.0.1:50725 --> *************:80 using GLOBAL"
time="2025-06-04T22:49:55.263169300+08:00" level=info msg="[TCP] 127.0.0.1:50724 --> *************:443 using GLOBAL"
time="2025-06-04T22:49:57.249682700+08:00" level=info msg="[TCP] 127.0.0.1:50732 --> **************:80 using GLOBAL"
time="2025-06-04T22:49:57.955492100+08:00" level=info msg="[TCP] 127.0.0.1:50734 --> **************:80 using GLOBAL"
time="2025-06-04T22:49:58.498643100+08:00" level=info msg="[TCP] 127.0.0.1:50739 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-04T22:49:58.499704700+08:00" level=info msg="[TCP] 127.0.0.1:50738 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-04T22:50:00.841828800+08:00" level=info msg="[TCP] 127.0.0.1:50742 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:02.774104200+08:00" level=info msg="[TCP] 127.0.0.1:50747 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:02.775787400+08:00" level=info msg="[TCP] 127.0.0.1:50746 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:03.460680400+08:00" level=info msg="[TCP] 127.0.0.1:50750 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:04.526562300+08:00" level=info msg="[TCP] 127.0.0.1:50753 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:06.645119000+08:00" level=info msg="[TCP] 127.0.0.1:50756 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:07.239151500+08:00" level=info msg="[TCP] 127.0.0.1:50758 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:08.672337900+08:00" level=info msg="[TCP] 127.0.0.1:50762 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:09.228892100+08:00" level=info msg="[TCP] 127.0.0.1:50764 --> go.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:50:09.396922800+08:00" level=info msg="[TCP] 127.0.0.1:50766 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:10.148789200+08:00" level=info msg="[TCP] 127.0.0.1:50768 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:17.214784300+08:00" level=info msg="[TCP] 127.0.0.1:50776 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:50:19.083723900+08:00" level=info msg="[TCP] 127.0.0.1:50778 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:20.202475300+08:00" level=info msg="[TCP] 127.0.0.1:50783 --> **************:80 using GLOBAL"
time="2025-06-04T22:50:20.896769000+08:00" level=info msg="[TCP] 127.0.0.1:50785 --> **************:80 using GLOBAL"
time="2025-06-04T22:51:04.465969800+08:00" level=info msg="[TCP] 127.0.0.1:50818 --> **************:443 using GLOBAL"
time="2025-06-04T22:51:04.465969800+08:00" level=info msg="[TCP] 127.0.0.1:50819 --> **************:80 using GLOBAL"
time="2025-06-04T22:51:04.468616700+08:00" level=info msg="[TCP] 127.0.0.1:50817 --> **************:443 using GLOBAL"
time="2025-06-04T22:51:04.469889700+08:00" level=info msg="[TCP] 127.0.0.1:50820 --> **************:80 using GLOBAL"
time="2025-06-04T22:51:05.162144300+08:00" level=info msg="[TCP] 127.0.0.1:50835 --> *************:80 using GLOBAL"
time="2025-06-04T22:51:05.165366100+08:00" level=info msg="[TCP] 127.0.0.1:50830 --> *************:443 using GLOBAL"
time="2025-06-04T22:51:05.166741300+08:00" level=info msg="[TCP] 127.0.0.1:50831 --> *************:80 using GLOBAL"
time="2025-06-04T22:51:05.167272300+08:00" level=info msg="[TCP] 127.0.0.1:50828 --> *************:443 using GLOBAL"
time="2025-06-04T22:51:05.167272300+08:00" level=info msg="[TCP] 127.0.0.1:50827 --> *************:443 using GLOBAL"
time="2025-06-04T22:51:05.167272300+08:00" level=info msg="[TCP] 127.0.0.1:50840 --> 149.154.167.43:80 using GLOBAL"
time="2025-06-04T22:51:05.168337000+08:00" level=info msg="[TCP] 127.0.0.1:50837 --> *************:80 using GLOBAL"
time="2025-06-04T22:51:05.168337000+08:00" level=info msg="[TCP] 127.0.0.1:50829 --> 149.154.167.43:443 using GLOBAL"
time="2025-06-04T22:51:15.917555000+08:00" level=info msg="[TCP] 127.0.0.1:50850 --> *************:80 using GLOBAL"
time="2025-06-04T22:51:16.925305800+08:00" level=info msg="[TCP] 127.0.0.1:50849 --> *************:443 using GLOBAL"
time="2025-06-04T22:51:36.437308900+08:00" level=info msg="[TCP] 127.0.0.1:50869 --> **************:80 using GLOBAL"
time="2025-06-04T22:51:36.437308900+08:00" level=info msg="[TCP] 127.0.0.1:50867 --> **************:443 using GLOBAL"
time="2025-06-04T22:51:36.666934900+08:00" level=info msg="[TCP] 127.0.0.1:50868 --> **************:443 using GLOBAL"
time="2025-06-04T22:51:37.362361000+08:00" level=info msg="[TCP] 127.0.0.1:50875 --> 149.154.167.223:443 using GLOBAL"
time="2025-06-04T22:51:37.363676900+08:00" level=info msg="[TCP] 127.0.0.1:50876 --> 149.154.167.223:80 using GLOBAL"
time="2025-06-04T22:51:37.428213100+08:00" level=info msg="[TCP] 127.0.0.1:50881 --> **************:80 using GLOBAL"
time="2025-06-04T22:51:37.576962000+08:00" level=info msg="[TCP] 127.0.0.1:50879 --> **************:443 using GLOBAL"
time="2025-06-04T22:51:37.579336200+08:00" level=info msg="[TCP] 127.0.0.1:50884 --> **************:80 using GLOBAL"
time="2025-06-04T22:51:37.619543100+08:00" level=info msg="[TCP] 127.0.0.1:50880 --> **************:443 using GLOBAL"
time="2025-06-04T22:51:38.443163500+08:00" level=info msg="[TCP] 127.0.0.1:50888 --> 149.154.167.223:443 using GLOBAL"
time="2025-06-04T22:51:41.360670100+08:00" level=info msg="[TCP] 127.0.0.1:50889 --> 149.154.167.223:80 using GLOBAL"
time="2025-06-04T22:51:41.385112600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:50870 --> **************:80 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp 120.198.71.225:19812: i/o timeout\ndial tcp 120.241.29.152:19812: i/o timeout"
time="2025-06-04T22:51:52.458230000+08:00" level=info msg="[TCP] 127.0.0.1:50901 --> go.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:52:00.727870600+08:00" level=info msg="[TCP] 127.0.0.1:50909 --> 149.154.167.223:443 using GLOBAL"
time="2025-06-04T22:52:00.730853600+08:00" level=info msg="[TCP] 127.0.0.1:50910 --> 149.154.167.223:80 using GLOBAL"
time="2025-06-04T22:52:58.101062400+08:00" level=info msg="[TCP] 127.0.0.1:50953 --> g.alicdn.com:443 using GLOBAL"
time="2025-06-04T22:52:58.101062400+08:00" level=info msg="[TCP] 127.0.0.1:50952 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-04T22:53:21.197777900+08:00" level=info msg="[TCP] 127.0.0.1:50969 --> g.alicdn.com:443 using GLOBAL"
time="2025-06-04T22:54:26.634596300+08:00" level=info msg="[TCP] 127.0.0.1:51020 --> 149.154.167.43:443 using GLOBAL"
time="2025-06-04T22:54:27.592114000+08:00" level=info msg="[TCP] 127.0.0.1:51024 --> 149.154.167.43:443 using GLOBAL"
time="2025-06-04T22:54:27.607099200+08:00" level=info msg="[TCP] 127.0.0.1:51025 --> 149.154.167.43:80 using GLOBAL"
time="2025-06-04T22:54:27.849055500+08:00" level=info msg="[TCP] 127.0.0.1:51021 --> 149.154.167.43:80 using GLOBAL"
time="2025-06-04T22:54:29.050644600+08:00" level=info msg="[TCP] 127.0.0.1:51041 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-06-04T22:54:29.109778400+08:00" level=info msg="[TCP] 127.0.0.1:51036 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:29.230326100+08:00" level=info msg="[TCP] 127.0.0.1:51032 --> config.edge.skype.com:443 using GLOBAL"
time="2025-06-04T22:54:29.239032000+08:00" level=info msg="[TCP] 127.0.0.1:51040 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:29.247161700+08:00" level=info msg="[TCP] 127.0.0.1:51046 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:29.359812100+08:00" level=info msg="[TCP] 127.0.0.1:51051 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:29.385364300+08:00" level=info msg="[TCP] 127.0.0.1:51050 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:29.400870000+08:00" level=info msg="[TCP] 127.0.0.1:51054 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:29.456170500+08:00" level=info msg="[TCP] 127.0.0.1:51058 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:29.646753300+08:00" level=info msg="[TCP] 127.0.0.1:51056 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:29.925241400+08:00" level=info msg="[TCP] 127.0.0.1:51030 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:54:30.077989600+08:00" level=info msg="[TCP] 127.0.0.1:51037 --> c.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.079495100+08:00" level=info msg="[TCP] 127.0.0.1:51044 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.092709500+08:00" level=info msg="[TCP] 127.0.0.1:51034 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:54:30.121591100+08:00" level=info msg="[TCP] 127.0.0.1:51064 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.187003800+08:00" level=info msg="[TCP] 127.0.0.1:51066 --> assets.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.208531200+08:00" level=info msg="[TCP] 127.0.0.1:51048 --> login.live.com:443 using GLOBAL"
time="2025-06-04T22:54:30.229819100+08:00" level=info msg="[TCP] 127.0.0.1:51062 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:30.448723100+08:00" level=info msg="[TCP] 127.0.0.1:51057 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.478849000+08:00" level=info msg="[TCP] 127.0.0.1:51072 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.480377800+08:00" level=info msg="[TCP] 127.0.0.1:51068 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.511366700+08:00" level=info msg="[TCP] 127.0.0.1:51075 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.536310300+08:00" level=info msg="[TCP] 127.0.0.1:51073 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.537429500+08:00" level=info msg="[TCP] 127.0.0.1:51070 --> c.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.610228400+08:00" level=info msg="[TCP] 127.0.0.1:51074 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.725476000+08:00" level=info msg="[TCP] 127.0.0.1:51071 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:30.985330700+08:00" level=info msg="[TCP] 127.0.0.1:51085 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:31.499013200+08:00" level=info msg="[TCP] 127.0.0.1:51092 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:31.581036700+08:00" level=info msg="[TCP] 127.0.0.1:51096 --> c.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:31.717343000+08:00" level=info msg="[TCP] 127.0.0.1:51098 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:32.235141800+08:00" level=info msg="[TCP] 127.0.0.1:51100 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:32.447394200+08:00" level=info msg="[TCP] 127.0.0.1:51090 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:32.562053900+08:00" level=info msg="[TCP] 127.0.0.1:51094 --> browser.events.data.msn.cn:443 using GLOBAL"
time="2025-06-04T22:54:33.865870900+08:00" level=info msg="[TCP] 127.0.0.1:51102 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:54:35.426619200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51069 --> www.bing.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp 120.198.71.225:19812: i/o timeout\ndial tcp 120.241.29.152:19812: i/o timeout"
time="2025-06-04T22:54:37.379064600+08:00" level=info msg="[TCP] 127.0.0.1:51107 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:39.065969200+08:00" level=info msg="[TCP] 127.0.0.1:51112 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:39.104878600+08:00" level=info msg="[TCP] 127.0.0.1:51114 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:54:40.284510100+08:00" level=info msg="[TCP] 127.0.0.1:51122 --> th.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:40.336498400+08:00" level=info msg="[TCP] 127.0.0.1:51117 --> r.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:40.603491300+08:00" level=info msg="[TCP] 127.0.0.1:51120 --> th.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:40.635935200+08:00" level=info msg="[TCP] 127.0.0.1:51118 --> r.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:40.847727300+08:00" level=info msg="[TCP] 127.0.0.1:51126 --> download.windowsupdate.com:80 using GLOBAL"
time="2025-06-04T22:54:41.077189900+08:00" level=info msg="[TCP] 127.0.0.1:51130 --> xpaywalletcdn-prod.azureedge.net:443 using GLOBAL"
time="2025-06-04T22:54:41.943031800+08:00" level=info msg="[TCP] 127.0.0.1:51132 --> storage.live.com:443 using GLOBAL"
time="2025-06-04T22:54:42.181695800+08:00" level=info msg="[TCP] 127.0.0.1:51128 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:54:43.072753600+08:00" level=info msg="[TCP] 127.0.0.1:51134 --> login.live.com:443 using GLOBAL"
time="2025-06-04T22:54:43.716940300+08:00" level=info msg="[TCP] 127.0.0.1:51137 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:54:45.065258100+08:00" level=info msg="[TCP] 127.0.0.1:51140 --> ntp.msn.com:443 using GLOBAL"
time="2025-06-04T22:54:45.200943200+08:00" level=info msg="[TCP] 127.0.0.1:51142 --> c.bing.com:443 using GLOBAL"
time="2025-06-04T22:54:46.801116700+08:00" level=info msg="[TCP] 127.0.0.1:51147 --> login.live.com:443 using GLOBAL"
time="2025-06-04T22:54:47.777576400+08:00" level=info msg="[TCP] 127.0.0.1:51151 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:54:47.841370500+08:00" level=info msg="[TCP] 127.0.0.1:51149 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:54:58.544475400+08:00" level=info msg="[TCP] 127.0.0.1:51166 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-04T22:54:58.546363200+08:00" level=info msg="[TCP] 127.0.0.1:51167 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-04T22:54:58.839178000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51159 --> ntp.msn.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp 120.198.71.225:19812: i/o timeout\ndial tcp 120.241.29.152:19812: i/o timeout"
time="2025-06-04T22:54:59.049590000+08:00" level=info msg="[TCP] 127.0.0.1:51171 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-04T22:55:00.020397900+08:00" level=info msg="[TCP] 127.0.0.1:51178 --> ntp.msn.com:443 using GLOBAL"
time="2025-06-04T22:55:00.710031000+08:00" level=info msg="[TCP] 127.0.0.1:51163 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:55:00.786207300+08:00" level=info msg="[TCP] 127.0.0.1:51176 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:55:02.403736800+08:00" level=info msg="[TCP] 127.0.0.1:51173 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-04T22:55:02.879114700+08:00" level=info msg="[TCP] 127.0.0.1:51183 --> login.live.com:443 using GLOBAL"
time="2025-06-04T22:55:05.764007700+08:00" level=info msg="[TCP] 127.0.0.1:51191 --> tse1.mm.bing.net:443 using GLOBAL"
time="2025-06-04T22:55:06.349383400+08:00" level=info msg="[TCP] 127.0.0.1:51187 --> tse1.mm.bing.net:443 using GLOBAL"
time="2025-06-04T22:55:06.776091600+08:00" level=info msg="[TCP] 127.0.0.1:51195 --> graph.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:55:07.134353400+08:00" level=info msg="[TCP] 127.0.0.1:51193 --> www.bingapis.com:443 using GLOBAL"
time="2025-06-04T22:55:08.715907300+08:00" level=info msg="[TCP] 127.0.0.1:51189 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:55:08.924599100+08:00" level=info msg="[TCP] 127.0.0.1:51199 --> graph.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:55:09.888339300+08:00" level=info msg="[TCP] 127.0.0.1:51203 --> login.live.com:443 using GLOBAL"
time="2025-06-04T22:55:10.388355000+08:00" level=info msg="[TCP] 127.0.0.1:51201 --> tse1.mm.bing.net:443 using GLOBAL"
time="2025-06-04T22:55:13.697907200+08:00" level=info msg="[TCP] 127.0.0.1:51208 --> geover.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:55:14.670822600+08:00" level=info msg="[TCP] 127.0.0.1:51211 --> kv601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:55:15.636636100+08:00" level=info msg="[TCP] 127.0.0.1:51213 --> login.live.com:443 using GLOBAL"
time="2025-06-04T22:55:15.738402800+08:00" level=info msg="[TCP] 127.0.0.1:51215 --> cp601.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:55:17.957587800+08:00" level=info msg="[TCP] 127.0.0.1:51220 --> tse1.mm.bing.net:443 using GLOBAL"
time="2025-06-04T22:55:17.968534400+08:00" level=info msg="[TCP] 127.0.0.1:51222 --> ntp.msn.com:443 using GLOBAL"
time="2025-06-04T22:55:21.030736800+08:00" level=info msg="[TCP] 127.0.0.1:51224 --> au.download.windowsupdate.com:80 using GLOBAL"
time="2025-06-04T22:55:35.942296900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51235 --> go.microsoft.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp 120.198.71.225:19812: i/o timeout\ndial tcp 120.241.29.152:19812: i/o timeout"
time="2025-06-04T22:55:37.959315300+08:00" level=info msg="[TCP] 127.0.0.1:51243 --> au.download.windowsupdate.com:80 using GLOBAL"
time="2025-06-04T22:55:38.219481700+08:00" level=info msg="[TCP] 127.0.0.1:51245 --> au.download.windowsupdate.com:80 using GLOBAL"
time="2025-06-04T22:55:40.314742600+08:00" level=info msg="[TCP] 127.0.0.1:51248 --> au.download.windowsupdate.com:80 using GLOBAL"
time="2025-06-04T22:55:43.849339300+08:00" level=info msg="[TCP] 127.0.0.1:51253 --> au.download.windowsupdate.com:80 using GLOBAL"
time="2025-06-04T22:55:44.798495500+08:00" level=info msg="[TCP] 127.0.0.1:51256 --> au.download.windowsupdate.com:80 using GLOBAL"
time="2025-06-04T22:55:45.327471200+08:00" level=info msg="[TCP] 127.0.0.1:51258 --> login.live.com:443 using GLOBAL"
time="2025-06-04T22:55:47.557570600+08:00" level=info msg="[TCP] 127.0.0.1:51262 --> au.download.windowsupdate.com:80 using GLOBAL"
time="2025-06-04T22:55:56.356484800+08:00" level=info msg="[TCP] 127.0.0.1:51270 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-04T22:56:21.877386900+08:00" level=info msg="[TCP] 127.0.0.1:51289 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:56:31.478704200+08:00" level=info msg="[TCP] 127.0.0.1:51299 --> go.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:57:12.291867200+08:00" level=info msg="[TCP] 127.0.0.1:51330 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-04T22:57:56.469868700+08:00" level=info msg="[TCP] 127.0.0.1:51360 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-04T22:57:58.104480200+08:00" level=info msg="[TCP] 127.0.0.1:51362 --> s-gm.mmstat.com:443 using GLOBAL"
time="2025-06-04T22:58:06.414812700+08:00" level=info msg="[TCP] 127.0.0.1:51370 --> login.live.com:443 using GLOBAL"
time="2025-06-04T22:58:07.430144700+08:00" level=info msg="[TCP] 127.0.0.1:51374 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:58:09.028086300+08:00" level=info msg="[TCP] 127.0.0.1:51377 --> graph.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:59:09.646337600+08:00" level=info msg="[TCP] 127.0.0.1:51420 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:59:09.879397700+08:00" level=info msg="[TCP] 127.0.0.1:51424 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:59:10.006348200+08:00" level=info msg="[TCP] 127.0.0.1:51422 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T22:59:11.233001000+08:00" level=info msg="[TCP] 127.0.0.1:51428 --> storage.live.com:443 using GLOBAL"
time="2025-06-04T22:59:12.460389600+08:00" level=info msg="[TCP] 127.0.0.1:51430 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:59:16.496570100+08:00" level=info msg="[TCP] 127.0.0.1:51434 --> login.live.com:443 using GLOBAL"
time="2025-06-04T22:59:31.819818000+08:00" level=info msg="[TCP] 127.0.0.1:51451 --> cs.dds.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:59:38.341889200+08:00" level=info msg="[TCP] 127.0.0.1:51457 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-04T22:59:58.541052700+08:00" level=info msg="[TCP] 127.0.0.1:51473 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-04T22:59:59.857192300+08:00" level=info msg="[TCP] 127.0.0.1:51472 --> services.bingapis.com:443 using GLOBAL"
time="2025-06-04T23:01:58.132893100+08:00" level=info msg="[TCP] 127.0.0.1:51559 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-04T23:03:59.537328700+08:00" level=info msg="[TCP] 127.0.0.1:51648 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-04T23:04:00.888138200+08:00" level=info msg="[TCP] 127.0.0.1:51650 --> www.bing.com:443 using GLOBAL"
time="2025-06-04T23:04:01.895520000+08:00" level=info msg="[TCP] 127.0.0.1:51654 --> edge.microsoft.com:443 using GLOBAL"
time="2025-06-04T23:04:05.995206900+08:00" level=info msg="[TCP] 127.0.0.1:51674 --> res.wx.qq.com:443 using GLOBAL"
time="2025-06-04T23:04:08.573008000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51657 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T23:04:08.586996200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51658 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T23:04:08.633757200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51661 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:08.633757200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51682 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:09.574779300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51667 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T23:04:09.574779300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51669 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T23:04:10.941450800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51672 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:10.941450800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51673 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:10.941957800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51675 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:11.573213900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51678 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T23:04:11.573213900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51679 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T23:04:13.634148700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51694 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:13.634148700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51693 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:13.682084800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51695 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:15.583374400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51697 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T23:04:15.597174100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51698 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T23:04:15.941471600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51707 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:15.941471600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51706 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:15.943069600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51708 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:17.768025000+08:00" level=info msg="[TCP] 127.0.0.1:51742 --> api.aliyundrive.com:443 using GLOBAL"
time="2025-06-04T23:04:17.775382200+08:00" level=info msg="[TCP] 127.0.0.1:51741 --> unpm-upaas.uc.cn:443 using GLOBAL"
time="2025-06-04T23:04:18.683022800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51719 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:20.614024600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51724 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T23:04:20.635820700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51725 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T23:04:20.941760900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51735 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:20.941760900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51734 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:22.018170600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51736 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:22.018170600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51751 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:24.318624400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51752 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:24.318713900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51777 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:24.695021300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51758 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:25.651420900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51760 --> *************:443 error: connect failed: dial tcp *************:443: i/o timeout"
time="2025-06-04T23:04:25.673548100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51761 --> *************:80 error: connect failed: dial tcp *************:80: i/o timeout"
time="2025-06-04T23:04:25.783768400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51764 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:25.942330700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51769 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:25.942330700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51770 --> 22k4.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:27.018986800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51776 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:27.018986800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51775 --> nav-edge.smartscreen.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:29.318843800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51793 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:29.318843800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51794 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:29.695464100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51801 --> ntp.msn.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:04:30.784214900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:51812 --> aks-prod-japaneast.access-point.cloudmessaging.edge.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-06-04T23:06:51.970825700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:52305 --> graph.microsoft.com:443 error: dns resolve failed: couldn't find ip"
