Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-08T22:53:40.311084800+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-08T22:53:40.322027000+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-08T22:53:40.322027000+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-08T22:53:40.322539600+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-08T22:53:40.344849800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-08T22:53:40.345362000+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-08T22:53:40.622505600+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-08T22:53:40.622505600+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-08T22:53:40.638519500+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-08T22:53:40.641521800+08:00" level=info msg="Initial configuration complete, total time: 322ms"
time="2025-07-08T22:53:40.642523300+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-08T22:53:40.643524500+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-08T22:53:40.643524500+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-08T22:53:40.643524500+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-08T22:53:40.643524500+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider TVB"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider NowE"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Lan"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider google"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Disney"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Line"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider apple"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Discord"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider telegram"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider BBC"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider TVer"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-08T22:53:40.650530100+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-08T22:53:43.534583100+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-08T22:53:43.535611100+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-08T22:53:43.535611100+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-08T22:53:43.536127400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-08T22:53:43.536127400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-08T22:53:43.536127400+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-08T22:53:43.537651600+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-08T22:53:45.652315000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.652315000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.652315000+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-08T22:53:45.652315000+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-08T22:53:45.654027100+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-08T22:53:45.654027100+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-08T22:53:45.654027100+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-08T22:53:45.654027100+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-08T22:53:45.654027100+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.653514400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-08T22:53:45.654027100+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-08T22:53:45.653008100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:53:45.654027100+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-08T22:53:45.656454000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-08T22:53:45.656454000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-08T22:53:45.656454000+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-08T22:53:45.656454000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Lan"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider google"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Line"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider TVer"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider NowE"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider BBC"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider telegram"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Discord"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider TVB"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Disney"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-08T22:53:45.657977700+08:00" level=info msg="Start initial provider apple"
time="2025-07-08T22:53:50.657989600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.657989600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.657989600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.657989600+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-08T22:53:50.657989600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.657989600+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-08T22:53:50.657989600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.657989600+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-08T22:53:50.657989600+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-08T22:53:50.657989600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.657989600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.657989600+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-08T22:53:50.657989600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.657989600+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-08T22:53:50.657989600+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-08T22:53:50.657989600+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-08T22:53:50.658498600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.658498600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.658498600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.658498600+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-08T22:53:50.658498600+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-08T22:53:50.658498600+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-08T22:53:50.659010600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659010600+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-08T22:53:50.659522000+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-08T22:53:50.660029600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.660029600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: h.cm.xiaomi-api.xyz:19812 connect error: connect failed: dial tcp **************:19812: i/o timeout\ndial tcp ***************:19812: i/o timeout"
time="2025-07-08T22:53:50.660029600+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-08T22:53:50.660029600+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-08T22:53:50.662169300+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-08T22:53:50.662169300+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-08T22:53:50.662169300+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-08T22:53:50.662169300+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-08T22:53:50.989689400+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-08T22:53:50.990710400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-08T22:53:50.990710400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-08T22:53:50.991224600+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-08T22:53:50.991224600+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-08T22:53:50.991737100+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-08T22:53:50.993273900+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-08T22:53:50.994814700+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider google"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider telegram"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Lan"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider TVB"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider NowE"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Disney"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider TVer"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Line"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Discord"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider apple"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider BBC"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-08T22:53:50.995328500+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-08T22:53:51.323247200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.324781000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.324781000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.325741600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.325741600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.326995300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.326995300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.326995300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.328208500+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.328720200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.329300300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.329300300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.329807700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.329807700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.329807700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.330773400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.330773400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.330773400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.331946100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.333093200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.333093200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.334251300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.334251300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.334251300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.335106900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.335106900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.336355200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.336355200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.336355200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.337752800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.337752800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.337752800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.337752800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.339098100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.339098100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.339098100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.341007300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.341007300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.341931400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.364882300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.365395000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.366573600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.366573600+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.367850800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.367850800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:51.368356800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:52.062765400+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-08T22:53:52.129455400+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-08T22:53:52.131541000+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-08T22:53:52.145253700+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-08T22:53:52.149309600+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-08T22:53:52.161669500+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-07-08T22:53:52.206033300+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-08T22:53:52.219023000+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-08T22:53:52.256721000+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-08T22:53:52.336101300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:52.363113700+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-08T22:53:52.378121200+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-08T22:53:52.391107800+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-08T22:53:52.391899100+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-08T22:53:52.392906400+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-08T22:53:52.399944900+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-08T22:53:52.408169700+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-08T22:53:52.408169700+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-08T22:53:52.415218800+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-08T22:53:52.429547100+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-08T22:53:52.433570100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:52.436634600+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-08T22:53:52.466637700+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-08T22:53:52.532996000+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-07-08T22:53:52.550320100+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-08T22:53:52.566176500+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-08T22:53:52.571708000+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-08T22:53:52.595450100+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-08T22:53:52.606734100+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-07-08T22:53:52.613193500+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-08T22:53:52.631224500+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-08T22:53:52.652105500+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-08T22:53:52.672900100+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-08T22:53:52.682510800+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-08T22:53:52.746697900+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-08T22:53:52.845878400+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-08T22:53:52.871176700+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-08T22:53:52.920685300+08:00" level=error msg="initial rule provider TVB error: 403 Forbidden"
time="2025-07-08T22:53:53.129519500+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-08T22:53:53.132275700+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-08T22:53:53.209020400+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-08T22:53:53.278823500+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-08T22:53:53.502103700+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-08T22:53:53.547691600+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-07-08T22:53:53.547691600+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-08T22:53:53.575219000+08:00" level=error msg="initial rule provider PrimeVideo error: 403 Forbidden"
time="2025-07-08T22:53:53.729731600+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-08T22:53:53.828544500+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-08T22:53:53.951334600+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-08T22:53:54.362350800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-08T22:53:55.198954000+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-08T22:53:55.339670500+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-08T22:53:55.345830700+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-08T22:53:55.345830700+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-08T22:53:55.345830700+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-08T22:53:55.345830700+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-08T22:53:56.027280900+08:00" level=info msg="[TCP] 127.0.0.1:56687 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-08T22:53:56.571911500+08:00" level=info msg="[TCP] 127.0.0.1:56717 --> cdn-cookieyes.com:443 using GLOBAL"
time="2025-07-08T22:53:56.582924200+08:00" level=info msg="[TCP] 127.0.0.1:56720 --> ahrefs.com:443 using GLOBAL"
time="2025-07-08T22:53:57.523490000+08:00" level=info msg="[TCP] 127.0.0.1:56746 --> log.cookieyes.com:443 using GLOBAL"
time="2025-07-08T22:53:57.550018300+08:00" level=info msg="[TCP] 127.0.0.1:56749 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-08T22:53:57.569424200+08:00" level=info msg="[TCP] 127.0.0.1:56754 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-08T22:53:57.569424200+08:00" level=info msg="[TCP] 127.0.0.1:56757 --> www.google.com:443 using GLOBAL"
time="2025-07-08T22:53:57.572446600+08:00" level=info msg="[TCP] 127.0.0.1:56760 --> www.google.com:443 using GLOBAL"
time="2025-07-08T22:53:57.631040700+08:00" level=info msg="[TCP] 127.0.0.1:56763 --> www.facebook.com:443 using GLOBAL"
time="2025-07-08T22:53:57.859026900+08:00" level=info msg="[TCP] 127.0.0.1:56773 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-08T22:53:57.877009500+08:00" level=info msg="[TCP] 127.0.0.1:56776 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-08T22:53:58.170956900+08:00" level=info msg="[TCP] 127.0.0.1:56785 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-08T22:53:58.347757700+08:00" level=info msg="[TCP] 127.0.0.1:56791 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-07-08T22:53:58.380407400+08:00" level=info msg="[TCP] 127.0.0.1:56788 --> download.clashverge.dev:443 using GLOBAL"
time="2025-07-08T22:53:58.383942900+08:00" level=info msg="[TCP] 127.0.0.1:56794 --> www.google.cn:443 using GLOBAL"
time="2025-07-08T22:53:58.391841800+08:00" level=info msg="[TCP] 127.0.0.1:56797 --> analytics.ahrefs.com:443 using GLOBAL"
time="2025-07-08T22:53:58.391841800+08:00" level=info msg="[TCP] 127.0.0.1:56798 --> analytics-staging.ahrefs.dev:443 using GLOBAL"
time="2025-07-08T22:53:58.645677900+08:00" level=info msg="[TCP] 127.0.0.1:56766 --> www.facebook.com:443 using GLOBAL"
time="2025-07-08T22:53:58.674676200+08:00" level=info msg="[TCP] 127.0.0.1:56803 --> cdn-cookieyes.com:443 using GLOBAL"
time="2025-07-08T22:54:01.181720500+08:00" level=info msg="[TCP] 127.0.0.1:56808 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-08T22:54:02.158867100+08:00" level=info msg="[TCP] 127.0.0.1:56811 --> www.bing.com:443 using GLOBAL"
time="2025-07-08T22:54:03.488328600+08:00" level=info msg="[TCP] 127.0.0.1:56815 --> google.com:443 using GLOBAL"
time="2025-07-08T22:54:03.535625600+08:00" level=info msg="[TCP] 127.0.0.1:56816 --> google.com:443 using GLOBAL"
time="2025-07-08T22:54:03.543903100+08:00" level=info msg="[TCP] 127.0.0.1:56824 --> analytics.ahrefs.com:443 using GLOBAL"
time="2025-07-08T22:54:03.548537100+08:00" level=info msg="[TCP] 127.0.0.1:56821 --> analytics-staging.ahrefs.dev:443 using GLOBAL"
time="2025-07-08T22:54:03.573719000+08:00" level=info msg="[TCP] 127.0.0.1:56828 --> google.com:443 using GLOBAL"
time="2025-07-08T22:54:03.680753000+08:00" level=info msg="[TCP] 127.0.0.1:56834 --> log.cookieyes.com:443 using GLOBAL"
time="2025-07-08T22:54:03.751130800+08:00" level=info msg="[TCP] 127.0.0.1:56829 --> challenges.cloudflare.com:443 using GLOBAL"
time="2025-07-08T22:54:03.847649500+08:00" level=info msg="[TCP] 127.0.0.1:56838 --> analytics.ahrefs.com:443 using GLOBAL"
time="2025-07-08T22:54:04.649164500+08:00" level=info msg="[TCP] 127.0.0.1:56846 --> analytics-staging.ahrefs.dev:443 using GLOBAL"
time="2025-07-08T22:54:05.495554900+08:00" level=info msg="[TCP] 127.0.0.1:56849 --> cdn-cookieyes.com:443 using GLOBAL"
time="2025-07-08T22:54:05.537320800+08:00" level=info msg="[TCP] 127.0.0.1:56843 --> gm.mmstat.com:443 using GLOBAL"
time="2025-07-08T22:54:05.862698100+08:00" level=info msg="[TCP] 127.0.0.1:56837 --> analytics-staging.ahrefs.dev:443 using GLOBAL"
time="2025-07-08T22:54:08.670778600+08:00" level=info msg="[TCP] 127.0.0.1:56854 --> cdn-cookieyes.com:443 using GLOBAL"
time="2025-07-08T22:54:09.730714200+08:00" level=info msg="[TCP] 127.0.0.1:56858 --> gm.mmstat.com:443 using GLOBAL"
time="2025-07-08T22:54:19.171089100+08:00" level=info msg="[TCP] 127.0.0.1:56868 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-08T22:54:25.343132300+08:00" level=info msg="[TCP] 127.0.0.1:56940 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-08T22:54:26.918867900+08:00" level=info msg="[TCP] 127.0.0.1:56943 --> assets.msn.com:443 using GLOBAL"
time="2025-07-08T22:54:40.001929400+08:00" level=info msg="[TCP] 127.0.0.1:56956 --> x1.c.lencr.org:80 using GLOBAL"
time="2025-07-08T22:54:40.282625000+08:00" level=info msg="[TCP] 127.0.0.1:56956 --> c.pki.goog:80 using GLOBAL"
time="2025-07-08T22:54:40.552826500+08:00" level=info msg="[TCP] 127.0.0.1:56962 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-08T22:54:40.719991700+08:00" level=info msg="[TCP] 127.0.0.1:56956 --> ctldl.windowsupdate.com:80 using GLOBAL"
time="2025-07-08T22:54:40.967190200+08:00" level=info msg="[TCP] 127.0.0.1:56967 --> cn.bing.com:443 using GLOBAL"
time="2025-07-08T22:54:43.662759100+08:00" level=info msg="[TCP] 127.0.0.1:56979 --> cn.bing.com:443 using GLOBAL"
time="2025-07-08T22:54:48.485780600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56956 --> ctldl.windowsupdate.com:80 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:54:49.465780300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56981 --> gm.mmstat.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:54:49.519469100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56982 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:54:54.466414000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56993 --> gm.mmstat.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T22:54:54.519964400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:56998 --> functional.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-09T00:23:19.171774800+08:00" level=info msg="[TCP] 127.0.0.1:53014 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-09T00:23:19.183285900+08:00" level=info msg="[TCP] 127.0.0.1:53015 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:23:19.186587600+08:00" level=info msg="[TCP] 127.0.0.1:53013 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:23:26.331010200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53054 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-09T00:23:26.384985400+08:00" level=info msg="[TCP] 127.0.0.1:53082 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:23:28.174734200+08:00" level=info msg="[TCP] 127.0.0.1:53086 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:23:33.159623800+08:00" level=info msg="[TCP] 127.0.0.1:53095 --> array501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:23:36.685107400+08:00" level=info msg="[TCP] 127.0.0.1:53101 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:24:10.771732800+08:00" level=info msg="[TCP] 127.0.0.1:53214 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:24:19.174013200+08:00" level=info msg="[TCP] 127.0.0.1:53226 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-09T00:24:20.044694400+08:00" level=info msg="[TCP] 127.0.0.1:53234 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-09T00:25:19.173774500+08:00" level=info msg="[TCP] 127.0.0.1:53312 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:25:19.487372700+08:00" level=info msg="[TCP] 127.0.0.1:53316 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:25:19.492517000+08:00" level=info msg="[TCP] 127.0.0.1:53318 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:25:35.232870000+08:00" level=info msg="[TCP] 127.0.0.1:53346 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T00:25:37.970613800+08:00" level=info msg="[TCP] 127.0.0.1:53351 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-09T00:25:40.490621200+08:00" level=info msg="[TCP] 127.0.0.1:53358 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:26:19.181024100+08:00" level=info msg="[TCP] 127.0.0.1:53410 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:27:19.502433700+08:00" level=info msg="[TCP] 127.0.0.1:53494 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:27:19.510256200+08:00" level=info msg="[TCP] 127.0.0.1:53486 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:27:19.555138000+08:00" level=info msg="[TCP] 127.0.0.1:53491 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:27:33.132121700+08:00" level=info msg="[TCP] 127.0.0.1:53511 --> activity.windows.com:443 using GLOBAL"
time="2025-07-09T00:28:14.779825100+08:00" level=info msg="[TCP] 127.0.0.1:53574 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:28:22.365464000+08:00" level=info msg="[TCP] 127.0.0.1:53609 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:28:37.196350000+08:00" level=info msg="[TCP] 127.0.0.1:53635 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:29:12.327478800+08:00" level=info msg="[TCP] 127.0.0.1:53726 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:29:19.183219200+08:00" level=info msg="[TCP] 127.0.0.1:53736 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:29:19.497896700+08:00" level=info msg="[TCP] 127.0.0.1:53742 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:29:19.500799700+08:00" level=info msg="[TCP] 127.0.0.1:53743 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:29:19.607259100+08:00" level=info msg="[TCP] 127.0.0.1:53748 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:30:34.816258100+08:00" level=info msg="[TCP] 127.0.0.1:53852 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:30:36.174797900+08:00" level=info msg="[TCP] 127.0.0.1:53857 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T00:30:38.491110300+08:00" level=info msg="[TCP] 127.0.0.1:53862 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-09T00:30:41.965497800+08:00" level=info msg="[TCP] 127.0.0.1:53868 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:30:45.467681900+08:00" level=info msg="[TCP] 127.0.0.1:53875 --> array510.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:30:46.627146500+08:00" level=info msg="[TCP] 127.0.0.1:53880 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:30:57.542671800+08:00" level=info msg="[TCP] 127.0.0.1:53899 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:31:13.402379500+08:00" level=info msg="[TCP] 127.0.0.1:53919 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:31:19.173208900+08:00" level=info msg="[TCP] 127.0.0.1:53930 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:31:19.183311100+08:00" level=info msg="[TCP] 127.0.0.1:53933 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:32:19.672291100+08:00" level=info msg="[TCP] 127.0.0.1:54014 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:32:38.713042000+08:00" level=info msg="[TCP] 127.0.0.1:54041 --> activity.windows.com:443 using GLOBAL"
time="2025-07-09T00:33:19.175854800+08:00" level=info msg="[TCP] 127.0.0.1:54149 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:33:52.321591000+08:00" level=info msg="[TCP] 127.0.0.1:54232 --> alive.github.com:443 using GLOBAL"
time="2025-07-09T00:34:11.398896300+08:00" level=info msg="[TCP] 127.0.0.1:54300 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:34:20.479563900+08:00" level=info msg="[TCP] 127.0.0.1:54315 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:35:19.188320200+08:00" level=info msg="[TCP] 127.0.0.1:54391 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:35:19.501458600+08:00" level=info msg="[TCP] 127.0.0.1:54396 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:35:19.503678000+08:00" level=info msg="[TCP] 127.0.0.1:54397 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:35:31.959767700+08:00" level=info msg="[TCP] 127.0.0.1:54436 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:35:37.171644100+08:00" level=info msg="[TCP] 127.0.0.1:54468 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T00:35:38.944803300+08:00" level=info msg="[TCP] 127.0.0.1:54476 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-09T00:35:41.460975400+08:00" level=info msg="[TCP] 127.0.0.1:54485 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:35:43.018320400+08:00" level=info msg="[TCP] 127.0.0.1:54490 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:36:19.214352400+08:00" level=info msg="[TCP] 127.0.0.1:54533 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:37:06.457147500+08:00" level=info msg="[TCP] 127.0.0.1:54593 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:37:19.180430400+08:00" level=info msg="[TCP] 127.0.0.1:54610 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:37:19.494858000+08:00" level=info msg="[TCP] 127.0.0.1:54616 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:37:19.495951700+08:00" level=info msg="[TCP] 127.0.0.1:54615 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:37:22.378219000+08:00" level=info msg="[TCP] 127.0.0.1:54624 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:37:22.669690000+08:00" level=info msg="[TCP] 127.0.0.1:54628 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:37:22.733595800+08:00" level=info msg="[TCP] 127.0.0.1:54631 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:37:38.418897000+08:00" level=info msg="[TCP] 127.0.0.1:54652 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:37:40.284160500+08:00" level=info msg="[TCP] 127.0.0.1:54656 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:39:19.182611600+08:00" level=info msg="[TCP] 127.0.0.1:54915 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:39:19.499483600+08:00" level=info msg="[TCP] 127.0.0.1:54921 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:39:19.502050300+08:00" level=info msg="[TCP] 127.0.0.1:54920 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:39:21.493480900+08:00" level=info msg="[TCP] 127.0.0.1:54930 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:39:47.470596700+08:00" level=info msg="[TCP] 127.0.0.1:54961 --> array501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:39:48.805802900+08:00" level=info msg="[TCP] 127.0.0.1:54966 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:39:58.151153600+08:00" level=info msg="[TCP] 127.0.0.1:54988 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:40:34.798963000+08:00" level=info msg="[TCP] 127.0.0.1:55041 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:40:38.174946100+08:00" level=info msg="[TCP] 127.0.0.1:55048 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T00:40:39.436577900+08:00" level=info msg="[TCP] 127.0.0.1:55051 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-09T00:40:42.416337700+08:00" level=info msg="[TCP] 127.0.0.1:55058 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:40:48.180748900+08:00" level=info msg="[TCP] 127.0.0.1:55068 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T00:40:53.939942800+08:00" level=info msg="[TCP] 127.0.0.1:55078 --> edge.microsoft.com:80 using GLOBAL"
time="2025-07-09T00:40:57.049946600+08:00" level=info msg="[TCP] 127.0.0.1:55083 --> update.code.visualstudio.com:443 using GLOBAL"
time="2025-07-09T00:41:02.163614000+08:00" level=info msg="[TCP] 127.0.0.1:55096 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:41:19.178168600+08:00" level=info msg="[TCP] 127.0.0.1:55122 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-09T00:41:19.179442800+08:00" level=info msg="[TCP] 127.0.0.1:55119 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:41:19.185578700+08:00" level=info msg="[TCP] 127.0.0.1:55125 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:41:19.500586100+08:00" level=info msg="[TCP] 127.0.0.1:55132 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:41:19.500586100+08:00" level=info msg="[TCP] 127.0.0.1:55131 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:42:39.376856000+08:00" level=info msg="[TCP] 127.0.0.1:55244 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:43:19.230464400+08:00" level=info msg="[TCP] 127.0.0.1:55300 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:43:19.492755000+08:00" level=info msg="[TCP] 127.0.0.1:55310 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:43:19.506689900+08:00" level=info msg="[TCP] 127.0.0.1:55313 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:43:27.607395000+08:00" level=info msg="[TCP] 127.0.0.1:55376 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:43:27.915586800+08:00" level=info msg="[TCP] 127.0.0.1:55380 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:43:27.917091800+08:00" level=info msg="[TCP] 127.0.0.1:55379 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:44:22.988612500+08:00" level=info msg="[TCP] 127.0.0.1:55492 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:44:49.179389300+08:00" level=info msg="[TCP] 127.0.0.1:55526 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:45:03.898363800+08:00" level=info msg="[TCP] 127.0.0.1:55548 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:45:19.181955800+08:00" level=info msg="[TCP] 127.0.0.1:55569 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:45:19.491875000+08:00" level=info msg="[TCP] 127.0.0.1:55574 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:45:19.491875000+08:00" level=info msg="[TCP] 127.0.0.1:55575 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:45:22.840704100+08:00" level=info msg="[TCP] 127.0.0.1:55590 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:45:23.160378800+08:00" level=info msg="[TCP] 127.0.0.1:55594 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:45:23.162342600+08:00" level=info msg="[TCP] 127.0.0.1:55593 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:45:50.174319300+08:00" level=info msg="[TCP] 127.0.0.1:55643 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T00:46:20.182593500+08:00" level=info msg="[TCP] 127.0.0.1:55681 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:47:19.178788500+08:00" level=info msg="[TCP] 127.0.0.1:55761 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:47:50.927188700+08:00" level=info msg="[TCP] 127.0.0.1:55806 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:49:19.179522200+08:00" level=info msg="[TCP] 127.0.0.1:56007 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:49:23.793874700+08:00" level=info msg="[TCP] 127.0.0.1:56020 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:50:02.792264900+08:00" level=info msg="[TCP] 127.0.0.1:56076 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:50:05.926992600+08:00" level=info msg="[TCP] 127.0.0.1:56083 --> g.live.com:443 using GLOBAL"
time="2025-07-09T00:50:06.464034700+08:00" level=info msg="[TCP] 127.0.0.1:56087 --> oneclient.sfx.ms:443 using GLOBAL"
time="2025-07-09T00:50:08.510211400+08:00" level=info msg="[TCP] 127.0.0.1:56092 --> ecs.office.com:443 using GLOBAL"
time="2025-07-09T00:50:17.950718200+08:00" level=info msg="[TCP] 127.0.0.1:56107 --> onedriveclucprodbn20029.blob.core.windows.net:443 using GLOBAL"
time="2025-07-09T00:50:20.880379100+08:00" level=info msg="[TCP] 127.0.0.1:56120 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:50:22.782218800+08:00" level=info msg="[TCP] 127.0.0.1:56127 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:50:26.454680700+08:00" level=info msg="[TCP] 127.0.0.1:56133 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:50:34.769342300+08:00" level=info msg="[TCP] 127.0.0.1:56155 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:50:43.629943100+08:00" level=info msg="[TCP] 127.0.0.1:56168 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:50:52.179472000+08:00" level=info msg="[TCP] 127.0.0.1:56181 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T00:50:56.994983500+08:00" level=info msg="[TCP] 127.0.0.1:56191 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-09T00:50:56.996074400+08:00" level=info msg="[TCP] 127.0.0.1:56190 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-09T00:51:11.523165500+08:00" level=info msg="[TCP] 127.0.0.1:56214 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:51:19.189401600+08:00" level=info msg="[TCP] 127.0.0.1:56226 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:51:19.201466100+08:00" level=info msg="[TCP] 127.0.0.1:56229 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:51:48.951330000+08:00" level=info msg="[TCP] 127.0.0.1:56268 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:52:33.046272400+08:00" level=info msg="[TCP] 127.0.0.1:56336 --> static.nvidiagrid.net:443 using GLOBAL"
time="2025-07-09T00:52:36.365140700+08:00" level=info msg="[TCP] 127.0.0.1:56344 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:52:53.697003500+08:00" level=info msg="[TCP] 127.0.0.1:56368 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:52:54.675022400+08:00" level=info msg="[TCP] 127.0.0.1:56372 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:52:54.843785200+08:00" level=info msg="[TCP] 127.0.0.1:56378 --> login.live.com:443 using GLOBAL"
time="2025-07-09T00:52:54.845583700+08:00" level=info msg="[TCP] 127.0.0.1:56381 --> login.live.com:443 using GLOBAL"
time="2025-07-09T00:52:55.707186700+08:00" level=info msg="[TCP] 127.0.0.1:56386 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:52:55.710473600+08:00" level=info msg="[TCP] 127.0.0.1:56375 --> www.bing.com:443 using GLOBAL"
time="2025-07-09T00:52:55.775795900+08:00" level=info msg="[TCP] 127.0.0.1:56389 --> www.bing.com:443 using GLOBAL"
time="2025-07-09T00:53:01.420376900+08:00" level=info msg="[TCP] 127.0.0.1:56405 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-09T00:53:01.724620500+08:00" level=info msg="[TCP] 127.0.0.1:56410 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-09T00:53:01.725665100+08:00" level=info msg="[TCP] 127.0.0.1:56409 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-09T00:53:06.744136900+08:00" level=info msg="[TCP] 127.0.0.1:56428 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:53:07.049204700+08:00" level=info msg="[TCP] 127.0.0.1:56431 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:53:19.182550500+08:00" level=info msg="[TCP] 127.0.0.1:56447 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:53:19.492247100+08:00" level=info msg="[TCP] 127.0.0.1:56463 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:53:19.496663400+08:00" level=info msg="[TCP] 127.0.0.1:56462 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:53:25.503540700+08:00" level=info msg="[TCP] 127.0.0.1:56514 --> array510.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:53:27.000392100+08:00" level=info msg="[TCP] 127.0.0.1:56520 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:53:58.119845400+08:00" level=info msg="[TCP] 127.0.0.1:56604 --> array513.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:53:59.370694400+08:00" level=info msg="[TCP] 127.0.0.1:56612 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:54:24.953011100+08:00" level=info msg="[TCP] 127.0.0.1:56653 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:54:55.838459800+08:00" level=info msg="[TCP] 127.0.0.1:56694 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:55:19.190956900+08:00" level=info msg="[TCP] 127.0.0.1:56723 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:55:19.497834800+08:00" level=info msg="[TCP] 127.0.0.1:56729 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:55:19.497834800+08:00" level=info msg="[TCP] 127.0.0.1:56728 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:55:25.553216300+08:00" level=info msg="[TCP] 127.0.0.1:56744 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-09T00:55:25.865135800+08:00" level=info msg="[TCP] 127.0.0.1:56747 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-09T00:55:31.621462000+08:00" level=info msg="[TCP] 127.0.0.1:56757 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:55:48.947913800+08:00" level=info msg="[TCP] 127.0.0.1:56780 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:55:49.423132700+08:00" level=info msg="[TCP] 127.0.0.1:56785 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:55:54.175175500+08:00" level=info msg="[TCP] 127.0.0.1:56791 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T00:56:19.180629000+08:00" level=info msg="[TCP] 127.0.0.1:56827 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:56:29.399980400+08:00" level=info msg="[TCP] 127.0.0.1:56846 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T00:57:19.183320900+08:00" level=info msg="[TCP] 127.0.0.1:56908 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:57:19.494132800+08:00" level=info msg="[TCP] 127.0.0.1:56915 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:57:20.510945100+08:00" level=info msg="[TCP] 127.0.0.1:56914 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:57:40.771260700+08:00" level=info msg="[TCP] 127.0.0.1:56945 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T00:57:49.450454600+08:00" level=info msg="[TCP] 127.0.0.1:56958 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T00:58:54.719074900+08:00" level=info msg="[TCP] 127.0.0.1:57141 --> alive.github.com:443 using GLOBAL"
time="2025-07-09T00:58:58.075741800+08:00" level=info msg="[TCP] 127.0.0.1:57175 --> github.com:443 using GLOBAL"
time="2025-07-09T00:58:59.121538800+08:00" level=info msg="[TCP] 127.0.0.1:57183 --> alive.github.com:443 using GLOBAL"
time="2025-07-09T00:59:19.181322500+08:00" level=info msg="[TCP] 127.0.0.1:57223 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T00:59:26.453338300+08:00" level=info msg="[TCP] 127.0.0.1:57256 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:00:04.931723400+08:00" level=info msg="[TCP] 127.0.0.1:57319 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:00:20.659451900+08:00" level=info msg="[TCP] 127.0.0.1:57345 --> array501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:00:22.086386000+08:00" level=info msg="[TCP] 127.0.0.1:57352 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:00:34.772259900+08:00" level=info msg="[TCP] 127.0.0.1:57370 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:00:56.178732400+08:00" level=info msg="[TCP] 127.0.0.1:57399 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T01:01:16.832902200+08:00" level=info msg="[TCP] 127.0.0.1:57432 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:01:17.148524700+08:00" level=info msg="[TCP] 127.0.0.1:57435 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:01:17.166733200+08:00" level=info msg="[TCP] 127.0.0.1:57438 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:01:19.181171900+08:00" level=info msg="[TCP] 127.0.0.1:57443 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:01:19.185748900+08:00" level=info msg="[TCP] 127.0.0.1:57446 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:01:35.289988500+08:00" level=info msg="[TCP] 127.0.0.1:57474 --> x1.c.lencr.org:80 using GLOBAL"
time="2025-07-09T01:01:35.571381000+08:00" level=info msg="[TCP] 127.0.0.1:57474 --> c.pki.goog:80 using GLOBAL"
time="2025-07-09T01:01:35.848498000+08:00" level=info msg="[TCP] 127.0.0.1:57474 --> ctldl.windowsupdate.com:80 using GLOBAL"
time="2025-07-09T01:02:53.591398600+08:00" level=info msg="[TCP] 127.0.0.1:57580 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:03:19.184748700+08:00" level=info msg="[TCP] 127.0.0.1:57626 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:03:24.214356100+08:00" level=info msg="[TCP] 127.0.0.1:57690 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:04:27.268828700+08:00" level=info msg="[TCP] 127.0.0.1:57832 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:05:19.178327200+08:00" level=info msg="[TCP] 127.0.0.1:57901 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:05:19.481672800+08:00" level=info msg="[TCP] 127.0.0.1:57906 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:05:19.493197600+08:00" level=info msg="[TCP] 127.0.0.1:57909 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:05:30.209183500+08:00" level=info msg="[TCP] 127.0.0.1:57925 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:05:57.168887600+08:00" level=info msg="[TCP] 127.0.0.1:57965 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T01:06:00.617429800+08:00" level=info msg="[TCP] 127.0.0.1:57973 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:06:19.171488900+08:00" level=info msg="[TCP] 127.0.0.1:57998 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:07:19.184260400+08:00" level=info msg="[TCP] 127.0.0.1:58079 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:07:43.547995700+08:00" level=info msg="[TCP] 127.0.0.1:58114 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:07:54.243109600+08:00" level=info msg="[TCP] 127.0.0.1:58129 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:09:19.180698600+08:00" level=info msg="[TCP] 127.0.0.1:58437 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:09:25.781951500+08:00" level=info msg="[TCP] 127.0.0.1:58473 --> e5.c.lencr.org:80 using GLOBAL"
time="2025-07-09T01:09:28.360548000+08:00" level=info msg="[TCP] 127.0.0.1:58489 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:09:31.743173200+08:00" level=info msg="[TCP] 127.0.0.1:58496 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:10:06.671898800+08:00" level=info msg="[TCP] 127.0.0.1:58473 --> e6.c.lencr.org:80 using GLOBAL"
time="2025-07-09T01:10:10.428733100+08:00" level=info msg="[TCP] 127.0.0.1:58563 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:10:34.751992800+08:00" level=info msg="[TCP] 127.0.0.1:58605 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:10:59.180527600+08:00" level=info msg="[TCP] 127.0.0.1:58644 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T01:11:19.173276800+08:00" level=info msg="[TCP] 127.0.0.1:58670 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:11:19.479184800+08:00" level=info msg="[TCP] 127.0.0.1:58678 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:11:19.497152900+08:00" level=info msg="[TCP] 127.0.0.1:58681 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:11:20.182368600+08:00" level=info msg="[TCP] 127.0.0.1:58673 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:13:03.918641400+08:00" level=info msg="[TCP] 127.0.0.1:58852 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:13:10.204012200+08:00" level=info msg="[TCP] 127.0.0.1:58865 --> www.bing.com:443 using GLOBAL"
time="2025-07-09T01:13:19.179287700+08:00" level=info msg="[TCP] 127.0.0.1:58886 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:13:26.939382700+08:00" level=info msg="[TCP] 127.0.0.1:58926 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-09T01:14:29.207594600+08:00" level=info msg="[TCP] 127.0.0.1:59052 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:15:01.369260000+08:00" level=info msg="[TCP] 127.0.0.1:59095 --> alive.github.com:443 using GLOBAL"
time="2025-07-09T01:15:19.183234000+08:00" level=info msg="[TCP] 127.0.0.1:59118 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:15:51.025374400+08:00" level=info msg="[TCP] 127.0.0.1:59164 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:16:00.172768500+08:00" level=info msg="[TCP] 127.0.0.1:59185 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T01:16:19.175727300+08:00" level=info msg="[TCP] 127.0.0.1:59211 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:16:56.735629600+08:00" level=info msg="[TCP] 127.0.0.1:59268 --> array510.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:16:57.901700700+08:00" level=info msg="[TCP] 127.0.0.1:59274 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:17:19.185923900+08:00" level=info msg="[TCP] 127.0.0.1:59301 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:17:19.501943700+08:00" level=info msg="[TCP] 127.0.0.1:59307 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:17:19.501943700+08:00" level=info msg="[TCP] 127.0.0.1:59308 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:18:07.934110000+08:00" level=info msg="[TCP] 127.0.0.1:59385 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:18:24.983463000+08:00" level=info msg="[TCP] 127.0.0.1:59463 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:19:19.184686100+08:00" level=info msg="[TCP] 127.0.0.1:59565 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:19:29.998303600+08:00" level=info msg="[TCP] 127.0.0.1:59587 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:19:50.386352400+08:00" level=info msg="[TCP] 127.0.0.1:59612 --> array501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:19:52.048567400+08:00" level=info msg="[TCP] 127.0.0.1:59617 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:20:20.882088300+08:00" level=info msg="[TCP] 127.0.0.1:59666 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:20:34.086448200+08:00" level=info msg="[TCP] 127.0.0.1:59691 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:20:34.772990400+08:00" level=info msg="[TCP] 127.0.0.1:59696 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:20:57.001636700+08:00" level=info msg="[TCP] 127.0.0.1:59731 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-09T01:20:58.004256200+08:00" level=info msg="[TCP] 127.0.0.1:59732 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-09T01:20:58.679799500+08:00" level=info msg="[TCP] 127.0.0.1:59742 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:21:01.174217000+08:00" level=info msg="[TCP] 127.0.0.1:59747 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T01:21:19.181070600+08:00" level=info msg="[TCP] 127.0.0.1:59769 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:21:19.190557300+08:00" level=info msg="[TCP] 127.0.0.1:59772 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:22:51.310675400+08:00" level=info msg="[TCP] 127.0.0.1:59892 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:23:19.491274900+08:00" level=info msg="[TCP] 127.0.0.1:59961 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:23:19.492333900+08:00" level=info msg="[TCP] 127.0.0.1:59962 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:23:20.188207100+08:00" level=info msg="[TCP] 127.0.0.1:59946 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:23:38.145563800+08:00" level=info msg="[TCP] 127.0.0.1:60039 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:24:30.850160300+08:00" level=info msg="[TCP] 127.0.0.1:60234 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:25:19.186227300+08:00" level=info msg="[TCP] 127.0.0.1:60292 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:25:26.303233300+08:00" level=info msg="[TCP] 127.0.0.1:60313 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:25:43.275346500+08:00" level=info msg="[TCP] 127.0.0.1:60334 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:26:02.174108300+08:00" level=info msg="[TCP] 127.0.0.1:60366 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T01:26:19.177707000+08:00" level=info msg="[TCP] 127.0.0.1:60387 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:27:19.502643500+08:00" level=info msg="[TCP] 127.0.0.1:60468 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:27:19.502643500+08:00" level=info msg="[TCP] 127.0.0.1:60467 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:27:20.197826500+08:00" level=info msg="[TCP] 127.0.0.1:60462 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:27:35.492834100+08:00" level=info msg="[TCP] 127.0.0.1:60494 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:27:46.405696500+08:00" level=info msg="[TCP] 127.0.0.1:60511 --> alive.github.com:443 using GLOBAL"
time="2025-07-09T01:27:48.509012800+08:00" level=info msg="[TCP] 127.0.0.1:60516 --> github.com:443 using GLOBAL"
time="2025-07-09T01:27:49.426165000+08:00" level=info msg="[TCP] 127.0.0.1:60520 --> alive.github.com:443 using GLOBAL"
time="2025-07-09T01:27:54.944452800+08:00" level=info msg="[TCP] 127.0.0.1:60530 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:28:07.843208700+08:00" level=info msg="[TCP] 127.0.0.1:60554 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:28:19.179850500+08:00" level=info msg="[TCP] 127.0.0.1:60590 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:28:19.184809100+08:00" level=info msg="[TCP] 127.0.0.1:60587 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-09T01:28:36.620495100+08:00" level=info msg="[TCP] 127.0.0.1:60693 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:28:55.460107200+08:00" level=info msg="[TCP] 127.0.0.1:60743 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:28:56.427282400+08:00" level=info msg="[TCP] 127.0.0.1:60766 --> alive.github.com:443 using GLOBAL"
time="2025-07-09T01:29:19.183045700+08:00" level=info msg="[TCP] 127.0.0.1:60845 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-09T01:29:19.187554200+08:00" level=info msg="[TCP] 127.0.0.1:60848 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:29:25.104717500+08:00" level=info msg="[TCP] 127.0.0.1:60875 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:29:31.723073000+08:00" level=info msg="[TCP] 127.0.0.1:60887 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:29:39.497424900+08:00" level=info msg="[TCP] 127.0.0.1:60902 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-09T01:29:39.498635000+08:00" level=info msg="[TCP] 127.0.0.1:60901 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-09T01:30:34.784046200+08:00" level=info msg="[TCP] 127.0.0.1:60971 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:31:03.178187600+08:00" level=info msg="[TCP] 127.0.0.1:61008 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-09T01:31:19.175766200+08:00" level=info msg="[TCP] 127.0.0.1:61029 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:31:19.179598500+08:00" level=info msg="[TCP] 127.0.0.1:61032 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:31:57.404732500+08:00" level=info msg="[TCP] 127.0.0.1:61081 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:32:03.914421800+08:00" level=info msg="[TCP] 127.0.0.1:61093 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:33:19.197247300+08:00" level=info msg="[TCP] 127.0.0.1:61204 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:33:20.718758300+08:00" level=info msg="[TCP] 127.0.0.1:61237 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-09T01:34:22.211408200+08:00" level=info msg="[TCP] 127.0.0.1:61380 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:34:28.006311800+08:00" level=info msg="[TCP] 127.0.0.1:61390 --> alive.github.com:443 using GLOBAL"
time="2025-07-09T01:34:33.730706500+08:00" level=info msg="[TCP] 127.0.0.1:61400 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:34:50.924824500+08:00" level=info msg="[TCP] 127.0.0.1:61421 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:35:19.175202400+08:00" level=info msg="[TCP] 127.0.0.1:61485 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-09T01:35:19.181155700+08:00" level=info msg="[TCP] 127.0.0.1:61488 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-09T01:35:31.463926700+08:00" level=info msg="[TCP] 127.0.0.1:61512 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:35:31.800306600+08:00" level=info msg="[TCP] 127.0.0.1:61515 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:35:31.814743300+08:00" level=info msg="[TCP] 127.0.0.1:61518 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-09T01:35:36.856051500+08:00" level=info msg="[TCP] 127.0.0.1:61527 --> alive.github.com:443 using GLOBAL"
time="2025-07-09T01:35:37.759672600+08:00" level=info msg="[TCP] 127.0.0.1:61532 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-09T01:35:44.519653000+08:00" level=info msg="[TCP] 127.0.0.1:61542 --> in.appcenter.ms:443 using GLOBAL"
time="2025-07-09T01:35:57.253045900+08:00" level=warning msg="Mihomo shutting down"
