Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-25T11:33:34.599885500+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-25T11:33:34.609221300+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-25T11:33:34.609221300+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-25T11:33:34.609733900+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-25T11:33:34.635132800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-25T11:33:34.635132800+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-25T11:33:34.931300400+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-25T11:33:34.931300400+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-25T11:33:34.947012200+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-25T11:33:34.949750300+08:00" level=info msg="Initial configuration complete, total time: 344ms"
time="2025-07-25T11:33:34.950765000+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-25T11:33:34.951787800+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-25T11:33:34.952297200+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-25T11:33:34.952297200+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-25T11:33:34.952297200+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider telegram"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Disney"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider TVer"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider BBC"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider google"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Discord"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Lan"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider apple"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Line"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider TVB"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider NowE"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-25T11:33:34.959481800+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-25T11:33:37.181726100+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-25T11:33:37.182239900+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-25T11:33:37.182239900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-25T11:33:37.182751800+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-25T11:33:37.182751800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-25T11:33:37.182751800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-25T11:33:37.184289200+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-25T11:33:39.961697200+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.961697200+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-25T11:33:39.962204900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962204900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962204900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962204900+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-25T11:33:39.962204900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962204900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962204900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962204900+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-25T11:33:39.962204900+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-25T11:33:39.962204900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962204900+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-25T11:33:39.962204900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962204900+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-25T11:33:39.962204900+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-25T11:33:39.962204900+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-25T11:33:39.962204900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962204900+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-25T11:33:39.962204900+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-25T11:33:39.962204900+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962204900+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-25T11:33:39.963772900+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-25T11:33:39.963222700+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-25T11:33:39.962713600+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-25T11:33:39.965520000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-25T11:33:39.965520000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-25T11:33:39.965520000+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-25T11:33:39.965520000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Disney"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Lan"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Discord"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider apple"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Line"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider BBC"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider google"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider TVB"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider TVer"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider NowE"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider telegram"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-25T11:33:39.967559800+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-25T11:33:44.968679900+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968679900+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-25T11:33:44.968174400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-25T11:33:44.968679900+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968679900+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-25T11:33:44.967672100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:44.968679900+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-25T11:33:44.968679900+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-25T11:33:44.970861500+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-25T11:33:44.970861500+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-25T11:33:44.970861500+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-25T11:33:44.970861500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-25T11:33:45.268440600+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-25T11:33:45.268949400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-25T11:33:45.268949400+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-25T11:33:45.269461500+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-25T11:33:45.269461500+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-25T11:33:45.269461500+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-25T11:33:45.271057500+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider NowE"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Line"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider telegram"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider BBC"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Discord"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider TVB"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider TVer"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Disney"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider apple"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Lan"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider google"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-25T11:33:45.272109800+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.273954700+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.272425500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-25T11:33:50.273954700+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-25T11:33:50.272928900+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-25T11:33:50.273954700+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-25T11:33:50.273444300+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-25T11:33:50.273954700+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-25T11:33:50.273954700+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-25T11:33:50.273954700+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-25T11:33:50.277531800+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-25T11:33:50.277531800+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-25T11:33:50.277531800+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-25T11:33:50.277531800+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-25T11:37:07.694125500+08:00" level=info msg="[TCP] 127.0.0.1:51665 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T11:37:08.506529100+08:00" level=info msg="[TCP] 127.0.0.1:51669 --> miyozmc.github.io:443 using GLOBAL"
time="2025-07-25T11:37:08.509933000+08:00" level=info msg="[TCP] 127.0.0.1:51670 --> miyozmc.github.io:443 using GLOBAL"
time="2025-07-25T11:37:09.238893700+08:00" level=info msg="[TCP] 127.0.0.1:51677 --> cdn.staticfile.org:443 using GLOBAL"
time="2025-07-25T11:37:09.258034600+08:00" level=info msg="[TCP] 127.0.0.1:51680 --> cdn.staticfile.org:443 using GLOBAL"
time="2025-07-25T11:37:09.262026700+08:00" level=info msg="[TCP] 127.0.0.1:51681 --> cdn.staticfile.org:443 using GLOBAL"
time="2025-07-25T11:37:09.609775700+08:00" level=info msg="[TCP] 127.0.0.1:51686 --> secure.globalsign.com:80 using GLOBAL"
time="2025-07-25T11:37:10.398440700+08:00" level=info msg="[TCP] 127.0.0.1:51690 --> cdn.staticfile.org:443 using GLOBAL"
time="2025-07-25T11:37:10.702373300+08:00" level=info msg="[TCP] 127.0.0.1:51693 --> cdn.staticfile.org:443 using GLOBAL"
time="2025-07-25T11:37:12.376701200+08:00" level=error msg="🇰🇷韩国01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T11:37:12.376701200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T11:37:12.389390700+08:00" level=error msg="🇰🇷韩国04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T11:37:12.389390700+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T11:37:33.569501800+08:00" level=info msg="[TCP] 127.0.0.1:51735 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-25T11:37:39.119747700+08:00" level=info msg="[TCP] 127.0.0.1:51753 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-25T11:37:39.124021100+08:00" level=info msg="[TCP] 127.0.0.1:51754 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-25T11:37:43.643267500+08:00" level=info msg="[TCP] 127.0.0.1:51764 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T11:37:47.178327400+08:00" level=info msg="[TCP] 127.0.0.1:51771 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T11:37:52.655094000+08:00" level=info msg="[TCP] 127.0.0.1:51782 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T11:38:12.360489200+08:00" level=info msg="[TCP] 127.0.0.1:51810 --> westeurope-5.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-25T11:38:31.630607800+08:00" level=info msg="[TCP] 127.0.0.1:51839 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T11:38:47.233387800+08:00" level=info msg="[TCP] 127.0.0.1:51860 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T11:39:07.844414900+08:00" level=info msg="[TCP] 127.0.0.1:51886 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T11:39:08.584348900+08:00" level=info msg="[TCP] 127.0.0.1:51889 --> cn.bing.com:443 using GLOBAL"
time="2025-07-25T11:39:10.707689100+08:00" level=info msg="[TCP] 127.0.0.1:51895 --> cn.bing.com:443 using GLOBAL"
time="2025-07-25T11:39:13.114408100+08:00" level=info msg="[TCP] 127.0.0.1:51899 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T11:39:15.858025000+08:00" level=info msg="[TCP] 127.0.0.1:51906 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:07:31.600736500+08:00" level=info msg="[TCP] 127.0.0.1:59293 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:07:31.625585900+08:00" level=info msg="[TCP] 127.0.0.1:59298 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:07:32.036343000+08:00" level=info msg="[TCP] 127.0.0.1:59303 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:07:32.228181900+08:00" level=info msg="[TCP] 127.0.0.1:59306 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:07:32.534999100+08:00" level=info msg="[TCP] 127.0.0.1:59309 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-25T13:07:33.980827900+08:00" level=info msg="[TCP] 127.0.0.1:59315 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-25T13:07:35.178822600+08:00" level=info msg="[TCP] 127.0.0.1:59319 --> content-autofill.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:07:39.125638800+08:00" level=info msg="[TCP] 127.0.0.1:59334 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-25T13:07:39.126787300+08:00" level=info msg="[TCP] 127.0.0.1:59335 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-25T13:07:42.110289800+08:00" level=info msg="[TCP] 127.0.0.1:59344 --> config.teams.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:07:43.010385100+08:00" level=info msg="[TCP] 127.0.0.1:59349 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:07:43.679829200+08:00" level=info msg="[TCP] 127.0.0.1:59354 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:07:47.267180400+08:00" level=info msg="[TCP] 127.0.0.1:59361 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-25T13:07:47.575931200+08:00" level=info msg="[TCP] 127.0.0.1:59365 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-25T13:07:47.579676700+08:00" level=info msg="[TCP] 127.0.0.1:59364 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-25T13:07:48.085585000+08:00" level=info msg="[TCP] 127.0.0.1:59371 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:07:51.070007600+08:00" level=info msg="[TCP] 127.0.0.1:59382 --> onedriveclucproddm20014.blob.core.windows.net:443 using GLOBAL"
time="2025-07-25T13:07:58.250269800+08:00" level=info msg="[TCP] 127.0.0.1:59396 --> ntp.msn.com:443 using GLOBAL"
time="2025-07-25T13:07:58.255509200+08:00" level=info msg="[TCP] 127.0.0.1:59399 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:07:58.318559300+08:00" level=info msg="[TCP] 127.0.0.1:59403 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:07:58.863973300+08:00" level=info msg="[TCP] 127.0.0.1:59412 --> c.bing.com:443 using GLOBAL"
time="2025-07-25T13:07:58.866433100+08:00" level=info msg="[TCP] 127.0.0.1:59409 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:07:58.866433100+08:00" level=info msg="[TCP] 127.0.0.1:59422 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-07-25T13:07:58.868835100+08:00" level=info msg="[TCP] 127.0.0.1:59424 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-25T13:07:58.868835100+08:00" level=info msg="[TCP] 127.0.0.1:59420 --> th.bing.com:443 using GLOBAL"
time="2025-07-25T13:07:58.869338200+08:00" level=info msg="[TCP] 127.0.0.1:59417 --> api.msn.com:443 using GLOBAL"
time="2025-07-25T13:07:58.869338200+08:00" level=info msg="[TCP] 127.0.0.1:59408 --> c.msn.com:443 using GLOBAL"
time="2025-07-25T13:07:58.916372500+08:00" level=info msg="[TCP] 127.0.0.1:59429 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T13:07:59.018269500+08:00" level=info msg="[TCP] 127.0.0.1:59432 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:07:59.026098700+08:00" level=info msg="[TCP] 127.0.0.1:59435 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:07:59.056683500+08:00" level=info msg="[TCP] 127.0.0.1:59438 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T13:07:59.223047000+08:00" level=info msg="[TCP] 127.0.0.1:59441 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T13:07:59.844458400+08:00" level=info msg="[TCP] 127.0.0.1:59444 --> login.live.com:443 using GLOBAL"
time="2025-07-25T13:08:00.949692300+08:00" level=info msg="[TCP] 127.0.0.1:59453 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-25T13:08:00.951625600+08:00" level=info msg="[TCP] 127.0.0.1:59450 --> r.bing.com:443 using GLOBAL"
time="2025-07-25T13:08:00.951625600+08:00" level=info msg="[TCP] 127.0.0.1:59458 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-25T13:08:00.953906700+08:00" level=info msg="[TCP] 127.0.0.1:59448 --> r.bing.com:443 using GLOBAL"
time="2025-07-25T13:08:01.512845500+08:00" level=info msg="[TCP] 127.0.0.1:59463 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-25T13:08:07.197497100+08:00" level=info msg="[TCP] 127.0.0.1:59472 --> settings-win.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:08:08.567712300+08:00" level=info msg="[TCP] 127.0.0.1:59481 --> th.bing.com:443 using GLOBAL"
time="2025-07-25T13:08:08.573435000+08:00" level=info msg="[TCP] 127.0.0.1:59477 --> r.bing.com:443 using GLOBAL"
time="2025-07-25T13:08:08.573435000+08:00" level=info msg="[TCP] 127.0.0.1:59484 --> th.bing.com:443 using GLOBAL"
time="2025-07-25T13:08:08.573435000+08:00" level=info msg="[TCP] 127.0.0.1:59478 --> r.bing.com:443 using GLOBAL"
time="2025-07-25T13:08:09.634182200+08:00" level=info msg="[TCP] 127.0.0.1:59489 --> storage.live.com:443 using GLOBAL"
time="2025-07-25T13:08:09.838774100+08:00" level=info msg="[TCP] 127.0.0.1:59492 --> cdn.sapphire.microsoftapp.net:443 using GLOBAL"
time="2025-07-25T13:08:11.210318200+08:00" level=info msg="[TCP] 127.0.0.1:59498 --> login.live.com:443 using GLOBAL"
time="2025-07-25T13:08:11.879298000+08:00" level=info msg="[TCP] 127.0.0.1:59501 --> www.cgcbk.com:443 using GLOBAL"
time="2025-07-25T13:08:11.882258800+08:00" level=info msg="[TCP] 127.0.0.1:59502 --> www.cgcbk.com:443 using GLOBAL"
time="2025-07-25T13:08:11.887405400+08:00" level=info msg="[TCP] 127.0.0.1:59505 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:08:12.386673500+08:00" level=info msg="[TCP] 127.0.0.1:59510 --> westeurope-5.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-25T13:08:15.251657900+08:00" level=info msg="[TCP] 127.0.0.1:59520 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:08:15.256161800+08:00" level=info msg="[TCP] 127.0.0.1:59523 --> at.alicdn.com:443 using GLOBAL"
time="2025-07-25T13:08:15.307119100+08:00" level=info msg="[TCP] 127.0.0.1:59529 --> img.acgcbk.link:443 using GLOBAL"
time="2025-07-25T13:08:15.307119100+08:00" level=info msg="[TCP] 127.0.0.1:59526 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:08:16.033574200+08:00" level=info msg="[TCP] 127.0.0.1:59532 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:08:16.047418500+08:00" level=info msg="[TCP] 127.0.0.1:59535 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-25T13:08:16.134770100+08:00" level=info msg="[TCP] 127.0.0.1:59538 --> sdk.51.la:443 using GLOBAL"
time="2025-07-25T13:08:16.968992000+08:00" level=info msg="[TCP] 127.0.0.1:59543 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:08:17.086313000+08:00" level=info msg="[TCP] 127.0.0.1:59546 --> collect-v6.51.la:443 using GLOBAL"
time="2025-07-25T13:08:17.232655800+08:00" level=info msg="[TCP] 127.0.0.1:59550 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:08:17.916377600+08:00" level=info msg="[TCP] 127.0.0.1:59553 --> ltss.notepin.co:443 using GLOBAL"
time="2025-07-25T13:08:17.917509700+08:00" level=info msg="[TCP] 127.0.0.1:59554 --> ltss.notepin.co:443 using GLOBAL"
time="2025-07-25T13:08:19.389867700+08:00" level=info msg="[TCP] 127.0.0.1:59562 --> cdnjs.cloudflare.com:443 using GLOBAL"
time="2025-07-25T13:08:19.416084700+08:00" level=info msg="[TCP] 127.0.0.1:59566 --> cdnjs.cloudflare.com:443 using GLOBAL"
time="2025-07-25T13:08:19.424931300+08:00" level=info msg="[TCP] 127.0.0.1:59565 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:08:19.884877800+08:00" level=info msg="[TCP] 127.0.0.1:59572 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:08:19.891360300+08:00" level=info msg="[TCP] 127.0.0.1:59575 --> cdn.usefathom.com:443 using GLOBAL"
time="2025-07-25T13:08:22.383325000+08:00" level=info msg="[TCP] 127.0.0.1:59580 --> telem-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:08:22.390340000+08:00" level=info msg="[TCP] 127.0.0.1:59583 --> ltss.notepin.co:443 using GLOBAL"
time="2025-07-25T13:08:22.987495800+08:00" level=info msg="[TCP] 127.0.0.1:59587 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:08:23.504371500+08:00" level=info msg="[TCP] 127.0.0.1:59590 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:08:30.938236200+08:00" level=info msg="[TCP] 127.0.0.1:59600 --> www.cgcbk.com:443 using GLOBAL"
time="2025-07-25T13:08:30.993302600+08:00" level=info msg="[TCP] 127.0.0.1:59603 --> img.acgcbk.link:443 using GLOBAL"
time="2025-07-25T13:08:33.571458800+08:00" level=info msg="[TCP] 127.0.0.1:59610 --> lynnconway.me:443 using GLOBAL"
time="2025-07-25T13:08:33.718498600+08:00" level=info msg="[TCP] 127.0.0.1:59613 --> lynnconway.me:443 using GLOBAL"
time="2025-07-25T13:08:35.732297600+08:00" level=info msg="[TCP] 127.0.0.1:59620 --> router.parklogic.com:443 using GLOBAL"
time="2025-07-25T13:08:45.438278200+08:00" level=info msg="[TCP] 127.0.0.1:59636 --> at.alicdn.com:443 using GLOBAL"
time="2025-07-25T13:08:46.599167800+08:00" level=info msg="[TCP] 127.0.0.1:59641 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:08:46.601753800+08:00" level=info msg="[TCP] 127.0.0.1:59640 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:08:47.890930600+08:00" level=info msg="[TCP] 127.0.0.1:59648 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:08:49.007985700+08:00" level=info msg="[TCP] 127.0.0.1:59651 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T13:08:49.823842600+08:00" level=info msg="[TCP] 127.0.0.1:59656 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:08:52.283470300+08:00" level=info msg="[TCP] 127.0.0.1:59661 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:08:54.281491300+08:00" level=info msg="[TCP] 127.0.0.1:59667 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:09:18.977485700+08:00" level=info msg="[TCP] 127.0.0.1:59701 --> static.nvidiagrid.net:443 using GLOBAL"
time="2025-07-25T13:09:27.015000200+08:00" level=info msg="[TCP] 127.0.0.1:59718 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T13:09:48.250103400+08:00" level=info msg="[TCP] 127.0.0.1:59744 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:09:48.396496500+08:00" level=info msg="[TCP] 127.0.0.1:59747 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:09:48.443295500+08:00" level=info msg="[TCP] 127.0.0.1:59750 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:09:49.570775500+08:00" level=info msg="[TCP] 127.0.0.1:59754 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:09:49.714433500+08:00" level=info msg="[TCP] 127.0.0.1:59757 --> sdk.51.la:443 using GLOBAL"
time="2025-07-25T13:09:50.961827800+08:00" level=info msg="[TCP] 127.0.0.1:59763 --> collect-v6.51.la:443 using GLOBAL"
time="2025-07-25T13:09:52.340199300+08:00" level=info msg="[TCP] 127.0.0.1:59766 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:09:54.332481100+08:00" level=info msg="[TCP] 127.0.0.1:59773 --> imgx.acgbuster.club:443 using GLOBAL"
time="2025-07-25T13:09:54.337922000+08:00" level=info msg="[TCP] 127.0.0.1:59774 --> imgx.acgbuster.club:443 using GLOBAL"
time="2025-07-25T13:09:55.922233900+08:00" level=info msg="[TCP] 127.0.0.1:59780 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:09:56.946805400+08:00" level=info msg="[TCP] 127.0.0.1:59785 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:10:09.163216700+08:00" level=info msg="[TCP] 127.0.0.1:59802 --> ww12.lynnconway.me:443 using GLOBAL"
time="2025-07-25T13:10:09.167046300+08:00" level=info msg="[TCP] 127.0.0.1:59803 --> ww12.lynnconway.me:443 using GLOBAL"
time="2025-07-25T13:10:09.636068700+08:00" level=info msg="[TCP] 127.0.0.1:59808 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:10:09.993323300+08:00" level=info msg="[TCP] 127.0.0.1:59811 --> edge-http.microsoft.com:80 using GLOBAL"
time="2025-07-25T13:10:11.179315200+08:00" level=info msg="[TCP] 127.0.0.1:59814 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:10:13.738469700+08:00" level=info msg="[TCP] 127.0.0.1:59820 --> www.cgcbk.com:443 using GLOBAL"
time="2025-07-25T13:10:13.839404200+08:00" level=info msg="[TCP] 127.0.0.1:59823 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:10:15.532702600+08:00" level=info msg="[TCP] 127.0.0.1:59829 --> img.acgcbk.link:443 using GLOBAL"
time="2025-07-25T13:10:18.237674500+08:00" level=info msg="[TCP] 127.0.0.1:59835 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:10:18.306068800+08:00" level=info msg="[TCP] 127.0.0.1:59839 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:10:19.667039200+08:00" level=info msg="[TCP] 127.0.0.1:59842 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:10:19.832694800+08:00" level=info msg="[TCP] 127.0.0.1:59845 --> sdk.51.la:443 using GLOBAL"
time="2025-07-25T13:10:24.622910000+08:00" level=info msg="[TCP] 127.0.0.1:59854 --> ww12.lynnconway.me:443 using GLOBAL"
time="2025-07-25T13:10:27.149685400+08:00" level=info msg="[TCP] 127.0.0.1:59860 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:10:27.478422300+08:00" level=info msg="[TCP] 127.0.0.1:59865 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:10:33.837311100+08:00" level=info msg="[TCP] 127.0.0.1:59874 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:10:35.372693200+08:00" level=info msg="[TCP] 127.0.0.1:59878 --> sdk.51.la:443 using GLOBAL"
time="2025-07-25T13:10:36.192512900+08:00" level=info msg="[TCP] 127.0.0.1:59881 --> collect-v6.51.la:443 using GLOBAL"
time="2025-07-25T13:11:00.242483700+08:00" level=info msg="[TCP] 127.0.0.1:59913 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:11:00.754231600+08:00" level=info msg="[TCP] 127.0.0.1:59917 --> android.clients.google.com:443 using GLOBAL"
time="2025-07-25T13:11:00.896897400+08:00" level=info msg="[TCP] 127.0.0.1:59920 --> android.clients.google.com:443 using GLOBAL"
time="2025-07-25T13:11:01.546383900+08:00" level=info msg="[TCP] 127.0.0.1:59925 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:11:03.721928700+08:00" level=info msg="[TCP] 127.0.0.1:59932 --> ww12.lynnconway.me:443 using GLOBAL"
time="2025-07-25T13:11:03.726335300+08:00" level=info msg="[TCP] 127.0.0.1:59933 --> ww12.lynnconway.me:443 using GLOBAL"
time="2025-07-25T13:11:03.927451400+08:00" level=info msg="[TCP] 127.0.0.1:59938 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:11:04.493581100+08:00" level=info msg="[TCP] 127.0.0.1:59943 --> ww12.lynnconway.me:443 using GLOBAL"
time="2025-07-25T13:11:05.551805400+08:00" level=info msg="[TCP] 127.0.0.1:59946 --> parking3.parklogic.com:443 using GLOBAL"
time="2025-07-25T13:11:05.570824100+08:00" level=info msg="[TCP] 127.0.0.1:59949 --> euob.youseasky.com:443 using GLOBAL"
time="2025-07-25T13:11:06.851337100+08:00" level=info msg="[TCP] 127.0.0.1:59953 --> parking3.parklogic.com:443 using GLOBAL"
time="2025-07-25T13:11:07.043629000+08:00" level=info msg="[TCP] 127.0.0.1:59958 --> obseu.youseasky.com:443 using GLOBAL"
time="2025-07-25T13:11:34.511096700+08:00" level=info msg="[TCP] 127.0.0.1:59994 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:11:38.657777100+08:00" level=info msg="[TCP] 127.0.0.1:60001 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:11:38.725140700+08:00" level=info msg="[TCP] 127.0.0.1:60004 --> fonts.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:11:39.479848500+08:00" level=info msg="[TCP] 127.0.0.1:60007 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:11:43.591086600+08:00" level=info msg="[TCP] 127.0.0.1:60015 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:11:43.626257700+08:00" level=info msg="[TCP] 127.0.0.1:60020 --> xpaycdn-prod.azureedge.net:443 using GLOBAL"
time="2025-07-25T13:11:43.627452200+08:00" level=info msg="[TCP] 127.0.0.1:60021 --> xpaywalletcdn-prod.azureedge.net:443 using GLOBAL"
time="2025-07-25T13:11:43.752908800+08:00" level=info msg="[TCP] 127.0.0.1:60026 --> login.live.com:443 using GLOBAL"
time="2025-07-25T13:11:44.967756500+08:00" level=info msg="[TCP] 127.0.0.1:60030 --> paymentinstruments.mp.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:11:46.776474600+08:00" level=info msg="[TCP] 127.0.0.1:60036 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:11:48.387256600+08:00" level=info msg="[TCP] 127.0.0.1:60040 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:11:49.572817400+08:00" level=info msg="[TCP] 127.0.0.1:60044 --> imgx.acgbuster.club:443 using GLOBAL"
time="2025-07-25T13:11:50.427746600+08:00" level=info msg="[TCP] 127.0.0.1:60049 --> img3.acgbuster.link:443 using GLOBAL"
time="2025-07-25T13:11:55.725685400+08:00" level=info msg="[TCP] 127.0.0.1:60058 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:12:06.012326700+08:00" level=info msg="[TCP] 127.0.0.1:60074 --> at.alicdn.com:443 using GLOBAL"
time="2025-07-25T13:12:09.007811000+08:00" level=info msg="[TCP] 127.0.0.1:60080 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:12:09.733747000+08:00" level=info msg="[TCP] 127.0.0.1:60083 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:12:12.827032300+08:00" level=info msg="[TCP] 127.0.0.1:60089 --> ntp.msn.com:443 using GLOBAL"
time="2025-07-25T13:12:13.477880700+08:00" level=info msg="[TCP] 127.0.0.1:60109 --> api.msn.com:443 using GLOBAL"
time="2025-07-25T13:12:13.480465200+08:00" level=info msg="[TCP] 127.0.0.1:60095 --> c.msn.com:443 using GLOBAL"
time="2025-07-25T13:12:13.480465200+08:00" level=info msg="[TCP] 127.0.0.1:60094 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T13:12:13.480967600+08:00" level=info msg="[TCP] 127.0.0.1:60098 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:12:13.481928100+08:00" level=info msg="[TCP] 127.0.0.1:60106 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T13:12:13.482430400+08:00" level=info msg="[TCP] 127.0.0.1:60116 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-25T13:12:13.482430400+08:00" level=info msg="[TCP] 127.0.0.1:60112 --> th.bing.com:443 using GLOBAL"
time="2025-07-25T13:12:13.483242200+08:00" level=info msg="[TCP] 127.0.0.1:60113 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-07-25T13:12:13.483242200+08:00" level=info msg="[TCP] 127.0.0.1:60101 --> c.bing.com:443 using GLOBAL"
time="2025-07-25T13:12:13.713807400+08:00" level=info msg="[TCP] 127.0.0.1:60122 --> r.bing.com:443 using GLOBAL"
time="2025-07-25T13:12:13.713807400+08:00" level=info msg="[TCP] 127.0.0.1:60126 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-25T13:12:13.714381100+08:00" level=info msg="[TCP] 127.0.0.1:60131 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-25T13:12:13.718900600+08:00" level=info msg="[TCP] 127.0.0.1:60123 --> r.bing.com:443 using GLOBAL"
time="2025-07-25T13:12:13.874830400+08:00" level=info msg="[TCP] 127.0.0.1:60134 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:12:14.246336700+08:00" level=info msg="[TCP] 127.0.0.1:60139 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:12:14.248116200+08:00" level=info msg="[TCP] 127.0.0.1:60140 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:12:14.275818100+08:00" level=info msg="[TCP] 127.0.0.1:60145 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:12:14.442843100+08:00" level=info msg="[TCP] 127.0.0.1:60148 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:12:14.678065400+08:00" level=info msg="[TCP] 127.0.0.1:60151 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:12:16.485592500+08:00" level=info msg="[TCP] 127.0.0.1:60156 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:12:16.680379900+08:00" level=info msg="[TCP] 127.0.0.1:60162 --> telegram.me:443 using GLOBAL"
time="2025-07-25T13:12:16.680379900+08:00" level=info msg="[TCP] 127.0.0.1:60159 --> t.me:443 using GLOBAL"
time="2025-07-25T13:12:16.996063400+08:00" level=info msg="[TCP] 127.0.0.1:60166 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:12:17.074695800+08:00" level=info msg="[TCP] 127.0.0.1:60170 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:12:18.087808200+08:00" level=info msg="[TCP] 127.0.0.1:60175 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:12:18.266115700+08:00" level=info msg="[TCP] 127.0.0.1:60178 --> pic.rmb.bdstatic.com:443 using GLOBAL"
time="2025-07-25T13:12:18.403200800+08:00" level=info msg="[TCP] 127.0.0.1:60181 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:12:20.020110800+08:00" level=info msg="[TCP] 127.0.0.1:60185 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:12:20.024593800+08:00" level=info msg="[TCP] 127.0.0.1:60188 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:12.157866700+08:00" level=info msg="[TCP] 127.0.0.1:60385 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:12.917078000+08:00" level=info msg="[TCP] 127.0.0.1:60390 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:13.418404200+08:00" level=info msg="[TCP] 127.0.0.1:60393 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:14.538134500+08:00" level=info msg="[TCP] 127.0.0.1:60397 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:15.132118300+08:00" level=info msg="[TCP] 127.0.0.1:60400 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:14:16.372889400+08:00" level=info msg="[TCP] 127.0.0.1:60406 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:16.427970900+08:00" level=info msg="[TCP] 127.0.0.1:60409 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:19.643830000+08:00" level=info msg="[TCP] 127.0.0.1:60415 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:20.803203200+08:00" level=info msg="[TCP] 127.0.0.1:60418 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:20.804411400+08:00" level=info msg="[TCP] 127.0.0.1:60421 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:26.986954400+08:00" level=info msg="[TCP] 127.0.0.1:60433 --> obseu.youseasky.com:443 using GLOBAL"
time="2025-07-25T13:14:27.525031400+08:00" level=info msg="[TCP] 127.0.0.1:60437 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T13:14:35.156244800+08:00" level=info msg="[TCP] 127.0.0.1:60449 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:14:35.186964700+08:00" level=info msg="[TCP] 127.0.0.1:60452 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:14:35.508337900+08:00" level=info msg="[TCP] 127.0.0.1:60455 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:35.705362200+08:00" level=info msg="[TCP] 127.0.0.1:60458 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:35.707139200+08:00" level=info msg="[TCP] 127.0.0.1:60459 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:36.130415500+08:00" level=info msg="[TCP] 127.0.0.1:60464 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:14:42.426757900+08:00" level=info msg="[TCP] 127.0.0.1:60476 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:42.523942000+08:00" level=info msg="[TCP] 127.0.0.1:60479 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:42.607723400+08:00" level=info msg="[TCP] 127.0.0.1:60482 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:42.684716100+08:00" level=info msg="[TCP] 127.0.0.1:60485 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:42.815905500+08:00" level=info msg="[TCP] 127.0.0.1:60489 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:43.014511400+08:00" level=info msg="[TCP] 127.0.0.1:60492 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:43.024593700+08:00" level=info msg="[TCP] 127.0.0.1:60495 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:43.206989800+08:00" level=info msg="[TCP] 127.0.0.1:60499 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:46.683395200+08:00" level=info msg="[TCP] 127.0.0.1:60506 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:47.025365700+08:00" level=info msg="[TCP] 127.0.0.1:60509 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:47.205033700+08:00" level=info msg="[TCP] 127.0.0.1:60512 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:47.296827300+08:00" level=info msg="[TCP] 127.0.0.1:60515 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:14:47.389664000+08:00" level=info msg="[TCP] 127.0.0.1:60518 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:47.506304900+08:00" level=info msg="[TCP] 127.0.0.1:60521 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:14:47.566927100+08:00" level=info msg="[TCP] 127.0.0.1:60524 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:52.600095500+08:00" level=info msg="[TCP] 127.0.0.1:60533 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:14:54.940565700+08:00" level=info msg="[TCP] 127.0.0.1:60538 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:55.249752200+08:00" level=info msg="[TCP] 127.0.0.1:60542 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:55.251477000+08:00" level=info msg="[TCP] 127.0.0.1:60543 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:58.024726400+08:00" level=info msg="[TCP] 127.0.0.1:60551 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:58.037614100+08:00" level=info msg="[TCP] 127.0.0.1:60554 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:58.283883600+08:00" level=info msg="[TCP] 127.0.0.1:60558 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:14:58.746489400+08:00" level=info msg="[TCP] 127.0.0.1:60561 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:15:02.628141200+08:00" level=info msg="[TCP] 127.0.0.1:60568 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:15:02.632890500+08:00" level=info msg="[TCP] 127.0.0.1:60571 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:15:03.144258200+08:00" level=info msg="[TCP] 127.0.0.1:60574 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:15:06.413529600+08:00" level=info msg="[TCP] 127.0.0.1:60582 --> twitter.com:443 using GLOBAL"
time="2025-07-25T13:15:06.484516000+08:00" level=info msg="[TCP] 127.0.0.1:60585 --> twitter.com:443 using GLOBAL"
time="2025-07-25T13:15:07.077752400+08:00" level=info msg="[TCP] 127.0.0.1:60590 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:15:07.100733600+08:00" level=info msg="[TCP] 127.0.0.1:60593 --> x.com:443 using GLOBAL"
time="2025-07-25T13:15:07.679027700+08:00" level=info msg="[TCP] 127.0.0.1:60606 --> t.co:443 using GLOBAL"
time="2025-07-25T13:15:07.679027700+08:00" level=info msg="[TCP] 127.0.0.1:60612 --> api.x.com:443 using GLOBAL"
time="2025-07-25T13:15:07.680152000+08:00" level=info msg="[TCP] 127.0.0.1:60609 --> pbs.twimg.com:443 using GLOBAL"
time="2025-07-25T13:15:07.680152000+08:00" level=info msg="[TCP] 127.0.0.1:60603 --> video.twimg.com:443 using GLOBAL"
time="2025-07-25T13:15:07.680152000+08:00" level=info msg="[TCP] 127.0.0.1:60598 --> api.twitter.com:443 using GLOBAL"
time="2025-07-25T13:15:07.681882400+08:00" level=info msg="[TCP] 127.0.0.1:60597 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T13:15:07.701656500+08:00" level=info msg="[TCP] 127.0.0.1:60618 --> abs-0.twimg.com:443 using GLOBAL"
time="2025-07-25T13:15:07.704588600+08:00" level=info msg="[TCP] 127.0.0.1:60615 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T13:15:08.485709000+08:00" level=info msg="[TCP] 127.0.0.1:60621 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:15:09.270578900+08:00" level=info msg="[TCP] 127.0.0.1:60625 --> api.x.com:443 using GLOBAL"
time="2025-07-25T13:15:15.007982000+08:00" level=info msg="[TCP] 127.0.0.1:60635 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:15:15.446365900+08:00" level=info msg="[TCP] 127.0.0.1:60638 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:15:20.492528400+08:00" level=info msg="[TCP] 127.0.0.1:60648 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:15:21.836291300+08:00" level=info msg="[TCP] 127.0.0.1:60651 --> twitter.com:443 using GLOBAL"
time="2025-07-25T13:15:23.108503700+08:00" level=info msg="[TCP] 127.0.0.1:60657 --> api.twitter.com:443 using GLOBAL"
time="2025-07-25T13:15:23.110491100+08:00" level=info msg="[TCP] 127.0.0.1:60660 --> video.twimg.com:443 using GLOBAL"
time="2025-07-25T13:15:28.865971900+08:00" level=info msg="[TCP] 127.0.0.1:60669 --> ntp.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:28.943103000+08:00" level=info msg="[TCP] 127.0.0.1:60681 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:28.945116900+08:00" level=info msg="[TCP] 127.0.0.1:60672 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:28.945116900+08:00" level=info msg="[TCP] 127.0.0.1:60676 --> c.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:28.946653100+08:00" level=info msg="[TCP] 127.0.0.1:60673 --> c.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:28.950218600+08:00" level=info msg="[TCP] 127.0.0.1:60684 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:28.950218600+08:00" level=info msg="[TCP] 127.0.0.1:60690 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:28.950218600+08:00" level=info msg="[TCP] 127.0.0.1:60687 --> api.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:28.950834500+08:00" level=info msg="[TCP] 127.0.0.1:60696 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-07-25T13:15:28.952599000+08:00" level=info msg="[TCP] 127.0.0.1:60693 --> th.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:28.952599000+08:00" level=info msg="[TCP] 127.0.0.1:60697 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-25T13:15:29.039165600+08:00" level=info msg="[TCP] 127.0.0.1:60702 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:29.171025500+08:00" level=info msg="[TCP] 127.0.0.1:60705 --> r.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:29.171563600+08:00" level=info msg="[TCP] 127.0.0.1:60711 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-25T13:15:29.175232000+08:00" level=info msg="[TCP] 127.0.0.1:60714 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-25T13:15:29.175232000+08:00" level=info msg="[TCP] 127.0.0.1:60708 --> r.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:29.707916600+08:00" level=info msg="[TCP] 127.0.0.1:60719 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:29.712956300+08:00" level=info msg="[TCP] 127.0.0.1:60720 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:29.712956300+08:00" level=info msg="[TCP] 127.0.0.1:60718 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:29.859373400+08:00" level=info msg="[TCP] 127.0.0.1:60727 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:33.770045300+08:00" level=info msg="[TCP] 127.0.0.1:60733 --> ntp.msn.com:443 using GLOBAL"
time="2025-07-25T13:15:34.304281100+08:00" level=info msg="[TCP] 127.0.0.1:60741 --> r.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:34.304281100+08:00" level=info msg="[TCP] 127.0.0.1:60745 --> th.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:34.304783700+08:00" level=info msg="[TCP] 127.0.0.1:60738 --> r.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:34.304783700+08:00" level=info msg="[TCP] 127.0.0.1:60744 --> th.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:36.007713300+08:00" level=info msg="[TCP] 127.0.0.1:60751 --> storage.live.com:443 using GLOBAL"
time="2025-07-25T13:15:36.090248100+08:00" level=info msg="[TCP] 127.0.0.1:60754 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:15:36.093721200+08:00" level=info msg="[TCP] 127.0.0.1:60755 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:15:36.960382700+08:00" level=info msg="[TCP] 127.0.0.1:60761 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:37.008495300+08:00" level=info msg="[TCP] 127.0.0.1:60764 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:37.039234100+08:00" level=info msg="[TCP] 127.0.0.1:60768 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:37.042956900+08:00" level=info msg="[TCP] 127.0.0.1:60767 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:37.153077500+08:00" level=info msg="[TCP] 127.0.0.1:60773 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:37.302718700+08:00" level=info msg="[TCP] 127.0.0.1:60776 --> login.live.com:443 using GLOBAL"
time="2025-07-25T13:15:38.032915400+08:00" level=info msg="[TCP] 127.0.0.1:60781 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-25T13:15:38.344296100+08:00" level=info msg="[TCP] 127.0.0.1:60785 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:15:38.781988100+08:00" level=info msg="[TCP] 127.0.0.1:60788 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:38.786915600+08:00" level=info msg="[TCP] 127.0.0.1:60790 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:38.788521900+08:00" level=info msg="[TCP] 127.0.0.1:60789 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:39.365771700+08:00" level=info msg="[TCP] 127.0.0.1:60797 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:15:39.569671700+08:00" level=info msg="[TCP] 127.0.0.1:60800 --> cdn.onesignal.com:443 using GLOBAL"
time="2025-07-25T13:15:40.442457500+08:00" level=info msg="[TCP] 127.0.0.1:60804 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:15:40.741274500+08:00" level=info msg="[TCP] 127.0.0.1:60808 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-25T13:15:40.747444000+08:00" level=info msg="[TCP] 127.0.0.1:60811 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-25T13:15:40.756050800+08:00" level=info msg="[TCP] 127.0.0.1:60814 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T13:15:41.509529000+08:00" level=info msg="[TCP] 127.0.0.1:60818 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:15:43.010538600+08:00" level=info msg="[TCP] 127.0.0.1:60821 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:15:43.545125900+08:00" level=info msg="[TCP] 127.0.0.1:60826 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:15:43.584826900+08:00" level=info msg="[TCP] 127.0.0.1:60829 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:15:43.714596900+08:00" level=info msg="[TCP] 127.0.0.1:60833 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:15:44.008108500+08:00" level=info msg="[TCP] 127.0.0.1:60836 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:44.482154100+08:00" level=info msg="[TCP] 127.0.0.1:60839 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:44.493511700+08:00" level=info msg="[TCP] 127.0.0.1:60843 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:44.494739200+08:00" level=info msg="[TCP] 127.0.0.1:60842 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:44.497833200+08:00" level=info msg="[TCP] 127.0.0.1:60848 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:15:45.528300400+08:00" level=info msg="[TCP] 127.0.0.1:60852 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:15:47.915596500+08:00" level=info msg="[TCP] 127.0.0.1:60858 --> cdn.onesignal.com:443 using GLOBAL"
time="2025-07-25T13:15:48.549316200+08:00" level=info msg="[TCP] 127.0.0.1:60861 --> onesignal.com:443 using GLOBAL"
time="2025-07-25T13:15:49.690500700+08:00" level=info msg="[TCP] 127.0.0.1:60866 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-25T13:15:50.151521100+08:00" level=info msg="[TCP] 127.0.0.1:60870 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:15:50.981482100+08:00" level=info msg="[TCP] 127.0.0.1:60874 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:15:51.288822900+08:00" level=info msg="[TCP] 127.0.0.1:60878 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:15:51.291440000+08:00" level=info msg="[TCP] 127.0.0.1:60877 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:16:01.013059700+08:00" level=info msg="[TCP] 127.0.0.1:60895 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:16:03.192561200+08:00" level=info msg="[TCP] 127.0.0.1:60901 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:16:03.933772600+08:00" level=info msg="[TCP] 127.0.0.1:60906 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:16:03.936747900+08:00" level=info msg="[TCP] 127.0.0.1:60907 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:16:03.937251500+08:00" level=info msg="[TCP] 127.0.0.1:60905 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:16:03.937251500+08:00" level=info msg="[TCP] 127.0.0.1:60909 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:16:03.937251500+08:00" level=info msg="[TCP] 127.0.0.1:60908 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:16:05.828471800+08:00" level=info msg="[TCP] 127.0.0.1:60923 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:16:10.763154700+08:00" level=info msg="[TCP] 127.0.0.1:60931 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T13:16:10.862766500+08:00" level=info msg="[TCP] 127.0.0.1:60934 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:16:13.217307800+08:00" level=info msg="[TCP] 127.0.0.1:60941 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:16:13.521597100+08:00" level=info msg="[TCP] 127.0.0.1:60947 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:16:13.525222800+08:00" level=info msg="[TCP] 127.0.0.1:60944 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:16:14.976476100+08:00" level=info msg="[TCP] 127.0.0.1:60953 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:16:19.811209100+08:00" level=info msg="[TCP] 127.0.0.1:60959 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:16:20.145605800+08:00" level=info msg="[TCP] 127.0.0.1:60964 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:16:20.404260400+08:00" level=info msg="[TCP] 127.0.0.1:60968 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:16:22.704220000+08:00" level=info msg="[TCP] 127.0.0.1:60972 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:16:29.914322900+08:00" level=info msg="[TCP] 127.0.0.1:60985 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:16:30.060402400+08:00" level=info msg="[TCP] 127.0.0.1:60988 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-25T13:16:30.656895500+08:00" level=info msg="[TCP] 127.0.0.1:60991 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:16:30.662355600+08:00" level=info msg="[TCP] 127.0.0.1:60994 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:16:34.280758100+08:00" level=info msg="[TCP] 127.0.0.1:61003 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:16:35.811261400+08:00" level=info msg="[TCP] 127.0.0.1:61008 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:16:40.865160200+08:00" level=info msg="[TCP] 127.0.0.1:61016 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:16:43.421139600+08:00" level=info msg="[TCP] 127.0.0.1:61022 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:16:44.414012800+08:00" level=info msg="[TCP] 127.0.0.1:61026 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:17:01.180445200+08:00" level=info msg="[TCP] 127.0.0.1:61050 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:17:05.793299000+08:00" level=info msg="[TCP] 127.0.0.1:61059 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:17:24.179817200+08:00" level=info msg="[TCP] 127.0.0.1:61311 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:17:25.194080500+08:00" level=info msg="[TCP] 127.0.0.1:61314 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:17:25.195306300+08:00" level=info msg="[TCP] 127.0.0.1:61318 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:17:25.197708000+08:00" level=info msg="[TCP] 127.0.0.1:61315 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:17:25.688685800+08:00" level=info msg="[TCP] 127.0.0.1:61323 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:17:25.986088600+08:00" level=info msg="[TCP] 127.0.0.1:61327 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:17:25.991244100+08:00" level=info msg="[TCP] 127.0.0.1:61330 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:17:26.027167400+08:00" level=info msg="[TCP] 127.0.0.1:61333 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:17:26.034929800+08:00" level=info msg="[TCP] 127.0.0.1:61336 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:17:27.002708500+08:00" level=info msg="[TCP] 127.0.0.1:61340 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:17:27.004215200+08:00" level=error msg="🇸🇬新加坡04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:17:27.004215200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:17:27.004215200+08:00" level=error msg="🇸🇬新加坡02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:17:27.004215200+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:17:27.014276100+08:00" level=error msg="🇯🇵日本01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:17:27.014778900+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:17:27.014778900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:17:27.014778900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:17:27.161614300+08:00" level=info msg="[TCP] 127.0.0.1:61344 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:17:27.314969300+08:00" level=info msg="[TCP] 127.0.0.1:61347 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:17:27.595988400+08:00" level=info msg="[TCP] 127.0.0.1:61350 --> sketch.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:17:27.608185400+08:00" level=info msg="[TCP] 127.0.0.1:61353 --> api.booth.pm:443 using GLOBAL"
time="2025-07-25T13:17:27.741716800+08:00" level=info msg="[TCP] 127.0.0.1:61356 --> sketch.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:17:27.998442100+08:00" level=info msg="[TCP] 127.0.0.1:61360 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:17:28.033905400+08:00" level=info msg="[TCP] 127.0.0.1:61363 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:17:28.143634300+08:00" level=info msg="[TCP] 127.0.0.1:61366 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:17:29.025205100+08:00" level=info msg="[TCP] 127.0.0.1:61369 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:17:30.084953800+08:00" level=info msg="[TCP] 127.0.0.1:61374 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:17:30.192284800+08:00" level=info msg="[TCP] 127.0.0.1:61377 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:17:30.276222500+08:00" level=info msg="[TCP] 127.0.0.1:61380 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:17:30.695974000+08:00" level=info msg="[TCP] 127.0.0.1:61383 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:17:30.940341200+08:00" level=info msg="[TCP] 127.0.0.1:61387 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:17:30.943933500+08:00" level=info msg="[TCP] 127.0.0.1:61390 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:17:31.236348700+08:00" level=info msg="[TCP] 127.0.0.1:61393 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:17:31.620456500+08:00" level=info msg="[TCP] 127.0.0.1:61396 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:17:31.702980400+08:00" level=info msg="[TCP] 127.0.0.1:61399 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:17:32.243968400+08:00" level=info msg="[TCP] 127.0.0.1:61402 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:17:32.985446600+08:00" level=info msg="[TCP] 127.0.0.1:61407 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:17:35.064595000+08:00" level=info msg="[TCP] 127.0.0.1:61412 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:19:20.318025700+08:00" level=info msg="[TCP] 127.0.0.1:61597 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:19:20.620098900+08:00" level=info msg="[TCP] 127.0.0.1:61600 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:19:20.625620300+08:00" level=info msg="[TCP] 127.0.0.1:61601 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:19:26.332143000+08:00" level=info msg="[TCP] 127.0.0.1:61615 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:19:26.919612200+08:00" level=info msg="[TCP] 127.0.0.1:61619 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:19:28.444089200+08:00" level=info msg="[TCP] 127.0.0.1:61623 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T13:19:32.873516900+08:00" level=info msg="[TCP] 127.0.0.1:61631 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:19:35.553123900+08:00" level=info msg="[TCP] 127.0.0.1:61637 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:19:35.581844500+08:00" level=info msg="[TCP] 127.0.0.1:61640 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:19:35.949048300+08:00" level=info msg="[TCP] 127.0.0.1:61644 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:19:35.949551200+08:00" level=info msg="[TCP] 127.0.0.1:61645 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:19:38.038303800+08:00" level=info msg="[TCP] 127.0.0.1:61652 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:19:41.993688100+08:00" level=info msg="[TCP] 127.0.0.1:61659 --> www.bingapis.com:443 using GLOBAL"
time="2025-07-25T13:19:55.454971200+08:00" level=info msg="[TCP] 127.0.0.1:61680 --> wx.qlogo.cn:443 using GLOBAL"
time="2025-07-25T13:19:55.613710000+08:00" level=info msg="[TCP] 127.0.0.1:61683 --> wx.qlogo.cn:443 using GLOBAL"
time="2025-07-25T13:19:55.979638900+08:00" level=info msg="[TCP] 127.0.0.1:61687 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-07-25T13:20:02.998329000+08:00" level=info msg="[TCP] 127.0.0.1:61698 --> mp.weixin.qq.com:443 using GLOBAL"
time="2025-07-25T13:20:03.567625000+08:00" level=info msg="[TCP] 127.0.0.1:61703 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:20:04.429084800+08:00" level=info msg="[TCP] 127.0.0.1:61707 --> badjs.weixinbridge.com:443 using GLOBAL"
time="2025-07-25T13:20:06.262415700+08:00" level=info msg="[TCP] 127.0.0.1:61712 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:20:08.547162500+08:00" level=info msg="[TCP] 127.0.0.1:61719 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:20:10.025398800+08:00" level=info msg="[TCP] 127.0.0.1:61722 --> x.com:443 using GLOBAL"
time="2025-07-25T13:20:14.440522200+08:00" level=info msg="[TCP] 127.0.0.1:61731 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:20:20.389333400+08:00" level=info msg="[TCP] 127.0.0.1:61740 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:20:34.976262000+08:00" level=info msg="[TCP] 127.0.0.1:61760 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:21:08.617602700+08:00" level=info msg="[TCP] 127.0.0.1:61800 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:21:31.958268700+08:00" level=info msg="[TCP] 127.0.0.1:61830 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:21:31.958268700+08:00" level=info msg="[TCP] 127.0.0.1:61831 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:21:31.963241600+08:00" level=info msg="[TCP] 127.0.0.1:61832 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:21:31.963744400+08:00" level=info msg="[TCP] 127.0.0.1:61833 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:21:33.509456900+08:00" level=info msg="[TCP] 127.0.0.1:61841 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:21:43.710921500+08:00" level=info msg="[TCP] 127.0.0.1:61856 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:21:52.981027700+08:00" level=info msg="[TCP] 127.0.0.1:61868 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:21:52.993964800+08:00" level=info msg="[TCP] 127.0.0.1:61870 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:21:58.496519700+08:00" level=info msg="[TCP] 127.0.0.1:61880 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:22:07.611670100+08:00" level=info msg="[TCP] 127.0.0.1:61892 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:22:19.538255800+08:00" level=info msg="[TCP] 127.0.0.1:61911 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:19.540838700+08:00" level=info msg="[TCP] 127.0.0.1:61913 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:19.540838700+08:00" level=info msg="[TCP] 127.0.0.1:61912 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:22.416454900+08:00" level=info msg="[TCP] 127.0.0.1:61920 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:23.542319900+08:00" level=info msg="[TCP] 127.0.0.1:61923 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:27.090690100+08:00" level=info msg="[TCP] 127.0.0.1:61928 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:22:28.695613000+08:00" level=info msg="[TCP] 127.0.0.1:62130 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:30.150079800+08:00" level=error msg="🇷🇺俄罗斯01 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": read tcp ***********:62106->**************:19892: use of closed network connection"
time="2025-07-25T13:22:30.150079800+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:22:30.186470900+08:00" level=error msg="🇯🇵日本03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": read tcp ***********:62056->**************:19831: use of closed network connection"
time="2025-07-25T13:22:30.186470900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:22:30.796155600+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": read tcp ***********:62043->**************:19832: use of closed network connection"
time="2025-07-25T13:22:30.796155600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:22:32.488579000+08:00" level=info msg="[TCP] 127.0.0.1:62154 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:22:32.552136100+08:00" level=info msg="[TCP] 127.0.0.1:62156 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:22:32.588882400+08:00" level=info msg="[TCP] 127.0.0.1:62160 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:32.593187300+08:00" level=info msg="[TCP] 127.0.0.1:62163 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:32.593187300+08:00" level=info msg="[TCP] 127.0.0.1:62169 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:22:32.595688600+08:00" level=info msg="[TCP] 127.0.0.1:62164 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:32.735727400+08:00" level=info msg="[TCP] 127.0.0.1:62173 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:22:33.593027300+08:00" level=info msg="[TCP] 127.0.0.1:62180 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:33.597005700+08:00" level=info msg="[TCP] 127.0.0.1:62181 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:33.657957200+08:00" level=info msg="[TCP] 127.0.0.1:62186 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:22:34.558555700+08:00" level=info msg="[TCP] 127.0.0.1:62192 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:35.605173300+08:00" level=info msg="[TCP] 127.0.0.1:62195 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:22:37.109576900+08:00" level=info msg="[TCP] 127.0.0.1:62201 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:22:37.807371800+08:00" level=info msg="[TCP] 127.0.0.1:62204 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:22:38.353415900+08:00" level=error msg="🇰🇷韩国04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:22:38.353415900+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:22:38.364585600+08:00" level=error msg="🇯🇵日本04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:22:38.364585600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:22:38.364585600+08:00" level=error msg="🇰🇷韩国03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:22:38.365094500+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:22:38.365280100+08:00" level=error msg="🇰🇷韩国04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:22:38.365280100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:22:38.365280100+08:00" level=error msg="🇰🇷韩国01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:22:38.365280100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:22:38.365280100+08:00" level=error msg="🇹🇷土耳其01 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:22:38.365280100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:22:53.040968100+08:00" level=info msg="[TCP] 127.0.0.1:62226 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:23:39.523419100+08:00" level=info msg="[TCP] 127.0.0.1:62283 --> www.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:23:41.206766200+08:00" level=info msg="[TCP] 127.0.0.1:62289 --> clients4.google.com:443 using GLOBAL"
time="2025-07-25T13:23:49.951489100+08:00" level=info msg="[TCP] 127.0.0.1:62301 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:23:53.083910600+08:00" level=info msg="[TCP] 127.0.0.1:62310 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:23:54.690038200+08:00" level=info msg="[TCP] 127.0.0.1:62314 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:23:55.344446900+08:00" level=info msg="[TCP] 127.0.0.1:62317 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:23:55.723753300+08:00" level=info msg="[TCP] 127.0.0.1:62320 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:23:55.975666100+08:00" level=info msg="[TCP] 127.0.0.1:62324 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:23:58.950841300+08:00" level=info msg="[TCP] 127.0.0.1:62331 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:24:03.034533700+08:00" level=info msg="[TCP] 127.0.0.1:62338 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:24:27.984247500+08:00" level=info msg="[TCP] 127.0.0.1:62371 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:24:28.288103200+08:00" level=info msg="[TCP] 127.0.0.1:62375 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:24:29.206740300+08:00" level=info msg="[TCP] 127.0.0.1:62379 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T13:24:49.675288900+08:00" level=info msg="[TCP] 127.0.0.1:62406 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:24:53.151060900+08:00" level=info msg="[TCP] 127.0.0.1:62412 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:25:11.258524700+08:00" level=info msg="[TCP] 127.0.0.1:62437 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:25:15.065333600+08:00" level=info msg="[TCP] 127.0.0.1:62446 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:25:21.693347300+08:00" level=info msg="[TCP] 127.0.0.1:62459 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:25:22.696556200+08:00" level=info msg="[TCP] 127.0.0.1:62462 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:25:22.937236000+08:00" level=info msg="[TCP] 127.0.0.1:62466 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:25:23.159772100+08:00" level=info msg="[TCP] 127.0.0.1:62469 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:25:53.206138000+08:00" level=info msg="[TCP] 127.0.0.1:62507 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:26:27.211240200+08:00" level=info msg="[TCP] 127.0.0.1:62547 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:26:31.899326000+08:00" level=info msg="[TCP] 127.0.0.1:62556 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:36.082119900+08:00" level=info msg="[TCP] 127.0.0.1:62563 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:26:36.424077300+08:00" level=info msg="[TCP] 127.0.0.1:62751 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:38.173154200+08:00" level=info msg="[TCP] 127.0.0.1:62786 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:39.322601300+08:00" level=info msg="[TCP] 127.0.0.1:62793 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:39.322601300+08:00" level=info msg="[TCP] 127.0.0.1:62790 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:40.315583500+08:00" level=info msg="[TCP] 127.0.0.1:62798 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:40.323679300+08:00" level=info msg="[TCP] 127.0.0.1:62801 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:40.326020900+08:00" level=info msg="[TCP] 127.0.0.1:62802 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:40.329606600+08:00" level=info msg="[TCP] 127.0.0.1:62807 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:40.329606600+08:00" level=info msg="[TCP] 127.0.0.1:62810 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:40.345675800+08:00" level=info msg="[TCP] 127.0.0.1:62813 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:40.630270200+08:00" level=info msg="[TCP] 127.0.0.1:62816 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:40.648206400+08:00" level=info msg="[TCP] 127.0.0.1:62819 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:41.817328500+08:00" level=info msg="[TCP] 127.0.0.1:62827 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:26:44.342462500+08:00" level=info msg="[TCP] 127.0.0.1:62835 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:46.235919600+08:00" level=error msg="🇰🇷韩国03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:26:46.235919600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:26:46.246323400+08:00" level=error msg="🇰🇷韩国04 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:26:46.246323400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:26:46.246323400+08:00" level=error msg="🇯🇵日本01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:26:46.246323400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:26:46.246323400+08:00" level=error msg="🇰🇷韩国01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:26:46.246323400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:26:46.246323400+08:00" level=error msg="🇰🇷韩国03 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-25T13:26:46.246832300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-25T13:26:49.185696500+08:00" level=info msg="[TCP] 127.0.0.1:62843 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:51.083606500+08:00" level=info msg="[TCP] 127.0.0.1:62848 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:26:53.263148300+08:00" level=info msg="[TCP] 127.0.0.1:62854 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:27:02.461805800+08:00" level=info msg="[TCP] 127.0.0.1:62867 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:27:06.028033000+08:00" level=info msg="[TCP] 127.0.0.1:62875 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:27:07.924435100+08:00" level=info msg="[TCP] 127.0.0.1:62881 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:27:09.990559500+08:00" level=info msg="[TCP] 127.0.0.1:62885 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:27:12.257719300+08:00" level=info msg="[TCP] 127.0.0.1:62891 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:27:15.254235100+08:00" level=info msg="[TCP] 127.0.0.1:62900 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:27:16.084304900+08:00" level=info msg="[TCP] 127.0.0.1:62904 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:27:16.443362400+08:00" level=info msg="[TCP] 127.0.0.1:62908 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:27:19.001412000+08:00" level=info msg="[TCP] 127.0.0.1:62914 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:27:23.304331200+08:00" level=info msg="[TCP] 127.0.0.1:62923 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:27:25.658594000+08:00" level=info msg="[TCP] 127.0.0.1:62928 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:29:49.795271400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63353 --> www.google-analytics.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:50.077833400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63356 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:50.078336400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63357 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:50.123836600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63362 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:50.170751800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63366 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:50.185769800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63369 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:50.311317100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63373 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:50.390985500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63376 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:50.421414600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63379 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:50.481259600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63383 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:50.498936700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63386 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:50.698281900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63389 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:50.731407400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63392 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:50.856758400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63395 --> zws5.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:50.902063900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63398 --> functional.events.data.microsoft.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:52.372701900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63402 --> www.googleapis.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:53.687601100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63407 --> self.events.data.microsoft.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:54.796594300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63410 --> www.google-analytics.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:55.078381800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63413 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:55.079392800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63416 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:55.124799900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63419 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:55.171744700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63422 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:55.186805900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63425 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:55.311874700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63428 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:55.391741000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63432 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:55.422278300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63435 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:55.482047400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63438 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:55.499739000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63441 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:55.699252100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63444 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:55.731958600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63447 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:55.857484800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63450 --> zws5.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:55.903068500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63453 --> functional.events.data.microsoft.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:57.373647000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63458 --> www.googleapis.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:58.382309000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63462 --> www.bing.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:58.688147000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63465 --> self.events.data.microsoft.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:29:59.135122400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63468 --> skydrive.wns.windows.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:59.700344600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63475 --> api.x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:29:59.702385400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63478 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:00.011531400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63481 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:00.012068300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63482 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:00.012068300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63483 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:00.326721300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63490 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:00.362785000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63493 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:01.089236100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63498 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:01.089767800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63499 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:01.117912000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63504 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:01.134491200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63507 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:01.148002500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63510 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:01.181130800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63513 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:01.194683200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63516 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:01.300451100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63519 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:01.317060200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63522 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:01.410160300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63525 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:01.439802200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63528 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:01.483843700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63532 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:01.515939600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63535 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:01.672263900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63538 --> functional.events.data.microsoft.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:01.716927500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63541 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:01.749004100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63544 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:01.871802900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63547 --> zws5.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:03.382874400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63553 --> www.bing.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:03.394218300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63556 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:04.701144800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63560 --> api.x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:04.702650100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63563 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:05.012228200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63566 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:05.013259900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63570 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:05.013259900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63569 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:05.327249100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63575 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:05.363218900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63578 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:05.975467900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63583 --> functional.events.data.microsoft.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:06.090225700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63586 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:06.090771700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63589 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:06.118751100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63592 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:06.135479100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63595 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:06.148018900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63598 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:06.181780600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63601 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:06.195316700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63604 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:06.301246400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63607 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:06.317588200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63610 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:06.410996800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63613 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:06.440591600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63616 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:06.484294500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63619 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:06.516198200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63622 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:06.673016500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63626 --> functional.events.data.microsoft.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:06.716977800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63629 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:06.749145600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63632 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:06.872739700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63635 --> zws5.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:08.394760200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63638 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:08.692802100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63643 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:09.471961600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63647 --> self.events.data.microsoft.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:10.364241900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63650 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:10.976015200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63654 --> functional.events.data.microsoft.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:11.434086500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63657 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:11.905678600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63662 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:11.923259400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63665 --> api.x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:12.094747100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63668 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:12.094747100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63669 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:12.141233900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63674 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:12.141233900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63675 --> zws1-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:12.187233800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63680 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:12.201657600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63683 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:12.417607300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63686 --> zws5-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:12.448225500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63689 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:12.471290100+08:00" level=info msg="[TCP] 127.0.0.1:63756 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:12.494225500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63692 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:12.494225500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63693 --> zws4-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:12.505448800+08:00" level=info msg="[TCP] 127.0.0.1:63759 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:12.505448800+08:00" level=info msg="[TCP] 127.0.0.1:63726 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:30:12.526740900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63698 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:12.550110200+08:00" level=info msg="[TCP] 127.0.0.1:63762 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:12.552468000+08:00" level=info msg="[TCP] 127.0.0.1:63763 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:12.553568200+08:00" level=info msg="[TCP] 127.0.0.1:63714 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:30:12.584478600+08:00" level=info msg="[TCP] 127.0.0.1:63769 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:12.755699400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63701 --> zws2-1.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:12.799684700+08:00" level=info msg="[TCP] 127.0.0.1:63718 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:30:12.812109100+08:00" level=info msg="[TCP] 127.0.0.1:63773 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:12.877381800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63704 --> zws5.web.telegram.org:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:12.922294200+08:00" level=info msg="[TCP] 127.0.0.1:63729 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:30:12.938864500+08:00" level=info msg="[TCP] 127.0.0.1:63777 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:12.966263200+08:00" level=info msg="[TCP] 127.0.0.1:63732 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:30:12.983147800+08:00" level=info msg="[TCP] 127.0.0.1:63735 --> api.x.com:443 using GLOBAL"
time="2025-07-25T13:30:13.149283800+08:00" level=info msg="[TCP] 127.0.0.1:63739 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:13.154254300+08:00" level=info msg="[TCP] 127.0.0.1:63738 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:13.202939200+08:00" level=info msg="[TCP] 127.0.0.1:63745 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:13.208801200+08:00" level=info msg="[TCP] 127.0.0.1:63744 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:13.257804000+08:00" level=info msg="[TCP] 127.0.0.1:63753 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:13.259651900+08:00" level=info msg="[TCP] 127.0.0.1:63750 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:13.428693600+08:00" level=info msg="[TCP] 127.0.0.1:63722 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:30:13.474573600+08:00" level=info msg="[TCP] 127.0.0.1:63780 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:13.567594800+08:00" level=info msg="[TCP] 127.0.0.1:63783 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:30:13.693475600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63708 --> x.com:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp **************:19862: i/o timeout\nconnect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout"
time="2025-07-25T13:30:13.717757200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:63711 --> www.pixiv.net:443 error: mm.huawei-api.top:19862 connect error: connect failed: dial tcp [2409:8754:5630:210:0:1:6666:3]:19862: i/o timeout\nconnect failed: dial tcp **************:19862: i/o timeout"
time="2025-07-25T13:30:13.730216700+08:00" level=info msg="[TCP] 127.0.0.1:63786 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:13.839314400+08:00" level=info msg="[TCP] 127.0.0.1:63789 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:30:13.849908200+08:00" level=info msg="[TCP] 127.0.0.1:63792 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:30:14.153038300+08:00" level=info msg="[TCP] 127.0.0.1:63795 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:14.190729200+08:00" level=info msg="[TCP] 127.0.0.1:63798 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:14.340352100+08:00" level=info msg="[TCP] 127.0.0.1:63801 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:30:14.341805000+08:00" level=info msg="[TCP] 127.0.0.1:63804 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-25T13:30:14.389188300+08:00" level=info msg="[TCP] 127.0.0.1:63807 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:14.431261200+08:00" level=info msg="[TCP] 127.0.0.1:63810 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:14.437338900+08:00" level=info msg="[TCP] 127.0.0.1:63816 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:14.439630700+08:00" level=info msg="[TCP] 127.0.0.1:63813 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:14.514963200+08:00" level=info msg="[TCP] 127.0.0.1:63819 --> onesignal.com:443 using GLOBAL"
time="2025-07-25T13:30:14.619745600+08:00" level=info msg="[TCP] 127.0.0.1:63822 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:14.627524400+08:00" level=info msg="[TCP] 127.0.0.1:63825 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:14.696548900+08:00" level=info msg="[TCP] 127.0.0.1:63828 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:14.696548900+08:00" level=info msg="[TCP] 127.0.0.1:63831 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:14.697887700+08:00" level=info msg="[TCP] 127.0.0.1:63832 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:14.697887700+08:00" level=info msg="[TCP] 127.0.0.1:63829 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:14.702068900+08:00" level=info msg="[TCP] 127.0.0.1:63830 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:15.444656200+08:00" level=info msg="[TCP] 127.0.0.1:63844 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:16.546791400+08:00" level=info msg="[TCP] 127.0.0.1:63849 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:18.097143400+08:00" level=info msg="[TCP] 127.0.0.1:63855 --> clientservices.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:30:19.962694000+08:00" level=info msg="[TCP] 127.0.0.1:63862 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:20.037254900+08:00" level=info msg="[TCP] 127.0.0.1:63865 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:21.460629600+08:00" level=info msg="[TCP] 127.0.0.1:63868 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:30:26.618177200+08:00" level=info msg="[TCP] 127.0.0.1:63877 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:30.979275000+08:00" level=info msg="[TCP] 127.0.0.1:63886 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:30:32.331527700+08:00" level=info msg="[TCP] 127.0.0.1:63890 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:30:37.051781900+08:00" level=info msg="[TCP] 127.0.0.1:63898 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:30:44.205117000+08:00" level=info msg="[TCP] 127.0.0.1:63911 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:44.560771300+08:00" level=info msg="[TCP] 127.0.0.1:63914 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:52.412201400+08:00" level=info msg="[TCP] 127.0.0.1:63927 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:30:52.438504200+08:00" level=info msg="[TCP] 127.0.0.1:63930 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:30:53.284938700+08:00" level=info msg="[TCP] 127.0.0.1:63933 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:30:53.490549800+08:00" level=info msg="[TCP] 127.0.0.1:63936 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:30:54.966164500+08:00" level=info msg="[TCP] 127.0.0.1:63940 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:55.273733900+08:00" level=info msg="[TCP] 127.0.0.1:63945 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:55.274297700+08:00" level=info msg="[TCP] 127.0.0.1:63946 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:55.274297700+08:00" level=info msg="[TCP] 127.0.0.1:63944 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:55.419439400+08:00" level=info msg="[TCP] 127.0.0.1:63955 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:55.419439400+08:00" level=info msg="[TCP] 127.0.0.1:63954 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:30:55.863068300+08:00" level=info msg="[TCP] 127.0.0.1:63960 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:31:22.942118400+08:00" level=info msg="[TCP] 127.0.0.1:63994 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:25.651874400+08:00" level=info msg="[TCP] 127.0.0.1:63999 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:25.960044800+08:00" level=info msg="[TCP] 127.0.0.1:64004 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:25.963338300+08:00" level=info msg="[TCP] 127.0.0.1:64006 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:25.963338300+08:00" level=info msg="[TCP] 127.0.0.1:64005 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:25.964977700+08:00" level=info msg="[TCP] 127.0.0.1:64007 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:25.965480300+08:00" level=info msg="[TCP] 127.0.0.1:64008 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:34.201206200+08:00" level=info msg="[TCP] 127.0.0.1:64027 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:34.517254300+08:00" level=info msg="[TCP] 127.0.0.1:64031 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:34.518650500+08:00" level=info msg="[TCP] 127.0.0.1:64032 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:34.519903200+08:00" level=info msg="[TCP] 127.0.0.1:64033 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:34.522175900+08:00" level=info msg="[TCP] 127.0.0.1:64030 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:34.950490600+08:00" level=info msg="[TCP] 127.0.0.1:64045 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:31:37.821514500+08:00" level=info msg="[TCP] 127.0.0.1:64050 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:31:38.559822600+08:00" level=info msg="[TCP] 127.0.0.1:64054 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:31:39.311188300+08:00" level=info msg="[TCP] 127.0.0.1:64057 --> api.booth.pm:443 using GLOBAL"
time="2025-07-25T13:31:47.974519000+08:00" level=info msg="[TCP] 127.0.0.1:64070 --> update.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:31:53.546577300+08:00" level=info msg="[TCP] 127.0.0.1:64080 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:32:00.468693500+08:00" level=info msg="[TCP] 127.0.0.1:64090 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:32:01.448773000+08:00" level=info msg="[TCP] 127.0.0.1:64093 --> play.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:32:04.598267900+08:00" level=info msg="[TCP] 127.0.0.1:64100 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:32:04.902512400+08:00" level=info msg="[TCP] 127.0.0.1:64104 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:32:04.906019700+08:00" level=info msg="[TCP] 127.0.0.1:64103 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:32:35.851733400+08:00" level=info msg="[TCP] 127.0.0.1:64148 --> safebrowsing.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:32:43.983261500+08:00" level=info msg="[TCP] 127.0.0.1:64159 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:32:53.592558500+08:00" level=info msg="[TCP] 127.0.0.1:64174 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:33:02.440756100+08:00" level=info msg="[TCP] 127.0.0.1:64187 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:33:04.529482200+08:00" level=info msg="[TCP] 127.0.0.1:64193 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:33:04.887051300+08:00" level=info msg="[TCP] 127.0.0.1:64196 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:33:05.465558000+08:00" level=info msg="[TCP] 127.0.0.1:64201 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:33:06.410085900+08:00" level=info msg="[TCP] 127.0.0.1:64208 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:33:22.065851700+08:00" level=info msg="[TCP] 127.0.0.1:64230 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:33:41.224149000+08:00" level=info msg="[TCP] 127.0.0.1:64257 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:33:41.533930900+08:00" level=info msg="[TCP] 127.0.0.1:64260 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:33:41.570470500+08:00" level=info msg="[TCP] 127.0.0.1:64263 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:33:53.658746400+08:00" level=info msg="[TCP] 127.0.0.1:64355 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:33:53.953524200+08:00" level=info msg="[TCP] 127.0.0.1:64358 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:33:54.506787000+08:00" level=info msg="[TCP] 127.0.0.1:64362 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:33:54.515005600+08:00" level=info msg="[TCP] 127.0.0.1:64366 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:33:54.555903000+08:00" level=info msg="[TCP] 127.0.0.1:64369 --> source.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:33:54.555903000+08:00" level=info msg="[TCP] 127.0.0.1:64382 --> source.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:33:54.555903000+08:00" level=info msg="[TCP] 127.0.0.1:64378 --> source.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:33:54.557939500+08:00" level=info msg="[TCP] 127.0.0.1:64387 --> source.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:33:54.557939500+08:00" level=info msg="[TCP] 127.0.0.1:64381 --> source.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:33:54.557939500+08:00" level=info msg="[TCP] 127.0.0.1:64375 --> source.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:33:54.559158200+08:00" level=info msg="[TCP] 127.0.0.1:64372 --> source.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:33:54.561551100+08:00" level=info msg="[TCP] 127.0.0.1:64383 --> source.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:33:54.561551100+08:00" level=info msg="[TCP] 127.0.0.1:64384 --> source.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:33:54.564322800+08:00" level=info msg="[TCP] 127.0.0.1:64396 --> a.pixiv.org:443 using GLOBAL"
time="2025-07-25T13:33:54.584057900+08:00" level=info msg="[TCP] 127.0.0.1:64399 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:33:55.964755300+08:00" level=info msg="[TCP] 127.0.0.1:64403 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:33:58.393729500+08:00" level=info msg="[TCP] 127.0.0.1:64409 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:33:58.554943100+08:00" level=info msg="[TCP] 127.0.0.1:64412 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-25T13:33:58.869447800+08:00" level=info msg="[TCP] 127.0.0.1:64415 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-25T13:34:00.676913500+08:00" level=info msg="[TCP] 127.0.0.1:64420 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:34:00.689965400+08:00" level=info msg="[TCP] 127.0.0.1:64423 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-25T13:34:01.215648000+08:00" level=info msg="[TCP] 127.0.0.1:64428 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:34:03.428012700+08:00" level=info msg="[TCP] 127.0.0.1:64432 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:34:08.653438400+08:00" level=info msg="[TCP] 127.0.0.1:64441 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:34:22.161175000+08:00" level=info msg="[TCP] 127.0.0.1:64478 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:34:22.468651600+08:00" level=info msg="[TCP] 127.0.0.1:64483 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:34:22.471993500+08:00" level=info msg="[TCP] 127.0.0.1:64484 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:34:22.476618200+08:00" level=info msg="[TCP] 127.0.0.1:64482 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:34:31.980417100+08:00" level=info msg="[TCP] 127.0.0.1:64535 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T13:34:47.521987600+08:00" level=info msg="[TCP] 127.0.0.1:64656 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:36:14.515317500+08:00" level=info msg="[TCP] 127.0.0.1:65069 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:36:20.162062200+08:00" level=info msg="[TCP] 127.0.0.1:65085 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:36:29.495478900+08:00" level=info msg="[TCP] 127.0.0.1:65113 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:36:29.823403900+08:00" level=info msg="[TCP] 127.0.0.1:65116 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:36:31.970275000+08:00" level=info msg="[TCP] 127.0.0.1:65124 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:36:32.027401900+08:00" level=info msg="[TCP] 127.0.0.1:65127 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:36:32.876169000+08:00" level=info msg="[TCP] 127.0.0.1:65132 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:36:32.927687600+08:00" level=info msg="[TCP] 127.0.0.1:65135 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:36:32.938010700+08:00" level=info msg="[TCP] 127.0.0.1:65138 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:36:34.125109100+08:00" level=info msg="[TCP] 127.0.0.1:65144 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:36:34.474896300+08:00" level=info msg="[TCP] 127.0.0.1:65148 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:36:34.482782700+08:00" level=info msg="[TCP] 127.0.0.1:65151 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:36:34.770265500+08:00" level=info msg="[TCP] 127.0.0.1:65155 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:36:37.088840000+08:00" level=info msg="[TCP] 127.0.0.1:65163 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:36:37.818584800+08:00" level=info msg="[TCP] 127.0.0.1:65167 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:36:38.108918500+08:00" level=info msg="[TCP] 127.0.0.1:65171 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:36:51.061359700+08:00" level=info msg="[TCP] 127.0.0.1:65211 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:36:53.817363900+08:00" level=info msg="[TCP] 127.0.0.1:65220 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:37:13.609329100+08:00" level=info msg="[TCP] 127.0.0.1:65276 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:37:28.274355500+08:00" level=info msg="[TCP] 127.0.0.1:65313 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:37:33.656542300+08:00" level=info msg="[TCP] 127.0.0.1:65329 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:37:34.020011600+08:00" level=info msg="[TCP] 127.0.0.1:65333 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-25T13:37:39.104405700+08:00" level=info msg="[TCP] 127.0.0.1:65356 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:37:39.126106900+08:00" level=info msg="[TCP] 127.0.0.1:65360 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-25T13:37:39.127625100+08:00" level=info msg="[TCP] 127.0.0.1:65359 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-25T13:37:41.012474900+08:00" level=info msg="[TCP] 127.0.0.1:65369 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:37:41.371760500+08:00" level=info msg="[TCP] 127.0.0.1:65373 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:37:43.666997100+08:00" level=info msg="[TCP] 127.0.0.1:65381 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:37:43.962163200+08:00" level=info msg="[TCP] 127.0.0.1:65384 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:37:47.374673900+08:00" level=info msg="[TCP] 127.0.0.1:65395 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:37:47.694448300+08:00" level=info msg="[TCP] 127.0.0.1:65398 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:37:48.695547300+08:00" level=info msg="[TCP] 127.0.0.1:65402 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:37:48.732142500+08:00" level=info msg="[TCP] 127.0.0.1:65405 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:37:49.025285300+08:00" level=info msg="[TCP] 127.0.0.1:65409 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:37:49.027180800+08:00" level=info msg="[TCP] 127.0.0.1:65411 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:37:49.028276100+08:00" level=info msg="[TCP] 127.0.0.1:65410 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:37:49.046056300+08:00" level=info msg="[TCP] 127.0.0.1:65418 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:37:49.422665400+08:00" level=info msg="[TCP] 127.0.0.1:65423 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-25T13:37:53.868079900+08:00" level=info msg="[TCP] 127.0.0.1:65435 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:37:54.163445500+08:00" level=info msg="[TCP] 127.0.0.1:65439 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:38:12.393636500+08:00" level=info msg="[TCP] 127.0.0.1:65481 --> westeurope-5.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-25T13:38:18.391596500+08:00" level=info msg="[TCP] 127.0.0.1:65499 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:38:33.534191600+08:00" level=info msg="[TCP] 127.0.0.1:49152 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:38:33.655955600+08:00" level=info msg="[TCP] 127.0.0.1:49155 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:38:34.884600900+08:00" level=info msg="[TCP] 127.0.0.1:49162 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:38:45.347352900+08:00" level=info msg="[TCP] 127.0.0.1:49188 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:38:47.431171100+08:00" level=info msg="[TCP] 127.0.0.1:49196 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:38:47.727479800+08:00" level=info msg="[TCP] 127.0.0.1:49200 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:38:47.729713400+08:00" level=info msg="[TCP] 127.0.0.1:49199 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:38:48.193719000+08:00" level=info msg="[TCP] 127.0.0.1:49207 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:38:53.917517400+08:00" level=info msg="[TCP] 127.0.0.1:49222 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:39:18.668580000+08:00" level=info msg="[TCP] 127.0.0.1:49284 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:39:19.057677100+08:00" level=info msg="[TCP] 127.0.0.1:49288 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:39:19.147034400+08:00" level=info msg="[TCP] 127.0.0.1:49292 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:39:20.881926900+08:00" level=info msg="[TCP] 127.0.0.1:49298 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:39:32.739811400+08:00" level=info msg="[TCP] 127.0.0.1:49327 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:39:33.040946500+08:00" level=info msg="[TCP] 127.0.0.1:49332 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:39:33.042343800+08:00" level=info msg="[TCP] 127.0.0.1:49331 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:39:33.482095700+08:00" level=info msg="[TCP] 127.0.0.1:49338 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:39:33.762645500+08:00" level=info msg="[TCP] 127.0.0.1:49341 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T13:39:51.571569900+08:00" level=info msg="[TCP] 127.0.0.1:49384 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:39:51.870137400+08:00" level=info msg="[TCP] 127.0.0.1:49387 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:39:51.876094000+08:00" level=info msg="[TCP] 127.0.0.1:49390 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:39:51.881259500+08:00" level=info msg="[TCP] 127.0.0.1:49393 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:39:51.885996500+08:00" level=info msg="[TCP] 127.0.0.1:49396 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:39:51.886570000+08:00" level=info msg="[TCP] 127.0.0.1:49399 --> lc-event.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:39:53.969459600+08:00" level=info msg="[TCP] 127.0.0.1:49407 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:40:19.055793600+08:00" level=info msg="[TCP] 127.0.0.1:49463 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:40:54.019460700+08:00" level=info msg="[TCP] 127.0.0.1:49540 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:41:00.988103900+08:00" level=info msg="[TCP] 127.0.0.1:49559 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:41:01.285886500+08:00" level=info msg="[TCP] 127.0.0.1:49564 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:41:01.310347600+08:00" level=info msg="[TCP] 127.0.0.1:49567 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:41:01.598411900+08:00" level=info msg="[TCP] 127.0.0.1:49574 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:41:01.600459900+08:00" level=info msg="[TCP] 127.0.0.1:49571 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:41:15.976682700+08:00" level=info msg="[TCP] 127.0.0.1:49609 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:41:16.958760900+08:00" level=info msg="[TCP] 127.0.0.1:49617 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:41:19.372130500+08:00" level=info msg="[TCP] 127.0.0.1:49625 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:41:21.157019300+08:00" level=info msg="[TCP] 127.0.0.1:49632 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:42:41.040560500+08:00" level=info msg="[TCP] 127.0.0.1:49856 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:42:54.120280900+08:00" level=info msg="[TCP] 127.0.0.1:49901 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:43:05.146330900+08:00" level=info msg="[TCP] 127.0.0.1:49932 --> content-autofill.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:43:25.390100200+08:00" level=info msg="[TCP] 127.0.0.1:49980 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:43:27.479161100+08:00" level=info msg="[TCP] 127.0.0.1:49989 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:43:34.559727000+08:00" level=info msg="[TCP] 127.0.0.1:50008 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:43:34.703879600+08:00" level=info msg="[TCP] 127.0.0.1:50011 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:43:35.158712400+08:00" level=info msg="[TCP] 127.0.0.1:50016 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:43:35.533607300+08:00" level=info msg="[TCP] 127.0.0.1:50019 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:43:35.866867500+08:00" level=info msg="[TCP] 127.0.0.1:50022 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:43:36.006615100+08:00" level=info msg="[TCP] 127.0.0.1:50025 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:43:39.057298900+08:00" level=info msg="[TCP] 127.0.0.1:50035 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:43:54.173426100+08:00" level=info msg="[TCP] 127.0.0.1:50079 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:43:58.938147500+08:00" level=info msg="[TCP] 127.0.0.1:50093 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:44:07.555855500+08:00" level=info msg="[TCP] 127.0.0.1:50117 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:10.040558600+08:00" level=info msg="[TCP] 127.0.0.1:50124 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:10.350399800+08:00" level=info msg="[TCP] 127.0.0.1:50129 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:11.173115100+08:00" level=info msg="[TCP] 127.0.0.1:50135 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:44:11.246854100+08:00" level=info msg="[TCP] 127.0.0.1:50138 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-25T13:44:12.223266400+08:00" level=info msg="[TCP] 127.0.0.1:50142 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:44:13.032846700+08:00" level=info msg="[TCP] 127.0.0.1:50146 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:44:13.037807900+08:00" level=info msg="[TCP] 127.0.0.1:50149 --> td.doubleclick.net:443 using GLOBAL"
time="2025-07-25T13:44:13.743342200+08:00" level=info msg="[TCP] 127.0.0.1:50154 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:14.049980500+08:00" level=info msg="[TCP] 127.0.0.1:50159 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:14.049980500+08:00" level=info msg="[TCP] 127.0.0.1:50162 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:14.049980500+08:00" level=info msg="[TCP] 127.0.0.1:50163 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:14.051685100+08:00" level=info msg="[TCP] 127.0.0.1:50161 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:14.052857000+08:00" level=info msg="[TCP] 127.0.0.1:50160 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:31.625710700+08:00" level=info msg="[TCP] 127.0.0.1:50216 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:44:32.389872700+08:00" level=info msg="[TCP] 127.0.0.1:50225 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:32.688739100+08:00" level=info msg="[TCP] 127.0.0.1:50229 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:32.696077100+08:00" level=info msg="[TCP] 127.0.0.1:50228 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:32.696077100+08:00" level=info msg="[TCP] 127.0.0.1:50230 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:44:35.373565300+08:00" level=info msg="[TCP] 127.0.0.1:50244 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T13:44:54.223666300+08:00" level=info msg="[TCP] 127.0.0.1:50289 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:45:03.944845700+08:00" level=info msg="[TCP] 127.0.0.1:50311 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:03.951935300+08:00" level=info msg="[TCP] 127.0.0.1:50314 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:04.246456900+08:00" level=info msg="[TCP] 127.0.0.1:50320 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:04.250984300+08:00" level=info msg="[TCP] 127.0.0.1:50318 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:04.252152800+08:00" level=info msg="[TCP] 127.0.0.1:50319 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:17.927966500+08:00" level=info msg="[TCP] 127.0.0.1:50359 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:18.241317300+08:00" level=info msg="[TCP] 127.0.0.1:50363 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:18.242528400+08:00" level=info msg="[TCP] 127.0.0.1:50365 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:18.242528400+08:00" level=info msg="[TCP] 127.0.0.1:50364 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:18.247806700+08:00" level=info msg="[TCP] 127.0.0.1:50366 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:22.176296300+08:00" level=info msg="[TCP] 127.0.0.1:50382 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:38.949142200+08:00" level=info msg="[TCP] 127.0.0.1:50422 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:40.452499800+08:00" level=info msg="[TCP] 127.0.0.1:50427 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:40.923414400+08:00" level=info msg="[TCP] 127.0.0.1:50433 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:40.924901800+08:00" level=info msg="[TCP] 127.0.0.1:50432 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:40.926402500+08:00" level=info msg="[TCP] 127.0.0.1:50434 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:50.901035600+08:00" level=info msg="[TCP] 127.0.0.1:50463 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:51.255953500+08:00" level=info msg="[TCP] 127.0.0.1:50467 --> sketch.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:45:51.273530900+08:00" level=info msg="[TCP] 127.0.0.1:50471 --> hub.vroid.com:443 using GLOBAL"
time="2025-07-25T13:45:51.276135200+08:00" level=info msg="[TCP] 127.0.0.1:50470 --> api.booth.pm:443 using GLOBAL"
time="2025-07-25T13:45:52.200296800+08:00" level=info msg="[TCP] 127.0.0.1:50479 --> booth.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:52.201376100+08:00" level=info msg="[TCP] 127.0.0.1:50480 --> webcatalog-auth.circle.ms:443 using GLOBAL"
time="2025-07-25T13:45:52.206258900+08:00" level=info msg="[TCP] 127.0.0.1:50481 --> booth.pximg.net:443 using GLOBAL"
time="2025-07-25T13:45:53.942271900+08:00" level=info msg="[TCP] 127.0.0.1:50492 --> webcatalogj07.blob.core.windows.net:443 using GLOBAL"
time="2025-07-25T13:45:54.275888700+08:00" level=info msg="[TCP] 127.0.0.1:50496 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:46:03.017414500+08:00" level=info msg="[TCP] 127.0.0.1:50517 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:46:36.675136800+08:00" level=info msg="[TCP] 127.0.0.1:50596 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:46:37.467530200+08:00" level=info msg="[TCP] 127.0.0.1:50600 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:46:37.770092000+08:00" level=info msg="[TCP] 127.0.0.1:50604 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:46:37.772654900+08:00" level=info msg="[TCP] 127.0.0.1:50606 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:46:37.774902300+08:00" level=info msg="[TCP] 127.0.0.1:50607 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:46:37.775995000+08:00" level=info msg="[TCP] 127.0.0.1:50605 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:46:37.775995000+08:00" level=info msg="[TCP] 127.0.0.1:50608 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:46:52.974421400+08:00" level=info msg="[TCP] 127.0.0.1:50651 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:46:53.141975600+08:00" level=info msg="[TCP] 127.0.0.1:50656 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:46:54.324430000+08:00" level=info msg="[TCP] 127.0.0.1:50662 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:47:08.713930700+08:00" level=info msg="[TCP] 127.0.0.1:50698 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:47:18.871447300+08:00" level=info msg="[TCP] 127.0.0.1:50726 --> booth.pximg.net:443 using GLOBAL"
time="2025-07-25T13:47:19.171400900+08:00" level=info msg="[TCP] 127.0.0.1:50730 --> booth.pximg.net:443 using GLOBAL"
time="2025-07-25T13:47:54.377471800+08:00" level=info msg="[TCP] 127.0.0.1:50809 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:48:37.646918000+08:00" level=info msg="[TCP] 127.0.0.1:50906 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:48:37.662340200+08:00" level=info msg="[TCP] 127.0.0.1:50909 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:48:37.707332900+08:00" level=info msg="[TCP] 127.0.0.1:50912 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:49:00.515788300+08:00" level=info msg="[TCP] 127.0.0.1:50964 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:50:45.516389800+08:00" level=info msg="[TCP] 127.0.0.1:51228 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:50:45.920468400+08:00" level=info msg="[TCP] 127.0.0.1:51231 --> api.x.com:443 using GLOBAL"
time="2025-07-25T13:50:45.923983000+08:00" level=info msg="[TCP] 127.0.0.1:51234 --> x.com:443 using GLOBAL"
time="2025-07-25T13:50:45.932078500+08:00" level=info msg="[TCP] 127.0.0.1:51237 --> api.x.com:443 using GLOBAL"
time="2025-07-25T13:50:45.958849700+08:00" level=info msg="[TCP] 127.0.0.1:51240 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:50:46.226923100+08:00" level=info msg="[TCP] 127.0.0.1:51244 --> x.com:443 using GLOBAL"
time="2025-07-25T13:50:46.226923100+08:00" level=info msg="[TCP] 127.0.0.1:51248 --> x.com:443 using GLOBAL"
time="2025-07-25T13:50:46.229033600+08:00" level=info msg="[TCP] 127.0.0.1:51245 --> x.com:443 using GLOBAL"
time="2025-07-25T13:50:46.229607000+08:00" level=info msg="[TCP] 127.0.0.1:51246 --> x.com:443 using GLOBAL"
time="2025-07-25T13:50:46.230109900+08:00" level=info msg="[TCP] 127.0.0.1:51247 --> x.com:443 using GLOBAL"
time="2025-07-25T13:50:46.315034700+08:00" level=info msg="[TCP] 127.0.0.1:51259 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:46.662247400+08:00" level=info msg="[TCP] 127.0.0.1:51262 --> web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.301940800+08:00" level=info msg="[TCP] 127.0.0.1:51274 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.302443700+08:00" level=info msg="[TCP] 127.0.0.1:51275 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.305756300+08:00" level=info msg="[TCP] 127.0.0.1:51282 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.305756300+08:00" level=info msg="[TCP] 127.0.0.1:51279 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.308011300+08:00" level=info msg="[TCP] 127.0.0.1:51278 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.310231400+08:00" level=info msg="[TCP] 127.0.0.1:51268 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.310231400+08:00" level=info msg="[TCP] 127.0.0.1:51271 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.607930900+08:00" level=info msg="[TCP] 127.0.0.1:51292 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.607930900+08:00" level=info msg="[TCP] 127.0.0.1:51290 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.612349200+08:00" level=info msg="[TCP] 127.0.0.1:51291 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.614712700+08:00" level=info msg="[TCP] 127.0.0.1:51293 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:47.621021500+08:00" level=info msg="[TCP] 127.0.0.1:51302 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:50:48.657482900+08:00" level=info msg="[TCP] 127.0.0.1:51306 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T13:50:48.819153300+08:00" level=info msg="[TCP] 127.0.0.1:51309 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T13:50:48.957012900+08:00" level=info msg="[TCP] 127.0.0.1:51313 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T13:50:48.957012900+08:00" level=info msg="[TCP] 127.0.0.1:51314 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T13:50:51.311576000+08:00" level=info msg="[TCP] 127.0.0.1:51325 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:51.501851300+08:00" level=info msg="[TCP] 127.0.0.1:51328 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:50:51.584321000+08:00" level=info msg="[TCP] 127.0.0.1:51331 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:51.831344900+08:00" level=info msg="[TCP] 127.0.0.1:51334 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:51.834486300+08:00" level=info msg="[TCP] 127.0.0.1:51335 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:51.841628900+08:00" level=info msg="[TCP] 127.0.0.1:51340 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:52.415403000+08:00" level=info msg="[TCP] 127.0.0.1:51344 --> s.pximg.net:443 using GLOBAL"
time="2025-07-25T13:50:52.419726500+08:00" level=info msg="[TCP] 127.0.0.1:51347 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:50:52.628182900+08:00" level=info msg="[TCP] 127.0.0.1:51350 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:52.718341900+08:00" level=info msg="[TCP] 127.0.0.1:51353 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T13:50:52.769783700+08:00" level=info msg="[TCP] 127.0.0.1:51356 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T13:50:53.079794800+08:00" level=info msg="[TCP] 127.0.0.1:51361 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:53.704136300+08:00" level=info msg="[TCP] 127.0.0.1:51366 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:54.856762500+08:00" level=info msg="[TCP] 127.0.0.1:51370 --> zws5.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:50:57.263007300+08:00" level=info msg="[TCP] 127.0.0.1:51379 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:51:34.160491000+08:00" level=info msg="[TCP] 127.0.0.1:51463 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T13:51:35.138393600+08:00" level=info msg="[TCP] 127.0.0.1:51468 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T13:51:38.342017200+08:00" level=info msg="[TCP] 127.0.0.1:51480 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:51:42.779884800+08:00" level=info msg="[TCP] 127.0.0.1:51491 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:52:16.944385900+08:00" level=info msg="[TCP] 127.0.0.1:51569 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-25T13:52:19.220098300+08:00" level=info msg="[TCP] 127.0.0.1:51581 --> optimizationguide-pa.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:52:30.602753300+08:00" level=info msg="[TCP] 127.0.0.1:51608 --> clients4.google.com:443 using GLOBAL"
time="2025-07-25T13:52:35.130873500+08:00" level=info msg="[TCP] 127.0.0.1:51621 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:52:36.752126600+08:00" level=info msg="[TCP] 127.0.0.1:51626 --> content-autofill.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:52:36.754883000+08:00" level=info msg="[TCP] 127.0.0.1:51627 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:52:36.936968300+08:00" level=info msg="[TCP] 127.0.0.1:51632 --> www.gstatic.com:443 using GLOBAL"
time="2025-07-25T13:52:38.298220300+08:00" level=info msg="[TCP] 127.0.0.1:51639 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:52:39.633392100+08:00" level=info msg="[TCP] 127.0.0.1:51647 --> www.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:52:40.466749300+08:00" level=info msg="[TCP] 127.0.0.1:51651 --> passwordsleakcheck-pa.googleapis.com:443 using GLOBAL"
time="2025-07-25T13:52:54.626378500+08:00" level=info msg="[TCP] 127.0.0.1:51684 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:53:17.984621400+08:00" level=info msg="[TCP] 127.0.0.1:51744 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:53:18.600562200+08:00" level=info msg="[TCP] 127.0.0.1:51748 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:53:54.691933200+08:00" level=info msg="[TCP] 127.0.0.1:51827 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:54:00.021298800+08:00" level=info msg="[TCP] 127.0.0.1:51841 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:54:00.325446700+08:00" level=info msg="[TCP] 127.0.0.1:51846 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:54:00.327935200+08:00" level=info msg="[TCP] 127.0.0.1:51847 --> zws4-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:54:04.019440900+08:00" level=info msg="[TCP] 127.0.0.1:51858 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:54:04.322070400+08:00" level=info msg="[TCP] 127.0.0.1:51862 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:54:04.477150900+08:00" level=info msg="[TCP] 127.0.0.1:51865 --> zws2-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:54:37.390091400+08:00" level=info msg="[TCP] 127.0.0.1:51941 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T13:54:54.733194600+08:00" level=info msg="[TCP] 127.0.0.1:51983 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T13:55:09.999966700+08:00" level=info msg="[TCP] 127.0.0.1:52019 --> x.com:443 using GLOBAL"
time="2025-07-25T13:55:12.463578800+08:00" level=info msg="[TCP] 127.0.0.1:52029 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T13:55:14.400828200+08:00" level=info msg="[TCP] 127.0.0.1:52036 --> windows.msn.com:443 using GLOBAL"
time="2025-07-25T13:55:17.567306000+08:00" level=info msg="[TCP] 127.0.0.1:52053 --> settings-win.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:56:02.777196500+08:00" level=info msg="[TCP] 127.0.0.1:52158 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:56:04.945172900+08:00" level=info msg="[TCP] 127.0.0.1:52164 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T13:56:24.187843000+08:00" level=info msg="[TCP] 127.0.0.1:52215 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:56:32.908234200+08:00" level=info msg="[TCP] 127.0.0.1:52237 --> zws1-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:56:39.589414300+08:00" level=info msg="[TCP] 127.0.0.1:52254 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:56:39.826221200+08:00" level=info msg="[TCP] 127.0.0.1:52257 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:56:40.295976400+08:00" level=info msg="[TCP] 127.0.0.1:52261 --> zws5-1.web.telegram.org:443 using GLOBAL"
time="2025-07-25T13:57:14.235026800+08:00" level=info msg="[TCP] 127.0.0.1:52339 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T13:57:25.583961600+08:00" level=info msg="[TCP] 127.0.0.1:52372 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:57:25.897605900+08:00" level=info msg="[TCP] 127.0.0.1:52375 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T13:57:25.900033500+08:00" level=info msg="[TCP] 127.0.0.1:52378 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:57:29.316557300+08:00" level=info msg="[TCP] 127.0.0.1:52388 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:57:29.633027300+08:00" level=info msg="[TCP] 127.0.0.1:52393 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T13:57:36.323382800+08:00" level=info msg="[TCP] 127.0.0.1:52410 --> zhijuanwang.com:443 using GLOBAL"
time="2025-07-25T14:04:56.244976300+08:00" level=info msg="[TCP] 127.0.0.1:53487 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T14:04:58.500158500+08:00" level=info msg="[TCP] 127.0.0.1:53499 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:04:59.376281000+08:00" level=info msg="[TCP] 127.0.0.1:53503 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T14:05:14.295816200+08:00" level=info msg="[TCP] 127.0.0.1:53539 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T14:05:21.809471800+08:00" level=info msg="[TCP] 127.0.0.1:53563 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T14:05:22.654792300+08:00" level=info msg="[TCP] 127.0.0.1:53567 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:05:23.244808400+08:00" level=info msg="[TCP] 127.0.0.1:53572 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:05:34.129316200+08:00" level=info msg="[TCP] 127.0.0.1:53603 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T14:05:55.293751600+08:00" level=info msg="[TCP] 127.0.0.1:53656 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T14:05:55.438311500+08:00" level=info msg="[TCP] 127.0.0.1:53659 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T14:05:59.562615900+08:00" level=info msg="[TCP] 127.0.0.1:53670 --> www.pixiv.net:443 using GLOBAL"
time="2025-07-25T14:06:01.633595100+08:00" level=info msg="[TCP] 127.0.0.1:53678 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:06:14.303820000+08:00" level=info msg="[TCP] 127.0.0.1:53709 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T14:06:20.592012600+08:00" level=info msg="[TCP] 127.0.0.1:53725 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T14:06:20.744091500+08:00" level=info msg="[TCP] 127.0.0.1:53728 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T14:06:22.269260900+08:00" level=info msg="[TCP] 127.0.0.1:53736 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T14:06:54.343684800+08:00" level=info msg="[TCP] 127.0.0.1:53811 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-25T14:06:55.346679800+08:00" level=info msg="[TCP] 127.0.0.1:53817 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T14:07:25.585052300+08:00" level=info msg="[TCP] 127.0.0.1:53896 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:07:30.042216900+08:00" level=info msg="[TCP] 127.0.0.1:53909 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T14:07:32.418859200+08:00" level=info msg="[TCP] 127.0.0.1:53917 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-25T14:07:32.478181200+08:00" level=info msg="[TCP] 127.0.0.1:53920 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T14:07:32.516405800+08:00" level=info msg="[TCP] 127.0.0.1:53923 --> www.bing.com:443 using GLOBAL"
time="2025-07-25T14:07:34.253996200+08:00" level=info msg="[TCP] 127.0.0.1:53931 --> wup.browser.qq.com:443 using GLOBAL"
time="2025-07-25T14:07:34.860710900+08:00" level=info msg="[TCP] 127.0.0.1:53934 --> mumu.nie.netease.com:443 using GLOBAL"
time="2025-07-25T14:07:39.129328700+08:00" level=info msg="[TCP] 127.0.0.1:53955 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-25T14:07:39.129328700+08:00" level=info msg="[TCP] 127.0.0.1:53956 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-25T14:07:43.057723300+08:00" level=info msg="[TCP] 127.0.0.1:53969 --> config.teams.microsoft.com:443 using GLOBAL"
time="2025-07-25T14:07:43.664775600+08:00" level=info msg="[TCP] 127.0.0.1:53973 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T14:07:44.792851900+08:00" level=info msg="[TCP] 127.0.0.1:53978 --> v10.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-25T14:07:50.076464400+08:00" level=info msg="[TCP] 127.0.0.1:53997 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-25T14:07:50.078650000+08:00" level=info msg="[TCP] 127.0.0.1:53996 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-25T14:07:50.774330500+08:00" level=info msg="[TCP] 127.0.0.1:53993 --> filters.adtidy.org:443 using GLOBAL"
time="2025-07-25T14:07:54.118708100+08:00" level=info msg="[TCP] 127.0.0.1:54013 --> onedriveclucprodbn20043.blob.core.windows.net:443 using GLOBAL"
time="2025-07-25T14:08:12.410118900+08:00" level=info msg="[TCP] 127.0.0.1:54061 --> westeurope-5.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-25T14:08:50.595797600+08:00" level=info msg="[TCP] 127.0.0.1:54152 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T14:09:08.585060700+08:00" level=info msg="[TCP] 127.0.0.1:54195 --> errortrace.dev:443 using GLOBAL"
time="2025-07-25T14:09:08.656859100+08:00" level=info msg="[TCP] 127.0.0.1:54198 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T14:09:09.493903300+08:00" level=info msg="[TCP] 127.0.0.1:54203 --> api.x.com:443 using GLOBAL"
time="2025-07-25T14:09:09.497219000+08:00" level=info msg="[TCP] 127.0.0.1:54204 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.548179300+08:00" level=info msg="[TCP] 127.0.0.1:54209 --> x.com:443 using GLOBAL"
time="2025-07-25T14:09:09.562553300+08:00" level=info msg="[TCP] 127.0.0.1:54212 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.587695000+08:00" level=info msg="[TCP] 127.0.0.1:54216 --> pbs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.789745200+08:00" level=info msg="[TCP] 127.0.0.1:54219 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.790909900+08:00" level=info msg="[TCP] 127.0.0.1:54220 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.794361200+08:00" level=info msg="[TCP] 127.0.0.1:54221 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.798928700+08:00" level=info msg="[TCP] 127.0.0.1:54226 --> abs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.876955200+08:00" level=info msg="[TCP] 127.0.0.1:54231 --> x.com:443 using GLOBAL"
time="2025-07-25T14:09:09.881877600+08:00" level=info msg="[TCP] 127.0.0.1:54232 --> x.com:443 using GLOBAL"
time="2025-07-25T14:09:09.881877600+08:00" level=info msg="[TCP] 127.0.0.1:54237 --> x.com:443 using GLOBAL"
time="2025-07-25T14:09:09.886144900+08:00" level=info msg="[TCP] 127.0.0.1:54242 --> pbs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.886144900+08:00" level=info msg="[TCP] 127.0.0.1:54240 --> pbs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.889384800+08:00" level=info msg="[TCP] 127.0.0.1:54243 --> pbs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.891775000+08:00" level=info msg="[TCP] 127.0.0.1:54241 --> pbs.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:09.891775000+08:00" level=info msg="[TCP] 127.0.0.1:54252 --> x.com:443 using GLOBAL"
time="2025-07-25T14:09:10.721440400+08:00" level=info msg="[TCP] 127.0.0.1:54257 --> video.twimg.com:443 using GLOBAL"
time="2025-07-25T14:09:11.361617000+08:00" level=info msg="[TCP] 127.0.0.1:54261 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T14:09:12.568749200+08:00" level=info msg="[TCP] 127.0.0.1:54267 --> www.recaptcha.net:443 using GLOBAL"
time="2025-07-25T14:09:14.319447500+08:00" level=info msg="[TCP] 127.0.0.1:54273 --> assets.msn.com:443 using GLOBAL"
time="2025-07-25T14:09:15.532795000+08:00" level=info msg="[TCP] 127.0.0.1:54280 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T14:09:15.832639300+08:00" level=info msg="[TCP] 127.0.0.1:54285 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-25T14:09:19.537515800+08:00" level=info msg="[TCP] 127.0.0.1:54296 --> static.nvidiagrid.net:443 using GLOBAL"
time="2025-07-25T14:09:26.137708300+08:00" level=info msg="[TCP] 127.0.0.1:54316 --> vscode-sync.trafficmanager.net:443 using GLOBAL"
time="2025-07-25T14:09:26.832931100+08:00" level=info msg="[TCP] 127.0.0.1:54320 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:09:27.135558200+08:00" level=info msg="[TCP] 127.0.0.1:54325 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:09:27.166970400+08:00" level=info msg="[TCP] 127.0.0.1:54328 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:09:40.851000100+08:00" level=info msg="[TCP] 127.0.0.1:54363 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-25T14:09:41.433659600+08:00" level=info msg="[TCP] 127.0.0.1:54367 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:09:41.574666100+08:00" level=info msg="[TCP] 127.0.0.1:54370 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:09:41.741664100+08:00" level=info msg="[TCP] 127.0.0.1:54374 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:09:41.747162400+08:00" level=info msg="[TCP] 127.0.0.1:54375 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:09:41.748300800+08:00" level=info msg="[TCP] 127.0.0.1:54376 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:09:41.750069700+08:00" level=info msg="[TCP] 127.0.0.1:54373 --> i.pximg.net:443 using GLOBAL"
time="2025-07-25T14:10:08.555763000+08:00" level=info msg="[TCP] 127.0.0.1:54447 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-25T14:10:15.715860200+08:00" level=info msg="[TCP] 127.0.0.1:54468 --> windows.msn.com:443 using GLOBAL"
time="2025-07-25T14:10:41.372229000+08:00" level=info msg="[TCP] 127.0.0.1:54528 --> download.clashverge.dev:443 using GLOBAL"
time="2025-07-26T00:56:37.687227100+08:00" level=warning msg="Mihomo shutting down"
