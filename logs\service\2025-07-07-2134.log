Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-07T21:34:46.080315700+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-07T21:34:46.090599400+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-07T21:34:46.091112700+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-07T21:34:46.091624800+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-07T21:34:46.116511400+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-07T21:34:46.117025100+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-07T21:34:46.426518600+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-07T21:34:46.426518600+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-07T21:34:46.442532500+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-07T21:34:46.445535400+08:00" level=info msg="Initial configuration complete, total time: 358ms"
time="2025-07-07T21:34:46.445535400+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-07T21:34:46.447537300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-07T21:34:46.447537300+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-07T21:34:46.447537300+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-07T21:34:46.447537300+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Disney"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Line"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Lan"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider NowE"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider TVer"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider google"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider BBC"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider apple"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider telegram"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider TVB"
time="2025-07-07T21:34:46.456545600+08:00" level=info msg="Start initial provider Discord"
time="2025-07-07T21:34:49.531036800+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-07T21:34:49.531544800+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-07T21:34:49.531544800+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-07T21:34:49.532079300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-07T21:34:49.532079300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-07T21:34:49.532079300+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-07T21:34:49.533646500+08:00" level=info msg="Initial configuration complete, total time: 2ms"
time="2025-07-07T21:34:51.457803400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.457803400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459319300+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.458811100+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-07T21:34:51.459832400+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-07T21:34:51.463168900+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-07T21:34:51.463168900+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-07T21:34:51.463168900+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-07T21:34:51.463168900+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider apple"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Line"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider telegram"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Discord"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider google"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Disney"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider NowE"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider TVer"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider TVB"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider BBC"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider Lan"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-07T21:34:51.464702900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-07T21:34:56.464823500+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-07T21:34:56.465374800+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-07T21:34:56.465897200+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-07T21:34:56.465897200+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-07T21:34:56.465384100+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.464872000+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-07T21:34:56.465897200+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-07T21:34:56.465897200+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-07T21:34:56.464872000+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:34:56.465897200+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-07T21:34:56.468159000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-07T21:34:56.468159000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-07T21:34:56.468159000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-07T21:34:56.468672500+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-07T21:34:56.825060200+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-07T21:34:56.825592800+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-07T21:34:56.825592800+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-07T21:34:56.826104900+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-07T21:34:56.826617800+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-07T21:34:56.826617800+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-07T21:34:56.828152900+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider TVer"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider BBC"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider apple"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider TVB"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider telegram"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Line"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Discord"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider NowE"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider google"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Disney"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Lan"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-07T21:34:56.829686600+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-07T21:35:00.542938600+08:00" level=error msg="🇯🇵日本02 [2x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-07T21:35:00.542938600+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T21:35:00.555820300+08:00" level=error msg="🇸🇬新加坡01 [1.5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-07T21:35:00.555820300+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T21:35:01.680192500+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:59286 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.831016600+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.831016600+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-07T21:35:01.831016600+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.830015400+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T21:35:01.831016600+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-07T21:35:01.831016600+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-07T21:35:01.831016600+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-07T21:35:01.830015400+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-07T21:35:01.831016600+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-07T21:35:01.831016600+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-07T21:35:01.831016600+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-07T21:35:01.835946800+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-07T21:35:01.835946800+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-07T21:35:01.835946800+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-07T21:35:01.835946800+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-07T22:44:01.299744700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53466 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:01.299921600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53474 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:01.299921600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53478 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:01.299921600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53477 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:01.299921600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53479 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:01.299921600+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53480 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:04.666214700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53600 --> www.bing.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:04.726519300+08:00" level=info msg="[TCP] 127.0.0.1:53636 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:44:06.301004200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53620 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:06.301004200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53616 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:06.301004200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53619 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:06.301004200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53615 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:06.301004200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53618 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:06.301004200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53617 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T22:44:06.356716800+08:00" level=info msg="[TCP] 127.0.0.1:53644 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:44:06.356716800+08:00" level=info msg="[TCP] 127.0.0.1:53648 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:44:06.356716800+08:00" level=info msg="[TCP] 127.0.0.1:53645 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:44:06.357289500+08:00" level=info msg="[TCP] 127.0.0.1:53646 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:44:06.359207500+08:00" level=info msg="[TCP] 127.0.0.1:53649 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:44:06.361109900+08:00" level=info msg="[TCP] 127.0.0.1:53647 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:44:08.171740600+08:00" level=info msg="[TCP] 127.0.0.1:53660 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:44:08.176080900+08:00" level=info msg="[TCP] 127.0.0.1:53658 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T22:44:08.177759000+08:00" level=info msg="[TCP] 127.0.0.1:53662 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:44:08.493907200+08:00" level=info msg="[TCP] 127.0.0.1:53667 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:44:08.494416400+08:00" level=info msg="[TCP] 127.0.0.1:53666 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:44:08.496137400+08:00" level=info msg="[TCP] 127.0.0.1:53665 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:44:08.496137400+08:00" level=info msg="[TCP] 127.0.0.1:53668 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:44:08.872699100+08:00" level=error msg="🇸🇬新加坡01 [5x] failed to get the second response from http://cp.cloudflare.com/generate_204: Head \"http://cp.cloudflare.com/generate_204\": context deadline exceeded"
time="2025-07-07T22:44:08.872699100+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T22:44:14.180104000+08:00" level=info msg="[TCP] 127.0.0.1:53687 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:44:18.629742200+08:00" level=info msg="[TCP] 127.0.0.1:53698 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-07T22:44:30.111914200+08:00" level=info msg="[TCP] 127.0.0.1:53730 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:44:59.774427800+08:00" level=info msg="[TCP] 127.0.0.1:53811 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:45:09.172739400+08:00" level=info msg="[TCP] 127.0.0.1:53830 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:45:17.030071300+08:00" level=info msg="[TCP] 127.0.0.1:53850 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:45:38.931267800+08:00" level=info msg="[TCP] 127.0.0.1:53928 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:45:44.074431600+08:00" level=info msg="[TCP] 127.0.0.1:53937 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T22:46:08.173357700+08:00" level=info msg="[TCP] 127.0.0.1:54014 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:46:37.685877900+08:00" level=info msg="[TCP] 127.0.0.1:54059 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:46:49.442020100+08:00" level=info msg="[TCP] 127.0.0.1:54074 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-07T22:46:49.443526000+08:00" level=info msg="[TCP] 127.0.0.1:54075 --> services.bingapis.com:443 using GLOBAL"
time="2025-07-07T22:46:49.448766100+08:00" level=info msg="[TCP] 127.0.0.1:54078 --> g.ceipmsn.com:80 using GLOBAL"
time="2025-07-07T22:46:50.099552800+08:00" level=info msg="[TCP] 127.0.0.1:54081 --> go.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:46:50.740684500+08:00" level=info msg="[TCP] 127.0.0.1:54084 --> bingwallpaper.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:46:51.976598600+08:00" level=info msg="[TCP] 127.0.0.1:54087 --> g.ceipmsn.com:80 using GLOBAL"
time="2025-07-07T22:46:52.941771400+08:00" level=info msg="[TCP] 127.0.0.1:54090 --> bingwallpaper.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:46:53.361029400+08:00" level=info msg="[TCP] 127.0.0.1:54092 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:46:54.110786200+08:00" level=info msg="[TCP] 127.0.0.1:54096 --> g.ceipmsn.com:80 using GLOBAL"
time="2025-07-07T22:46:55.140428400+08:00" level=info msg="[TCP] 127.0.0.1:54100 --> bingwallpaper.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:47:12.904427600+08:00" level=info msg="[TCP] 127.0.0.1:54121 --> self.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:47:16.168671900+08:00" level=info msg="[TCP] 127.0.0.1:54127 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:47:19.458570500+08:00" level=info msg="[TCP] 127.0.0.1:54132 --> westus-0.in.applicationinsights.azure.com:443 using GLOBAL"
time="2025-07-07T22:47:23.498358900+08:00" level=info msg="[TCP] 127.0.0.1:54138 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:47:56.159437200+08:00" level=info msg="[TCP] 127.0.0.1:54187 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:48:08.182336900+08:00" level=info msg="[TCP] 127.0.0.1:54210 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:48:08.182336900+08:00" level=info msg="[TCP] 127.0.0.1:54212 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:48:22.866531300+08:00" level=info msg="[TCP] 127.0.0.1:54232 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T22:48:36.470799400+08:00" level=info msg="[TCP] 127.0.0.1:54256 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-07T22:48:37.498297400+08:00" level=info msg="[TCP] 127.0.0.1:54259 --> cn.bing.com:443 using GLOBAL"
time="2025-07-07T22:48:37.976667500+08:00" level=info msg="[TCP] 127.0.0.1:54262 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:48:38.413357800+08:00" level=info msg="[TCP] 127.0.0.1:54265 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:48:46.167449000+08:00" level=info msg="[TCP] 127.0.0.1:54274 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:48:46.369960900+08:00" level=info msg="[TCP] 127.0.0.1:54277 --> activity.windows.com:443 using GLOBAL"
time="2025-07-07T22:48:49.785623500+08:00" level=info msg="[TCP] 127.0.0.1:54283 --> login.live.com:443 using GLOBAL"
time="2025-07-07T22:48:54.058688300+08:00" level=info msg="[TCP] 127.0.0.1:54292 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:48:59.997391600+08:00" level=info msg="[TCP] 127.0.0.1:54300 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:49:09.452836600+08:00" level=info msg="[TCP] 127.0.0.1:54312 --> checkappexec.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:49:10.734671100+08:00" level=info msg="[TCP] 127.0.0.1:54315 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-07T22:49:11.184702300+08:00" level=info msg="[TCP] 127.0.0.1:54318 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-07T22:49:11.478035400+08:00" level=info msg="[TCP] 127.0.0.1:54320 --> update.code.visualstudio.com:443 using GLOBAL"
time="2025-07-07T22:49:12.193378400+08:00" level=info msg="[TCP] 127.0.0.1:54324 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-07T22:49:12.196289600+08:00" level=info msg="[TCP] 127.0.0.1:54323 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-07T22:49:13.255036400+08:00" level=info msg="[TCP] 127.0.0.1:54333 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:49:13.270843600+08:00" level=info msg="[TCP] 127.0.0.1:54335 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-07T22:49:13.338899900+08:00" level=info msg="[TCP] 127.0.0.1:54337 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-07T22:49:13.575825200+08:00" level=info msg="[TCP] 127.0.0.1:54339 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-07T22:49:13.577546400+08:00" level=info msg="[TCP] 127.0.0.1:54341 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-07T22:49:13.579226300+08:00" level=info msg="[TCP] 127.0.0.1:54340 --> marketplace.visualstudio.com:443 using GLOBAL"
time="2025-07-07T22:49:14.368344800+08:00" level=info msg="[TCP] 127.0.0.1:54346 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:49:14.768074900+08:00" level=info msg="[TCP] 127.0.0.1:54349 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:49:22.098414100+08:00" level=info msg="[TCP] 127.0.0.1:54360 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:49:29.346067400+08:00" level=info msg="[TCP] 127.0.0.1:54370 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:49:34.179137700+08:00" level=info msg="[TCP] 127.0.0.1:54386 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:49:34.182129500+08:00" level=info msg="[TCP] 127.0.0.1:54384 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:49:37.487324300+08:00" level=info msg="[TCP] 127.0.0.1:54391 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:49:45.174859100+08:00" level=info msg="[TCP] 127.0.0.1:54402 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:49:55.175438100+08:00" level=info msg="[TCP] 127.0.0.1:54418 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:50:00.053771300+08:00" level=info msg="[TCP] 127.0.0.1:54425 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:50:08.194525400+08:00" level=info msg="[TCP] 127.0.0.1:54436 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:50:09.204746300+08:00" level=info msg="[TCP] 127.0.0.1:54439 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-07T22:50:39.846232900+08:00" level=info msg="[TCP] 127.0.0.1:54524 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:50:39.861754600+08:00" level=info msg="[TCP] 127.0.0.1:54526 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T22:50:54.858523300+08:00" level=info msg="[TCP] 127.0.0.1:54622 --> ntp.msn.com:443 using GLOBAL"
time="2025-07-07T22:50:54.918595500+08:00" level=info msg="[TCP] 127.0.0.1:54625 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-07T22:50:55.758652400+08:00" level=info msg="[TCP] 127.0.0.1:54630 --> c.msn.com:443 using GLOBAL"
time="2025-07-07T22:50:55.759653700+08:00" level=info msg="[TCP] 127.0.0.1:54631 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:50:55.760164900+08:00" level=info msg="[TCP] 127.0.0.1:54644 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-07-07T22:50:55.760164900+08:00" level=info msg="[TCP] 127.0.0.1:54633 --> c.bing.com:443 using GLOBAL"
time="2025-07-07T22:50:55.760164900+08:00" level=info msg="[TCP] 127.0.0.1:54635 --> assets.msn.com:443 using GLOBAL"
time="2025-07-07T22:50:55.761276900+08:00" level=info msg="[TCP] 127.0.0.1:54640 --> api.msn.com:443 using GLOBAL"
time="2025-07-07T22:50:55.762261500+08:00" level=info msg="[TCP] 127.0.0.1:54645 --> img-s-msn-com.akamaized.net:443 using GLOBAL"
time="2025-07-07T22:50:55.764767400+08:00" level=info msg="[TCP] 127.0.0.1:54638 --> assets.msn.com:443 using GLOBAL"
time="2025-07-07T22:50:55.764767400+08:00" level=info msg="[TCP] 127.0.0.1:54642 --> th.bing.com:443 using GLOBAL"
time="2025-07-07T22:50:55.809808600+08:00" level=info msg="[TCP] 127.0.0.1:54648 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-07T22:50:56.115757300+08:00" level=info msg="[TCP] 127.0.0.1:54651 --> assets.msn.com:443 using GLOBAL"
time="2025-07-07T22:50:56.858352800+08:00" level=info msg="[TCP] 127.0.0.1:54656 --> login.live.com:443 using GLOBAL"
time="2025-07-07T22:50:57.929299800+08:00" level=info msg="[TCP] 127.0.0.1:54667 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-07T22:50:57.929299800+08:00" level=info msg="[TCP] 127.0.0.1:54670 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-07T22:50:57.930922500+08:00" level=info msg="[TCP] 127.0.0.1:54664 --> r.bing.com:443 using GLOBAL"
time="2025-07-07T22:50:57.934004800+08:00" level=info msg="[TCP] 127.0.0.1:54665 --> r.bing.com:443 using GLOBAL"
time="2025-07-07T22:50:58.176077600+08:00" level=info msg="[TCP] 127.0.0.1:54673 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:51:00.108108200+08:00" level=info msg="[TCP] 127.0.0.1:54688 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:51:00.263621500+08:00" level=info msg="[TCP] 127.0.0.1:54691 --> cn.bing.com:443 using GLOBAL"
time="2025-07-07T22:51:01.022326300+08:00" level=info msg="[TCP] 127.0.0.1:54696 --> r.bing.com:443 using GLOBAL"
time="2025-07-07T22:51:01.024071600+08:00" level=info msg="[TCP] 127.0.0.1:54698 --> r.bing.com:443 using GLOBAL"
time="2025-07-07T22:51:01.026617200+08:00" level=info msg="[TCP] 127.0.0.1:54701 --> th.bing.com:443 using GLOBAL"
time="2025-07-07T22:51:01.028402800+08:00" level=info msg="[TCP] 127.0.0.1:54699 --> th.bing.com:443 using GLOBAL"
time="2025-07-07T22:51:02.134752900+08:00" level=info msg="[TCP] 127.0.0.1:54707 --> storage.live.com:443 using GLOBAL"
time="2025-07-07T22:51:03.108657700+08:00" level=info msg="[TCP] 127.0.0.1:54714 --> cdn.sapphire.microsoftapp.net:443 using GLOBAL"
time="2025-07-07T22:51:03.663913800+08:00" level=info msg="[TCP] 127.0.0.1:54721 --> login.live.com:443 using GLOBAL"
time="2025-07-07T22:51:03.833111400+08:00" level=info msg="[TCP] 127.0.0.1:54726 --> www.iconfont.cn:443 using GLOBAL"
time="2025-07-07T22:51:03.834476600+08:00" level=info msg="[TCP] 127.0.0.1:54725 --> www.iconfont.cn:443 using GLOBAL"
time="2025-07-07T22:51:03.838308500+08:00" level=info msg="[TCP] 127.0.0.1:54728 --> nav-edge.smartscreen.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:51:05.446372400+08:00" level=info msg="[TCP] 127.0.0.1:54739 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:51:11.335015300+08:00" level=info msg="[TCP] 127.0.0.1:54764 --> www.iconfont.cn:443 using GLOBAL"
time="2025-07-07T22:51:11.339809800+08:00" level=info msg="[TCP] 127.0.0.1:54766 --> www.iconfont.cn:443 using GLOBAL"
time="2025-07-07T22:51:12.487706000+08:00" level=info msg="[TCP] 127.0.0.1:54771 --> www.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:12.487706000+08:00" level=info msg="[TCP] 127.0.0.1:54772 --> www.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.139467400+08:00" level=info msg="[TCP] 127.0.0.1:54779 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-07T22:51:13.141641900+08:00" level=info msg="[TCP] 127.0.0.1:54778 --> media.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.141641900+08:00" level=info msg="[TCP] 127.0.0.1:54776 --> media.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.141641900+08:00" level=info msg="[TCP] 127.0.0.1:54790 --> media.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.141641900+08:00" level=info msg="[TCP] 127.0.0.1:54783 --> www.googletagservices.com:443 using GLOBAL"
time="2025-07-07T22:51:13.142155600+08:00" level=info msg="[TCP] 127.0.0.1:54781 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-07T22:51:13.143139100+08:00" level=info msg="[TCP] 127.0.0.1:54789 --> media.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.146241100+08:00" level=info msg="[TCP] 127.0.0.1:54791 --> media.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.146241100+08:00" level=info msg="[TCP] 127.0.0.1:54788 --> media.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.146750800+08:00" level=info msg="[TCP] 127.0.0.1:54784 --> media.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.191043000+08:00" level=info msg="[TCP] 127.0.0.1:54796 --> cdn-ukwest.onetrust.com:443 using GLOBAL"
time="2025-07-07T22:51:13.193956400+08:00" level=info msg="[TCP] 127.0.0.1:54798 --> try.abtasty.com:443 using GLOBAL"
time="2025-07-07T22:51:13.894072700+08:00" level=info msg="[TCP] 127.0.0.1:54805 --> cdn-ukwest.onetrust.com:443 using GLOBAL"
time="2025-07-07T22:51:13.902489700+08:00" level=info msg="[TCP] 127.0.0.1:54809 --> cdn-share-sprites.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.905166100+08:00" level=info msg="[TCP] 127.0.0.1:54807 --> cdn-share-sprites.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54814 --> cdn-share-sprites.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54808 --> cdn-share-sprites.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54816 --> cdn-share-sprites.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54811 --> cdn-share-sprites.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54819 --> accounts.google.com:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54827 --> fps.cdnpk.net:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54822 --> js.hs-scripts.com:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54825 --> fps.cdnpk.net:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54829 --> fps.cdnpk.net:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54821 --> s.go-mpulse.net:443 using GLOBAL"
time="2025-07-07T22:51:13.*********+08:00" level=info msg="[TCP] 127.0.0.1:54830 --> fps.cdnpk.net:443 using GLOBAL"
time="2025-07-07T22:51:14.*********+08:00" level=info msg="[TCP] 127.0.0.1:54833 --> geolocation.onetrust.com:443 using GLOBAL"
time="2025-07-07T22:51:14.*********+08:00" level=info msg="[TCP] 127.0.0.1:54835 --> polyfill-fastly.io:443 using GLOBAL"
time="2025-07-07T22:51:14.270793000+08:00" level=info msg="[TCP] 127.0.0.1:54837 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-07T22:51:14.382002800+08:00" level=info msg="[TCP] 127.0.0.1:54839 --> www.google.com:443 using GLOBAL"
time="2025-07-07T22:51:14.396597400+08:00" level=info msg="[TCP] 127.0.0.1:54841 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-07T22:51:14.411501100+08:00" level=info msg="[TCP] 127.0.0.1:54843 --> s.pinimg.com:443 using GLOBAL"
time="2025-07-07T22:51:14.439959300+08:00" level=info msg="[TCP] 127.0.0.1:54845 --> seoab.io:443 using GLOBAL"
time="2025-07-07T22:51:14.564763900+08:00" level=info msg="[TCP] 127.0.0.1:54847 --> c.go-mpulse.net:443 using GLOBAL"
time="2025-07-07T22:51:14.667853600+08:00" level=info msg="[TCP] 127.0.0.1:54849 --> cdn-ukwest.onetrust.com:443 using GLOBAL"
time="2025-07-07T22:51:14.706397800+08:00" level=info msg="[TCP] 127.0.0.1:54851 --> seoab.io:443 using GLOBAL"
time="2025-07-07T22:51:14.962313300+08:00" level=info msg="[TCP] 127.0.0.1:54853 --> www.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:15.015109200+08:00" level=info msg="[TCP] 127.0.0.1:54856 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:51:15.171278400+08:00" level=info msg="[TCP] 127.0.0.1:54858 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:51:15.550283500+08:00" level=info msg="[TCP] 127.0.0.1:54864 --> config.trackingplan.com:443 using GLOBAL"
time="2025-07-07T22:51:15.551374100+08:00" level=info msg="[TCP] 127.0.0.1:54863 --> static.hotjar.com:443 using GLOBAL"
time="2025-07-07T22:51:15.758140200+08:00" level=info msg="[TCP] 127.0.0.1:54867 --> analytics.google.com:443 using GLOBAL"
time="2025-07-07T22:51:15.789515700+08:00" level=info msg="[TCP] 127.0.0.1:54871 --> analytics.google.com:443 using GLOBAL"
time="2025-07-07T22:51:15.798821300+08:00" level=info msg="[TCP] 127.0.0.1:54875 --> ct.pinterest.com:443 using GLOBAL"
time="2025-07-07T22:51:15.801326500+08:00" level=info msg="[TCP] 127.0.0.1:54873 --> ct.pinterest.com:443 using GLOBAL"
time="2025-07-07T22:51:15.802734600+08:00" level=info msg="[TCP] 127.0.0.1:54877 --> ct.pinterest.com:443 using GLOBAL"
time="2025-07-07T22:51:15.*********+08:00" level=info msg="[TCP] 127.0.0.1:54879 --> script.hotjar.com:443 using GLOBAL"
time="2025-07-07T22:51:16.*********+08:00" level=info msg="[TCP] 127.0.0.1:54881 --> vc.hotjar.io:443 using GLOBAL"
time="2025-07-07T22:51:16.*********+08:00" level=info msg="[TCP] 127.0.0.1:54883 --> ct.pinterest.com:443 using GLOBAL"
time="2025-07-07T22:51:16.*********+08:00" level=info msg="[TCP] 127.0.0.1:54885 --> accounts.google.com:443 using GLOBAL"
time="2025-07-07T22:51:17.*********+08:00" level=info msg="[TCP] 127.0.0.1:54888 --> lh3.googleusercontent.com:443 using GLOBAL"
time="2025-07-07T22:51:18.*********+08:00" level=info msg="[TCP] 127.0.0.1:54890 --> fonts.gstatic.com:443 using GLOBAL"
time="2025-07-07T22:51:18.*********+08:00" level=info msg="[TCP] 127.0.0.1:54897 --> media.flaticon.com:443 using GLOBAL"
time="2025-07-07T22:51:27.*********+08:00" level=info msg="[TCP] 127.0.0.1:54925 --> play.google.com:443 using GLOBAL"
time="2025-07-07T22:51:29.*********+08:00" level=info msg="[TCP] 127.0.0.1:54932 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:51:40.*********+08:00" level=info msg="[TCP] 127.0.0.1:54978 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:51:42.*********+08:00" level=info msg="[TCP] 127.0.0.1:54983 --> www.iconfont.cn:443 using GLOBAL"
time="2025-07-07T22:51:42.*********+08:00" level=info msg="[TCP] 127.0.0.1:54984 --> www.iconfont.cn:443 using GLOBAL"
time="2025-07-07T22:51:48.150325700+08:00" level=info msg="[TCP] 127.0.0.1:54996 --> icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:48.153424700+08:00" level=info msg="[TCP] 127.0.0.1:54995 --> icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.375486400+08:00" level=info msg="[TCP] 127.0.0.1:55002 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-07T22:51:49.378876600+08:00" level=info msg="[TCP] 127.0.0.1:55004 --> cdn.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.380257400+08:00" level=info msg="[TCP] 127.0.0.1:55006 --> cdn.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.381333800+08:00" level=info msg="[TCP] 127.0.0.1:55007 --> cdn.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.450850700+08:00" level=info msg="[TCP] 127.0.0.1:55010 --> cdn.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.454438300+08:00" level=info msg="[TCP] 127.0.0.1:55016 --> images.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.454438300+08:00" level=info msg="[TCP] 127.0.0.1:55012 --> cdn.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.457784100+08:00" level=info msg="[TCP] 127.0.0.1:55015 --> images.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.457784100+08:00" level=info msg="[TCP] 127.0.0.1:55018 --> images.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.459409600+08:00" level=info msg="[TCP] 127.0.0.1:55013 --> cdn.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.460934000+08:00" level=info msg="[TCP] 127.0.0.1:55019 --> images.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.463489100+08:00" level=info msg="[TCP] 127.0.0.1:55017 --> images.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.481177700+08:00" level=info msg="[TCP] 127.0.0.1:55026 --> www.iconfont.cn:443 using GLOBAL"
time="2025-07-07T22:51:49.501329100+08:00" level=info msg="[TCP] 127.0.0.1:55028 --> images.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.501329100+08:00" level=info msg="[TCP] 127.0.0.1:55032 --> www.iconfont.cn:443 using GLOBAL"
time="2025-07-07T22:51:49.503501700+08:00" level=info msg="[TCP] 127.0.0.1:55029 --> static.cloudflareinsights.com:443 using GLOBAL"
time="2025-07-07T22:51:49.775393800+08:00" level=info msg="[TCP] 127.0.0.1:55034 --> cdn.icon-icons.com:443 using GLOBAL"
time="2025-07-07T22:51:49.891056400+08:00" level=info msg="[TCP] 127.0.0.1:55036 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:51:52.108167800+08:00" level=info msg="[TCP] 127.0.0.1:55040 --> m.servedby-buysellads.com:443 using GLOBAL"
time="2025-07-07T22:51:52.737956300+08:00" level=info msg="[TCP] 127.0.0.1:55044 --> cdn4.buysellads.net:443 using GLOBAL"
time="2025-07-07T22:51:53.421349000+08:00" level=info msg="[TCP] 127.0.0.1:55047 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-07T22:51:56.490832200+08:00" level=info msg="[TCP] 127.0.0.1:55057 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:51:56.629994000+08:00" level=info msg="[TCP] 127.0.0.1:55059 --> media.istockphoto.com:443 using GLOBAL"
time="2025-07-07T22:51:56.632959700+08:00" level=info msg="[TCP] 127.0.0.1:55060 --> media.istockphoto.com:443 using GLOBAL"
time="2025-07-07T22:51:56.967152700+08:00" level=info msg="[TCP] 127.0.0.1:55063 --> media.istockphoto.com:443 using GLOBAL"
time="2025-07-07T22:51:56.970347400+08:00" level=info msg="[TCP] 127.0.0.1:55064 --> media.istockphoto.com:443 using GLOBAL"
time="2025-07-07T22:51:56.973929700+08:00" level=info msg="[TCP] 127.0.0.1:55066 --> media.istockphoto.com:443 using GLOBAL"
time="2025-07-07T22:51:57.057447400+08:00" level=info msg="[TCP] 127.0.0.1:55069 --> media.istockphoto.com:443 using GLOBAL"
time="2025-07-07T22:52:01.012907900+08:00" level=info msg="[TCP] 127.0.0.1:55075 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:52:01.074952100+08:00" level=info msg="[TCP] 127.0.0.1:55077 --> static.cloudflareinsights.com:443 using GLOBAL"
time="2025-07-07T22:52:08.175214800+08:00" level=info msg="[TCP] 127.0.0.1:55087 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T22:52:08.188658700+08:00" level=info msg="[TCP] 127.0.0.1:55089 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:52:12.175495500+08:00" level=info msg="[TCP] 127.0.0.1:55096 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:52:13.599782700+08:00" level=info msg="[TCP] 127.0.0.1:55100 --> kv501.prod.do.dsp.mp.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:52:19.430732800+08:00" level=info msg="[TCP] 127.0.0.1:55109 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:52:24.704102600+08:00" level=info msg="[TCP] 127.0.0.1:55118 --> th.bing.com:443 using GLOBAL"
time="2025-07-07T22:52:24.705605900+08:00" level=info msg="[TCP] 127.0.0.1:55119 --> th.bing.com:443 using GLOBAL"
time="2025-07-07T22:52:25.883374400+08:00" level=info msg="[TCP] 127.0.0.1:55124 --> storage.live.com:443 using GLOBAL"
time="2025-07-07T22:52:27.191993900+08:00" level=info msg="[TCP] 127.0.0.1:55128 --> login.live.com:443 using GLOBAL"
time="2025-07-07T22:52:30.143988700+08:00" level=info msg="[TCP] 127.0.0.1:55137 --> www.bossdesign.cn:443 using GLOBAL"
time="2025-07-07T22:52:30.143988700+08:00" level=info msg="[TCP] 127.0.0.1:55136 --> www.bossdesign.cn:443 using GLOBAL"
time="2025-07-07T22:52:37.860466500+08:00" level=info msg="[TCP] 127.0.0.1:55154 --> www.bossdesign.cn:443 using GLOBAL"
time="2025-07-07T22:52:37.881418900+08:00" level=info msg="[TCP] 127.0.0.1:55156 --> www.bossdesign.cn:443 using GLOBAL"
time="2025-07-07T22:52:42.049038500+08:00" level=info msg="[TCP] 127.0.0.1:55164 --> igoutu.cn:443 using GLOBAL"
time="2025-07-07T22:52:42.049038500+08:00" level=info msg="[TCP] 127.0.0.1:55163 --> igoutu.cn:443 using GLOBAL"
time="2025-07-07T22:52:43.526775500+08:00" level=info msg="[TCP] 127.0.0.1:55171 --> goodies.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.527282200+08:00" level=info msg="[TCP] 127.0.0.1:55169 --> maxst.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.754311700+08:00" level=info msg="[TCP] 127.0.0.1:55173 --> img.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.794831600+08:00" level=info msg="[TCP] 127.0.0.1:55175 --> goodies.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.797415000+08:00" level=info msg="[TCP] 127.0.0.1:55178 --> img.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.798743100+08:00" level=info msg="[TCP] 127.0.0.1:55177 --> img.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.798743100+08:00" level=info msg="[TCP] 127.0.0.1:55179 --> img.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.798743100+08:00" level=info msg="[TCP] 127.0.0.1:55186 --> maxst.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.800845000+08:00" level=info msg="[TCP] 127.0.0.1:55180 --> img.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.801365000+08:00" level=info msg="[TCP] 127.0.0.1:55181 --> img.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.904939100+08:00" level=info msg="[TCP] 127.0.0.1:55189 --> img.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:43.967935400+08:00" level=info msg="[TCP] 127.0.0.1:55191 --> goodies.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:44.013560200+08:00" level=info msg="[TCP] 127.0.0.1:55193 --> img.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:44.164943700+08:00" level=info msg="[TCP] 127.0.0.1:55196 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:52:46.451695500+08:00" level=info msg="[TCP] 127.0.0.1:55201 --> maxcdn.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:46.453608500+08:00" level=info msg="[TCP] 127.0.0.1:55204 --> maxcdn.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:46.453608500+08:00" level=info msg="[TCP] 127.0.0.1:55200 --> maxcdn.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:46.456124000+08:00" level=info msg="[TCP] 127.0.0.1:55207 --> maxcdn.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:46.457939600+08:00" level=info msg="[TCP] 127.0.0.1:55206 --> maxcdn.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:46.510047500+08:00" level=info msg="[TCP] 127.0.0.1:55210 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:52:46.594654600+08:00" level=info msg="[TCP] 127.0.0.1:55212 --> maxcdn.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:46.674774900+08:00" level=info msg="[TCP] 127.0.0.1:55214 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:52:46.834456900+08:00" level=info msg="[TCP] 127.0.0.1:55216 --> maxcdn.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:46.848274000+08:00" level=info msg="[TCP] 127.0.0.1:55218 --> distributions.crowdin.net:443 using GLOBAL"
time="2025-07-07T22:52:47.863444300+08:00" level=info msg="[TCP] 127.0.0.1:55220 --> distributions.crowdin.net:443 using GLOBAL"
time="2025-07-07T22:52:48.609824300+08:00" level=info msg="[TCP] 127.0.0.1:55225 --> api-icons.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:51.318916700+08:00" level=info msg="[TCP] 127.0.0.1:55230 --> search-app.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:51.858601000+08:00" level=info msg="[TCP] 127.0.0.1:55232 --> search-app.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:51.897427900+08:00" level=info msg="[TCP] 127.0.0.1:55236 --> auth.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:52.001706900+08:00" level=info msg="[TCP] 127.0.0.1:55233 --> search-app.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:52.205922900+08:00" level=info msg="[TCP] 127.0.0.1:55238 --> sentry.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:52.300757200+08:00" level=info msg="[TCP] 127.0.0.1:55241 --> api.shutterstock.com:443 using GLOBAL"
time="2025-07-07T22:52:52.980774800+08:00" level=info msg="[TCP] 127.0.0.1:55246 --> image.shutterstock.com:443 using GLOBAL"
time="2025-07-07T22:52:52.981988300+08:00" level=info msg="[TCP] 127.0.0.1:55245 --> image.shutterstock.com:443 using GLOBAL"
time="2025-07-07T22:52:53.113307800+08:00" level=info msg="[TCP] 127.0.0.1:55249 --> image.shutterstock.com:443 using GLOBAL"
time="2025-07-07T22:52:53.205525600+08:00" level=info msg="[TCP] 127.0.0.1:55251 --> www.googletagmanager.com:443 using GLOBAL"
time="2025-07-07T22:52:53.369148200+08:00" level=info msg="[TCP] 127.0.0.1:55254 --> auth.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:53.542486300+08:00" level=info msg="[TCP] 127.0.0.1:55243 --> cdn.carbonads.com:443 using GLOBAL"
time="2025-07-07T22:52:53.690688100+08:00" level=info msg="[TCP] 127.0.0.1:55257 --> shutterstock.7eer.net:443 using GLOBAL"
time="2025-07-07T22:52:53.803765200+08:00" level=info msg="[TCP] 127.0.0.1:55260 --> shutterstock.7eer.net:443 using GLOBAL"
time="2025-07-07T22:52:53.945354700+08:00" level=info msg="[TCP] 127.0.0.1:55262 --> api.growthbook.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:54.049180800+08:00" level=info msg="[TCP] 127.0.0.1:55265 --> api-analista.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:54.239884600+08:00" level=info msg="[TCP] 127.0.0.1:55267 --> srv.carbonads.net:443 using GLOBAL"
time="2025-07-07T22:52:54.520605200+08:00" level=info msg="[TCP] 127.0.0.1:55269 --> www.shutterstock.com:443 using GLOBAL"
time="2025-07-07T22:52:55.179656000+08:00" level=info msg="[TCP] 127.0.0.1:55276 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:52:55.539922900+08:00" level=info msg="[TCP] 127.0.0.1:55278 --> bst.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:56.866570700+08:00" level=info msg="[TCP] 127.0.0.1:55282 --> sentry.icons8.com:443 using GLOBAL"
time="2025-07-07T22:52:57.831211500+08:00" level=info msg="[TCP] 127.0.0.1:55285 --> www.bossdesign.cn:443 using GLOBAL"
time="2025-07-07T22:52:57.834503000+08:00" level=info msg="[TCP] 127.0.0.1:55286 --> www.bossdesign.cn:443 using GLOBAL"
time="2025-07-07T22:52:57.867115700+08:00" level=info msg="[TCP] 127.0.0.1:55289 --> ntp.msn.com:443 using GLOBAL"
time="2025-07-07T22:52:57.945437900+08:00" level=info msg="[TCP] 127.0.0.1:55291 --> www.bossdesign.cn:443 using GLOBAL"
time="2025-07-07T22:52:58.359203000+08:00" level=info msg="[TCP] 127.0.0.1:55293 --> api.msn.com:443 using GLOBAL"
time="2025-07-07T22:52:58.364426000+08:00" level=info msg="[TCP] 127.0.0.1:55299 --> assets.msn.com:443 using GLOBAL"
time="2025-07-07T22:52:58.389199300+08:00" level=info msg="[TCP] 127.0.0.1:55295 --> c.msn.com:443 using GLOBAL"
time="2025-07-07T22:52:58.441028700+08:00" level=info msg="[TCP] 127.0.0.1:55303 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-07-07T22:52:58.560558700+08:00" level=info msg="[TCP] 127.0.0.1:55305 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:52:58.581952300+08:00" level=info msg="[TCP] 127.0.0.1:55307 --> sb.scorecardresearch.com:443 using GLOBAL"
time="2025-07-07T22:52:58.645002000+08:00" level=info msg="[TCP] 127.0.0.1:55309 --> browser.events.data.msn.com:443 using GLOBAL"
time="2025-07-07T22:52:59.095935000+08:00" level=info msg="[TCP] 127.0.0.1:55314 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-07T22:52:59.095935000+08:00" level=info msg="[TCP] 127.0.0.1:55313 --> r.bing.com:443 using GLOBAL"
time="2025-07-07T22:52:59.097280500+08:00" level=info msg="[TCP] 127.0.0.1:55317 --> r.msftstatic.com:443 using GLOBAL"
time="2025-07-07T22:52:59.097280500+08:00" level=info msg="[TCP] 127.0.0.1:55311 --> r.bing.com:443 using GLOBAL"
time="2025-07-07T22:52:59.372165700+08:00" level=info msg="[TCP] 127.0.0.1:55297 --> c.bing.com:443 using GLOBAL"
time="2025-07-07T22:52:59.373433600+08:00" level=info msg="[TCP] 127.0.0.1:55301 --> assets.msn.com:443 using GLOBAL"
time="2025-07-07T22:52:59.401006700+08:00" level=info msg="[TCP] 127.0.0.1:55321 --> assets.msn.com:443 using GLOBAL"
time="2025-07-07T22:53:00.209874200+08:00" level=info msg="[TCP] 127.0.0.1:55323 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:53:02.176993500+08:00" level=info msg="[TCP] 127.0.0.1:55328 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:53:02.206509600+08:00" level=info msg="[TCP] 127.0.0.1:55330 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:53:02.208845800+08:00" level=info msg="[TCP] 127.0.0.1:55332 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:53:02.451063600+08:00" level=info msg="[TCP] 127.0.0.1:55335 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:53:05.250597100+08:00" level=info msg="[TCP] 127.0.0.1:55339 --> www.bossdesign.cn:443 using GLOBAL"
time="2025-07-07T22:53:07.180451800+08:00" level=info msg="[TCP] 127.0.0.1:55345 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:53:07.305255200+08:00" level=info msg="[TCP] 127.0.0.1:55347 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T22:53:08.182073800+08:00" level=info msg="[TCP] 127.0.0.1:55349 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:53:13.824549400+08:00" level=info msg="[TCP] 127.0.0.1:55360 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T22:53:14.180490700+08:00" level=info msg="[TCP] 127.0.0.1:55358 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:53:23.199500600+08:00" level=info msg="[TCP] 127.0.0.1:55374 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:53:48.751933700+08:00" level=info msg="[TCP] 127.0.0.1:55413 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-07T22:53:51.194831100+08:00" level=info msg="[TCP] 127.0.0.1:55418 --> www.miibeian.gov.cn:80 using GLOBAL"
time="2025-07-07T22:53:59.157499500+08:00" level=info msg="[TCP] 127.0.0.1:55438 --> login.live.com:443 using GLOBAL"
time="2025-07-07T22:54:00.262772100+08:00" level=info msg="[TCP] 127.0.0.1:55442 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:54:02.399120400+08:00" level=info msg="[TCP] 127.0.0.1:55446 --> activity.windows.com:443 using GLOBAL"
time="2025-07-07T22:54:08.173457100+08:00" level=info msg="[TCP] 127.0.0.1:55457 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:54:08.190295300+08:00" level=info msg="[TCP] 127.0.0.1:55455 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T22:54:08.495155600+08:00" level=info msg="[TCP] 127.0.0.1:55461 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:54:08.500664300+08:00" level=info msg="[TCP] 127.0.0.1:55459 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:54:08.619831300+08:00" level=info msg="[TCP] 127.0.0.1:55463 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:54:08.627240800+08:00" level=info msg="[TCP] 127.0.0.1:55465 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:54:12.786710300+08:00" level=info msg="[TCP] 127.0.0.1:55470 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-07T22:54:12.903777500+08:00" level=info msg="[TCP] 127.0.0.1:55474 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-07T22:54:17.606794300+08:00" level=info msg="[TCP] 127.0.0.1:55481 --> activity.windows.com:443 using GLOBAL"
time="2025-07-07T22:54:56.361711200+08:00" level=info msg="[TCP] 127.0.0.1:55537 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:55:21.737647000+08:00" level=info msg="[TCP] 127.0.0.1:55569 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T22:55:31.179216100+08:00" level=info msg="[TCP] 127.0.0.1:55620 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:55:32.181902300+08:00" level=info msg="[TCP] 127.0.0.1:55622 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:55:42.165609900+08:00" level=info msg="[TCP] 127.0.0.1:55652 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:55:52.269213800+08:00" level=info msg="[TCP] 127.0.0.1:55664 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:55:53.943924000+08:00" level=info msg="[TCP] 127.0.0.1:55696 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-07T22:56:00.384964000+08:00" level=info msg="[TCP] 127.0.0.1:55723 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:56:08.187631600+08:00" level=info msg="[TCP] 127.0.0.1:55742 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:56:08.429456700+08:00" level=info msg="[TCP] 127.0.0.1:55745 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T22:56:08.499609600+08:00" level=info msg="[TCP] 127.0.0.1:55747 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:56:08.504104500+08:00" level=info msg="[TCP] 127.0.0.1:55748 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:56:29.592935700+08:00" level=info msg="[TCP] 127.0.0.1:55777 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:56:29.701913800+08:00" level=info msg="[TCP] 127.0.0.1:55779 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:56:30.086906700+08:00" level=info msg="[TCP] 127.0.0.1:55786 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:56:30.088296200+08:00" level=info msg="[TCP] 127.0.0.1:55784 --> cn.bing.com:443 using GLOBAL"
time="2025-07-07T22:56:45.178652900+08:00" level=info msg="[TCP] 127.0.0.1:55810 --> c.go-mpulse.net:443 using GLOBAL"
time="2025-07-07T22:56:45.323431200+08:00" level=info msg="[TCP] 127.0.0.1:55812 --> c.go-mpulse.net:443 using GLOBAL"
time="2025-07-07T22:57:00.447185100+08:00" level=info msg="[TCP] 127.0.0.1:55883 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:57:29.730805100+08:00" level=info msg="[TCP] 127.0.0.1:55978 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T22:57:36.038628000+08:00" level=info msg="[TCP] 127.0.0.1:55995 --> cdnjs.cloudflare.com:443 using GLOBAL"
time="2025-07-07T22:57:39.409472400+08:00" level=info msg="[TCP] 127.0.0.1:56006 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T22:58:00.498093900+08:00" level=info msg="[TCP] 127.0.0.1:56124 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:58:08.188417400+08:00" level=info msg="[TCP] 127.0.0.1:56142 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:58:08.188417400+08:00" level=info msg="[TCP] 127.0.0.1:56140 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:58:08.297543000+08:00" level=info msg="[TCP] 127.0.0.1:56145 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:58:08.489653800+08:00" level=info msg="[TCP] 127.0.0.1:56149 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:58:08.505407700+08:00" level=info msg="[TCP] 127.0.0.1:56151 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:58:09.181046000+08:00" level=info msg="[TCP] 127.0.0.1:56138 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T22:58:26.160263500+08:00" level=info msg="[TCP] 127.0.0.1:56183 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T22:59:00.557855500+08:00" level=info msg="[TCP] 127.0.0.1:56269 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T22:59:13.965676100+08:00" level=info msg="[TCP] 127.0.0.1:56297 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-07T22:59:20.179412900+08:00" level=info msg="[TCP] 127.0.0.1:56324 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:59:20.233296200+08:00" level=info msg="[TCP] 127.0.0.1:56326 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:59:20.287708600+08:00" level=info msg="[TCP] 127.0.0.1:56328 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:59:20.600653300+08:00" level=info msg="[TCP] 127.0.0.1:56331 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T22:59:31.175735000+08:00" level=info msg="[TCP] 127.0.0.1:56378 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:59:41.276670400+08:00" level=info msg="[TCP] 127.0.0.1:56399 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T22:59:41.452272600+08:00" level=info msg="[TCP] 127.0.0.1:56402 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:00:08.167569300+08:00" level=info msg="[TCP] 127.0.0.1:56489 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:00:08.175248700+08:00" level=info msg="[TCP] 127.0.0.1:56491 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:00:08.283611900+08:00" level=info msg="[TCP] 127.0.0.1:56493 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:00:08.378788900+08:00" level=info msg="[TCP] 127.0.0.1:56496 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:00:08.487374800+08:00" level=info msg="[TCP] 127.0.0.1:56498 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:00:08.488616000+08:00" level=info msg="[TCP] 127.0.0.1:56499 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:00:25.178231900+08:00" level=info msg="[TCP] 127.0.0.1:56534 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:00:36.175013300+08:00" level=info msg="[TCP] 127.0.0.1:56576 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:00:46.314258300+08:00" level=info msg="[TCP] 127.0.0.1:56604 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:00:46.530754400+08:00" level=info msg="[TCP] 127.0.0.1:56606 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:00:47.956766800+08:00" level=info msg="[TCP] 127.0.0.1:56610 --> activity.windows.com:443 using GLOBAL"
time="2025-07-07T23:00:48.860674200+08:00" level=info msg="[TCP] 127.0.0.1:56612 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T23:00:54.922379300+08:00" level=info msg="[TCP] 127.0.0.1:56662 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:00:59.162671700+08:00" level=info msg="[TCP] 127.0.0.1:56677 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:01:00.478443700+08:00" level=info msg="[TCP] 127.0.0.1:56680 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:01:00.666585300+08:00" level=info msg="[TCP] 127.0.0.1:56685 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:01:00.778222900+08:00" level=info msg="[TCP] 127.0.0.1:56687 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:01:40.024249900+08:00" level=info msg="[TCP] 127.0.0.1:56775 --> cdnjs.cloudflare.com:443 using GLOBAL"
time="2025-07-07T23:02:00.730557400+08:00" level=info msg="[TCP] 127.0.0.1:56816 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:02:05.284128100+08:00" level=info msg="[TCP] 127.0.0.1:56823 --> activity.windows.com:443 using GLOBAL"
time="2025-07-07T23:02:07.370968300+08:00" level=info msg="[TCP] 127.0.0.1:56827 --> activity.windows.com:443 using GLOBAL"
time="2025-07-07T23:02:08.174411300+08:00" level=info msg="[TCP] 127.0.0.1:56831 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:02:08.340003100+08:00" level=info msg="[TCP] 127.0.0.1:56829 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:02:08.489107700+08:00" level=info msg="[TCP] 127.0.0.1:56834 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:02:08.490195500+08:00" level=info msg="[TCP] 127.0.0.1:56835 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:02:10.466016300+08:00" level=info msg="[TCP] 127.0.0.1:56840 --> cdnjs.cloudflare.com:443 using GLOBAL"
time="2025-07-07T23:02:48.694502500+08:00" level=info msg="[TCP] 127.0.0.1:56908 --> blacklist.tampermonkey.net:443 using GLOBAL"
time="2025-07-07T23:03:08.189440500+08:00" level=info msg="[TCP] 127.0.0.1:56961 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:03:32.594169100+08:00" level=info msg="[TCP] 127.0.0.1:57054 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T23:04:00.837905300+08:00" level=info msg="[TCP] 127.0.0.1:57164 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:04:08.183854800+08:00" level=info msg="[TCP] 127.0.0.1:57189 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:04:08.185475300+08:00" level=info msg="[TCP] 127.0.0.1:57191 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:04:08.511003700+08:00" level=info msg="[TCP] 127.0.0.1:57194 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:04:08.536583900+08:00" level=info msg="[TCP] 127.0.0.1:57195 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:04:23.997226600+08:00" level=info msg="[TCP] 127.0.0.1:57262 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-07T23:04:44.947968300+08:00" level=info msg="[TCP] 127.0.0.1:57371 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T23:04:47.312495800+08:00" level=info msg="[TCP] 127.0.0.1:57380 --> www.google.com:443 using GLOBAL"
time="2025-07-07T23:04:47.420979600+08:00" level=info msg="[TCP] 127.0.0.1:57382 --> www.google.com:443 using GLOBAL"
time="2025-07-07T23:04:48.088059800+08:00" level=info msg="[TCP] 127.0.0.1:57388 --> t0.gstatic.com:443 using GLOBAL"
time="2025-07-07T23:04:48.218803900+08:00" level=info msg="[TCP] 127.0.0.1:57390 --> t0.gstatic.com:443 using GLOBAL"
time="2025-07-07T23:04:57.427383500+08:00" level=info msg="[TCP] 127.0.0.1:57448 --> io.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:00.229114800+08:00" level=info msg="[TCP] 127.0.0.1:57462 --> io.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:00.233535200+08:00" level=info msg="[TCP] 127.0.0.1:57464 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:00.342380700+08:00" level=info msg="[TCP] 127.0.0.1:57467 --> io.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:00.393264200+08:00" level=info msg="[TCP] 127.0.0.1:57469 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:00.507040100+08:00" level=info msg="[TCP] 127.0.0.1:57472 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:00.530170300+08:00" level=info msg="[TCP] 127.0.0.1:57471 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:00.889986700+08:00" level=info msg="[TCP] 127.0.0.1:57476 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:05:05.451346200+08:00" level=info msg="[TCP] 127.0.0.1:57496 --> io.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:08.003899700+08:00" level=info msg="[TCP] 127.0.0.1:57503 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:08.076253700+08:00" level=info msg="[TCP] 127.0.0.1:57505 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:08.223710700+08:00" level=info msg="[TCP] 127.0.0.1:57508 --> hm.baidu.com:443 using GLOBAL"
time="2025-07-07T23:05:08.386390900+08:00" level=info msg="[TCP] 127.0.0.1:57513 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:05:08.676501200+08:00" level=info msg="[TCP] 127.0.0.1:57518 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:05:08.679062500+08:00" level=info msg="[TCP] 127.0.0.1:57517 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:05:08.681000700+08:00" level=info msg="[TCP] 127.0.0.1:57516 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:05:08.707821100+08:00" level=info msg="[TCP] 127.0.0.1:57520 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:05:08.728984400+08:00" level=info msg="[TCP] 127.0.0.1:57519 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:05:09.222831800+08:00" level=info msg="[TCP] 127.0.0.1:57529 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:09.229456000+08:00" level=info msg="[TCP] 127.0.0.1:57528 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:09.396580500+08:00" level=info msg="[TCP] 127.0.0.1:57533 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:09.400087400+08:00" level=info msg="[TCP] 127.0.0.1:57532 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:05:15.678261300+08:00" level=info msg="[TCP] 127.0.0.1:57563 --> hm.baidu.com:443 using GLOBAL"
time="2025-07-07T23:05:47.177945200+08:00" level=info msg="[TCP] 127.0.0.1:57694 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T23:05:55.912205800+08:00" level=info msg="[TCP] 127.0.0.1:57742 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:06:00.942734200+08:00" level=info msg="[TCP] 127.0.0.1:57772 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:06:02.398524500+08:00" level=info msg="[TCP] 127.0.0.1:57778 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T23:06:08.169818900+08:00" level=info msg="[TCP] 127.0.0.1:57796 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:06:08.294441000+08:00" level=info msg="[TCP] 127.0.0.1:57801 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:06:08.492425400+08:00" level=info msg="[TCP] 127.0.0.1:57803 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:06:08.493501500+08:00" level=info msg="[TCP] 127.0.0.1:57804 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:06:08.601055100+08:00" level=info msg="[TCP] 127.0.0.1:57808 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:06:09.185426600+08:00" level=info msg="[TCP] 127.0.0.1:57798 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:06:18.691193500+08:00" level=info msg="[TCP] 127.0.0.1:57821 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T23:06:20.177747200+08:00" level=info msg="[TCP] 127.0.0.1:57829 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:06:20.179194200+08:00" level=info msg="[TCP] 127.0.0.1:57827 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:06:20.285219100+08:00" level=info msg="[TCP] 127.0.0.1:57832 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:06:23.515070000+08:00" level=info msg="[TCP] 127.0.0.1:57844 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T23:06:23.516230000+08:00" level=info msg="[TCP] 127.0.0.1:57843 --> cn.bing.com:443 using GLOBAL"
time="2025-07-07T23:06:23.626531900+08:00" level=info msg="[TCP] 127.0.0.1:57847 --> cn.bing.com:443 using GLOBAL"
time="2025-07-07T23:06:23.649671600+08:00" level=info msg="[TCP] 127.0.0.1:57841 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:06:23.765993600+08:00" level=info msg="[TCP] 127.0.0.1:57849 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:06:30.153736300+08:00" level=info msg="[TCP] 127.0.0.1:57861 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T23:06:30.258414400+08:00" level=info msg="[TCP] 127.0.0.1:57863 --> edge.microsoft.com:443 using GLOBAL"
time="2025-07-07T23:06:31.181743800+08:00" level=info msg="[TCP] 127.0.0.1:57870 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:06:31.290936800+08:00" level=info msg="[TCP] 127.0.0.1:57872 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:06:32.657962000+08:00" level=info msg="[TCP] 127.0.0.1:57875 --> www.jyeoo.com:443 using GLOBAL"
time="2025-07-07T23:06:41.197258400+08:00" level=info msg="[TCP] 127.0.0.1:57887 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:06:42.196139600+08:00" level=info msg="[TCP] 127.0.0.1:57891 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:06:42.264962600+08:00" level=info msg="[TCP] 127.0.0.1:57890 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:06:42.394369500+08:00" level=info msg="[TCP] 127.0.0.1:57894 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:06:44.423371200+08:00" level=info msg="[TCP] 127.0.0.1:57898 --> io.xkw.com:443 using GLOBAL"
time="2025-07-07T23:06:49.861944500+08:00" level=info msg="[TCP] 127.0.0.1:57906 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:06:49.865203200+08:00" level=info msg="[TCP] 127.0.0.1:57908 --> hm.baidu.com:443 using GLOBAL"
time="2025-07-07T23:06:50.870262100+08:00" level=info msg="[TCP] 127.0.0.1:57911 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:06:51.874691100+08:00" level=info msg="[TCP] 127.0.0.1:57914 --> io.xkw.com:443 using GLOBAL"
time="2025-07-07T23:06:57.278515800+08:00" level=info msg="[TCP] 127.0.0.1:57924 --> hm.baidu.com:443 using GLOBAL"
time="2025-07-07T23:06:58.784387000+08:00" level=info msg="[TCP] 127.0.0.1:57928 --> zujuan.xkw.com:443 using GLOBAL"
time="2025-07-07T23:06:59.601321100+08:00" level=info msg="[TCP] 127.0.0.1:57931 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:06:59.921536400+08:00" level=info msg="[TCP] 127.0.0.1:57933 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:07:02.023949000+08:00" level=info msg="[TCP] 127.0.0.1:57936 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:19:11.852512700+08:00" level=info msg="[TCP] 127.0.0.1:60178 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-07T23:19:11.956026600+08:00" level=info msg="[TCP] 127.0.0.1:60181 --> default.exp-tas.com:443 using GLOBAL"
time="2025-07-07T23:32:01.595602300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62206 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:01.595602300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62214 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:01.595602300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62213 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:01.595602300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62217 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:01.595602300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62212 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:01.595602300+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62215 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:02.430645200+08:00" level=info msg="[TCP] 127.0.0.1:62235 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:32:06.595843200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62231 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:06.595843200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62229 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:06.595843200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62230 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:06.595843200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62228 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:06.595843200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62227 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:06.595843200+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:62226 --> metrics.cursor.sh:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:32:06.651264900+08:00" level=info msg="[TCP] 127.0.0.1:62245 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:06.651775700+08:00" level=info msg="[TCP] 127.0.0.1:62247 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:06.652909500+08:00" level=info msg="[TCP] 127.0.0.1:62248 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:06.653413400+08:00" level=info msg="[TCP] 127.0.0.1:62246 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:06.654966300+08:00" level=info msg="[TCP] 127.0.0.1:62244 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:06.658139900+08:00" level=info msg="[TCP] 127.0.0.1:62249 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:06.847712500+08:00" level=info msg="[TCP] 127.0.0.1:62256 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:06.853556800+08:00" level=info msg="[TCP] 127.0.0.1:62258 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:06.857100200+08:00" level=info msg="[TCP] 127.0.0.1:62260 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:06.875440600+08:00" level=info msg="[TCP] 127.0.0.1:62262 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:06.961963500+08:00" level=info msg="[TCP] 127.0.0.1:62264 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.040360800+08:00" level=info msg="[TCP] 127.0.0.1:62266 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.065719100+08:00" level=info msg="[TCP] 127.0.0.1:62270 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.069829100+08:00" level=info msg="[TCP] 127.0.0.1:62268 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.178531100+08:00" level=info msg="[TCP] 127.0.0.1:62273 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.185236800+08:00" level=info msg="[TCP] 127.0.0.1:62275 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.398763200+08:00" level=info msg="[TCP] 127.0.0.1:62278 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.402734100+08:00" level=info msg="[TCP] 127.0.0.1:62280 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.443094800+08:00" level=info msg="[TCP] 127.0.0.1:62282 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.529487500+08:00" level=info msg="[TCP] 127.0.0.1:62284 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.579610800+08:00" level=info msg="[TCP] 127.0.0.1:62288 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.658983300+08:00" level=info msg="[TCP] 127.0.0.1:62290 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.664362300+08:00" level=info msg="[TCP] 127.0.0.1:62292 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.745702800+08:00" level=info msg="[TCP] 127.0.0.1:62294 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.766271000+08:00" level=info msg="[TCP] 127.0.0.1:62296 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.861408600+08:00" level=info msg="[TCP] 127.0.0.1:62299 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.901748100+08:00" level=info msg="[TCP] 127.0.0.1:62301 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.920614100+08:00" level=info msg="[TCP] 127.0.0.1:62303 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:07.985621200+08:00" level=info msg="[TCP] 127.0.0.1:62305 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.004142900+08:00" level=info msg="[TCP] 127.0.0.1:62309 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.029511900+08:00" level=info msg="[TCP] 127.0.0.1:62311 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.093489300+08:00" level=info msg="[TCP] 127.0.0.1:62314 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.179459300+08:00" level=info msg="[TCP] 127.0.0.1:62320 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:32:08.199703600+08:00" level=info msg="[TCP] 127.0.0.1:62322 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.363904200+08:00" level=info msg="[TCP] 127.0.0.1:62327 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:32:08.367107600+08:00" level=info msg="[TCP] 127.0.0.1:62325 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.489221800+08:00" level=info msg="[TCP] 127.0.0.1:62333 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.489734300+08:00" level=info msg="[TCP] 127.0.0.1:62329 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:32:08.492473100+08:00" level=info msg="[TCP] 127.0.0.1:62330 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:32:08.506323400+08:00" level=info msg="[TCP] 127.0.0.1:62336 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.526742900+08:00" level=info msg="[TCP] 127.0.0.1:62338 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:32:08.595262900+08:00" level=info msg="[TCP] 127.0.0.1:62341 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:32:08.611269700+08:00" level=info msg="[TCP] 127.0.0.1:62344 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.618596900+08:00" level=info msg="[TCP] 127.0.0.1:62346 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.646212900+08:00" level=info msg="[TCP] 127.0.0.1:62348 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.650834900+08:00" level=info msg="[TCP] 127.0.0.1:62350 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:08.671930800+08:00" level=info msg="[TCP] 127.0.0.1:62352 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:32:08.689120500+08:00" level=info msg="[TCP] 127.0.0.1:62354 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:32:09.019027300+08:00" level=info msg="[TCP] 127.0.0.1:62360 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:32:09.040112100+08:00" level=info msg="[TCP] 127.0.0.1:62362 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.099314300+08:00" level=info msg="[TCP] 127.0.0.1:62364 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.128228900+08:00" level=info msg="[TCP] 127.0.0.1:62369 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.180164800+08:00" level=info msg="[TCP] 127.0.0.1:62316 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:32:09.181245100+08:00" level=info msg="[TCP] 127.0.0.1:62318 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.261448200+08:00" level=info msg="[TCP] 127.0.0.1:62371 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.389023100+08:00" level=info msg="[TCP] 127.0.0.1:62377 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.392582700+08:00" level=info msg="[TCP] 127.0.0.1:62375 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.442927400+08:00" level=info msg="[TCP] 127.0.0.1:62379 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.583446600+08:00" level=info msg="[TCP] 127.0.0.1:62381 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.621095200+08:00" level=info msg="[TCP] 127.0.0.1:62383 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.755630100+08:00" level=info msg="[TCP] 127.0.0.1:62385 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.766009400+08:00" level=info msg="[TCP] 127.0.0.1:62387 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:32:09.780027800+08:00" level=info msg="[TCP] 127.0.0.1:62389 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.782241500+08:00" level=info msg="[TCP] 127.0.0.1:62391 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.797685700+08:00" level=info msg="[TCP] 127.0.0.1:62356 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.960831400+08:00" level=info msg="[TCP] 127.0.0.1:62395 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.968452400+08:00" level=info msg="[TCP] 127.0.0.1:62397 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:09.999524800+08:00" level=info msg="[TCP] 127.0.0.1:62399 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.081770600+08:00" level=info msg="[TCP] 127.0.0.1:62402 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.097843900+08:00" level=info msg="[TCP] 127.0.0.1:62404 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.104775900+08:00" level=info msg="[TCP] 127.0.0.1:62406 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.202095200+08:00" level=info msg="[TCP] 127.0.0.1:62408 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.205034900+08:00" level=info msg="[TCP] 127.0.0.1:62410 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.305600800+08:00" level=info msg="[TCP] 127.0.0.1:62412 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.319377800+08:00" level=info msg="[TCP] 127.0.0.1:62373 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:32:10.376937300+08:00" level=info msg="[TCP] 127.0.0.1:62415 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.506280500+08:00" level=info msg="[TCP] 127.0.0.1:62417 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.525331100+08:00" level=info msg="[TCP] 127.0.0.1:62419 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:32:10.726185600+08:00" level=info msg="[TCP] 127.0.0.1:62423 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.814852700+08:00" level=info msg="[TCP] 127.0.0.1:62425 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:32:10.905834000+08:00" level=info msg="[TCP] 127.0.0.1:62393 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:10.911389500+08:00" level=info msg="[TCP] 127.0.0.1:62430 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.012192500+08:00" level=info msg="[TCP] 127.0.0.1:62307 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.047298200+08:00" level=info msg="[TCP] 127.0.0.1:62434 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.120393600+08:00" level=info msg="[TCP] 127.0.0.1:62437 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.136571800+08:00" level=info msg="[TCP] 127.0.0.1:62439 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.157944500+08:00" level=info msg="[TCP] 127.0.0.1:62441 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.301279900+08:00" level=info msg="[TCP] 127.0.0.1:62444 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.308348200+08:00" level=info msg="[TCP] 127.0.0.1:62446 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.585126600+08:00" level=info msg="[TCP] 127.0.0.1:62451 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.595062600+08:00" level=info msg="[TCP] 127.0.0.1:62453 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.650708500+08:00" level=info msg="[TCP] 127.0.0.1:62455 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.698014800+08:00" level=info msg="[TCP] 127.0.0.1:62421 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.712535800+08:00" level=info msg="[TCP] 127.0.0.1:62457 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.864015900+08:00" level=info msg="[TCP] 127.0.0.1:62459 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.871457800+08:00" level=info msg="[TCP] 127.0.0.1:62461 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.885513200+08:00" level=info msg="[TCP] 127.0.0.1:62428 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.898889300+08:00" level=info msg="[TCP] 127.0.0.1:62463 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:11.964572600+08:00" level=info msg="[TCP] 127.0.0.1:62432 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:32:12.012652100+08:00" level=info msg="[TCP] 127.0.0.1:62465 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.052967000+08:00" level=info msg="[TCP] 127.0.0.1:62471 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.175960700+08:00" level=info msg="[TCP] 127.0.0.1:62473 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.255350500+08:00" level=info msg="[TCP] 127.0.0.1:62475 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.317986700+08:00" level=info msg="[TCP] 127.0.0.1:62477 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.360729200+08:00" level=info msg="[TCP] 127.0.0.1:62479 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.436302400+08:00" level=info msg="[TCP] 127.0.0.1:62448 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.473198200+08:00" level=info msg="[TCP] 127.0.0.1:62481 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.591301500+08:00" level=info msg="[TCP] 127.0.0.1:62485 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.599044800+08:00" level=info msg="[TCP] 127.0.0.1:62487 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.629356700+08:00" level=info msg="[TCP] 127.0.0.1:62489 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.814386200+08:00" level=info msg="[TCP] 127.0.0.1:62491 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.846495500+08:00" level=info msg="[TCP] 127.0.0.1:62493 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.975708300+08:00" level=info msg="[TCP] 127.0.0.1:62495 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:12.983707100+08:00" level=info msg="[TCP] 127.0.0.1:62497 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.037544400+08:00" level=info msg="[TCP] 127.0.0.1:62467 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.049464800+08:00" level=info msg="[TCP] 127.0.0.1:62469 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.133714400+08:00" level=info msg="[TCP] 127.0.0.1:62499 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.188563800+08:00" level=info msg="[TCP] 127.0.0.1:62503 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.233179300+08:00" level=info msg="[TCP] 127.0.0.1:62506 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.375307900+08:00" level=info msg="[TCP] 127.0.0.1:62510 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.416728700+08:00" level=info msg="[TCP] 127.0.0.1:62513 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.441441400+08:00" level=info msg="[TCP] 127.0.0.1:62515 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.460016100+08:00" level=info msg="[TCP] 127.0.0.1:62483 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:32:13.562456800+08:00" level=info msg="[TCP] 127.0.0.1:62517 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.675584900+08:00" level=info msg="[TCP] 127.0.0.1:62519 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:32:13.693963700+08:00" level=info msg="[TCP] 127.0.0.1:62521 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.735632200+08:00" level=info msg="[TCP] 127.0.0.1:62523 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.770365300+08:00" level=info msg="[TCP] 127.0.0.1:62525 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.787468000+08:00" level=info msg="[TCP] 127.0.0.1:62527 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:32:13.831498900+08:00" level=info msg="[TCP] 127.0.0.1:62529 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.923027000+08:00" level=info msg="[TCP] 127.0.0.1:62531 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:13.989906800+08:00" level=info msg="[TCP] 127.0.0.1:62533 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.023907200+08:00" level=info msg="[TCP] 127.0.0.1:62535 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.097848300+08:00" level=info msg="[TCP] 127.0.0.1:62541 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.097848300+08:00" level=info msg="[TCP] 127.0.0.1:62539 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.164012000+08:00" level=info msg="[TCP] 127.0.0.1:62501 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.165139700+08:00" level=info msg="[TCP] 127.0.0.1:62543 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.275577100+08:00" level=info msg="[TCP] 127.0.0.1:62547 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.278105200+08:00" level=info msg="[TCP] 127.0.0.1:62549 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.336204800+08:00" level=info msg="[TCP] 127.0.0.1:62551 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.456368100+08:00" level=info msg="[TCP] 127.0.0.1:62553 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.501370600+08:00" level=info msg="[TCP] 127.0.0.1:62555 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.512443300+08:00" level=info msg="[TCP] 127.0.0.1:62557 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.596022200+08:00" level=info msg="[TCP] 127.0.0.1:62560 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.713440200+08:00" level=info msg="[TCP] 127.0.0.1:62562 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:14.764772400+08:00" level=info msg="[TCP] 127.0.0.1:62565 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:15.080106200+08:00" level=info msg="[TCP] 127.0.0.1:62537 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:15.284913600+08:00" level=info msg="[TCP] 127.0.0.1:62545 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:16.489961200+08:00" level=info msg="[TCP] 127.0.0.1:62508 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:32:19.193260600+08:00" level=info msg="[TCP] 127.0.0.1:62578 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:33:02.486733700+08:00" level=info msg="[TCP] 127.0.0.1:62689 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:33:08.212169400+08:00" level=info msg="[TCP] 127.0.0.1:62707 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:33:08.261742700+08:00" level=info msg="[TCP] 127.0.0.1:62705 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:33:08.313193500+08:00" level=info msg="[TCP] 127.0.0.1:62703 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:33:08.499389600+08:00" level=info msg="[TCP] 127.0.0.1:62712 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:33:08.624158600+08:00" level=info msg="[TCP] 127.0.0.1:62711 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:33:21.179640400+08:00" level=info msg="[TCP] 127.0.0.1:62742 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:33:24.452669600+08:00" level=info msg="[TCP] 127.0.0.1:62740 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:33:29.147128200+08:00" level=info msg="[TCP] 127.0.0.1:62752 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T23:33:32.178977500+08:00" level=info msg="[TCP] 127.0.0.1:62764 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:33:43.170479500+08:00" level=info msg="[TCP] 127.0.0.1:62800 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:34:08.245817900+08:00" level=info msg="[TCP] 127.0.0.1:62862 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:34:08.494734800+08:00" level=info msg="[TCP] 127.0.0.1:62866 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:34:08.509008600+08:00" level=info msg="[TCP] 127.0.0.1:62865 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:34:28.635010800+08:00" level=info msg="[TCP] 127.0.0.1:62908 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-07T23:35:08.188052900+08:00" level=info msg="[TCP] 127.0.0.1:63070 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:35:08.496956700+08:00" level=info msg="[TCP] 127.0.0.1:63074 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:35:08.496956700+08:00" level=info msg="[TCP] 127.0.0.1:63075 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:35:52.780477400+08:00" level=info msg="[TCP] 127.0.0.1:63267 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T23:36:01.407582300+08:00" level=info msg="[TCP] 127.0.0.1:63333 --> api2.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:36:08.431039000+08:00" level=info msg="[TCP] 127.0.0.1:63358 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:37:00.094901700+08:00" level=info msg="[TCP] 127.0.0.1:63444 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T23:37:08.193815100+08:00" level=info msg="[TCP] 127.0.0.1:63481 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:37:36.438121200+08:00" level=info msg="[TCP] 127.0.0.1:63538 --> mobile.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T23:38:08.185186400+08:00" level=info msg="[TCP] 127.0.0.1:63648 --> i0.hdslb.com:443 using GLOBAL"
time="2025-07-07T23:38:08.198907700+08:00" level=info msg="[TCP] 127.0.0.1:63650 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:38:39.733069500+08:00" level=info msg="[TCP] 127.0.0.1:63776 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T23:38:48.070265100+08:00" level=info msg="[TCP] 127.0.0.1:63799 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-07T23:38:48.304482100+08:00" level=info msg="[TCP] 127.0.0.1:63802 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-07T23:38:48.503582000+08:00" level=info msg="[TCP] 127.0.0.1:63804 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:48.651390600+08:00" level=info msg="[TCP] 127.0.0.1:63806 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:48.812102900+08:00" level=info msg="[TCP] 127.0.0.1:63809 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:48.814635600+08:00" level=info msg="[TCP] 127.0.0.1:63808 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:48.961642500+08:00" level=info msg="[TCP] 127.0.0.1:63812 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:48.990613800+08:00" level=info msg="[TCP] 127.0.0.1:63814 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.256312300+08:00" level=info msg="[TCP] 127.0.0.1:63821 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.445045900+08:00" level=info msg="[TCP] 127.0.0.1:63823 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.559581800+08:00" level=info msg="[TCP] 127.0.0.1:63826 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.561213800+08:00" level=info msg="[TCP] 127.0.0.1:63828 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.589967500+08:00" level=info msg="[TCP] 127.0.0.1:63833 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.592780400+08:00" level=info msg="[TCP] 127.0.0.1:63832 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.619379300+08:00" level=info msg="[TCP] 127.0.0.1:63836 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.702871400+08:00" level=info msg="[TCP] 127.0.0.1:63838 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.768331900+08:00" level=info msg="[TCP] 127.0.0.1:63842 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.823950700+08:00" level=info msg="[TCP] 127.0.0.1:63845 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.932970700+08:00" level=info msg="[TCP] 127.0.0.1:63847 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:53.946096400+08:00" level=info msg="[TCP] 127.0.0.1:63849 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.017120700+08:00" level=info msg="[TCP] 127.0.0.1:63851 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.064159500+08:00" level=info msg="[TCP] 127.0.0.1:63853 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.081473600+08:00" level=info msg="[TCP] 127.0.0.1:63855 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.142278300+08:00" level=info msg="[TCP] 127.0.0.1:63857 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.221179000+08:00" level=info msg="[TCP] 127.0.0.1:63860 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.234009700+08:00" level=info msg="[TCP] 127.0.0.1:63862 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.356175100+08:00" level=info msg="[TCP] 127.0.0.1:63866 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.400722100+08:00" level=info msg="[TCP] 127.0.0.1:63868 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.419316000+08:00" level=info msg="[TCP] 127.0.0.1:63870 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.563384400+08:00" level=info msg="[TCP] 127.0.0.1:63827 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.732840500+08:00" level=info msg="[TCP] 127.0.0.1:63874 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.764323600+08:00" level=info msg="[TCP] 127.0.0.1:63840 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.920430000+08:00" level=info msg="[TCP] 127.0.0.1:63876 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.928129700+08:00" level=info msg="[TCP] 127.0.0.1:63878 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:54.940850900+08:00" level=info msg="[TCP] 127.0.0.1:63880 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:55.048335900+08:00" level=info msg="[TCP] 127.0.0.1:63883 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:55.080929000+08:00" level=info msg="[TCP] 127.0.0.1:63885 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:55.095337900+08:00" level=info msg="[TCP] 127.0.0.1:63887 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:55.097744600+08:00" level=info msg="[TCP] 127.0.0.1:63889 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:55.245086000+08:00" level=info msg="[TCP] 127.0.0.1:63864 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:55.262780000+08:00" level=info msg="[TCP] 127.0.0.1:63892 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:55.270011600+08:00" level=info msg="[TCP] 127.0.0.1:63896 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:55.418348800+08:00" level=info msg="[TCP] 127.0.0.1:63899 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:55.649097300+08:00" level=info msg="[TCP] 127.0.0.1:63872 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:56.271659600+08:00" level=info msg="[TCP] 127.0.0.1:63893 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:38:59.178514700+08:00" level=info msg="[TCP] 127.0.0.1:63918 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:39:01.472910700+08:00" level=info msg="[TCP] 127.0.0.1:63927 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:39:01.650693500+08:00" level=info msg="[TCP] 127.0.0.1:63929 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:39:02.203060000+08:00" level=info msg="[TCP] 127.0.0.1:63933 --> metrics.cursor.sh:443 using GLOBAL"
time="2025-07-07T23:39:02.844856700+08:00" level=info msg="[TCP] 127.0.0.1:63935 --> www.bing.com:443 using GLOBAL"
time="2025-07-07T23:39:03.174312600+08:00" level=info msg="[TCP] 127.0.0.1:63937 --> api.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:39:08.172837700+08:00" level=info msg="[TCP] 127.0.0.1:63944 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:39:08.181595700+08:00" level=info msg="[TCP] 127.0.0.1:63946 --> api.vc.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:39:08.487895200+08:00" level=info msg="[TCP] 127.0.0.1:63950 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:39:08.496155500+08:00" level=info msg="[TCP] 127.0.0.1:63949 --> data.bilibili.com:443 using GLOBAL"
time="2025-07-07T23:39:20.173233800+08:00" level=info msg="[TCP] 127.0.0.1:63966 --> broadcast.chat.bilibili.com:7826 using GLOBAL"
time="2025-07-07T23:39:25.682592100+08:00" level=info msg="[TCP] 127.0.0.1:63983 --> skydrive.wns.windows.com:443 using GLOBAL"
time="2025-07-07T23:39:29.785460400+08:00" level=info msg="[TCP] 127.0.0.1:63991 --> main.vscode-cdn.net:443 using GLOBAL"
time="2025-07-07T23:39:38.531410800+08:00" level=info msg="[TCP] 127.0.0.1:64011 --> www.google-analytics.com:443 using GLOBAL"
time="2025-07-07T23:39:40.477214800+08:00" level=info msg="[TCP] 127.0.0.1:64022 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T23:39:43.869221700+08:00" level=info msg="[TCP] 127.0.0.1:64032 --> functional.events.data.microsoft.com:443 using GLOBAL"
time="2025-07-07T23:49:16.801123800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49214 --> default.exp-tas.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:49:16.801123800+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49219 --> default.exp-tas.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:49:23.817230700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49246 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:49:23.817230700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49245 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:49:32.006696900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49263 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-07T23:49:32.006696900+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:49268 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T00:19:16.803375700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53785 --> default.exp-tas.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T00:19:16.803375700+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53791 --> default.exp-tas.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T00:19:27.595747100+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53831 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T00:19:27.595860000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53835 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T00:19:32.595878400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53867 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T00:19:32.595878400+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:53868 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T00:27:18.389996000+08:00" level=warning msg="[TCP] dial GLOBAL 127.0.0.1:54730 --> mobile.events.data.microsoft.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-08T00:27:31.235483100+08:00" level=warning msg="Mihomo shutting down"
