2025-07-08 22:53:40 INFO - try to run core in service mode
2025-07-08 22:53:40 INFO - start service: {"config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "core_type": "verge-mihomo-alpha", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-08-2253.log"}
2025-07-08 22:53:40 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-08 22:53:40 INFO - No hotkeys configured
2025-07-08 22:53:40 INFO - Starting to create window
2025-07-08 22:53:40 INFO - Creating new window
2025-07-08 22:53:40 INFO - Window created successfully, attempting to show
2025-07-08 22:53:40 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-08 22:53:40 INFO - Successfully registered hotkey Control+Q for quit
2025-07-08 22:53:40 INFO - running timer task `R3SfPp0qApId`
2025-07-08 22:53:53 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-08 22:53:53 INFO - Successfully registered hotkey Control+Q for quit
2025-07-08 22:54:42 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-08 22:54:42 INFO - Successfully registered hotkey Control+Q for quit
2025-07-09 00:23:17 INFO - Starting to create window
2025-07-09 00:23:17 INFO - Found existing window, trying to show it
2025-07-09 00:23:17 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-09 00:23:17 INFO - Successfully registered hotkey Control+Q for quit
