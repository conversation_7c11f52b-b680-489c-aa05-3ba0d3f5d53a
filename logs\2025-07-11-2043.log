2025-07-11 20:43:31 INFO - try to run core in service mode
2025-07-11 20:43:31 INFO - start service: {"core_type": "verge-mihomo-alpha", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-11-2043.log", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev"}
2025-07-11 20:43:31 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-11 20:43:31 INFO - No hotkeys configured
2025-07-11 20:43:31 INFO - Starting to create window
2025-07-11 20:43:31 INFO - Creating new window
2025-07-11 20:43:31 INFO - Window created successfully, attempting to show
2025-07-11 20:43:31 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-11 20:43:31 INFO - Successfully registered hotkey Control+Q for quit
2025-07-11 20:43:54 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-11 20:43:54 INFO - Successfully registered hotkey Control+Q for quit
2025-07-11 20:55:20 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-11 20:55:20 INFO - Successfully registered hotkey Control+Q for quit
2025-07-11 20:56:17 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-11 20:56:17 INFO - Successfully registered hotkey Control+Q for quit
