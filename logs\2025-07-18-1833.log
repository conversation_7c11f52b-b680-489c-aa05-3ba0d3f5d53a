2025-07-18 18:33:37 INFO - try to run core in service mode
2025-07-18 18:33:37 INFO - start service: {"bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-07-18-1833.log", "core_type": "verge-mihomo-alpha", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml", "config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev"}
2025-07-18 18:33:38 INFO - Initializing hotkeys, global hotkey enabled: true
2025-07-18 18:33:38 INFO - No hotkeys configured
2025-07-18 18:33:38 INFO - Starting to create window
2025-07-18 18:33:38 INFO - Creating new window
2025-07-18 18:33:38 INFO - Window created successfully, attempting to show
2025-07-18 18:33:38 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:33:38 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:33:42 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:33:42 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:33:42 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:33:42 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:34:11 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:34:11 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:34:27 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:34:27 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:38:29 INFO - Starting to create window
2025-07-18 18:38:29 INFO - Found existing window, trying to show it
2025-07-18 18:38:29 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:38:29 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:40:35 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:40:35 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:40:35 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-07-18 18:40:35 INFO - Successfully registered hotkey Control+Q for quit
2025-07-18 18:40:47 INFO - stop the core by service
2025-07-18 18:40:47 ERROR - Failed to unregister hotkey: HotKey { mods: Modifiers(CONTROL), key: KeyQ, id: 524323 }
