2025-06-03 20:42:39 INFO - try to run core in service mode
2025-06-03 20:42:39 INFO - start service: {"config_dir": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev", "log_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\logs\\service\\2025-06-03-2042.log", "core_type": "verge-mihomo-alpha", "bin_path": "C:\\Program Files\\Clash Verge\\verge-mihomo-alpha.exe", "config_file": "C:\\Users\\<USER>\\AppData\\Roaming\\io.github.clash-verge-rev.clash-verge-rev\\clash-verge.yaml"}
2025-06-03 20:42:39 INFO - Initializing hotkeys, global hotkey enabled: true
2025-06-03 20:42:39 INFO - No hotkeys configured
2025-06-03 20:42:39 INFO - Starting to create window
2025-06-03 20:42:39 INFO - Creating new window
2025-06-03 20:42:39 INFO - Window created successfully, attempting to show
2025-06-03 20:42:39 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-03 20:42:39 INFO - Successfully registered hotkey Control+Q for quit
2025-06-03 20:42:39 INFO - running timer task `R3SfPp0qApId`
2025-06-03 21:36:29 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-03 21:36:29 INFO - Successfully registered hotkey Control+Q for quit
2025-06-03 22:41:20 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-03 22:41:20 INFO - Successfully registered hotkey Control+Q for quit
2025-06-03 22:41:20 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-03 22:41:20 INFO - Successfully registered hotkey Control+Q for quit
2025-06-03 22:51:20 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-03 22:51:20 INFO - Successfully registered hotkey Control+Q for quit
2025-06-03 22:51:20 INFO - Attempting to register hotkey: Control+Q for function: quit
2025-06-03 22:51:20 INFO - Successfully registered hotkey Control+Q for quit
