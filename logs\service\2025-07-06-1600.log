Spawning process: C:\Program Files\Clash Verge\verge-mihomo-alpha.exe -d C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev -f C:\Users\<USER>\AppData\Roaming\io.github.clash-verge-rev.clash-verge-rev\clash-verge.yaml
time="2025-07-06T16:00:44.043185200+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-06T16:00:44.051899600+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-06T16:00:44.051899600+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-06T16:00:44.052924300+08:00" level=info msg="Load GeoIP rule: cn"
time="2025-07-06T16:00:44.072573700+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-06T16:00:44.073574900+08:00" level=info msg="Load GeoSite rule: cn"
time="2025-07-06T16:00:44.330960200+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-06T16:00:44.331961200+08:00" level=info msg="Load GeoSite rule: private"
time="2025-07-06T16:00:44.347976300+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-06T16:00:44.350978800+08:00" level=info msg="Initial configuration complete, total time: 301ms"
time="2025-07-06T16:00:44.351500800+08:00" level=info msg="RESTful API listening at: 127.0.0.1:9097"
time="2025-07-06T16:00:44.352483300+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-06T16:00:44.352483300+08:00" level=info msg="Mixed(http+socks) proxy listening at: 127.0.0.1:7897"
time="2025-07-06T16:00:44.352483300+08:00" level=info msg="DNS server(UDP) listening at: [::]:1053"
time="2025-07-06T16:00:44.352483300+08:00" level=info msg="DNS server(TCP) listening at: [::]:1053"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider telegram"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider apple"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Discord"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider BBC"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider TVer"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Disney"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider TVB"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider google"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Lan"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider NowE"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Line"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-06T16:00:44.357493700+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-06T16:00:47.235214600+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-06T16:00:47.236255500+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-06T16:00:47.236255500+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-06T16:00:47.236767900+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-06T16:00:47.236767900+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-06T16:00:47.237281000+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-06T16:00:47.238308600+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider NaverTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NaverTV/NaverTV.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider HBOAsia error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOAsia/HBOAsia.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider Overcast error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Overcast/Overcast.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider OneDrive error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OneDrive/OneDrive.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider microsoft error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Microsoft/Microsoft.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider DiscoveryPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/DiscoveryPlus/DiscoveryPlus.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider myTVSUPER error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/myTVSUPER/myTVSUPER.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider Spotify error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Spotify/Spotify.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider PotatoChat error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PotatoChat/PotatoChat.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider FOXNOW error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXNOW/FOXNOW.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider apple error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Apple/Apple_Classical.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider Whatsapp error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Whatsapp/Whatsapp.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider AppleTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleTV/AppleTV.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider BritboxUK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BritboxUK/BritboxUK.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider telegram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Telegram/Telegram.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider KakaoTalk error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KakaoTalk/KakaoTalk.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider HBOUSA error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOUSA/HBOUSA.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider HBOHK error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HBOHK/HBOHK.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider Lan error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Lan/Lan.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider OpenAI error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/OpenAI/OpenAI.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-06T16:00:49.361025200+08:00" level=error msg="initial rule provider YouTubeMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/YouTubeMusic/YouTubeMusic.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.360140300+08:00" level=warning msg="[TCP] dial GLOBAL mihomo --> gitlab.com:443 error: dns resolve failed: couldn't find ip"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider Tiktok error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TikTok/TikTok.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider GameDownloadCN error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Game/GameDownloadCN/GameDownloadCN.yaml\": EOF"
time="2025-07-06T16:00:49.360140300+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-06T16:00:49.361036200+08:00" level=error msg="initial rule provider PandoraTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PandoraTV/PandoraTV.yaml\": EOF"
time="2025-07-06T16:00:49.365599000+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-06T16:00:49.365599000+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-06T16:00:49.365599000+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-06T16:00:49.365599000+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider NowE"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider telegram"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Lan"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Disney"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider apple"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Line"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Discord"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider google"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider BBC"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider TVB"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider TVer"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-06T16:00:49.367125900+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-06T16:00:49.633801700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.633801700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.634305100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.634316800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.635071700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.635071700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.635744900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.635744900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.635744900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.635744900+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.636994400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.636994400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.636994400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.636994400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.636994400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.636994400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.636994400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640018800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640018800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640018800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640018800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640018800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640018800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640529800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640529800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640529800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640529800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640529800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640529800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640529800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640529800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.640529800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.641043700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.641043700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.641043700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.641043700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.641043700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.641043700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.641043700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.646787800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.646787800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.646787800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.646787800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.646787800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.646787800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.646787800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.647300700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.647300700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.647300700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:49.718330500+08:00" level=error msg="initial rule provider ParamountPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/ParamountPlus/ParamountPlus.yaml\": EOF"
time="2025-07-06T16:00:49.738243200+08:00" level=error msg="initial rule provider BiliBili error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBili/BiliBili.yaml\": EOF"
time="2025-07-06T16:00:49.738243200+08:00" level=error msg="initial rule provider TVer error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVer/TVer.yaml\": EOF"
time="2025-07-06T16:00:49.739559200+08:00" level=error msg="initial rule provider Hulu error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Hulu/Hulu.yaml\": EOF"
time="2025-07-06T16:00:49.741174400+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-06T16:00:49.742680100+08:00" level=error msg="initial rule provider BiliBiliIntl error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BiliBiliIntl/BiliBiliIntl.yaml\": EOF"
time="2025-07-06T16:00:49.747884500+08:00" level=error msg="initial rule provider NowE error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/NowE/NowE.yaml\": EOF"
time="2025-07-06T16:00:49.776172500+08:00" level=error msg="initial rule provider Instagram error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Instagram/Instagram.yaml\": EOF"
time="2025-07-06T16:00:49.781158600+08:00" level=error msg="initial rule provider BBC error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/BBC/BBC.yaml\": EOF"
time="2025-07-06T16:00:49.869898100+08:00" level=error msg="initial rule provider KKTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/KKTV/KKTV.yaml\": EOF"
time="2025-07-06T16:00:49.986313400+08:00" level=error msg="initial rule provider google error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Google/Google.yaml\": EOF"
time="2025-07-06T16:00:50.015100800+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-06T16:00:50.180615400+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-06T16:00:50.191431400+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-06T16:00:50.212305500+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-06T16:00:50.239230000+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-06T16:00:50.251814600+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-06T16:00:50.251814600+08:00" level=error msg="initial rule provider Disney error: 403 Forbidden"
time="2025-07-06T16:00:50.258554600+08:00" level=error msg="initial rule provider Peacock error: 403 Forbidden"
time="2025-07-06T16:00:50.265381000+08:00" level=error msg="initial rule provider HKOpenTV error: 403 Forbidden"
time="2025-07-06T16:00:50.266689700+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-07-06T16:00:50.267800600+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-06T16:00:50.298045100+08:00" level=error msg="initial rule provider Line error: 403 Forbidden"
time="2025-07-06T16:00:50.308939300+08:00" level=error msg="initial rule provider AmazonIP error: 403 Forbidden"
time="2025-07-06T16:00:50.317157600+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-07-06T16:00:50.320117500+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-06T16:00:50.339665000+08:00" level=error msg="initial rule provider Twitter error: 403 Forbidden"
time="2025-07-06T16:00:50.342195500+08:00" level=error msg="initial rule provider AppleMusic error: 403 Forbidden"
time="2025-07-06T16:00:50.353662100+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-06T16:00:50.357610000+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-06T16:00:50.358938900+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-06T16:00:50.386235000+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-06T16:00:50.387419600+08:00" level=error msg="initial rule provider Bahamut error: 403 Forbidden"
time="2025-07-06T16:00:50.391155800+08:00" level=error msg="initial rule provider Discord error: 403 Forbidden"
time="2025-07-06T16:00:50.391155800+08:00" level=error msg="initial rule provider FOXPlus error: 403 Forbidden"
time="2025-07-06T16:00:50.394586700+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-06T16:00:50.395341500+08:00" level=error msg="initial rule provider Niconico error: 403 Forbidden"
time="2025-07-06T16:00:50.397625500+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-06T16:00:50.402475400+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-06T16:00:50.418081300+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-06T16:00:50.451670100+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-06T16:00:50.476909000+08:00" level=error msg="initial rule provider Acplay error: 403 Forbidden"
time="2025-07-06T16:00:50.522075200+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-07-06T16:00:50.630705600+08:00" level=error msg="initial rule provider Netflix error: 403 Forbidden"
time="2025-07-06T16:00:50.649661400+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-06T16:00:50.736446700+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-06T16:00:50.739159700+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-06T16:00:50.764852100+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-06T16:00:50.990154600+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-07-06T16:00:50.992822700+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-06T16:00:50.992822700+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-06T16:00:50.992822700+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-06T16:00:50.992822700+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-06T16:00:51.346728100+08:00" level=info msg="Start initial configuration in progress"
time="2025-07-06T16:00:51.347237900+08:00" level=info msg="Geodata Loader mode: memconservative"
time="2025-07-06T16:00:51.347237900+08:00" level=info msg="Geosite Matcher implementation: succinct"
time="2025-07-06T16:00:51.348285300+08:00" level=info msg="Finished initial GeoIP rule cn => DIRECT, records: 19242"
time="2025-07-06T16:00:51.348285300+08:00" level=info msg="Finished initial GeoSite rule cn => dns.nameserver-policy, records: 96338"
time="2025-07-06T16:00:51.348285300+08:00" level=info msg="Finished initial GeoSite rule private => dns.nameserver-policy, records: 131"
time="2025-07-06T16:00:51.349818800+08:00" level=info msg="Initial configuration complete, total time: 3ms"
time="2025-07-06T16:00:51.350872100+08:00" level=info msg="Sniffer is loaded and working"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider NowE"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Hulu"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider AppleTV"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider TVB"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider TVer"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider KKTV"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider GameDownloadCN"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Overcast"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider DiscoveryPlus"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider PotatoChat"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider BiliBili"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider AppleMusic"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Bahamut"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Twitter"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider FOXPlus"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider YouTubeMusic"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Netflix"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider google"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Spotify"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider telegram"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider FOXNOW"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Lan"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider BBC"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Peacock"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Discord"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider HBOHK"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Niconico"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Disney"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider PrimeVideo"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider NaverTV"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider HBOAsia"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider PandoraTV"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider BiliBiliIntl"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Tiktok"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider OpenAI"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider myTVSUPER"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider KakaoTalk"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Whatsapp"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider OneDrive"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Instagram"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider HKOpenTV"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider HBOUSA"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider apple"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Line"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider AmazonIP"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider microsoft"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider BritboxUK"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider Acplay"
time="2025-07-06T16:00:51.351383200+08:00" level=info msg="Start initial provider ParamountPlus"
time="2025-07-06T16:00:51.499173400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.499173400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.501426800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.501426800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.502253300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.502253300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.503863800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.503863800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.503863800+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.504377000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.504986200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.504986200+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.505494700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.505494700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.505494700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.505494700+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.506804000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.506804000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.506804000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.506804000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.506804000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.506804000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509157300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509157300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509157300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509157300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509157300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509157300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509669100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509669100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509669100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509669100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509669100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.509669100+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.514910000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.514910000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.514910000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.514910000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.514910000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.515435400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.515435400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.515435400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.515435400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.515435400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.515435400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.515435400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.515435400+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.515946300+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.519672000+08:00" level=info msg="[TCP] mihomo --> gitlab.com:443 using GLOBAL"
time="2025-07-06T16:00:51.579474000+08:00" level=error msg="initial rule provider AppleMusic error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AppleMusic/AppleMusic.yaml\": EOF"
time="2025-07-06T16:00:51.580185600+08:00" level=error msg="initial rule provider Twitter error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Twitter/Twitter.yaml\": EOF"
time="2025-07-06T16:00:51.590961700+08:00" level=error msg="initial rule provider Acplay error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Acplay/Acplay.yaml\": EOF"
time="2025-07-06T16:00:51.597649400+08:00" level=error msg="initial rule provider AmazonIP error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/AmazonIP/AmazonIP_IP.yaml\": EOF"
time="2025-07-06T16:00:51.602643400+08:00" level=error msg="initial rule provider Bahamut error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Bahamut/Bahamut.yaml\": EOF"
time="2025-07-06T16:00:51.606328500+08:00" level=error msg="initial rule provider Niconico error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Niconico/Niconico.yaml\": EOF"
time="2025-07-06T16:00:51.614163500+08:00" level=error msg="initial rule provider TVB error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/TVB/TVB.yaml\": EOF"
time="2025-07-06T16:00:51.618269600+08:00" level=error msg="initial rule provider Discord error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Discord/Discord.yaml\": EOF"
time="2025-07-06T16:00:51.620654500+08:00" level=error msg="initial rule provider Line error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Line/Line.yaml\": EOF"
time="2025-07-06T16:00:51.698955000+08:00" level=error msg="initial rule provider PrimeVideo error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/PrimeVideo/PrimeVideo.yaml\": EOF"
time="2025-07-06T16:00:51.716134100+08:00" level=error msg="initial rule provider Peacock error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Peacock/Peacock.yaml\": EOF"
time="2025-07-06T16:00:51.811596800+08:00" level=error msg="initial rule provider Disney error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Disney/Disney.yaml\": EOF"
time="2025-07-06T16:00:52.051976200+08:00" level=error msg="initial rule provider DiscoveryPlus error: 403 Forbidden"
time="2025-07-06T16:00:52.062287200+08:00" level=error msg="initial rule provider KakaoTalk error: 403 Forbidden"
time="2025-07-06T16:00:52.129584700+08:00" level=error msg="initial rule provider KKTV error: 403 Forbidden"
time="2025-07-06T16:00:52.131588800+08:00" level=error msg="initial rule provider Overcast error: 403 Forbidden"
time="2025-07-06T16:00:52.138616300+08:00" level=error msg="initial rule provider GameDownloadCN error: 403 Forbidden"
time="2025-07-06T16:00:52.145956000+08:00" level=error msg="initial rule provider Hulu error: 403 Forbidden"
time="2025-07-06T16:00:52.150755500+08:00" level=error msg="initial rule provider TVer error: 403 Forbidden"
time="2025-07-06T16:00:52.154936700+08:00" level=error msg="initial rule provider ParamountPlus error: 403 Forbidden"
time="2025-07-06T16:00:52.156487500+08:00" level=error msg="initial rule provider BiliBili error: 403 Forbidden"
time="2025-07-06T16:00:52.167420400+08:00" level=error msg="initial rule provider Tiktok error: 403 Forbidden"
time="2025-07-06T16:00:52.173860400+08:00" level=error msg="initial rule provider google error: 403 Forbidden"
time="2025-07-06T16:00:52.175206000+08:00" level=error msg="initial rule provider Instagram error: 403 Forbidden"
time="2025-07-06T16:00:52.176489600+08:00" level=error msg="initial rule provider Lan error: 403 Forbidden"
time="2025-07-06T16:00:52.176489600+08:00" level=error msg="initial rule provider OneDrive error: 403 Forbidden"
time="2025-07-06T16:00:52.181282700+08:00" level=error msg="initial rule provider HBOAsia error: 403 Forbidden"
time="2025-07-06T16:00:52.183234500+08:00" level=error msg="initial rule provider NowE error: 403 Forbidden"
time="2025-07-06T16:00:52.186357600+08:00" level=error msg="initial rule provider NaverTV error: 403 Forbidden"
time="2025-07-06T16:00:52.194660600+08:00" level=error msg="initial rule provider PotatoChat error: 403 Forbidden"
time="2025-07-06T16:00:52.197967500+08:00" level=error msg="initial rule provider telegram error: 403 Forbidden"
time="2025-07-06T16:00:52.199339300+08:00" level=error msg="initial rule provider HBOHK error: 403 Forbidden"
time="2025-07-06T16:00:52.220751200+08:00" level=error msg="initial rule provider PandoraTV error: 403 Forbidden"
time="2025-07-06T16:00:52.221693500+08:00" level=error msg="initial rule provider BBC error: 403 Forbidden"
time="2025-07-06T16:00:52.224790300+08:00" level=error msg="initial rule provider myTVSUPER error: 403 Forbidden"
time="2025-07-06T16:00:52.225529000+08:00" level=error msg="initial rule provider Spotify error: 403 Forbidden"
time="2025-07-06T16:00:52.230589600+08:00" level=error msg="initial rule provider apple error: 403 Forbidden"
time="2025-07-06T16:00:52.235649900+08:00" level=error msg="initial rule provider BiliBiliIntl error: 403 Forbidden"
time="2025-07-06T16:00:52.273006400+08:00" level=error msg="initial rule provider FOXPlus error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/FOXPlus/FOXPlus.yaml\": EOF"
time="2025-07-06T16:00:52.284713600+08:00" level=error msg="initial rule provider BritboxUK error: 403 Forbidden"
time="2025-07-06T16:00:52.286300900+08:00" level=error msg="initial rule provider Netflix error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/Netflix/Netflix_Classical.yaml\": EOF"
time="2025-07-06T16:00:52.296556300+08:00" level=error msg="initial rule provider OpenAI error: 403 Forbidden"
time="2025-07-06T16:00:52.315425600+08:00" level=error msg="initial rule provider Whatsapp error: 403 Forbidden"
time="2025-07-06T16:00:52.369789900+08:00" level=error msg="initial rule provider microsoft error: 403 Forbidden"
time="2025-07-06T16:00:52.441273200+08:00" level=error msg="initial rule provider FOXNOW error: 403 Forbidden"
time="2025-07-06T16:00:52.481733900+08:00" level=error msg="initial rule provider YouTubeMusic error: 403 Forbidden"
time="2025-07-06T16:00:52.568551100+08:00" level=error msg="initial rule provider AppleTV error: 403 Forbidden"
time="2025-07-06T16:00:52.605305400+08:00" level=error msg="initial rule provider HKOpenTV error: Get \"https://gitlab.com/lodepuly/proxy_tool_resources/-/raw/master/rule/Clash/HKOpenTV/HKOpenTV.yaml\": EOF"
time="2025-07-06T16:00:52.719170200+08:00" level=error msg="initial rule provider HBOUSA error: 403 Forbidden"
time="2025-07-06T16:00:52.722919800+08:00" level=info msg="Start initial Compatible provider 故障转移"
time="2025-07-06T16:00:52.722919800+08:00" level=info msg="Start initial Compatible provider 自动选择"
time="2025-07-06T16:00:52.722919800+08:00" level=info msg="Start initial Compatible provider 贤者云xianzheyun.top"
time="2025-07-06T16:00:52.722919800+08:00" level=info msg="Start initial Compatible provider default"
time="2025-07-06T16:00:55.445978400+08:00" level=error msg="🇮🇳印度-下载专享[0.5x] ipv6 only failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T16:00:55.445978400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-06T16:00:58.788130400+08:00" level=error msg="🇮🇳印度-下载专享[0.5x] ipv6 only failed to get the second response from http://www.gstatic.com/generate_204: Head \"http://www.gstatic.com/generate_204\": context deadline exceeded"
time="2025-07-06T16:00:58.788130400+08:00" level=warning msg="It is recommended to use HTTPS for provider.health-check.url and group.url to ensure better reliability. Due to some proxy providers hijacking test addresses and not being compatible with repeated HEAD requests, using HTTP may result in failed tests."
time="2025-07-07T00:07:34.312788700+08:00" level=warning msg="Mihomo shutting down"
